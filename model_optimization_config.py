"""
模型优化配置文件
包含改进模型准确性的各种配置参数
"""

# 数据预处理优化配置
DATA_PREPROCESSING_CONFIG = {
    # 异常值处理
    'outlier_detection': {
        'method': 'iqr',  # 'iqr', 'zscore', 'isolation_forest'
        'iqr_multiplier': 3.0,  # IQR倍数，3.0比1.5更宽松
        'zscore_threshold': 3.0,
        'isolation_forest_contamination': 0.1
    },
    
    # NaN值处理策略
    'nan_handling': {
        'max_nan_ratio': 0.5,  # 超过50%NaN的特征将被删除
        'strategies': {
            'return_features': 'zero',  # 收益率用0填充
            'price_features': 'forward_fill',  # 价格用前向填充
            'oscillator_features': 'median',  # 振荡器用中位数
            'volume_features': 'forward_fill_zero',  # 成交量先前向填充再用0
            'default': 'median'
        }
    },
    
    # 特征缩放
    'scaling': {
        'method': 'robust',  # 'standard', 'minmax', 'robust'
        'robust_quantile_range': (25.0, 75.0)
    }
}

# 特征工程优化配置
FEATURE_ENGINEERING_CONFIG = {
    # 技术指标参数优化
    'technical_indicators': {
        'rsi_periods': [6, 9, 14, 21, 28],  # 更多RSI周期
        'ma_periods': [5, 10, 20, 30, 50, 100, 200],  # 扩展MA周期
        'bb_windows': [10, 20, 30],  # 多个布林带窗口
        'macd_configs': [
            {'fast': 12, 'slow': 26, 'signal': 9},
            {'fast': 8, 'slow': 21, 'signal': 5},  # 更敏感的MACD
        ],
        'atr_periods': [7, 14, 21, 28]  # 多个ATR周期
    },
    
    # 高级特征
    'advanced_features': {
        'enable_fractal_features': True,
        'enable_market_microstructure': True,
        'enable_regime_detection': True,
        'enable_volatility_clustering': True
    },
    
    # 特征选择
    'feature_selection': {
        'variance_threshold': 0.01,
        'correlation_threshold': 0.95,
        'max_features': 100,
        'selection_methods': ['variance', 'correlation', 'importance', 'mutual_info']
    }
}

# 模型优化配置
MODEL_OPTIMIZATION_CONFIG = {
    # XGBoost优化参数
    'xgboost': {
        'param_space': {
            'n_estimators': [100, 200, 300, 500],
            'max_depth': [3, 4, 5, 6, 7],
            'learning_rate': [0.01, 0.05, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0],
            'colsample_bytree': [0.8, 0.9, 1.0],
            'reg_alpha': [0, 0.1, 0.5, 1.0],
            'reg_lambda': [0, 0.1, 0.5, 1.0]
        },
        'early_stopping_rounds': 50,
        'eval_metric': 'mlogloss'
    },
    
    # LightGBM优化参数
    'lightgbm': {
        'param_space': {
            'n_estimators': [100, 200, 300, 500],
            'max_depth': [3, 4, 5, 6, 7],
            'learning_rate': [0.01, 0.05, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0],
            'colsample_bytree': [0.8, 0.9, 1.0],
            'reg_alpha': [0, 0.1, 0.5, 1.0],
            'reg_lambda': [0, 0.1, 0.5, 1.0],
            'num_leaves': [31, 50, 100, 200]
        },
        'early_stopping_rounds': 50
    },
    
    # LSTM优化参数
    'lstm': {
        'param_space': {
            'units': [32, 64, 128, 256],
            'dropout': [0.1, 0.2, 0.3, 0.4],
            'recurrent_dropout': [0.1, 0.2, 0.3],
            'learning_rate': [0.0001, 0.001, 0.01],
            'batch_size': [16, 32, 64, 128],
            'timesteps': [5, 10, 15, 20, 30]
        },
        'architecture_options': {
            'bidirectional': True,
            'attention_mechanism': True,
            'residual_connections': True
        }
    },
    
    # 集成模型配置
    'ensemble': {
        'base_models': ['xgb', 'lgb', 'rf'],
        'meta_learner': 'logistic_regression',
        'stacking_cv_folds': 5,
        'voting_weights': 'auto'  # 'auto' or specific weights
    }
}

# 交叉验证优化配置
CROSS_VALIDATION_CONFIG = {
    'method': 'time_series_split',
    'n_splits': 5,
    'test_size': 0.2,
    'gap': 0,  # 训练集和测试集之间的间隔
    'purged_cv': True,  # 是否使用purged CV避免数据泄露
    'embargo': 24  # 禁运期（小时）
}

# 目标变量优化配置
TARGET_OPTIMIZATION_CONFIG = {
    # 标签平滑
    'label_smoothing': {
        'enabled': True,
        'alpha': 0.1
    },
    
    # 类别平衡
    'class_balancing': {
        'method': 'smote',  # 'smote', 'adasyn', 'random_oversample'
        'sampling_strategy': 'auto'
    },
    
    # 多标签策略
    'multi_target': {
        'enabled': False,
        'targets': ['direction', 'magnitude', 'volatility']
    }
}

# 模型评估优化配置
EVALUATION_CONFIG = {
    'metrics': [
        'accuracy', 'precision', 'recall', 'f1_macro', 'f1_weighted',
        'roc_auc_ovr', 'log_loss', 'matthews_corrcoef'
    ],
    'custom_metrics': {
        'directional_accuracy': True,
        'profit_based_accuracy': True,
        'risk_adjusted_return': True
    },
    'evaluation_windows': [
        {'name': 'short_term', 'periods': 24},
        {'name': 'medium_term', 'periods': 168},
        {'name': 'long_term', 'periods': 720}
    ]
}

# 在线学习配置
ONLINE_LEARNING_CONFIG = {
    'update_frequency': 'daily',  # 'hourly', 'daily', 'weekly'
    'window_size': 1000,
    'decay_factor': 0.95,
    'performance_threshold': 0.6,  # 性能低于此值时重新训练
    'concept_drift_detection': True
}

# 风险管理配置
RISK_MANAGEMENT_CONFIG = {
    'position_sizing': {
        'method': 'kelly_criterion',  # 'fixed', 'kelly_criterion', 'volatility_adjusted'
        'max_position_size': 0.1,
        'min_confidence_threshold': 0.6
    },
    'stop_loss': {
        'method': 'atr_based',  # 'fixed', 'atr_based', 'volatility_based'
        'atr_multiplier': 2.0,
        'max_loss_pct': 0.02
    }
}
