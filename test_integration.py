#!/usr/bin/env python3
"""
测试集成系统 - 验证第一阶段功能
"""

import sys
import os
import numpy as np
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.getcwd())

def test_enhanced_risk_management():
    """测试增强风险管理"""
    print("🧪 测试增强风险管理模块...")
    
    try:
        from enhanced_risk_management import EnhancedRiskManager
        
        # 创建风险管理器
        risk_manager = EnhancedRiskManager(50.0)
        
        # 测试动态参数计算
        test_params = risk_manager.calculate_dynamic_parameters(
            current_equity=45.0,
            market_volatility=0.045,
            recent_performance={
                'recent_trades': [
                    {'final_pnl': -1.2},
                    {'final_pnl': -0.8},
                    {'final_pnl': 2.1}
                ]
            }
        )
        
        print(f"✅ 风险管理测试成功")
        print(f"   动作: {test_params['action']}")
        print(f"   止损: {test_params['stop_loss']:.1%}")
        print(f"   止盈: {test_params['take_profit']:.1%}")
        print(f"   风险等级: {test_params['risk_level']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风险管理测试失败: {str(e)}")
        return False

def test_multi_timeframe_analysis():
    """测试多时间框架分析"""
    print("\n🧪 测试多时间框架分析模块...")
    
    try:
        from multi_timeframe_analysis import MultiTimeFrameAnalyzer
        
        # 创建分析器
        analyzer = MultiTimeFrameAnalyzer()
        
        # 模拟测试 (不需要真实数据)
        print(f"✅ 多时间框架分析模块加载成功")
        print(f"   支持时间框架: {list(analyzer.timeframes.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多时间框架分析测试失败: {str(e)}")
        return False

def test_monitoring_dashboard():
    """测试监控仪表盘"""
    print("\n🧪 测试监控仪表盘模块...")
    
    try:
        from monitoring_dashboard import MonitoringDashboard
        
        # 创建仪表盘
        dashboard = MonitoringDashboard()
        
        print(f"✅ 监控仪表盘模块加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 监控仪表盘测试失败: {str(e)}")
        return False

def test_integrated_system():
    """测试集成系统"""
    print("\n🧪 测试集成交易系统...")
    
    try:
        # 模拟集成系统的核心功能
        print("📊 模拟集成系统初始化...")
        
        # 模拟AI预测
        ai_probability = 0.372
        print(f"   AI预测: {ai_probability:.1%}上涨概率")
        
        # 模拟技术指标
        indicators = {
            'rsi': 55.6,
            'macd_trend': 'bullish',
            'bb_position': 0.363,
            'volume_ratio': 0.4,
            'atr_percentage': 0.025,
            'price': 104290.0
        }
        print(f"   技术指标: RSI={indicators['rsi']:.1f}, MACD={indicators['macd_trend']}")
        
        # 模拟信号生成
        confirmations = []
        conflicts = []
        
        if ai_probability < 0.45:  # AI看跌
            if indicators['rsi'] > 50:
                confirmations.append("RSI支持看跌")
            else:
                conflicts.append("RSI与AI冲突")
                
            if indicators['macd_trend'] == 'bearish':
                confirmations.append("MACD支持看跌")
            else:
                conflicts.append("MACD与AI冲突")
        
        print(f"   信号确认: {len(confirmations)}个确认, {len(conflicts)}个冲突")
        
        # 模拟决策
        if len(confirmations) >= 2 and len(confirmations) > len(conflicts):
            decision = "LONG" if ai_probability > 0.5 else "SHORT"
        else:
            decision = "WAIT"
        
        print(f"   最终决策: {decision}")
        print(f"✅ 集成系统核心逻辑测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成系统测试失败: {str(e)}")
        return False

def create_sample_state_file():
    """创建示例状态文件用于测试"""
    print("\n📝 创建示例状态文件...")
    
    import json
    
    sample_state = {
        "capital": 48.75,
        "position": {
            "size": 0.0,
            "side": None,
            "entry_price": 0.0,
            "entry_time": None,
            "margin_used": 0.0,
            "unrealized_pnl": 0.0,
            "last_funding_time": None
        },
        "trades": [
            {
                "timestamp": "2025-06-20T13:30:00",
                "action": "OPEN",
                "side": "SHORT",
                "size": 0.000956,
                "price": 104500.0,
                "confidence": 0.75,
                "confirmations": 3,
                "conflicts": 1,
                "ai_probability": 0.285,
                "mtf_alignment": 0.82
            },
            {
                "timestamp": "2025-06-20T15:45:00",
                "action": "CLOSE",
                "side": "SHORT",
                "final_pnl": 2.15,
                "reason": "止盈触发"
            }
        ],
        "equity_history": [
            {
                "timestamp": "2025-06-20T13:00:00",
                "capital": 50.0,
                "unrealized_pnl": 0.0,
                "total_equity": 50.0,
                "btc_price": 104000.0,
                "ai_probability": 0.372,
                "mtf_alignment": 0.65,
                "risk_level": "LOW",
                "confirmations": 1,
                "conflicts": 3
            },
            {
                "timestamp": "2025-06-20T13:30:00",
                "capital": 48.75,
                "unrealized_pnl": 1.25,
                "total_equity": 50.0,
                "btc_price": 103500.0,
                "ai_probability": 0.285,
                "mtf_alignment": 0.82,
                "risk_level": "LOW",
                "confirmations": 3,
                "conflicts": 1
            },
            {
                "timestamp": "2025-06-20T15:45:00",
                "capital": 50.9,
                "unrealized_pnl": 0.0,
                "total_equity": 50.9,
                "btc_price": 102800.0,
                "ai_probability": 0.445,
                "mtf_alignment": 0.58,
                "risk_level": "LOW",
                "confirmations": 2,
                "conflicts": 2
            }
        ],
        "performance_stats": {
            "total_trades": 1,
            "winning_trades": 1,
            "losing_trades": 0,
            "total_pnl": 2.15,
            "max_drawdown": 0.0,
            "max_equity": 50.9,
            "win_rate": 1.0,
            "avg_win": 2.15,
            "avg_loss": 0.0,
            "profit_factor": 0.0
        },
        "config": {
            "ai_confidence_base": 0.65,
            "mtf_weight_multiplier": 1.2,
            "risk_adjustment_sensitivity": 1.0,
            "signal_confirmation_threshold": 2,
            "max_position_hold_hours": 24
        },
        "last_saved": "2025-06-20T16:00:00"
    }
    
    with open("test_trading_state.json", "w", encoding="utf-8") as f:
        json.dump(sample_state, f, indent=2, ensure_ascii=False)
    
    print("✅ 示例状态文件创建成功: test_trading_state.json")

def test_dashboard_with_sample_data():
    """使用示例数据测试仪表盘"""
    print("\n🧪 测试仪表盘生成...")
    
    try:
        from monitoring_dashboard import MonitoringDashboard
        
        # 使用示例数据创建仪表盘
        dashboard = MonitoringDashboard("test_trading_state.json")
        
        # 生成简单报告
        dashboard.generate_simple_report()
        
        print("✅ 仪表盘测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 仪表盘测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🎯 第一阶段集成测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试各个模块
    test_results.append(("增强风险管理", test_enhanced_risk_management()))
    test_results.append(("多时间框架分析", test_multi_timeframe_analysis()))
    test_results.append(("监控仪表盘", test_monitoring_dashboard()))
    test_results.append(("集成系统逻辑", test_integrated_system()))
    
    # 创建示例数据并测试
    create_sample_state_file()
    test_results.append(("仪表盘数据测试", test_dashboard_with_sample_data()))
    
    # 汇总结果
    print(f"\n📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 第一阶段集成测试全部通过！")
        print("✅ 系统已准备好进行实际运行测试")
    elif passed >= total * 0.8:
        print("📈 第一阶段集成测试基本通过")
        print("🔧 部分功能需要进一步调试")
    else:
        print("🔧 第一阶段集成测试需要重大修复")
        print("❌ 建议检查模块依赖和配置")
    
    print(f"\n💡 下一步:")
    print(f"1. 如果测试通过，可以运行: python integrated_trading_system.py")
    print(f"2. 监控仪表盘: python monitoring_dashboard.py")
    print(f"3. 查看示例数据: test_trading_state.json")

if __name__ == "__main__":
    main()
