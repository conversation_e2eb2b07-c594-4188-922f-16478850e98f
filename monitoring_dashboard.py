#!/usr/bin/env python3
"""
实时监控仪表盘 - 第一阶段可视化界面
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import seaborn as sns
from typing import Dict, List
import os

class MonitoringDashboard:
    """
    实时监控仪表盘
    """
    
    def __init__(self, state_file: str = "integrated_trading_state.json"):
        self.state_file = state_file
        self.data = None
        self.last_update = None
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def load_data(self) -> bool:
        """加载交易数据"""
        try:
            if not os.path.exists(self.state_file):
                print(f"❌ 状态文件不存在: {self.state_file}")
                return False
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            self.last_update = datetime.now()
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False
    
    def generate_dashboard(self, save_path: str = "dashboard.png"):
        """生成完整仪表盘"""
        if not self.load_data():
            return False
        
        # 创建子图
        fig = plt.figure(figsize=(20, 16))
        fig.suptitle('AI增强交易系统 - 实时监控仪表盘', fontsize=20, fontweight='bold')
        
        # 1. 权益曲线 (左上)
        ax1 = plt.subplot(3, 3, (1, 2))
        self._plot_equity_curve(ax1)
        
        # 2. 回撤分析 (右上)
        ax2 = plt.subplot(3, 3, 3)
        self._plot_drawdown_analysis(ax2)
        
        # 3. 交易信号分析 (左中)
        ax3 = plt.subplot(3, 3, (4, 5))
        self._plot_signal_analysis(ax3)
        
        # 4. 风险管理状态 (右中)
        ax4 = plt.subplot(3, 3, 6)
        self._plot_risk_management(ax4)
        
        # 5. 多时间框架一致性 (左下)
        ax5 = plt.subplot(3, 3, 7)
        self._plot_mtf_consistency(ax5)
        
        # 6. 交易统计 (中下)
        ax6 = plt.subplot(3, 3, 8)
        self._plot_trading_stats(ax6)
        
        # 7. 系统状态总览 (右下)
        ax7 = plt.subplot(3, 3, 9)
        self._plot_system_overview(ax7)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📊 仪表盘已保存: {save_path}")
        
        return True
    
    def _plot_equity_curve(self, ax):
        """绘制权益曲线"""
        equity_history = self.data.get('equity_history', [])
        
        if not equity_history:
            ax.text(0.5, 0.5, '暂无权益数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('权益曲线')
            return
        
        df = pd.DataFrame(equity_history)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 绘制权益曲线
        ax.plot(df['timestamp'], df['total_equity'], label='总权益', linewidth=2, color='blue')
        ax.plot(df['timestamp'], df['capital'], label='可用资金', linewidth=1, color='green', alpha=0.7)
        
        # 添加BTC价格对比 (右轴)
        ax2 = ax.twinx()
        ax2.plot(df['timestamp'], df['btc_price'], label='BTC价格', linewidth=1, color='orange', alpha=0.6)
        ax2.set_ylabel('BTC价格 ($)', color='orange')
        ax2.tick_params(axis='y', labelcolor='orange')
        
        ax.set_title('权益曲线 vs BTC价格')
        ax.set_ylabel('账户权益 ($)')
        ax.legend(loc='upper left')
        ax2.legend(loc='upper right')
        ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def _plot_drawdown_analysis(self, ax):
        """绘制回撤分析"""
        equity_history = self.data.get('equity_history', [])
        
        if not equity_history:
            ax.text(0.5, 0.5, '暂无回撤数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('回撤分析')
            return
        
        df = pd.DataFrame(equity_history)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 计算回撤
        df['peak'] = df['total_equity'].expanding().max()
        df['drawdown'] = (df['peak'] - df['total_equity']) / df['peak'] * 100
        
        # 绘制回撤
        ax.fill_between(df['timestamp'], 0, -df['drawdown'], alpha=0.3, color='red', label='回撤')
        ax.plot(df['timestamp'], -df['drawdown'], color='red', linewidth=1)
        
        # 添加最大回撤线
        max_dd = df['drawdown'].max()
        ax.axhline(y=-max_dd, color='darkred', linestyle='--', alpha=0.7, label=f'最大回撤: {max_dd:.1f}%')
        
        # 添加风险限制线
        ax.axhline(y=-15, color='orange', linestyle=':', alpha=0.7, label='风险限制: 15%')
        
        ax.set_title('回撤分析')
        ax.set_ylabel('回撤 (%)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def _plot_signal_analysis(self, ax):
        """绘制信号分析"""
        equity_history = self.data.get('equity_history', [])
        
        if not equity_history:
            ax.text(0.5, 0.5, '暂无信号数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('信号分析')
            return
        
        df = pd.DataFrame(equity_history)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 绘制AI概率
        ax.plot(df['timestamp'], df['ai_probability'] * 100, label='AI上涨概率', linewidth=2, color='blue')
        
        # 添加确认信号数量
        ax2 = ax.twinx()
        ax2.bar(df['timestamp'], df['confirmations'], alpha=0.3, color='green', label='确认信号数', width=0.02)
        ax2.bar(df['timestamp'], -np.array(df['conflicts']), alpha=0.3, color='red', label='冲突信号数', width=0.02)
        
        # 添加参考线
        ax.axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='中性线')
        ax.axhline(y=65, color='green', linestyle=':', alpha=0.7, label='看涨阈值')
        ax.axhline(y=35, color='red', linestyle=':', alpha=0.7, label='看跌阈值')
        
        ax.set_title('AI信号 & 确认分析')
        ax.set_ylabel('AI概率 (%)')
        ax2.set_ylabel('信号数量')
        ax.legend(loc='upper left')
        ax2.legend(loc='upper right')
        ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def _plot_risk_management(self, ax):
        """绘制风险管理状态"""
        equity_history = self.data.get('equity_history', [])
        
        if not equity_history:
            ax.text(0.5, 0.5, '暂无风险数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('风险管理')
            return
        
        # 风险等级统计
        risk_levels = [item.get('risk_level', 'UNKNOWN') for item in equity_history]
        risk_counts = pd.Series(risk_levels).value_counts()
        
        # 绘制饼图
        colors = {'LOW': 'green', 'MEDIUM': 'yellow', 'HIGH': 'orange', 'EXTREME': 'red'}
        pie_colors = [colors.get(level, 'gray') for level in risk_counts.index]
        
        wedges, texts, autotexts = ax.pie(risk_counts.values, labels=risk_counts.index, 
                                         autopct='%1.1f%%', colors=pie_colors)
        
        ax.set_title('风险等级分布')
        
        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
    
    def _plot_mtf_consistency(self, ax):
        """绘制多时间框架一致性"""
        equity_history = self.data.get('equity_history', [])
        
        if not equity_history:
            ax.text(0.5, 0.5, '暂无MTF数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('多时间框架一致性')
            return
        
        df = pd.DataFrame(equity_history)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 绘制一致性得分
        ax.plot(df['timestamp'], df['mtf_alignment'] * 100, label='一致性得分', 
                linewidth=2, color='purple')
        
        # 添加参考线
        ax.axhline(y=60, color='orange', linestyle='--', alpha=0.7, label='良好阈值: 60%')
        ax.axhline(y=80, color='green', linestyle='--', alpha=0.7, label='优秀阈值: 80%')
        
        # 填充区域
        ax.fill_between(df['timestamp'], 0, df['mtf_alignment'] * 100, 
                       alpha=0.2, color='purple')
        
        ax.set_title('多时间框架一致性')
        ax.set_ylabel('一致性得分 (%)')
        ax.set_ylim(0, 100)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    def _plot_trading_stats(self, ax):
        """绘制交易统计"""
        trades = self.data.get('trades', [])
        performance_stats = self.data.get('performance_stats', {})
        
        if not trades:
            ax.text(0.5, 0.5, '暂无交易数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('交易统计')
            return
        
        # 统计数据
        stats = {
            '总交易': performance_stats.get('total_trades', 0),
            '盈利交易': performance_stats.get('winning_trades', 0),
            '亏损交易': performance_stats.get('losing_trades', 0),
            '胜率': f"{performance_stats.get('win_rate', 0):.1%}",
            '总盈亏': f"${performance_stats.get('total_pnl', 0):+.2f}",
            '最大回撤': f"{performance_stats.get('max_drawdown', 0):.1%}"
        }
        
        # 创建表格
        table_data = [[k, v] for k, v in stats.items()]
        
        ax.axis('tight')
        ax.axis('off')
        
        table = ax.table(cellText=table_data,
                        colLabels=['指标', '数值'],
                        cellLoc='center',
                        loc='center',
                        colWidths=[0.5, 0.5])
        
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)
        
        # 设置表格样式
        for i in range(len(table_data) + 1):
            for j in range(2):
                cell = table[(i, j)]
                if i == 0:  # 标题行
                    cell.set_facecolor('#4CAF50')
                    cell.set_text_props(weight='bold', color='white')
                else:
                    cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
        
        ax.set_title('交易统计总览')
    
    def _plot_system_overview(self, ax):
        """绘制系统状态总览"""
        current_data = self.data
        
        # 系统状态指标
        capital = current_data.get('capital', 0)
        initial_capital = 50.0  # 假设初始资金
        
        position = current_data.get('position', {})
        has_position = position.get('size', 0) != 0
        
        equity_history = current_data.get('equity_history', [])
        last_equity = equity_history[-1]['total_equity'] if equity_history else capital
        
        return_pct = (last_equity - initial_capital) / initial_capital * 100
        
        # 状态指标
        status_items = [
            ('系统状态', '🟢 运行中' if self.last_update else '🔴 离线'),
            ('当前持仓', '🔥 有持仓' if has_position else '💤 空仓'),
            ('账户权益', f'${last_equity:.2f}'),
            ('收益率', f'{return_pct:+.2f}%'),
            ('最后更新', self.last_update.strftime('%H:%M:%S') if self.last_update else 'N/A')
        ]
        
        # 创建状态表格
        ax.axis('tight')
        ax.axis('off')
        
        table_data = [[item[0], item[1]] for item in status_items]
        
        table = ax.table(cellText=table_data,
                        colLabels=['状态', '数值'],
                        cellLoc='center',
                        loc='center',
                        colWidths=[0.5, 0.5])
        
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)
        
        # 设置表格样式
        for i in range(len(table_data) + 1):
            for j in range(2):
                cell = table[(i, j)]
                if i == 0:  # 标题行
                    cell.set_facecolor('#2196F3')
                    cell.set_text_props(weight='bold', color='white')
                else:
                    cell.set_facecolor('#e3f2fd' if i % 2 == 0 else 'white')
        
        ax.set_title('系统状态总览')
    
    def generate_simple_report(self):
        """生成简单文本报告"""
        if not self.load_data():
            return
        
        print(f"\n📊 交易系统监控报告")
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 账户状态
        capital = self.data.get('capital', 0)
        position = self.data.get('position', {})
        equity_history = self.data.get('equity_history', [])
        
        if equity_history:
            last_record = equity_history[-1]
            total_equity = last_record['total_equity']
            return_pct = (total_equity - 50) / 50 * 100
            
            print(f"💰 账户状态:")
            print(f"   可用资金: ${capital:.2f}")
            print(f"   总权益: ${total_equity:.2f}")
            print(f"   收益率: {return_pct:+.2f}%")
        
        # 持仓状态
        print(f"\n📊 持仓状态:")
        if position.get('size', 0) != 0:
            print(f"   方向: {position['side']}")
            print(f"   数量: {abs(position['size']):.6f} BTC")
            print(f"   入场价: ${position['entry_price']:,.2f}")
        else:
            print(f"   当前: 空仓")
        
        # 交易统计
        stats = self.data.get('performance_stats', {})
        print(f"\n📈 交易统计:")
        print(f"   总交易数: {stats.get('total_trades', 0)}")
        print(f"   胜率: {stats.get('win_rate', 0):.1%}")
        print(f"   总盈亏: ${stats.get('total_pnl', 0):+.2f}")
        print(f"   最大回撤: {stats.get('max_drawdown', 0):.1%}")
        
        print("=" * 60)

if __name__ == "__main__":
    # 创建监控仪表盘
    dashboard = MonitoringDashboard()
    
    # 生成仪表盘
    if dashboard.generate_dashboard():
        print("✅ 仪表盘生成成功")
    
    # 生成简单报告
    dashboard.generate_simple_report()
