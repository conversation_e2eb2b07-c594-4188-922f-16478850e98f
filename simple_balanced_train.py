#!/usr/bin/env python3
"""
简化版平衡训练 - 最直接的解决方案
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, balanced_accuracy_score
from sklearn.model_selection import train_test_split
import joblib

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

def simple_balanced_train(symbol='BTCUSDT', months_back=24):
    """
    简化版平衡训练
    """
    print(f"🚀 简化平衡训练 {symbol}...")
    
    # 1. 获取数据
    print("📊 获取数据...")
    start_date = (datetime.now() - timedelta(days=months_back*30)).strftime('%Y-%m-%d')
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date)
    print(f"✅ 获取到 {len(df)} 条数据")
    
    # 2. 特征工程
    print("🔧 特征工程...")
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    # 3. 数据准备
    X = df_features.drop(columns=['target'])
    y = df_features['target']
    
    # 移除原始列
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    # 清理数据
    mask = ~(X.isna().any(axis=1) | y.isna())
    X = X[mask]
    y = y[mask]
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    print(f"✅ 清理后: {len(X)} 样本, {X.shape[1]} 特征")
    
    # 4. 简化类别 - 只保留3个主要状态
    print("🔄 简化类别...")
    
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)
    
    unique, counts = np.unique(y_encoded, return_counts=True)
    print(f"原始类别分布: {dict(zip(unique, counts))}")
    
    # 找出样本最多的3个类别
    top3_indices = np.argsort(counts)[-3:]  # 最大的3个
    top3_classes = unique[top3_indices]
    
    print(f"保留前3大类别: {top3_classes}")
    
    # 重新映射：只保留前3大类别，其他归为"其他"
    y_simple = np.full_like(y_encoded, 3)  # 默认为类别3（其他）
    
    for i, class_id in enumerate(top3_classes):
        mask = y_encoded == class_id
        y_simple[mask] = i  # 重新编号为0,1,2
    
    # 显示简化后分布
    unique_simple, counts_simple = np.unique(y_simple, return_counts=True)
    print(f"简化后分布: {dict(zip(unique_simple, counts_simple))}")
    
    # 5. 激进下采样 - 每个类别最多1000样本
    print("⚖️ 平衡数据...")
    
    balanced_X = []
    balanced_y = []
    
    max_samples = 1000  # 每个类别最多1000样本
    
    for class_id in unique_simple:
        class_mask = y_simple == class_id
        class_X = X[class_mask]
        class_y = y_simple[class_mask]
        
        if len(class_X) > max_samples:
            # 随机采样
            indices = np.random.choice(len(class_X), max_samples, replace=False)
            class_X = class_X.iloc[indices]
            class_y = class_y[indices]
        
        balanced_X.append(class_X)
        balanced_y.append(class_y)
        
        print(f"   类别 {class_id}: {len(class_X)} 样本")
    
    # 合并平衡后的数据
    X_balanced = pd.concat(balanced_X, ignore_index=True)
    y_balanced = np.concatenate(balanced_y)
    
    print(f"✅ 平衡后总样本: {len(X_balanced)}")
    
    # 6. 分割数据
    print("✂️ 分割数据...")
    X_train, X_test, y_train, y_test = train_test_split(
        X_balanced, y_balanced, test_size=0.2, random_state=42, stratify=y_balanced
    )
    
    print(f"训练集: {len(X_train)}, 测试集: {len(X_test)}")
    
    # 7. 特征缩放
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 8. 训练简单模型
    print("🎯 训练模型...")
    
    if HAS_XGB:
        model = xgb.XGBClassifier(
            n_estimators=50,
            max_depth=4,
            learning_rate=0.1,
            random_state=42,
            n_jobs=-1
        )
    else:
        model = RandomForestClassifier(
            n_estimators=50,
            max_depth=6,
            random_state=42,
            n_jobs=-1
        )
    
    model.fit(X_train_scaled, y_train)
    
    # 9. 评估
    print("📊 评估模型...")
    
    y_train_pred = model.predict(X_train_scaled)
    y_test_pred = model.predict(X_test_scaled)
    
    train_acc = accuracy_score(y_train, y_train_pred)
    test_acc = accuracy_score(y_test, y_test_pred)
    test_balanced = balanced_accuracy_score(y_test, y_test_pred)
    
    # 10. 显示结果
    print("\n" + "="*50)
    print("🎉 简化训练完成!")
    print("="*50)
    print(f"训练准确率: {train_acc:.4f}")
    print(f"测试准确率: {test_acc:.4f}")
    print(f"平衡准确率: {test_balanced:.4f}")
    print(f"过拟合程度: {train_acc - test_acc:.4f}")
    
    print("\n📋 分类报告:")
    print(classification_report(y_test, y_test_pred))
    
    # 11. 保存模型
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = f"models/simple_balanced_{symbol}_{timestamp}.joblib"
    
    import os
    os.makedirs("models", exist_ok=True)
    
    joblib.dump({
        'model': model,
        'scaler': scaler,
        'label_encoder': label_encoder
    }, model_path)
    
    print(f"💾 模型已保存: {model_path}")
    
    return {
        'train_accuracy': train_acc,
        'test_accuracy': test_acc,
        'balanced_accuracy': test_balanced,
        'model_path': model_path
    }

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    months = int(sys.argv[2]) if len(sys.argv) > 2 else 24
    
    simple_balanced_train(symbol, months)
