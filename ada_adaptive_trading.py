#!/usr/bin/env python3
"""
ADAUSDT自适应高频剥头皮交易系统
基于真实数据训练的自适应AI模型 (58.2%验证准确率)
"""

import pandas as pd
import numpy as np
import logging
import time
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import deque
import sys
import os

# 导入自适应AI模型
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from adaptive_ai_model import AdaptiveAIModel

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ADAAdaptiveTrading:
    """ADAUSDT自适应交易系统"""
    
    def __init__(self, initial_balance: float = 50.0):
        self.symbol = "ADAUSDT"
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.leverage = 125.0
        
        # 初始化自适应AI模型
        self.ai_model = AdaptiveAIModel(self.symbol)
        self.model_initialized = False
        
        # 交易参数 (将由AI模型自动设置)
        self.trading_params = {}
        
        # 交易状态
        self.current_position = None
        self.last_trade_time = None
        self.total_trades = 0
        self.winning_trades = 0
        self.trade_history = []
        
        # 数据缓存
        self.price_history = deque(maxlen=1000)
        self.market_data_cache = None
        
        # 运行状态
        self.is_running = False
        self.polling_interval = 8  # 8秒轮询 (高频)
        
        # 性能统计
        self.performance_stats = {
            'quick_profits': 0,
            'avg_holding_time': 0,
            'best_trade': 0,
            'worst_trade': 0,
            'consecutive_wins': 0,
            'consecutive_losses': 0,
            'max_consecutive_wins': 0,
            'max_consecutive_losses': 0
        }
    
    def initialize_system(self) -> bool:
        """初始化交易系统"""
        logger.info("🚀 初始化ADAUSDT自适应交易系统...")
        
        # 初始化AI模型
        if not self.ai_model.initialize_for_symbol():
            logger.error("❌ AI模型初始化失败")
            return False
        
        # 获取交易参数
        self.trading_params = self.ai_model.get_trading_params()
        self.model_initialized = True
        
        logger.info("✅ 系统初始化完成")
        logger.info(f"🎯 AI模型参数:")
        logger.info(f"   置信度门槛: {self.trading_params['min_confidence']:.1%}")
        logger.info(f"   仓位风险: {self.trading_params['position_risk']:.1%}")
        logger.info(f"   止损: {self.trading_params['stop_loss_ratio']:.1%}")
        logger.info(f"   止盈: {self.trading_params['take_profit_ratio']:.1%}")
        
        return True
    
    def get_current_price(self) -> float:
        """获取当前ADA价格"""
        try:
            url = "https://fapi.binance.com/fapi/v1/ticker/price"
            params = {'symbol': self.symbol}
            
            response = requests.get(url, params=params, timeout=3)
            response.raise_for_status()
            
            data = response.json()
            price = float(data['price'])
            
            # 记录价格历史
            self.price_history.append({
                'timestamp': datetime.now(),
                'price': price
            })
            
            return price
            
        except Exception as e:
            logger.error(f"获取ADA价格失败: {e}")
            return 0.0
    
    def get_market_data(self) -> Optional[pd.DataFrame]:
        """获取市场数据"""
        try:
            url = "https://fapi.binance.com/fapi/v1/klines"
            params = {
                'symbol': self.symbol,
                'interval': '1m',
                'limit': 100  # 获取100分钟数据用于AI预测
            }
            
            response = requests.get(url, params=params, timeout=8)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades_count',
                'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
            ])
            
            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            self.market_data_cache = df[['open', 'high', 'low', 'close', 'volume']]
            return self.market_data_cache
            
        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return None
    
    def can_trade(self) -> bool:
        """检查是否可以交易"""
        # 检查模型是否初始化
        if not self.model_initialized:
            return False
        
        # 检查是否有持仓
        if self.current_position:
            return False
        
        # 检查交易间隔 (90秒)
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < 90:
                return False
        
        # 检查余额
        if self.current_balance < self.initial_balance * 0.3:
            logger.warning("余额过低，停止交易")
            return False
        
        # 检查连续亏损 (风险控制)
        if self.performance_stats['consecutive_losses'] >= 5:
            logger.warning("连续亏损过多，暂停交易")
            return False
        
        return True
    
    def execute_trade(self, direction: str, confidence: float, entry_price: float):
        """执行交易"""
        # 计算仓位大小
        risk_amount = self.current_balance * self.trading_params['position_risk']
        position_value = risk_amount * self.leverage
        position_size = position_value / entry_price
        
        # 计算止损止盈
        if direction == "LONG":
            stop_loss = entry_price * (1 - self.trading_params['stop_loss_ratio'])
            take_profit = entry_price * (1 + self.trading_params['take_profit_ratio'])
        else:
            stop_loss = entry_price * (1 + self.trading_params['stop_loss_ratio'])
            take_profit = entry_price * (1 - self.trading_params['take_profit_ratio'])
        
        # 创建持仓记录
        self.current_position = {
            'direction': direction,
            'entry_price': entry_price,
            'entry_time': datetime.now(),
            'position_size': position_size,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'confidence': confidence,
            'model_type': 'ADAPTIVE_AI'
        }
        
        self.total_trades += 1
        self.last_trade_time = datetime.now()
        
        logger.info(f"🚀 ADA交易: {direction} @ {entry_price:.4f}")
        logger.info(f"   AI置信度: {confidence:.1%}, 仓位: {position_size:.0f} ADA")
        logger.info(f"   止损: {stop_loss:.4f}, 止盈: {take_profit:.4f}")
    
    def check_exit_conditions(self, current_price: float):
        """检查退出条件"""
        if not self.current_position:
            return
        
        pos = self.current_position
        should_exit = False
        exit_reason = ""
        
        # 检查止损止盈
        if pos['direction'] == "LONG":
            if current_price <= pos['stop_loss']:
                should_exit = True
                exit_reason = "止损"
            elif current_price >= pos['take_profit']:
                should_exit = True
                exit_reason = "止盈"
        else:  # SHORT
            if current_price >= pos['stop_loss']:
                should_exit = True
                exit_reason = "止损"
            elif current_price <= pos['take_profit']:
                should_exit = True
                exit_reason = "止盈"
        
        # 检查最大持仓时间 (8分钟)
        duration = (datetime.now() - pos['entry_time']).total_seconds()
        if duration > 480:  # 8分钟
            should_exit = True
            exit_reason = "时间退出"
        
        if should_exit:
            self.close_position(current_price, exit_reason)
    
    def close_position(self, exit_price: float, exit_reason: str):
        """平仓"""
        if not self.current_position:
            return
        
        pos = self.current_position
        
        # 计算盈亏
        if pos['direction'] == "LONG":
            pnl_pct = (exit_price - pos['entry_price']) / pos['entry_price']
        else:
            pnl_pct = (pos['entry_price'] - exit_price) / pos['entry_price']
        
        # 应用杠杆
        leveraged_pnl = pnl_pct * self.leverage
        
        # 计算实际盈亏金额
        position_value = pos['position_size'] * pos['entry_price']
        margin_used = position_value / self.leverage
        pnl_amount = margin_used * leveraged_pnl
        
        # 更新余额
        self.current_balance += pnl_amount
        
        # 计算持仓时间
        holding_time = (datetime.now() - pos['entry_time']).total_seconds()
        
        # 更新统计
        is_winner = pnl_amount > 0
        if is_winner:
            self.winning_trades += 1
            self.performance_stats['consecutive_wins'] += 1
            self.performance_stats['consecutive_losses'] = 0
            self.performance_stats['max_consecutive_wins'] = max(
                self.performance_stats['max_consecutive_wins'],
                self.performance_stats['consecutive_wins']
            )
            
            if holding_time < 240:  # 4分钟内盈利算快速盈利
                self.performance_stats['quick_profits'] += 1
        else:
            self.performance_stats['consecutive_losses'] += 1
            self.performance_stats['consecutive_wins'] = 0
            self.performance_stats['max_consecutive_losses'] = max(
                self.performance_stats['max_consecutive_losses'],
                self.performance_stats['consecutive_losses']
            )
        
        # 更新其他统计
        self.performance_stats['avg_holding_time'] = (
            (self.performance_stats['avg_holding_time'] * (self.total_trades - 1) + holding_time) / self.total_trades
        )
        
        if pnl_amount > self.performance_stats['best_trade']:
            self.performance_stats['best_trade'] = pnl_amount
        if pnl_amount < self.performance_stats['worst_trade']:
            self.performance_stats['worst_trade'] = pnl_amount
        
        # 记录交易
        trade_record = {
            'trade_id': self.total_trades,
            'symbol': self.symbol,
            'entry_time': pos['entry_time'],
            'exit_time': datetime.now(),
            'direction': pos['direction'],
            'entry_price': pos['entry_price'],
            'exit_price': exit_price,
            'pnl_amount': pnl_amount,
            'pnl_pct': leveraged_pnl,
            'holding_time': holding_time,
            'is_winner': is_winner,
            'exit_reason': exit_reason,
            'confidence': pos['confidence'],
            'model_type': pos['model_type']
        }
        
        self.trade_history.append(trade_record)
        self.current_position = None
        
        # 显示结果
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        
        status = "✅ 盈利" if is_winner else "❌ 亏损"
        logger.info(f"📈 ADA平仓: {pos['direction']} @ {exit_price:.4f} ({exit_reason})")
        logger.info(f"   {status} 盈亏: ${pnl_amount:+.2f} ({leveraged_pnl:+.2%})")
        logger.info(f"   持仓: {holding_time:.0f}秒, 余额: ${self.current_balance:.2f}")
        logger.info(f"   胜率: {win_rate:.1%}, 总收益: {total_return:+.1%}")
    
    def run_adaptive_trading(self, duration_minutes: int = 60):
        """运行自适应交易"""
        if not self.initialize_system():
            logger.error("❌ 系统初始化失败")
            return
        
        logger.info(f"🚀 启动ADAUSDT自适应高频交易")
        logger.info(f"💰 初始资金: ${self.initial_balance}")
        logger.info(f"🤖 AI模型: 自适应模型 (验证准确率: 58.2%)")
        logger.info(f"⚡ 杠杆: {self.leverage}x")
        logger.info(f"⏰ 运行时间: {duration_minutes} 分钟")
        
        self.is_running = True
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        cycle_count = 0
        
        try:
            while datetime.now() < end_time and self.is_running:
                cycle_count += 1
                cycle_start = time.time()
                
                # 获取当前价格
                current_price = self.get_current_price()
                if current_price == 0:
                    time.sleep(3)
                    continue
                
                # 检查退出条件
                if self.current_position:
                    self.check_exit_conditions(current_price)
                
                # 每2个周期(16秒)进行一次交易决策
                if cycle_count % 2 == 0:
                    # 获取市场数据
                    market_data = self.get_market_data()
                    if market_data is not None and len(market_data) >= 60:
                        
                        if self.can_trade():
                            # AI预测
                            direction, confidence = self.ai_model.predict(market_data)
                            
                            if direction in ["LONG", "SHORT"] and confidence >= self.trading_params['min_confidence']:
                                self.execute_trade(direction, confidence, current_price)
                
                # 显示状态 (每15个周期=2分钟)
                if cycle_count % 15 == 0:
                    self.display_status(current_price)
                
                # 控制轮询间隔
                elapsed = time.time() - cycle_start
                sleep_time = max(0, self.polling_interval - elapsed)
                time.sleep(sleep_time)
                
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断交易")
        except Exception as e:
            logger.error(f"❌ 交易异常: {e}")
        finally:
            self.is_running = False
            
            # 如果有持仓，强制平仓
            if self.current_position:
                final_price = self.get_current_price()
                if final_price > 0:
                    self.close_position(final_price, "系统停止")
            
            self.show_final_results()
    
    def display_status(self, current_price: float):
        """显示状态"""
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        quick_profit_rate = self.performance_stats['quick_profits'] / self.total_trades if self.total_trades > 0 else 0
        
        position_info = ""
        if self.current_position:
            pos = self.current_position
            duration = (datetime.now() - pos['entry_time']).total_seconds()
            position_info = f", 持仓: {pos['direction']} {duration:.0f}秒"
        
        logger.info(f"📊 ADA: {current_price:.4f}, 余额: ${self.current_balance:.2f}, "
                   f"胜率: {win_rate:.1%}, 收益: {total_return:+.1%}, "
                   f"快速盈利: {quick_profit_rate:.1%}{position_info}")
    
    def show_final_results(self):
        """显示最终结果"""
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        total_pnl = self.current_balance - self.initial_balance
        
        print("\n" + "="*80)
        print("🎉 ADAUSDT自适应高频交易完成")
        print("="*80)
        
        print(f"📊 交易统计:")
        print(f"  总交易数: {self.total_trades}")
        print(f"  盈利交易: {self.winning_trades}")
        print(f"  实际胜率: {win_rate:.1%}")
        print(f"  AI验证准确率: 58.2%")
        
        print(f"\n💰 财务表现:")
        print(f"  初始资金: ${self.initial_balance:.2f}")
        print(f"  最终余额: ${self.current_balance:.2f}")
        print(f"  总盈亏: ${total_pnl:+.2f}")
        print(f"  收益率: {total_return:+.1%}")
        
        print(f"\n⚡ 高频交易统计:")
        print(f"  快速盈利次数: {self.performance_stats['quick_profits']}")
        print(f"  快速盈利率: {self.performance_stats['quick_profits']/self.total_trades:.1%}" if self.total_trades > 0 else "  快速盈利率: 0.0%")
        print(f"  平均持仓时间: {self.performance_stats['avg_holding_time']:.0f} 秒")
        print(f"  最佳单笔: ${self.performance_stats['best_trade']:+.2f}")
        print(f"  最差单笔: ${self.performance_stats['worst_trade']:+.2f}")
        print(f"  最大连胜: {self.performance_stats['max_consecutive_wins']}")
        print(f"  最大连败: {self.performance_stats['max_consecutive_losses']}")
        
        # 保存结果
        self.save_results()
    
    def save_results(self):
        """保存交易结果"""
        results = {
            'system_info': {
                'symbol': self.symbol,
                'timestamp': datetime.now().isoformat(),
                'model_type': 'ADAPTIVE_AI',
                'model_accuracy': '58.2%',
                'initial_balance': self.initial_balance,
                'final_balance': self.current_balance,
                'leverage': self.leverage,
                'trading_params': self.trading_params
            },
            'performance': {
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'win_rate': self.winning_trades / self.total_trades if self.total_trades > 0 else 0,
                'total_return': (self.current_balance - self.initial_balance) / self.initial_balance,
                'total_pnl': self.current_balance - self.initial_balance
            },
            'hft_stats': self.performance_stats,
            'trades': [
                {
                    'trade_id': trade['trade_id'],
                    'entry_time': trade['entry_time'].isoformat(),
                    'exit_time': trade['exit_time'].isoformat(),
                    'direction': trade['direction'],
                    'entry_price': trade['entry_price'],
                    'exit_price': trade['exit_price'],
                    'pnl_amount': trade['pnl_amount'],
                    'holding_time': trade['holding_time'],
                    'is_winner': trade['is_winner'],
                    'exit_reason': trade['exit_reason'],
                    'confidence': trade['confidence']
                }
                for trade in self.trade_history
            ]
        }
        
        filename = f"ada_adaptive_trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 交易结果已保存: {filename}")

if __name__ == "__main__":
    print("🎯 ADAUSDT自适应高频剥头皮交易系统")
    print("🤖 基于真实数据训练的自适应AI模型")
    print("📊 验证准确率: 58.2%")
    print("⚡ ADA专用参数优化")
    
    # 创建自适应交易系统
    ada_trader = ADAAdaptiveTrading(initial_balance=50.0)
    
    try:
        duration = int(input("\n请输入运行时间(分钟，默认30): ") or "30")
        ada_trader.run_adaptive_trading(duration_minutes=duration)
    except:
        print("使用默认30分钟运行...")
        ada_trader.run_adaptive_trading(duration_minutes=30)
