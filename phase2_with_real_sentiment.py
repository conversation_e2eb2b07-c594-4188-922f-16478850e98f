#!/usr/bin/env python3
"""
第二阶段集成系统 - 使用100%真实情绪数据
整合您的CoinMarketCap和NewsAPI密钥
"""

import pandas as pd
import numpy as np
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 导入第一阶段模块
from integrated_trading_system import IntegratedTradingSystem
from enhanced_risk_management import EnhancedRiskManager
from multi_timeframe_analysis import MultiTimeFrameAnalyzer

# 导入第二阶段模块
from multi_strategy_library import StrategyManager
from enhanced_real_sentiment import EnhancedRealSentimentAnalyzer  # 使用真实情绪分析
from ai_model_monitor import AIModelMonitor

class Phase2RealSentimentTradingSystem(IntegratedTradingSystem):
    """
    第二阶段真实情绪交易系统
    使用100%真实API数据进行情绪分析
    """
    
    def __init__(self, initial_capital: float = 50.0, leverage: int = 2):
        # 继承第一阶段系统
        super().__init__(initial_capital, leverage)
        
        # 第二阶段新增模块 - 使用真实情绪分析
        self.strategy_manager = StrategyManager()
        self.sentiment_analyzer = EnhancedRealSentimentAnalyzer()  # 100%真实数据
        self.ai_monitor = AIModelMonitor()
        
        # 第二阶段配置
        self.phase2_config = {
            'enable_multi_strategy': True,
            'enable_real_sentiment_analysis': True,  # 真实情绪分析
            'enable_ai_monitoring': True,
            'sentiment_weight': 0.2,                 # 增加情绪权重
            'multi_strategy_weight': 0.35,           
            'original_signal_weight': 0.45,          
            'min_strategy_agreement': 0.6,           
            'sentiment_confirmation_threshold': 0.65  # 降低阈值，更敏感
        }
        
        # 状态文件更新
        self.state_file = "phase2_real_sentiment_state.json"
        
        # 性能跟踪
        self.phase2_performance = {
            'strategy_contributions': {},
            'real_sentiment_accuracy': [],
            'ai_monitoring_alerts': [],
            'decision_breakdown': [],
            'sentiment_vs_price_correlation': []
        }
        
        print(f"🚀 第二阶段真实情绪AI交易系统启动")
        print(f"   ✅ 多策略库: {len(self.strategy_manager.strategies)}个策略")
        print(f"   ✅ 真实情绪分析: 100%真实API数据")
        print(f"   ✅ AI监控: 实时性能监控")
        print(f"   📊 集成权重: 原始{self.phase2_config['original_signal_weight']:.0%} + 多策略{self.phase2_config['multi_strategy_weight']:.0%} + 真实情绪{self.phase2_config['sentiment_weight']:.0%}")
    
    def generate_phase2_real_sentiment_signal(self, ai_probability: float, indicators: Dict) -> Dict:
        """
        生成第二阶段真实情绪增强信号
        """
        # 1. 获取第一阶段原始信号
        original_signal = self.generate_integrated_signal(ai_probability, indicators)
        
        # 2. 获取多策略信号
        multi_strategy_signal = None
        if self.phase2_config['enable_multi_strategy']:
            try:
                strategy_data = self._prepare_strategy_data(indicators)
                multi_strategy_signal = self.strategy_manager.generate_combined_signal(strategy_data)
            except Exception as e:
                print(f"⚠️ 多策略信号生成失败: {str(e)}")
                multi_strategy_signal = self._get_default_strategy_signal()
        
        # 3. 获取100%真实情绪分析信号
        real_sentiment_signal = None
        if self.phase2_config['enable_real_sentiment_analysis']:
            try:
                print(f"🔍 获取真实情绪数据...")
                sentiment_analysis = self.sentiment_analyzer.get_comprehensive_sentiment()
                real_sentiment_signal = sentiment_analysis['trading_signal']
                
                # 记录情绪与价格的相关性
                self._record_sentiment_price_correlation(sentiment_analysis, indicators['price'])
                
            except Exception as e:
                print(f"⚠️ 真实情绪分析失败: {str(e)}")
                real_sentiment_signal = self._get_default_sentiment_signal()
        
        # 4. AI模型监控
        ai_monitoring_status = None
        if self.phase2_config['enable_ai_monitoring']:
            try:
                features = self._extract_features_for_monitoring(indicators)
                monitoring_result = self.ai_monitor.record_prediction(
                    features, ai_probability, confidence=original_signal.get('confidence', 0.5)
                )
                ai_monitoring_status = monitoring_result
            except Exception as e:
                print(f"⚠️ AI监控记录失败: {str(e)}")
                ai_monitoring_status = {'status': 'error'}
        
        # 5. 综合信号融合 (包含真实情绪数据)
        final_signal = self._fuse_real_sentiment_signals(
            original_signal, 
            multi_strategy_signal, 
            real_sentiment_signal,
            ai_monitoring_status
        )
        
        # 6. 记录决策过程
        self._record_real_sentiment_decision_breakdown(
            original_signal, multi_strategy_signal, real_sentiment_signal, final_signal
        )
        
        return final_signal
    
    def _fuse_real_sentiment_signals(self, original_signal: Dict, multi_strategy_signal: Dict, 
                                   real_sentiment_signal: Dict, ai_monitoring: Dict) -> Dict:
        """融合包含真实情绪的所有信号"""
        
        # 权重配置 (真实情绪数据权重更高)
        original_weight = self.phase2_config['original_signal_weight']
        strategy_weight = self.phase2_config['multi_strategy_weight']
        sentiment_weight = self.phase2_config['sentiment_weight']
        
        # 信号强度和方向收集
        signals = []
        
        # 1. 原始信号
        if original_signal['direction'] != 'WAIT':
            signals.append({
                'source': 'original',
                'direction': original_signal['direction'],
                'strength': original_signal['strength'],
                'confidence': original_signal['confidence'],
                'weight': original_weight
            })
        
        # 2. 多策略信号
        if multi_strategy_signal and multi_strategy_signal['direction'] != 'WAIT':
            agreement_score = multi_strategy_signal.get('agreement_score', 0.5)
            if agreement_score >= self.phase2_config['min_strategy_agreement']:
                signals.append({
                    'source': 'multi_strategy',
                    'direction': multi_strategy_signal['direction'],
                    'strength': multi_strategy_signal['strength'],
                    'confidence': multi_strategy_signal['confidence'],
                    'weight': strategy_weight,
                    'agreement': agreement_score
                })
        
        # 3. 真实情绪信号 (更敏感的阈值)
        if real_sentiment_signal and real_sentiment_signal['direction'] != 'WAIT':
            sentiment_confidence = real_sentiment_signal.get('confidence', 0.5)
            if sentiment_confidence >= self.phase2_config['sentiment_confirmation_threshold']:
                # 真实情绪数据权重根据信号类型调整
                signal_type = real_sentiment_signal.get('signal_type', 'neutral')
                adjusted_weight = sentiment_weight
                
                if signal_type == 'contrarian_strong':
                    adjusted_weight *= 1.5  # 强逆向信号权重增加
                elif signal_type == 'contrarian':
                    adjusted_weight *= 1.2  # 逆向信号权重增加
                
                signals.append({
                    'source': 'real_sentiment',
                    'direction': real_sentiment_signal['direction'],
                    'strength': real_sentiment_signal['strength'],
                    'confidence': sentiment_confidence,
                    'weight': adjusted_weight,
                    'signal_type': signal_type
                })
        
        # 4. 如果没有有效信号
        if not signals:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'reason': '所有信号源建议等待',
                'signal_breakdown': {
                    'original': original_signal,
                    'multi_strategy': multi_strategy_signal,
                    'real_sentiment': real_sentiment_signal
                },
                'ai_monitoring': ai_monitoring,
                'fusion_method': 'no_signals'
            }
        
        # 5. 加权融合计算
        weighted_long = 0
        weighted_short = 0
        total_weight = 0
        confidence_sum = 0
        
        signal_details = []
        
        for signal in signals:
            weight = signal['weight']
            strength = signal['strength']
            confidence = signal['confidence']
            
            total_weight += weight
            confidence_sum += confidence * weight
            
            if signal['direction'] == 'LONG':
                weighted_long += weight * strength
                signal_details.append(f"{signal['source']}看涨({strength:.1%})")
            elif signal['direction'] == 'SHORT':
                weighted_short += weight * strength
                signal_details.append(f"{signal['source']}看跌({strength:.1%})")
        
        # 6. 计算最终信号
        if total_weight == 0:
            final_direction = 'WAIT'
            final_strength = 0
            final_confidence = 0.3
        else:
            avg_confidence = confidence_sum / total_weight
            
            if weighted_long > weighted_short:
                final_direction = 'LONG'
                final_strength = weighted_long / total_weight
                signal_agreement = weighted_long / (weighted_long + weighted_short)
            elif weighted_short > weighted_long:
                final_direction = 'SHORT'
                final_strength = weighted_short / total_weight
                signal_agreement = weighted_short / (weighted_long + weighted_short)
            else:
                final_direction = 'WAIT'
                final_strength = 0
                signal_agreement = 0.5
            
            # 调整最终置信度
            final_confidence = avg_confidence * signal_agreement
        
        # 7. AI监控状态检查
        ai_warning = ""
        if ai_monitoring and ai_monitoring.get('drift_score', 0) > 0.1:
            final_confidence *= 0.8
            ai_warning = " (AI漂移警告)"
        
        return {
            'direction': final_direction,
            'strength': final_strength,
            'confidence': final_confidence,
            'reason': f"真实情绪融合: {', '.join(signal_details)}{ai_warning}",
            'signal_breakdown': {
                'original': original_signal,
                'multi_strategy': multi_strategy_signal,
                'real_sentiment': real_sentiment_signal
            },
            'fusion_details': {
                'active_signals': len(signals),
                'total_weight': total_weight,
                'signal_agreement': signal_agreement if 'signal_agreement' in locals() else 0.5,
                'confidence_sources': [s['source'] for s in signals],
                'real_sentiment_weight': sum(s['weight'] for s in signals if s['source'] == 'real_sentiment')
            },
            'ai_monitoring': ai_monitoring,
            'fusion_method': 'real_sentiment_weighted'
        }
    
    def _record_sentiment_price_correlation(self, sentiment_analysis: Dict, current_price: float):
        """记录情绪与价格的相关性"""
        correlation_record = {
            'timestamp': datetime.now().isoformat(),
            'sentiment_score': sentiment_analysis['overall_sentiment_score'],
            'sentiment_classification': sentiment_analysis['sentiment_classification'],
            'btc_price': current_price,
            'sentiment_sources': {
                source: data['score'] for source, data in sentiment_analysis['sentiment_breakdown'].items()
            }
        }
        
        self.phase2_performance['sentiment_vs_price_correlation'].append(correlation_record)
        
        # 保持记录在合理范围
        if len(self.phase2_performance['sentiment_vs_price_correlation']) > 100:
            self.phase2_performance['sentiment_vs_price_correlation'] = self.phase2_performance['sentiment_vs_price_correlation'][-100:]
    
    def _record_real_sentiment_decision_breakdown(self, original: Dict, strategy: Dict, 
                                                real_sentiment: Dict, final: Dict):
        """记录真实情绪决策分解过程"""
        breakdown = {
            'timestamp': datetime.now().isoformat(),
            'original_signal': {
                'direction': original['direction'],
                'strength': original['strength'],
                'confidence': original['confidence']
            },
            'strategy_signal': {
                'direction': strategy['direction'] if strategy else 'WAIT',
                'strength': strategy.get('strength', 0) if strategy else 0,
                'confidence': strategy.get('confidence', 0) if strategy else 0,
                'active_strategies': strategy.get('active_strategies', 0) if strategy else 0
            },
            'real_sentiment_signal': {
                'direction': real_sentiment['direction'] if real_sentiment else 'WAIT',
                'strength': real_sentiment.get('strength', 0) if real_sentiment else 0,
                'confidence': real_sentiment.get('confidence', 0) if real_sentiment else 0,
                'signal_type': real_sentiment.get('signal_type', 'none') if real_sentiment else 'none'
            },
            'final_signal': {
                'direction': final['direction'],
                'strength': final['strength'],
                'confidence': final['confidence'],
                'fusion_method': final.get('fusion_method', 'unknown')
            }
        }
        
        self.phase2_performance['decision_breakdown'].append(breakdown)
        
        # 保持记录在合理范围
        if len(self.phase2_performance['decision_breakdown']) > 100:
            self.phase2_performance['decision_breakdown'] = self.phase2_performance['decision_breakdown'][-100:]
    
    def run_phase2_real_sentiment_cycle(self) -> bool:
        """运行第二阶段真实情绪交易循环"""
        try:
            # 1. 获取AI预测
            ai_probability, current_price = self.get_ai_prediction()
            
            # 2. 计算技术指标
            indicators = self.calculate_technical_indicators()
            indicators['price'] = current_price
            
            # 3. 生成第二阶段真实情绪增强信号
            signal = self.generate_phase2_real_sentiment_signal(ai_probability, indicators)
            
            # 4. 检查止损止盈
            if self.position['size'] != 0:
                if self.check_stop_loss_take_profit(current_price, signal):
                    signal = self.generate_phase2_real_sentiment_signal(ai_probability, indicators)
            
            # 5. 执行交易决策
            if signal['direction'] in ['LONG', 'SHORT'] and self.position['size'] == 0:
                if self.risk_manager.should_trade(signal['confidence'], signal.get('risk_params', {})):
                    size_info = self.calculate_position_size(current_price, signal)
                    if size_info:
                        self.open_position(signal['direction'], current_price, size_info, signal)
                else:
                    print(f"🛡️ 风险管理阻止交易")
            
            # 6. 打印真实情绪状态
            self.print_phase2_real_sentiment_status(ai_probability, indicators, signal)
            
            # 7. 记录权益历史
            unrealized_pnl = self.calculate_unrealized_pnl(current_price)
            total_equity = self.capital + unrealized_pnl
            
            equity_record = {
                'timestamp': datetime.now().isoformat(),
                'capital': self.capital,
                'unrealized_pnl': unrealized_pnl,
                'total_equity': total_equity,
                'btc_price': current_price,
                'ai_probability': ai_probability,
                'final_signal': signal['direction'],
                'signal_confidence': signal['confidence'],
                'real_sentiment_weight': signal.get('fusion_details', {}).get('real_sentiment_weight', 0),
                'phase': '2_real_sentiment'
            }
            
            self.equity_history.append(equity_record)
            
            # 8. 保存状态
            self._save_phase2_real_sentiment_state()
            
            return True
            
        except Exception as e:
            print(f"❌ 第二阶段真实情绪交易循环失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def print_phase2_real_sentiment_status(self, ai_probability: float, indicators: Dict, signal: Dict):
        """打印第二阶段真实情绪状态"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_price = indicators['price']
        
        print(f"\n⏰ {current_time} - 第二阶段真实情绪系统")
        print("=" * 140)
        
        # 账户状态
        unrealized_pnl = self.calculate_unrealized_pnl(current_price)
        total_equity = self.capital + unrealized_pnl
        total_return = (total_equity - self.initial_capital) / self.initial_capital * 100
        
        print(f"💰 账户: ${self.capital:.2f} + ${unrealized_pnl:+.2f} = ${total_equity:.2f} ({total_return:+.2f}%)")
        
        # 持仓状态
        if self.position['size'] != 0:
            hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            print(f"📊 持仓: 🔥 {self.position['side']} {abs(self.position['size']):.6f} BTC @ ${self.position['entry_price']:,.0f} | {hold_hours:.1f}h")
        else:
            print(f"📊 持仓: 💤 空仓 | BTC: ${current_price:,.0f}")
        
        # 真实情绪信号分解显示
        breakdown = signal.get('signal_breakdown', {})
        
        print(f"\n🧠 真实情绪信号分解:")
        
        # 原始信号
        original = breakdown.get('original', {})
        print(f"   🎯 原始信号: {original.get('direction', 'WAIT')} (强度:{original.get('strength', 0):.1%}, 置信度:{original.get('confidence', 0):.1%})")
        
        # 多策略信号
        strategy = breakdown.get('multi_strategy', {})
        if strategy:
            print(f"   📊 多策略: {strategy.get('direction', 'WAIT')} (强度:{strategy.get('strength', 0):.1%}, 一致性:{strategy.get('agreement_score', 0):.1%})")
        
        # 真实情绪信号
        real_sentiment = breakdown.get('real_sentiment', {})
        if real_sentiment:
            print(f"   🌍 真实情绪: {real_sentiment.get('direction', 'WAIT')} (强度:{real_sentiment.get('strength', 0):.1%}, 类型:{real_sentiment.get('signal_type', 'unknown')})")
            print(f"      📊 100%真实API数据支持")
        
        # 融合详情
        fusion = signal.get('fusion_details', {})
        real_sentiment_weight = fusion.get('real_sentiment_weight', 0)
        print(f"   🔗 信号融合: {fusion.get('active_signals', 0)}个信号源, 真实情绪权重:{real_sentiment_weight:.1%}")
        
        # 最终决策
        print(f"\n🚀 第二阶段真实情绪最终决策:")
        signal_emoji = {"LONG": "🟢", "SHORT": "🔴", "WAIT": "⏸️"}
        decision_color = signal_emoji.get(signal['direction'], '❓')
        
        print(f"   {decision_color} 决策: {signal['direction']}")
        print(f"   📊 置信度: {signal['confidence']:.1%} | 信号强度: {signal['strength']:.1%}")
        print(f"   💭 决策理由: {signal['reason']}")
        print(f"   🔧 融合方法: {signal.get('fusion_method', 'unknown')}")
        print(f"   🌍 数据质量: 100%真实API数据")
        
        print("=" * 140)
    
    def _save_phase2_real_sentiment_state(self):
        """保存第二阶段真实情绪状态"""
        self._save_state()
        
        # 保存第二阶段真实情绪特有数据
        phase2_data = {
            'phase2_config': self.phase2_config,
            'phase2_performance': self.phase2_performance,
            'strategy_manager_report': self.strategy_manager.get_strategy_report(),
            'ai_monitoring_report': self.ai_monitor.get_monitoring_report(),
            'real_sentiment_enabled': True,
            'api_coverage': '100% Real Data'
        }
        
        with open("phase2_real_sentiment_data.json", 'w', encoding='utf-8') as f:
            json.dump(phase2_data, f, indent=2, ensure_ascii=False, default=str)
    
    def _prepare_strategy_data(self, indicators: Dict) -> Dict:
        """为策略准备数据"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            
            df = self.fetcher.get_historical_data(
                'BTCUSDT', '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            price_history = df['close'].tolist() if len(df) > 0 else []
        except:
            price_history = []
        
        return {
            'price': indicators['price'],
            'rsi': indicators['rsi'],
            'bb_position': indicators['bb_position'],
            'volume_ratio': indicators['volume_ratio'],
            'atr_percentage': indicators['atr_percentage'],
            'macd_trend': indicators['macd_trend'],
            'price_history': price_history
        }
    
    def _extract_features_for_monitoring(self, indicators: Dict) -> np.ndarray:
        """提取特征用于AI监控"""
        features = [
            indicators.get('rsi', 50),
            indicators.get('bb_position', 0.5),
            indicators.get('volume_ratio', 1.0),
            indicators.get('atr_percentage', 0.02),
            1.0 if indicators.get('macd_trend') == 'bullish' else 0.0,
            indicators.get('price', 104000) / 100000
        ]
        
        return np.array(features)
    
    def _get_default_strategy_signal(self) -> Dict:
        """默认策略信号"""
        return {
            'direction': 'WAIT',
            'strength': 0,
            'confidence': 0.3,
            'reason': '策略信号生成失败',
            'active_strategies': 0,
            'total_strategies': 0
        }
    
    def _get_default_sentiment_signal(self) -> Dict:
        """默认情绪信号"""
        return {
            'direction': 'WAIT',
            'strength': 0,
            'confidence': 0.3,
            'reason': '真实情绪分析失败',
            'signal_type': 'error'
        }

if __name__ == "__main__":
    print("🚀 第二阶段真实情绪AI交易系统")
    print("=" * 80)
    print("✅ 使用您的CoinMarketCap和NewsAPI密钥")
    print("✅ 100%真实情绪数据分析")
    print("✅ 增强信号融合算法")
    print("")
    
    # 创建真实情绪交易系统
    trader = Phase2RealSentimentTradingSystem(initial_capital=50.0, leverage=2)
    
    # 运行一次测试循环
    print("🔄 运行测试循环...")
    success = trader.run_phase2_real_sentiment_cycle()
    
    if success:
        print("✅ 第二阶段真实情绪系统测试成功！")
        print("🎯 系统已准备好使用100%真实数据进行交易")
    else:
        print("❌ 测试失败，请检查配置")
