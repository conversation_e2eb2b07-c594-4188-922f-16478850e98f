#!/usr/bin/env python3
"""
永续合约适配策略 - 针对永续合约交易的风险调整版本
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
import json
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class PerpetualFuturesStrategy:
    """
    永续合约交易策略
    """
    
    def __init__(self, model_path, leverage=2):
        """
        初始化永续合约策略
        
        Args:
            model_path: 模型文件路径
            leverage: 杠杆倍数 (建议2-3倍)
        """
        # 永续合约专用参数
        self.leverage = min(leverage, 3)  # 最大3倍杠杆
        self.confidence_threshold = 0.68  # 提高置信度要求
        self.stop_loss_ratio = 0.025      # 降低止损到2.5%
        self.take_profit_ratio = 0.06     # 设置止盈6%
        
        # 资金费率考虑
        self.funding_rate_threshold = 0.01  # 1%资金费率阈值
        self.max_hold_hours = 16           # 最大持仓时间(避免多次资金费率)
        
        # 加载模型
        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']
        
        # 交易状态
        self.capital = 50
        self.position = 0  # 0=空仓, 1=多头, -1=空头
        self.entry_price = 0
        self.entry_time = None
        self.trades = []
        
        print(f"🚀 永续合约策略初始化")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   置信度阈值: {self.confidence_threshold}")
        print(f"   止损: {self.stop_loss_ratio:.1%}")
        print(f"   止盈: {self.take_profit_ratio:.1%}")
        print(f"   最大持仓: {self.max_hold_hours}小时")
    
    def should_trade(self, up_probability, current_time):
        """
        判断是否应该交易（考虑永续合约特殊因素）
        """
        # 基本置信度检查
        if up_probability < self.confidence_threshold and up_probability > (1 - self.confidence_threshold):
            return False, 0, "置信度不足"
        
        # 避免在资金费率收取前后交易
        hour = current_time.hour
        if hour in [7, 15, 23]:  # 资金费率收取前1小时
            return False, 0, "避开资金费率时间"
        
        # 确定交易方向
        if up_probability > self.confidence_threshold:
            return True, 1, f"做多信号 (置信度: {up_probability:.3f})"
        elif up_probability < (1 - self.confidence_threshold):
            return True, -1, f"做空信号 (置信度: {up_probability:.3f})"
        
        return False, 0, "无明确信号"
    
    def should_close_position(self, current_price, current_time, up_probability):
        """
        判断是否应该平仓
        """
        if self.position == 0:
            return False, "无持仓"
        
        # 计算当前盈亏
        if self.position == 1:  # 多头
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:  # 空头
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        # 止损检查
        if pnl_ratio < -self.stop_loss_ratio:
            return True, f"止损 (亏损: {pnl_ratio:.2%})"
        
        # 止盈检查
        if pnl_ratio > self.take_profit_ratio:
            return True, f"止盈 (盈利: {pnl_ratio:.2%})"
        
        # 时间止损（避免长时间持仓）
        if self.entry_time:
            hold_hours = (current_time - self.entry_time).total_seconds() / 3600
            if hold_hours > self.max_hold_hours:
                return True, f"时间止损 (持仓{hold_hours:.1f}小时)"
        
        # 信号反转检查
        if self.position == 1 and up_probability < 0.4:
            return True, f"多头信号反转 (置信度: {up_probability:.3f})"
        elif self.position == -1 and up_probability > 0.6:
            return True, f"空头信号反转 (置信度: {up_probability:.3f})"
        
        return False, "持有"
    
    def execute_trade(self, action, direction, price, timestamp, confidence=None):
        """
        执行永续合约交易
        
        Args:
            action: 'OPEN' 或 'CLOSE'
            direction: 1=多头, -1=空头, 0=平仓
            price: 交易价格
            timestamp: 时间戳
            confidence: 预测置信度
        """
        if action == 'OPEN':
            self.position = direction
            self.entry_price = price
            self.entry_time = timestamp
            
            # 计算保证金（考虑杠杆）
            margin_used = self.capital / self.leverage
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'OPEN',
                'direction': 'LONG' if direction == 1 else 'SHORT',
                'price': price,
                'leverage': self.leverage,
                'margin_used': margin_used,
                'confidence': confidence
            }
            
            direction_text = "做多" if direction == 1 else "做空"
            print(f"✅ {direction_text} @ ${price:.2f} (杠杆: {self.leverage}x, 置信度: {confidence:.3f})")
            
        elif action == 'CLOSE':
            if self.position == 0:
                return
            
            # 计算盈亏（考虑杠杆）
            if self.position == 1:  # 平多
                pnl_ratio = (price - self.entry_price) / self.entry_price
            else:  # 平空
                pnl_ratio = (self.entry_price - price) / self.entry_price
            
            # 杠杆放大盈亏
            leveraged_pnl_ratio = pnl_ratio * self.leverage
            
            # 扣除手续费（开仓+平仓）
            total_fees = 0.0004 * self.leverage  # 0.02% * 2 * 杠杆
            final_pnl_ratio = leveraged_pnl_ratio - total_fees
            
            # 更新资金
            self.capital = self.capital * (1 + final_pnl_ratio)
            
            # 计算持仓时间
            hold_time = (timestamp - self.entry_time).total_seconds() / 3600 if self.entry_time else 0
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'CLOSE',
                'direction': 'LONG' if self.position == 1 else 'SHORT',
                'entry_price': self.entry_price,
                'exit_price': price,
                'pnl_ratio': pnl_ratio,
                'leveraged_pnl_ratio': leveraged_pnl_ratio,
                'final_pnl_ratio': final_pnl_ratio,
                'hold_hours': hold_time,
                'capital_after': self.capital,
                'confidence': confidence
            }
            
            direction_text = "平多" if self.position == 1 else "平空"
            print(f"✅ {direction_text} @ ${price:.2f} (盈亏: {final_pnl_ratio:+.2%}, 资金: ${self.capital:.2f})")
            
            # 重置持仓
            self.position = 0
            self.entry_price = 0
            self.entry_time = None
        
        self.trades.append(trade_record)
    
    def get_performance_stats(self):
        """
        获取绩效统计
        """
        close_trades = [t for t in self.trades if t['action'] == 'CLOSE']
        
        if not close_trades:
            return {
                'total_return': 0,
                'total_trades': 0,
                'win_rate': 0,
                'avg_hold_time': 0
            }
        
        total_return = (self.capital - 50) / 50
        profitable_trades = [t for t in close_trades if t['final_pnl_ratio'] > 0]
        win_rate = len(profitable_trades) / len(close_trades)
        avg_hold_time = np.mean([t['hold_hours'] for t in close_trades])
        
        return {
            'total_return': total_return,
            'total_trades': len(close_trades),
            'win_rate': win_rate,
            'avg_hold_time': avg_hold_time,
            'final_capital': self.capital
        }

def perpetual_futures_backtest(symbol='BTCUSDT', leverage=2, test_months=3):
    """
    永续合约回测
    """
    print(f"🚀 永续合约回测 {symbol}")
    print(f"杠杆: {leverage}x, 测试期间: {test_months}个月")
    print("=" * 60)
    
    # 使用最新模型
    import glob
    model_files = glob.glob(f"models/binary_trend_{symbol}_*.joblib")
    if not model_files:
        print(f"❌ 未找到 {symbol} 模型文件")
        return None
    
    model_path = max(model_files, key=lambda x: x.split('_')[-1])
    print(f"📦 使用模型: {model_path}")
    
    # 初始化策略
    strategy = PerpetualFuturesStrategy(model_path, leverage)
    
    # 获取测试数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=test_months*30)
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date.strftime('%Y-%m-%d'))
    
    # 特征工程
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    # 数据准备
    X = df_features.drop(columns=['target'], errors='ignore')
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    mask = ~X.isna().any(axis=1)
    X = X[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    # 获取预测
    X_scaled = strategy.scaler.transform(X)
    prediction_proba = strategy.model.predict_proba(X_scaled)
    up_probabilities = prediction_proba[:, 1]
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    print(f"\n📈 开始永续合约回测...")
    
    # 执行回测
    for i in range(len(prices)):
        price = prices[i]
        timestamp = timestamps[i]
        up_prob = up_probabilities[i]
        
        # 检查是否需要平仓
        should_close, close_reason = strategy.should_close_position(price, timestamp, up_prob)
        if should_close:
            strategy.execute_trade('CLOSE', 0, price, timestamp, up_prob)
        
        # 检查是否需要开仓
        should_trade, direction, trade_reason = strategy.should_trade(up_prob, timestamp)
        if should_trade and strategy.position == 0:
            strategy.execute_trade('OPEN', direction, price, timestamp, up_prob)
    
    # 强制平仓
    if strategy.position != 0:
        strategy.execute_trade('CLOSE', 0, prices[-1], timestamps[-1], up_probabilities[-1])
        print("🔄 强制平仓")
    
    # 生成报告
    stats = strategy.get_performance_stats()
    buy_hold_return = (prices[-1] - prices[0]) / prices[0]
    
    print(f"\n📊 永续合约回测结果:")
    print("=" * 50)
    print(f"💰 最终资金: ${stats['final_capital']:,.2f}")
    print(f"📈 总收益率: {stats['total_return']:+.2%}")
    print(f"📈 基准收益率: {buy_hold_return:+.2%}")
    print(f"🎯 超额收益: {stats['total_return'] - buy_hold_return:+.2%}")
    print(f"📊 完成交易: {stats['total_trades']}")
    print(f"📊 胜率: {stats['win_rate']:.2%}")
    print(f"⏱️  平均持仓: {stats['avg_hold_time']:.1f}小时")
    print(f"🔧 杠杆倍数: {leverage}x")
    
    # 风险提示
    print(f"\n⚠️  永续合约风险提示:")
    print(f"   - 杠杆交易风险极大，可能导致快速亏损")
    print(f"   - 资金费率会影响实际收益")
    print(f"   - 建议从小额开始，严格控制风险")
    
    return strategy

if __name__ == "__main__":
    import sys
    
    leverage = int(sys.argv[1]) if len(sys.argv) > 1 else 2
    leverage = min(max(leverage, 1), 3)  # 限制在1-3倍
    
    print(f"⚠️  永续合约交易风险提示:")
    print(f"   本策略基于现货数据训练，用于永续合约需要额外谨慎")
    print(f"   建议杠杆不超过3倍，从小额开始测试")
    print(f"")
    
    strategy = perpetual_futures_backtest('BTCUSDT', leverage, 3)
    
    if strategy:
        stats = strategy.get_performance_stats()
        if stats['total_return'] > 0.1 and stats['win_rate'] > 0.5:
            print(f"\n✅ 永续合约策略表现良好")
            print(f"建议谨慎进行小额实盘测试")
        else:
            print(f"\n⚠️  永续合约策略需要进一步优化")
            print(f"建议降低杠杆或调整参数")
