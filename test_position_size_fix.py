#!/usr/bin/env python3
"""
Test script to verify the position size calculation fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader
from datetime import datetime

def test_position_size_fix():
    """Test the position size calculation fix"""
    print("🧪 Testing Position Size Calculation Fix")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 Your Original Calculation:")
    print("💰 资金: $50")
    print("⚡ 杠杆: 125x")
    print("🎯 最大购买力: $50 × 125 = $6,250")
    print("📊 当前BTC价格: ~$102,000")
    print("🔢 理论最大仓位: $6,250 ÷ $102,000 = 0.0613 BTC")
    
    # Test the position calculation
    current_price = 102000.0
    
    # Create a mock signal for testing
    mock_signal = {
        'direction': 'LONG',
        'confidence': 0.4,  # 40% confidence
        'trading_style': 'left_side',
        'signal_count': 2,
        'reasons': ['Test signal']
    }
    
    mock_market_data = {
        'current_price': current_price,
        'volatility': 0.005,  # 0.5% volatility
        'volume_ratio': 1.0
    }
    
    print(f"\n🔧 Testing Position Size Calculation:")
    print(f"   Current Price: ${current_price:,.2f}")
    print(f"   Available Balance: ${trader.account['balance']:.2f}")
    print(f"   Leverage: {trader.leverage}x")
    
    # Calculate position size using the fixed method
    position_size = trader.calculate_dynamic_position_size(mock_signal, mock_market_data)
    
    # Calculate related values
    nominal_value = position_size * current_price
    margin_required = nominal_value / trader.leverage
    leverage_utilization = (nominal_value / (trader.account['balance'] * trader.leverage)) * 100
    
    print(f"\n✅ Fixed Position Size Results:")
    print(f"   📊 BTC Position Size: {position_size:.6f} BTC")
    print(f"   💎 Nominal Value: ${nominal_value:,.2f}")
    print(f"   💰 Margin Required: ${margin_required:.2f}")
    print(f"   📊 Leverage Utilization: {leverage_utilization:.1f}%")
    
    # Compare with your theoretical maximum
    theoretical_max = 6250 / current_price
    utilization_percentage = (position_size / theoretical_max) * 100
    
    print(f"\n📊 Comparison with Theoretical Maximum:")
    print(f"   🎯 Your Calculated Max: {theoretical_max:.6f} BTC")
    print(f"   📊 Actual Position: {position_size:.6f} BTC")
    print(f"   📈 Utilization: {utilization_percentage:.1f}% of maximum")
    
    # Analyze the improvement
    old_position_size = 0.000153  # From your original complaint
    improvement_factor = position_size / old_position_size
    
    print(f"\n🚀 Improvement Analysis:")
    print(f"   ❌ Old Position Size: {old_position_size:.6f} BTC")
    print(f"   ✅ New Position Size: {position_size:.6f} BTC")
    print(f"   📈 Improvement Factor: {improvement_factor:.1f}x larger")
    print(f"   💰 Old Nominal Value: ${old_position_size * current_price:,.2f}")
    print(f"   💰 New Nominal Value: ${nominal_value:,.2f}")
    
    # Risk analysis
    print(f"\n⚖️ Risk Analysis:")
    margin_usage_pct = (margin_required / trader.account['balance']) * 100
    print(f"   📊 Margin Usage: {margin_usage_pct:.1f}%")
    print(f"   🛡️ Available Margin: ${trader.account['balance'] - margin_required:.2f}")
    
    if margin_usage_pct > 80:
        print(f"   ⚠️ High margin usage - consider reducing position")
    elif margin_usage_pct > 50:
        print(f"   ✅ Moderate margin usage - good balance")
    else:
        print(f"   💚 Conservative margin usage - room for more")
    
    # Expected profit/loss scenarios
    print(f"\n💰 Expected P&L Scenarios (125x leverage):")
    scenarios = [0.001, 0.002, 0.005, 0.01]  # 0.1%, 0.2%, 0.5%, 1.0% price moves
    
    for pct in scenarios:
        price_change = current_price * pct
        pnl = position_size * price_change * trader.leverage
        roi = (pnl / margin_required) * 100
        print(f"   📈 {pct:.1%} price move: ${pnl:+.2f} ({roi:+.1f}% ROI)")
    
    print(f"\n" + "="*60)
    print("🎉 Position Size Fix Test Complete!")
    
    if position_size > 0.001:  # More than 0.001 BTC
        print("✅ Fixed: Position size now properly utilizes 125x leverage")
        print("✅ Improved: Much larger position than before")
        print("✅ Balanced: Reasonable margin usage")
        print("✅ Effective: Better profit potential")
    else:
        print("❌ Still too small: Position size needs further adjustment")
    
    print(f"\n💡 Key Improvements:")
    print(f"   🔧 Fixed calculation: Now uses leverage properly")
    print(f"   📈 Increased margin usage: 30% → 60%")
    print(f"   🚀 Better leverage utilization: {leverage_utilization:.1f}%")
    print(f"   💰 Larger positions: {improvement_factor:.1f}x increase")

if __name__ == "__main__":
    test_position_size_fix()
