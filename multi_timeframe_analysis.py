#!/usr/bin/env python3
"""
多时间框架分析模块 - 增强信号确认
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
from data_fetcher import BinanceDataFetcher

class MultiTimeFrameAnalyzer:
    """
    多时间框架分析器 - 提供多维度市场分析
    """
    
    def __init__(self):
        self.timeframes = {
            '1h': {'weight': 0.2, 'lookback_days': 7},
            '4h': {'weight': 0.3, 'lookback_days': 14}, 
            '1d': {'weight': 0.5, 'lookback_days': 30}
        }
        self.fetcher = BinanceDataFetcher()
        
    def get_multi_timeframe_analysis(self, symbol: str = 'BTCUSDT') -> Dict:
        """
        获取多时间框架综合分析
        """
        analysis_results = {}
        
        for timeframe, config in self.timeframes.items():
            try:
                # 获取对应时间框架的数据
                end_time = datetime.now()
                start_time = end_time - timedelta(days=config['lookback_days'])
                
                df = self.fetcher.get_historical_data(
                    symbol, 
                    timeframe, 
                    start_time.strftime('%Y-%m-%d'),
                    is_futures=True
                )
                
                if len(df) > 20:  # 确保有足够数据
                    tf_analysis = self._analyze_timeframe(df, timeframe)
                    tf_analysis['weight'] = config['weight']
                    analysis_results[timeframe] = tf_analysis
                    
            except Exception as e:
                print(f"❌ {timeframe}时间框架分析失败: {str(e)}")
                # 使用默认中性分析
                analysis_results[timeframe] = self._get_neutral_analysis(timeframe, config['weight'])
        
        # 计算综合信号
        combined_signal = self._combine_timeframe_signals(analysis_results)
        
        return {
            'timeframe_analysis': analysis_results,
            'combined_signal': combined_signal,
            'alignment_score': self._calculate_alignment_score(analysis_results)
        }
    
    def _analyze_timeframe(self, df: pd.DataFrame, timeframe: str) -> Dict:
        """
        分析单个时间框架
        """
        close = df['close']
        high = df['high']
        low = df['low']
        volume = df['volume']
        
        # 1. 趋势分析
        trend_analysis = self._analyze_trend(close)
        
        # 2. 动量分析
        momentum_analysis = self._analyze_momentum(close, high, low)
        
        # 3. 支撑阻力分析
        support_resistance = self._analyze_support_resistance(close, high, low)
        
        # 4. 成交量分析
        volume_analysis = self._analyze_volume(close, volume)
        
        # 5. 波动率分析
        volatility_analysis = self._analyze_volatility(close)
        
        # 综合评分
        overall_signal = self._calculate_timeframe_signal(
            trend_analysis, momentum_analysis, support_resistance, 
            volume_analysis, volatility_analysis
        )
        
        return {
            'timeframe': timeframe,
            'trend': trend_analysis,
            'momentum': momentum_analysis,
            'support_resistance': support_resistance,
            'volume': volume_analysis,
            'volatility': volatility_analysis,
            'overall_signal': overall_signal,
            'current_price': close.iloc[-1]
        }
    
    def _analyze_trend(self, close: pd.Series) -> Dict:
        """趋势分析"""
        # 多重移动平均线
        ma_5 = close.rolling(5).mean()
        ma_20 = close.rolling(20).mean()
        ma_50 = close.rolling(50).mean() if len(close) >= 50 else close.rolling(len(close)//2).mean()
        
        current_price = close.iloc[-1]
        current_ma5 = ma_5.iloc[-1]
        current_ma20 = ma_20.iloc[-1]
        current_ma50 = ma_50.iloc[-1]
        
        # 趋势强度
        if current_ma5 > current_ma20 > current_ma50 and current_price > current_ma5:
            trend_direction = 'strong_bullish'
            trend_strength = 0.8
        elif current_ma5 > current_ma20 and current_price > current_ma20:
            trend_direction = 'bullish'
            trend_strength = 0.6
        elif current_ma5 < current_ma20 < current_ma50 and current_price < current_ma5:
            trend_direction = 'strong_bearish'
            trend_strength = 0.8
        elif current_ma5 < current_ma20 and current_price < current_ma20:
            trend_direction = 'bearish'
            trend_strength = 0.6
        else:
            trend_direction = 'sideways'
            trend_strength = 0.3
        
        # 趋势一致性
        ma_alignment = self._check_ma_alignment(current_ma5, current_ma20, current_ma50)
        
        return {
            'direction': trend_direction,
            'strength': trend_strength,
            'ma_alignment': ma_alignment,
            'price_vs_ma20': (current_price - current_ma20) / current_ma20,
            'signal': 'bullish' if trend_strength > 0.5 and 'bullish' in trend_direction else 'bearish' if trend_strength > 0.5 and 'bearish' in trend_direction else 'neutral'
        }
    
    def _analyze_momentum(self, close: pd.Series, high: pd.Series, low: pd.Series) -> Dict:
        """动量分析"""
        # RSI
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1]
        
        # MACD
        ema_12 = close.ewm(span=12).mean()
        ema_26 = close.ewm(span=26).mean()
        macd_line = ema_12 - ema_26
        signal_line = macd_line.ewm(span=9).mean()
        
        macd_signal = 'bullish' if macd_line.iloc[-1] > signal_line.iloc[-1] else 'bearish'
        
        # 随机指标
        lowest_low = low.rolling(window=14).min()
        highest_high = high.rolling(window=14).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=3).mean()
        
        stoch_signal = 'bullish' if k_percent.iloc[-1] > d_percent.iloc[-1] else 'bearish'
        
        # 综合动量信号
        momentum_signals = [
            'bullish' if current_rsi < 70 and current_rsi > 50 else 'bearish' if current_rsi > 30 and current_rsi < 50 else 'neutral',
            macd_signal,
            stoch_signal
        ]
        
        bullish_count = momentum_signals.count('bullish')
        bearish_count = momentum_signals.count('bearish')
        
        if bullish_count > bearish_count:
            overall_momentum = 'bullish'
            momentum_strength = bullish_count / len(momentum_signals)
        elif bearish_count > bullish_count:
            overall_momentum = 'bearish'
            momentum_strength = bearish_count / len(momentum_signals)
        else:
            overall_momentum = 'neutral'
            momentum_strength = 0.5
        
        return {
            'rsi': current_rsi,
            'macd_signal': macd_signal,
            'stochastic_signal': stoch_signal,
            'overall_signal': overall_momentum,
            'strength': momentum_strength,
            'signal': overall_momentum
        }
    
    def _analyze_support_resistance(self, close: pd.Series, high: pd.Series, low: pd.Series) -> Dict:
        """支撑阻力分析"""
        current_price = close.iloc[-1]
        
        # 计算近期高低点
        recent_highs = high.rolling(10).max()
        recent_lows = low.rolling(10).min()
        
        # 找到关键支撑阻力位
        resistance_levels = []
        support_levels = []
        
        # 简化的支撑阻力识别
        for i in range(len(close)-20, len(close)):
            if i > 5 and i < len(close)-5:
                # 阻力位：局部高点
                if high.iloc[i] == high.iloc[i-5:i+5].max():
                    resistance_levels.append(high.iloc[i])
                
                # 支撑位：局部低点
                if low.iloc[i] == low.iloc[i-5:i+5].min():
                    support_levels.append(low.iloc[i])
        
        # 找到最近的支撑阻力
        resistance_levels = sorted(set(resistance_levels), reverse=True)
        support_levels = sorted(set(support_levels), reverse=True)
        
        nearest_resistance = None
        nearest_support = None
        
        for level in resistance_levels:
            if level > current_price:
                nearest_resistance = level
                break
        
        for level in support_levels:
            if level < current_price:
                nearest_support = level
                break
        
        # 计算价格位置
        if nearest_resistance and nearest_support:
            price_position = (current_price - nearest_support) / (nearest_resistance - nearest_support)
        else:
            price_position = 0.5
        
        return {
            'nearest_resistance': nearest_resistance,
            'nearest_support': nearest_support,
            'price_position': price_position,
            'signal': 'bullish' if price_position < 0.3 else 'bearish' if price_position > 0.7 else 'neutral'
        }
    
    def _analyze_volume(self, close: pd.Series, volume: pd.Series) -> Dict:
        """成交量分析"""
        volume_ma = volume.rolling(20).mean()
        current_volume = volume.iloc[-1]
        avg_volume = volume_ma.iloc[-1]
        
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
        
        # 价格变化
        price_change = close.pct_change().iloc[-1]
        
        # 价量关系
        if volume_ratio > 1.5 and price_change > 0.01:
            volume_signal = 'strong_bullish'
        elif volume_ratio > 1.5 and price_change < -0.01:
            volume_signal = 'strong_bearish'
        elif volume_ratio > 1.2:
            volume_signal = 'bullish' if price_change > 0 else 'bearish'
        else:
            volume_signal = 'neutral'
        
        return {
            'volume_ratio': volume_ratio,
            'price_change': price_change,
            'signal': volume_signal,
            'strength': min(volume_ratio / 2, 1.0)
        }
    
    def _analyze_volatility(self, close: pd.Series) -> Dict:
        """波动率分析"""
        returns = close.pct_change().dropna()
        volatility = returns.rolling(20).std() * np.sqrt(24)  # 年化波动率
        current_volatility = volatility.iloc[-1]
        
        # 波动率等级
        if current_volatility > 0.6:
            vol_level = 'extreme'
        elif current_volatility > 0.4:
            vol_level = 'high'
        elif current_volatility > 0.2:
            vol_level = 'normal'
        else:
            vol_level = 'low'
        
        return {
            'current_volatility': current_volatility,
            'level': vol_level,
            'signal': 'caution' if vol_level in ['extreme', 'high'] else 'normal'
        }
    
    def _calculate_timeframe_signal(self, trend, momentum, support_resistance, volume, volatility) -> Dict:
        """计算时间框架综合信号"""
        signals = [
            trend['signal'],
            momentum['signal'], 
            support_resistance['signal'],
            volume['signal']
        ]
        
        # 过滤中性信号
        active_signals = [s for s in signals if s not in ['neutral', 'normal', 'caution']]
        
        if not active_signals:
            return {'direction': 'neutral', 'strength': 0.5, 'confidence': 0.3}
        
        bullish_count = len([s for s in active_signals if 'bullish' in s])
        bearish_count = len([s for s in active_signals if 'bearish' in s])
        
        if bullish_count > bearish_count:
            direction = 'bullish'
            strength = bullish_count / len(active_signals)
        elif bearish_count > bullish_count:
            direction = 'bearish'
            strength = bearish_count / len(active_signals)
        else:
            direction = 'neutral'
            strength = 0.5
        
        # 计算置信度
        confidence = (abs(bullish_count - bearish_count) / len(active_signals)) * 0.8 + 0.2
        
        # 波动率调整
        if volatility['level'] in ['extreme', 'high']:
            confidence *= 0.8  # 高波动率降低置信度
        
        return {
            'direction': direction,
            'strength': strength,
            'confidence': confidence,
            'supporting_signals': active_signals
        }
    
    def _combine_timeframe_signals(self, timeframe_results: Dict) -> Dict:
        """合并多时间框架信号"""
        if not timeframe_results:
            return {'direction': 'neutral', 'strength': 0.5, 'confidence': 0.3}
        
        weighted_bullish = 0
        weighted_bearish = 0
        total_weight = 0
        confidence_sum = 0
        
        for tf, result in timeframe_results.items():
            if 'overall_signal' in result:
                weight = result['weight']
                signal = result['overall_signal']
                confidence = signal.get('confidence', 0.5)
                strength = signal.get('strength', 0.5)
                
                total_weight += weight
                confidence_sum += confidence * weight
                
                if signal['direction'] == 'bullish':
                    weighted_bullish += weight * strength
                elif signal['direction'] == 'bearish':
                    weighted_bearish += weight * strength
        
        if total_weight == 0:
            return {'direction': 'neutral', 'strength': 0.5, 'confidence': 0.3}
        
        avg_confidence = confidence_sum / total_weight
        
        if weighted_bullish > weighted_bearish:
            direction = 'bullish'
            strength = weighted_bullish / total_weight
        elif weighted_bearish > weighted_bullish:
            direction = 'bearish'
            strength = weighted_bearish / total_weight
        else:
            direction = 'neutral'
            strength = 0.5
        
        return {
            'direction': direction,
            'strength': strength,
            'confidence': avg_confidence,
            'timeframe_agreement': abs(weighted_bullish - weighted_bearish) / total_weight
        }
    
    def _calculate_alignment_score(self, timeframe_results: Dict) -> float:
        """计算时间框架一致性分数"""
        if len(timeframe_results) < 2:
            return 0.5
        
        signals = []
        for tf, result in timeframe_results.items():
            if 'overall_signal' in result:
                signals.append(result['overall_signal']['direction'])
        
        if not signals:
            return 0.5
        
        # 计算一致性
        bullish_count = signals.count('bullish')
        bearish_count = signals.count('bearish')
        neutral_count = signals.count('neutral')
        
        total_signals = len(signals)
        max_agreement = max(bullish_count, bearish_count, neutral_count)
        
        alignment_score = max_agreement / total_signals
        
        return alignment_score
    
    def _check_ma_alignment(self, ma5: float, ma20: float, ma50: float) -> str:
        """检查移动平均线排列"""
        if ma5 > ma20 > ma50:
            return 'bullish_aligned'
        elif ma5 < ma20 < ma50:
            return 'bearish_aligned'
        else:
            return 'mixed'
    
    def _get_neutral_analysis(self, timeframe: str, weight: float) -> Dict:
        """获取中性分析结果"""
        return {
            'timeframe': timeframe,
            'weight': weight,
            'overall_signal': {
                'direction': 'neutral',
                'strength': 0.5,
                'confidence': 0.3
            },
            'error': True
        }

if __name__ == "__main__":
    # 测试多时间框架分析
    analyzer = MultiTimeFrameAnalyzer()
    
    try:
        result = analyzer.get_multi_timeframe_analysis('BTCUSDT')
        
        print("多时间框架分析结果:")
        print(f"综合信号: {result['combined_signal']}")
        print(f"一致性分数: {result['alignment_score']:.2f}")
        
        for tf, analysis in result['timeframe_analysis'].items():
            if not analysis.get('error', False):
                signal = analysis['overall_signal']
                print(f"{tf}: {signal['direction']} (强度: {signal['strength']:.2f}, 置信度: {signal['confidence']:.2f})")
    
    except Exception as e:
        print(f"测试失败: {str(e)}")
