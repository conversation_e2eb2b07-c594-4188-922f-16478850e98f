#!/usr/bin/env python3
"""
参数优化脚本 - 寻找最佳策略参数
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

def optimize_parameters(symbol='BTCUSDT', model_path=None):
    """
    优化策略参数
    """
    print(f"🔧 开始参数优化 {symbol}...")
    
    if model_path is None:
        # 使用最新的模型
        import glob
        model_files = glob.glob(f"models/binary_trend_{symbol}_*.joblib")
        if not model_files:
            print(f"❌ 未找到 {symbol} 的模型文件")
            return None
        model_path = max(model_files, key=lambda x: x.split('_')[-1])
    
    print(f"📦 使用模型: {model_path}")
    
    # 加载模型
    model_data = joblib.load(model_path)
    model = model_data['model']
    scaler = model_data['scaler']
    
    # 获取测试数据
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', '2025-03-01')
    
    # 特征工程
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    X = df_features.drop(columns=['target'])
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    mask = ~X.isna().any(axis=1)
    X = X[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    # 预测
    X_scaled = scaler.transform(X)
    prediction_proba = model.predict_proba(X_scaled)
    up_proba = prediction_proba[:, 1]
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    # 参数网格
    confidence_thresholds = [0.55, 0.6, 0.65, 0.7, 0.75]
    stop_loss_ratios = [0.02, 0.03, 0.04, 0.05]
    
    print(f"🔍 测试参数组合...")
    print(f"   置信度阈值: {confidence_thresholds}")
    print(f"   止损比例: {stop_loss_ratios}")
    
    results = []
    
    for conf_threshold in confidence_thresholds:
        for stop_loss in stop_loss_ratios:
            result = backtest_with_parameters(
                up_proba, prices, timestamps, 
                conf_threshold, stop_loss
            )
            
            results.append({
                'confidence_threshold': conf_threshold,
                'stop_loss_ratio': stop_loss,
                'total_return': result['total_return'],
                'win_rate': result['win_rate'],
                'max_drawdown': result['max_drawdown'],
                'total_trades': result['total_trades'],
                'sharpe_ratio': result.get('sharpe_ratio', 0)
            })
    
    # 转换为DataFrame便于分析
    results_df = pd.DataFrame(results)
    
    # 找到最佳参数
    best_return = results_df.loc[results_df['total_return'].idxmax()]
    best_sharpe = results_df.loc[results_df['sharpe_ratio'].idxmax()]
    best_winrate = results_df.loc[results_df['win_rate'].idxmax()]
    
    print(f"\n📊 参数优化结果:")
    print("=" * 60)
    
    print(f"🏆 最佳收益率参数:")
    print(f"   置信度阈值: {best_return['confidence_threshold']}")
    print(f"   止损比例: {best_return['stop_loss_ratio']}")
    print(f"   总收益率: {best_return['total_return']:.2%}")
    print(f"   胜率: {best_return['win_rate']:.2%}")
    print(f"   最大回撤: {best_return['max_drawdown']:.2%}")
    
    print(f"\n📈 最佳夏普比率参数:")
    print(f"   置信度阈值: {best_sharpe['confidence_threshold']}")
    print(f"   止损比例: {best_sharpe['stop_loss_ratio']}")
    print(f"   夏普比率: {best_sharpe['sharpe_ratio']:.2f}")
    print(f"   总收益率: {best_sharpe['total_return']:.2%}")
    
    print(f"\n🎯 最佳胜率参数:")
    print(f"   置信度阈值: {best_winrate['confidence_threshold']}")
    print(f"   止损比例: {best_winrate['stop_loss_ratio']}")
    print(f"   胜率: {best_winrate['win_rate']:.2%}")
    print(f"   总收益率: {best_winrate['total_return']:.2%}")
    
    # 生成热力图
    print(f"\n📊 生成参数优化热力图...")
    
    # 创建透视表
    pivot_return = results_df.pivot(
        index='stop_loss_ratio', 
        columns='confidence_threshold', 
        values='total_return'
    )
    
    pivot_winrate = results_df.pivot(
        index='stop_loss_ratio', 
        columns='confidence_threshold', 
        values='win_rate'
    )
    
    # 绘制热力图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 收益率热力图
    im1 = ax1.imshow(pivot_return.values, cmap='RdYlGn', aspect='auto')
    ax1.set_title('Total Return Heatmap')
    ax1.set_xlabel('Confidence Threshold')
    ax1.set_ylabel('Stop Loss Ratio')
    ax1.set_xticks(range(len(confidence_thresholds)))
    ax1.set_xticklabels(confidence_thresholds)
    ax1.set_yticks(range(len(stop_loss_ratios)))
    ax1.set_yticklabels(stop_loss_ratios)
    
    # 添加数值标签
    for i in range(len(stop_loss_ratios)):
        for j in range(len(confidence_thresholds)):
            text = ax1.text(j, i, f'{pivot_return.iloc[i, j]:.1%}',
                           ha="center", va="center", color="black", fontsize=8)
    
    plt.colorbar(im1, ax=ax1)
    
    # 胜率热力图
    im2 = ax2.imshow(pivot_winrate.values, cmap='RdYlGn', aspect='auto')
    ax2.set_title('Win Rate Heatmap')
    ax2.set_xlabel('Confidence Threshold')
    ax2.set_ylabel('Stop Loss Ratio')
    ax2.set_xticks(range(len(confidence_thresholds)))
    ax2.set_xticklabels(confidence_thresholds)
    ax2.set_yticks(range(len(stop_loss_ratios)))
    ax2.set_yticklabels(stop_loss_ratios)
    
    # 添加数值标签
    for i in range(len(stop_loss_ratios)):
        for j in range(len(confidence_thresholds)):
            text = ax2.text(j, i, f'{pivot_winrate.iloc[i, j]:.1%}',
                           ha="center", va="center", color="black", fontsize=8)
    
    plt.colorbar(im2, ax=ax2)
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = f"parameter_optimization_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"📊 热力图已保存: {chart_path}")
    
    plt.show()
    
    return {
        'best_return_params': best_return.to_dict(),
        'best_sharpe_params': best_sharpe.to_dict(),
        'best_winrate_params': best_winrate.to_dict(),
        'all_results': results_df
    }

def backtest_with_parameters(up_proba, prices, timestamps, conf_threshold, stop_loss_ratio):
    """
    使用指定参数进行回测
    """
    capital = 10000
    position = 0
    trades = []
    equity_curve = []
    
    for i in range(len(up_proba)):
        price = prices[i]
        up_prob = up_proba[i]
        
        # 买入信号
        if up_prob > conf_threshold and position == 0:
            position = 1
            entry_price = price
            trades.append({
                'type': 'buy',
                'price': price,
                'time': timestamps[i]
            })
        
        # 卖出信号（概率低或止损）
        elif position == 1 and (up_prob < (1 - conf_threshold) or 
                               (price / entry_price - 1) < -stop_loss_ratio):
            position = 0
            pnl_ratio = (price - entry_price) / entry_price
            capital = capital * (1 + pnl_ratio - 0.002)
            
            trades.append({
                'type': 'sell',
                'price': price,
                'entry_price': entry_price,
                'pnl_ratio': pnl_ratio,
                'time': timestamps[i]
            })
        
        # 记录权益
        if position == 1:
            current_value = capital * (price / entry_price)
        else:
            current_value = capital
        
        equity_curve.append(current_value)
    
    # 最后平仓
    if position == 1:
        final_pnl = (prices[-1] - entry_price) / entry_price
        capital = capital * (1 + final_pnl - 0.002)
    
    # 计算指标
    total_return = (capital - 10000) / 10000
    
    profitable_trades = len([t for t in trades if t.get('pnl_ratio', 0) > 0])
    total_completed_trades = len([t for t in trades if 'pnl_ratio' in t])
    win_rate = profitable_trades / total_completed_trades if total_completed_trades > 0 else 0
    
    # 最大回撤
    peak = np.maximum.accumulate(equity_curve)
    drawdown = (np.array(equity_curve) - peak) / peak
    max_drawdown = np.min(drawdown)
    
    # 夏普比率（简化）
    returns = np.diff(equity_curve) / equity_curve[:-1]
    sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(24*365) if np.std(returns) > 0 else 0
    
    return {
        'total_return': total_return,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'total_trades': total_completed_trades,
        'sharpe_ratio': sharpe_ratio,
        'final_capital': capital
    }

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    
    results = optimize_parameters(symbol)
    
    if results:
        print(f"\n🎯 参数优化完成!")
        print(f"建议使用最佳收益率参数进行实盘测试。")
    else:
        print(f"❌ 参数优化失败")
