#!/usr/bin/env python3
"""
超简单训练脚本 - 完全避免分层抽样问题
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer, FeatureConfig

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score
import joblib

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

try:
    import lightgbm as lgb
    HAS_LGB = True
except ImportError:
    HAS_LGB = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def ultra_simple_train(symbol='BTCUSDT', model_type='xgb', days_back=90):
    """
    超简单训练 - 避免所有复杂问题
    """
    print(f"🚀 开始超简单训练 {symbol} {model_type.upper()} 模型...")
    
    try:
        # 1. 获取数据
        print("📊 获取数据...")
        start_date = (datetime.now() - pd.Timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, '1h', start_date)
        print(f"✅ 获取到 {len(df)} 条数据")
        
        # 2. 特征工程
        print("🔧 特征工程...")
        config = FeatureConfig(
            ma_periods=[5, 10, 20, 50, 120, 200],  # 保持完整的MA周期
            rsi_periods=[14],
            prediction_window=24
        )
        engineer = FeatureEngineer(config=config)
        df_features = engineer.create_features(df)
        
        # 3. 准备数据
        print("📋 准备数据...")
        
        if 'target' not in df_features.columns:
            raise ValueError("目标列 'target' 未找到")
        
        # 分离特征和目标
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始列
        original_cols = ['open', 'high', 'low', 'close', 'volume']
        X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
        
        # 清理数据
        print("🧹 清理数据...")
        
        # 移除包含NaN的行
        before_clean = len(X)
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]
        
        # 处理无穷大值
        X.replace([np.inf, -np.inf], np.nan, inplace=True)
        X.fillna(X.median(), inplace=True)
        
        print(f"数据清理: {before_clean} -> {len(X)} 样本")
        
        # 编码标签
        label_encoder = LabelEncoder()
        y_encoded = label_encoder.fit_transform(y)
        
        print(f"✅ 数据准备完成: {X.shape[1]} 特征, {len(y_encoded)} 样本")
        
        # 显示类别分布
        unique, counts = np.unique(y_encoded, return_counts=True)
        print(f"类别分布: {dict(zip(unique, counts))}")
        
        # 合并稀有类别
        print("🔄 合并稀有类别...")
        min_samples = 50  # 每个类别至少50个样本
        
        # 找出样本数充足的主要类别
        major_classes = unique[counts >= min_samples]
        print(f"主要类别: {major_classes} (样本数 >= {min_samples})")
        
        if len(major_classes) < 2:
            # 如果主要类别太少，降低阈值
            min_samples = 20
            major_classes = unique[counts >= min_samples]
            print(f"降低阈值后主要类别: {major_classes}")
        
        # 将稀有类别合并为"其他"类别
        y_simplified = y_encoded.copy()
        other_class_label = len(major_classes)  # 新的"其他"类别标签
        
        for class_label in unique:
            if class_label not in major_classes:
                y_simplified[y_encoded == class_label] = other_class_label
        
        # 重新映射标签为连续的0,1,2...
        unique_simplified = np.unique(y_simplified)
        label_mapping = {old: new for new, old in enumerate(unique_simplified)}
        y_final = np.array([label_mapping[label] for label in y_simplified])
        
        unique_final, counts_final = np.unique(y_final, return_counts=True)
        print(f"简化后类别分布: {dict(zip(unique_final, counts_final))}")
        
        # 4. 分割数据 (不使用分层抽样)
        print("✂️ 分割数据 (随机分割)...")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_final, test_size=0.2, random_state=42
        )
        
        print(f"训练集: {len(X_train)} 样本")
        print(f"测试集: {len(X_test)} 样本")
        
        # 5. 特征缩放
        print("📏 特征缩放...")
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 6. 创建模型
        print(f"🎯 创建 {model_type.upper()} 模型...")
        
        if model_type == 'xgb' and HAS_XGB:
            model = xgb.XGBClassifier(
                n_estimators=100,  # 减少树的数量加快训练
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1,
                eval_metric='mlogloss'
            )
        elif model_type == 'lgb' and HAS_LGB:
            model = lgb.LGBMClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            )
        else:
            # 默认使用随机森林
            print("使用随机森林模型")
            model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
        
        # 7. 训练模型
        print("🏃 训练模型...")
        model.fit(X_train_scaled, y_train)
        
        # 8. 评估模型
        print("📊 评估模型...")
        
        # 预测
        y_train_pred = model.predict(X_train_scaled)
        y_test_pred = model.predict(X_test_scaled)
        
        # 计算准确率
        train_accuracy = accuracy_score(y_train, y_train_pred)
        test_accuracy = accuracy_score(y_test, y_test_pred)
        
        # 9. 显示结果
        print("\n" + "="*50)
        print("🎉 训练完成!")
        print("="*50)
        print(f"训练集准确率: {train_accuracy:.4f}")
        print(f"测试集准确率: {test_accuracy:.4f}")
        
        # 分类报告
        print("\n📋 分类报告:")
        print(classification_report(y_test, y_test_pred))
        
        # 10. 保存模型
        print("💾 保存模型...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path("models")
        model_dir.mkdir(exist_ok=True)
        
        model_path = model_dir / f"ultra_simple_{model_type}_{symbol}_{timestamp}.joblib"
        scaler_path = model_dir / f"ultra_scaler_{model_type}_{symbol}_{timestamp}.joblib"
        encoder_path = model_dir / f"ultra_encoder_{model_type}_{symbol}_{timestamp}.joblib"
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        joblib.dump(label_encoder, encoder_path)
        
        print(f"✅ 模型已保存:")
        print(f"   模型: {model_path}")
        print(f"   缩放器: {scaler_path}")
        print(f"   编码器: {encoder_path}")
        
        # 11. 特征重要性
        if hasattr(model, 'feature_importances_'):
            print("\n🔍 Top 10 重要特征:")
            feature_importance = pd.Series(
                model.feature_importances_, 
                index=X.columns
            ).sort_values(ascending=False)
            
            for i, (feature, importance) in enumerate(feature_importance.head(10).items(), 1):
                print(f"{i:2d}. {feature:25}: {importance:.6f}")
        
        return {
            'model': model,
            'scaler': scaler,
            'label_encoder': label_encoder,
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'feature_names': X.columns.tolist(),
            'model_path': model_path,
            'scaler_path': scaler_path,
            'encoder_path': encoder_path
        }
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    import sys
    
    # 默认参数
    symbol = 'BTCUSDT'
    model_type = 'xgb'
    
    # 从命令行获取参数
    if len(sys.argv) > 1:
        symbol = sys.argv[1]
    if len(sys.argv) > 2:
        model_type = sys.argv[2]
    
    # 检查模型可用性
    if model_type == 'xgb' and not HAS_XGB:
        print("XGBoost不可用，使用随机森林")
        model_type = 'rf'
    elif model_type == 'lgb' and not HAS_LGB:
        print("LightGBM不可用，使用随机森林")
        model_type = 'rf'
    
    # 执行训练
    ultra_simple_train(symbol, model_type)
