#!/usr/bin/env python3
"""
自学习永续合约交易系统 - 基于交易记录动态优化策略
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
from collections import defaultdict, deque
import pickle
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class SelfLearningFuturesTrader:
    """
    自学习永续合约交易器 - 从交易记录中学习并优化策略
    """

    def __init__(self, initial_capital=50, leverage=2, model_path=None, learning_file="trading_memory.pkl"):
        """
        初始化自学习交易器
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = min(max(leverage, 1), 3)
        self.learning_file = learning_file

        # 持仓状态
        self.position = 0
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0

        # 学习记忆系统
        self.trading_memory = self.load_trading_memory()
        self.performance_tracker = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0,
            'best_conditions': {},
            'worst_conditions': {},
            'pattern_success': defaultdict(list),
            'threshold_performance': defaultdict(list),
            'time_performance': defaultdict(list)
        }

        # 动态策略参数 (会根据学习结果调整)
        self.adaptive_params = {
            'base_thresholds': {
                'strong_long': 0.70, 'weak_long': 0.55,
                'weak_short': 0.45, 'strong_short': 0.30
            },
            'position_sizes': {
                'strong': 0.7, 'weak': 0.5
            },
            'risk_params': {
                'strong_signal': {'stop_loss': 0.03, 'take_profit': 0.08, 'max_hold_hours': 24},
                'weak_signal': {'stop_loss': 0.025, 'take_profit': 0.06, 'max_hold_hours': 16}
            },
            'confidence_multipliers': {
                'high_success_pattern': 1.2,
                'low_success_pattern': 0.8,
                'neutral_pattern': 1.0
            }
        }

        # 学习窗口
        self.learning_window = 50  # 最近50笔交易用于学习
        self.min_trades_for_learning = 10  # 最少10笔交易开始学习

        self.commission_rate = 0.0004
        self.funding_rate = 0.0001

        # 交易记录
        self.trades = []
        self.equity_history = []
        self.learning_log = []

        # 加载模型
        if model_path is None:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if not model_files:
                raise ValueError("未找到BTCUSDT模型文件")
            model_path = max(model_files, key=lambda x: x.split('_')[-1])

        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']

        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()

        print(f"🧠 自学习永续合约交易器初始化完成")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   学习记忆: {len(self.trading_memory.get('historical_trades', []))} 条历史记录")
        print(f"   学习窗口: {self.learning_window} 笔交易")
        print(f"   自适应优化: 阈值/仓位/风险参数动态调整")

    def load_trading_memory(self):
        """
        加载交易记忆
        """
        try:
            with open(self.learning_file, 'rb') as f:
                memory = pickle.load(f)
                print(f"📚 加载交易记忆: {len(memory.get('historical_trades', []))} 条记录")
                return memory
        except FileNotFoundError:
            print(f"📚 创建新的交易记忆文件")
            return {
                'historical_trades': [],
                'performance_patterns': {},
                'optimal_conditions': {},
                'learning_insights': []
            }

    def save_trading_memory(self):
        """
        保存交易记忆
        """
        self.trading_memory['last_updated'] = datetime.now().isoformat()
        with open(self.learning_file, 'wb') as f:
            pickle.dump(self.trading_memory, f)

    def analyze_trading_patterns(self):
        """
        分析交易模式并学习
        """
        if len(self.trades) < self.min_trades_for_learning:
            return None

        # 获取最近的交易记录
        recent_trades = [t for t in self.trades if t['action'] == 'CLOSE'][-self.learning_window:]

        if len(recent_trades) < 5:
            return None

        # 分析成功模式
        successful_trades = [t for t in recent_trades if t.get('final_pnl', 0) > 0]
        failed_trades = [t for t in recent_trades if t.get('final_pnl', 0) <= 0]

        current_win_rate = len(successful_trades) / len(recent_trades)
        avg_profit = np.mean([t.get('final_pnl', 0) for t in successful_trades]) if successful_trades else 0
        avg_loss = np.mean([t.get('final_pnl', 0) for t in failed_trades]) if failed_trades else 0

        # 分析最佳交易条件
        best_conditions = self.find_optimal_conditions(successful_trades)
        worst_conditions = self.find_optimal_conditions(failed_trades)

        # 分析置信度vs成功率关系
        confidence_analysis = self.analyze_confidence_performance(recent_trades)

        # 分析持仓时间vs收益关系
        time_analysis = self.analyze_time_performance(recent_trades)

        learning_result = {
            'timestamp': datetime.now(),
            'sample_size': len(recent_trades),
            'win_rate': current_win_rate,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'profit_loss_ratio': abs(avg_profit / avg_loss) if avg_loss != 0 else float('inf'),
            'best_conditions': best_conditions,
            'worst_conditions': worst_conditions,
            'confidence_analysis': confidence_analysis,
            'time_analysis': time_analysis
        }

        self.learning_log.append(learning_result)
        return learning_result

    def find_optimal_conditions(self, trades):
        """
        找到最优交易条件
        """
        if not trades:
            return {}

        conditions = {
            'confidence_ranges': defaultdict(list),
            'directions': defaultdict(list),
            'hold_times': [],
            'market_conditions': defaultdict(list)
        }

        for trade in trades:
            confidence = trade.get('confidence', 0.5)
            pnl = trade.get('final_pnl', 0)
            direction = trade.get('direction', 'UNKNOWN')
            hold_time = trade.get('hold_hours', 0)

            # 置信度区间分析
            if confidence > 0.7:
                conditions['confidence_ranges']['high'].append(pnl)
            elif confidence > 0.6:
                conditions['confidence_ranges']['medium_high'].append(pnl)
            elif confidence > 0.4:
                conditions['confidence_ranges']['medium_low'].append(pnl)
            else:
                conditions['confidence_ranges']['low'].append(pnl)

            # 方向分析
            conditions['directions'][direction].append(pnl)

            # 持仓时间
            conditions['hold_times'].append((hold_time, pnl))

        # 计算最优条件
        optimal = {}

        # 最佳置信度区间
        best_confidence_range = None
        best_confidence_avg = float('-inf')
        for range_name, pnls in conditions['confidence_ranges'].items():
            if pnls:
                avg_pnl = np.mean(pnls)
                if avg_pnl > best_confidence_avg:
                    best_confidence_avg = avg_pnl
                    best_confidence_range = range_name

        optimal['best_confidence_range'] = best_confidence_range
        optimal['best_confidence_avg_pnl'] = best_confidence_avg

        # 最佳方向
        best_direction = None
        best_direction_avg = float('-inf')
        for direction, pnls in conditions['directions'].items():
            if pnls:
                avg_pnl = np.mean(pnls)
                if avg_pnl > best_direction_avg:
                    best_direction_avg = avg_pnl
                    best_direction = direction

        optimal['best_direction'] = best_direction
        optimal['best_direction_avg_pnl'] = best_direction_avg

        # 最佳持仓时间
        if conditions['hold_times']:
            hold_times_df = pd.DataFrame(conditions['hold_times'], columns=['hold_time', 'pnl'])
            # 按持仓时间分组分析
            time_groups = pd.cut(hold_times_df['hold_time'], bins=[0, 4, 8, 16, 24, float('inf')],
                               labels=['0-4h', '4-8h', '8-16h', '16-24h', '24h+'])
            time_analysis = hold_times_df.groupby(time_groups)['pnl'].agg(['mean', 'count']).to_dict()
            optimal['time_analysis'] = time_analysis

        return optimal

    def analyze_confidence_performance(self, trades):
        """
        分析置信度与表现的关系
        """
        confidence_buckets = {
            'very_high': [],  # >0.8
            'high': [],       # 0.7-0.8
            'medium': [],     # 0.4-0.7
            'low': []         # <0.4
        }

        for trade in trades:
            confidence = trade.get('confidence', 0.5)
            pnl = trade.get('final_pnl', 0)

            if confidence > 0.8:
                confidence_buckets['very_high'].append(pnl)
            elif confidence > 0.7:
                confidence_buckets['high'].append(pnl)
            elif confidence > 0.4:
                confidence_buckets['medium'].append(pnl)
            else:
                confidence_buckets['low'].append(pnl)

        analysis = {}
        for bucket, pnls in confidence_buckets.items():
            if pnls:
                analysis[bucket] = {
                    'count': len(pnls),
                    'win_rate': len([p for p in pnls if p > 0]) / len(pnls),
                    'avg_pnl': np.mean(pnls),
                    'total_pnl': sum(pnls)
                }

        return analysis

    def analyze_time_performance(self, trades):
        """
        分析时间与表现的关系
        """
        time_buckets = {
            'short': [],    # <4h
            'medium': [],   # 4-12h
            'long': []      # >12h
        }

        for trade in trades:
            hold_time = trade.get('hold_hours', 0)
            pnl = trade.get('final_pnl', 0)

            if hold_time < 4:
                time_buckets['short'].append(pnl)
            elif hold_time < 12:
                time_buckets['medium'].append(pnl)
            else:
                time_buckets['long'].append(pnl)

        analysis = {}
        for bucket, pnls in time_buckets.items():
            if pnls:
                analysis[bucket] = {
                    'count': len(pnls),
                    'win_rate': len([p for p in pnls if p > 0]) / len(pnls),
                    'avg_pnl': np.mean(pnls),
                    'avg_hold_time': np.mean([t.get('hold_hours', 0) for t in trades
                                            if (bucket == 'short' and t.get('hold_hours', 0) < 4) or
                                               (bucket == 'medium' and 4 <= t.get('hold_hours', 0) < 12) or
                                               (bucket == 'long' and t.get('hold_hours', 0) >= 12)])
                }

        return analysis

    def optimize_strategy_parameters(self, learning_result):
        """
        基于学习结果优化策略参数
        """
        if not learning_result:
            return

        print(f"\n🧠 策略学习与优化")
        print("=" * 50)

        # 优化阈值
        confidence_analysis = learning_result['confidence_analysis']

        # 如果高置信度表现更好，降低阈值以获得更多高质量信号
        if 'high' in confidence_analysis and 'medium' in confidence_analysis:
            high_performance = confidence_analysis['high']['avg_pnl']
            medium_performance = confidence_analysis['medium']['avg_pnl']

            if high_performance > medium_performance * 1.5:
                # 高置信度表现显著更好，降低阈值
                self.adaptive_params['base_thresholds']['strong_long'] *= 0.98
                self.adaptive_params['base_thresholds']['strong_short'] *= 1.02
                print(f"📈 检测到高置信度优势，调整阈值更激进")
            elif medium_performance > high_performance:
                # 中等置信度表现更好，提高阈值
                self.adaptive_params['base_thresholds']['weak_long'] *= 1.02
                self.adaptive_params['base_thresholds']['weak_short'] *= 0.98
                print(f"📊 检测到中等置信度优势，调整阈值更保守")

        # 优化仓位大小
        best_conditions = learning_result['best_conditions']
        if best_conditions.get('best_confidence_range') == 'high':
            self.adaptive_params['position_sizes']['strong'] = min(0.8,
                self.adaptive_params['position_sizes']['strong'] * 1.05)
            print(f"📊 增加强信号仓位至 {self.adaptive_params['position_sizes']['strong']:.0%}")

        # 优化风险参数
        time_analysis = learning_result['time_analysis']
        if 'short' in time_analysis and time_analysis['short']['win_rate'] > 0.7:
            # 短期持仓表现好，缩短最大持仓时间
            self.adaptive_params['risk_params']['weak_signal']['max_hold_hours'] *= 0.9
            print(f"⏰ 短期持仓优势明显，缩短持仓时间")
        elif 'long' in time_analysis and time_analysis['long']['avg_pnl'] > 0:
            # 长期持仓有利，延长持仓时间
            self.adaptive_params['risk_params']['strong_signal']['max_hold_hours'] *= 1.1
            print(f"⏰ 长期持仓有利，延长持仓时间")

        # 优化止损止盈
        profit_loss_ratio = learning_result['profit_loss_ratio']
        if profit_loss_ratio > 2.0:
            # 盈亏比很好，可以适当放宽止损
            for signal_type in self.adaptive_params['risk_params']:
                self.adaptive_params['risk_params'][signal_type]['stop_loss'] *= 1.05
                print(f"🛡️ 盈亏比优秀，适当放宽止损")
        elif profit_loss_ratio < 1.0:
            # 盈亏比不好，收紧止损
            for signal_type in self.adaptive_params['risk_params']:
                self.adaptive_params['risk_params'][signal_type]['stop_loss'] *= 0.95
                print(f"🛡️ 盈亏比需改善，收紧止损")

        # 保存学习结果到记忆
        self.trading_memory['learning_insights'].append({
            'timestamp': datetime.now().isoformat(),
            'learning_result': learning_result,
            'parameter_adjustments': self.adaptive_params.copy()
        })

        # 限制记忆大小
        if len(self.trading_memory['learning_insights']) > 100:
            self.trading_memory['learning_insights'] = self.trading_memory['learning_insights'][-50:]

        self.save_trading_memory()

        print(f"💾 学习结果已保存到记忆系统")

    def get_current_prediction(self, symbol='BTCUSDT'):
        """
        获取当前预测
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)

            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )

            if len(df) < 200:
                return None, None, None

            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')

            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)

            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]

            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            current_time = datetime.now()

            return up_probability, current_price, current_time

        except Exception as e:
            print(f"❌ 获取预测错误: {str(e)}")
            return None, None, None

    def should_open_position(self, up_probability):
        """
        判断是否开仓 - 自学习版本
        """
        if self.position != 0:
            return False, 0, 0, None, "已有持仓"

        # 使用自适应阈值
        thresholds = self.adaptive_params['base_thresholds']
        position_sizes = self.adaptive_params['position_sizes']

        # 根据学习结果调整置信度
        adjusted_probability = self.apply_learning_adjustments(up_probability)

        # 确定信号类型
        if adjusted_probability > thresholds['strong_long']:
            signal_type = 'strong_long'
            position_size = position_sizes['strong']
            risk_params = self.adaptive_params['risk_params']['strong_signal'].copy()
        elif adjusted_probability > thresholds['weak_long']:
            signal_type = 'weak_long'
            position_size = position_sizes['weak']
            risk_params = self.adaptive_params['risk_params']['weak_signal'].copy()
        elif adjusted_probability < thresholds['strong_short']:
            signal_type = 'strong_short'
            position_size = position_sizes['strong']
            risk_params = self.adaptive_params['risk_params']['strong_signal'].copy()
        elif adjusted_probability < thresholds['weak_short']:
            signal_type = 'weak_short'
            position_size = position_sizes['weak']
            risk_params = self.adaptive_params['risk_params']['weak_signal'].copy()
        else:
            return False, 0, 0, None, f"中性信号 (原始:{up_probability:.1%}, 调整:{adjusted_probability:.1%})"

        # 确定方向
        direction = 1 if 'long' in signal_type else -1

        reason = f"{signal_type} (学习调整: {up_probability:.1%}→{adjusted_probability:.1%})"

        return True, direction, position_size, risk_params, reason

    def apply_learning_adjustments(self, up_probability):
        """
        应用学习调整
        """
        # 基于历史表现调整置信度
        if len(self.learning_log) > 0:
            latest_learning = self.learning_log[-1]

            # 如果最近胜率很高，增强信号
            if latest_learning['win_rate'] > 0.7:
                if up_probability > 0.5:
                    return min(0.95, up_probability * 1.1)
                else:
                    return max(0.05, up_probability * 0.9)

            # 如果最近胜率很低，减弱信号
            elif latest_learning['win_rate'] < 0.4:
                return 0.5 + (up_probability - 0.5) * 0.8

        return up_probability

    def should_close_position(self, up_probability, current_price):
        """
        判断是否平仓 - 自学习版本 + 利润保护
        """
        if self.position == 0:
            return False, "无持仓"

        # 🛡️ 利润保护检查
        if self.equity_history:
            current_equity = self.equity_history[-1]['equity']
            total_return = (current_equity - self.initial_capital) / self.initial_capital

            # 40%收益率触发70%保护
            if total_return >= 0.40 and abs(self.position) > 0.000150:  # 还有足够头寸可保护
                protection_ratio = 0.70
                print(f"🛡️ 触发利润保护: 收益率{total_return:.1%} → 保护{protection_ratio:.0%}头寸")
                return True, f"利润保护平仓{protection_ratio:.0%} (收益率: {total_return:.1%})"

        # 计算盈亏
        if self.position > 0:
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - current_price) / self.entry_price

        # 使用当前风险参数
        if hasattr(self, 'current_risk_params'):
            risk_params = self.current_risk_params
        else:
            risk_params = self.adaptive_params['risk_params']['weak_signal']

        # 学习调整的平仓逻辑
        adjusted_probability = self.apply_learning_adjustments(up_probability)

        # 基于学习结果的动态平仓
        if len(self.learning_log) > 0:
            latest_learning = self.learning_log[-1]

            # 如果最近短期持仓表现更好，更快平仓
            time_analysis = latest_learning.get('time_analysis', {})
            if 'short' in time_analysis and time_analysis['short']['win_rate'] > 0.7:
                if self.entry_time:
                    hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
                    if hold_hours > 4 and pnl_ratio > 0.02:  # 4小时后有2%盈利就考虑平仓
                        return True, f"学习优化: 短期盈利平仓 ({pnl_ratio:.2%})"

        # 标准平仓逻辑
        if pnl_ratio < -risk_params['stop_loss']:
            return True, f"止损 ({pnl_ratio:.2%})"

        if pnl_ratio > risk_params['take_profit']:
            return True, f"止盈 ({pnl_ratio:.2%})"

        if self.entry_time:
            hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
            if hold_hours > risk_params['max_hold_hours']:
                return True, f"时间止损 ({hold_hours:.1f}h)"

        # 信号反转
        if self.position > 0 and adjusted_probability < 0.35:
            return True, f"多头信号反转 ({adjusted_probability:.1%})"
        elif self.position < 0 and adjusted_probability > 0.65:
            return True, f"空头信号反转 ({adjusted_probability:.1%})"

        return False, "持有"

    def execute_trade(self, action, direction, position_size_ratio, price, timestamp, confidence=None, reason="", risk_params=None):
        """
        执行交易并记录学习数据
        """
        if action == 'OPEN':
            available_margin = self.capital * 0.8
            position_value = available_margin * position_size_ratio * self.leverage
            position_size = position_value / price

            if direction == -1:
                position_size = -position_size

            self.position = position_size
            self.entry_price = price
            self.entry_time = timestamp
            self.margin_used = available_margin * position_size_ratio
            self.current_risk_params = risk_params

            opening_fee = abs(position_size) * price * self.commission_rate
            self.capital -= opening_fee

            trade_record = {
                'timestamp': timestamp,
                'action': 'OPEN',
                'direction': 'LONG' if direction == 1 else 'SHORT',
                'price': price,
                'position_size': position_size,
                'position_ratio': position_size_ratio,
                'margin_used': self.margin_used,
                'leverage': self.leverage,
                'confidence': confidence,
                'reason': reason,
                'risk_params': risk_params,
                'opening_fee': opening_fee,
                'adaptive_params_snapshot': self.adaptive_params.copy()
            }

            direction_text = "做多" if direction == 1 else "做空"
            print(f"✅ {direction_text} {abs(position_size):.6f} BTC @ ${price:,.2f}")
            print(f"   仓位: {position_size_ratio:.0%}, 置信度: {confidence:.1%}")
            print(f"   学习调整: {reason}")

        elif action == 'CLOSE':
            if self.position > 0:
                pnl_ratio = (price - self.entry_price) / self.entry_price
            else:
                pnl_ratio = (self.entry_price - price) / self.entry_price

            leveraged_pnl = pnl_ratio * self.leverage * self.margin_used
            closing_fee = abs(self.position) * price * self.commission_rate
            funding_fee = self.calculate_funding_fee()
            final_pnl = leveraged_pnl - closing_fee + funding_fee

            self.capital = self.capital + self.margin_used + final_pnl

            hold_time = (timestamp - self.entry_time).total_seconds() / 3600 if self.entry_time else 0

            trade_record = {
                'timestamp': timestamp,
                'action': 'CLOSE',
                'direction': 'LONG' if self.position > 0 else 'SHORT',
                'price': price,
                'entry_price': self.entry_price,
                'pnl_ratio': pnl_ratio,
                'leveraged_pnl': leveraged_pnl,
                'final_pnl': final_pnl,
                'hold_hours': hold_time,
                'confidence': confidence,
                'reason': reason,
                'capital_after': self.capital
            }

            direction_text = "平多" if self.position > 0 else "平空"
            print(f"✅ {direction_text} @ ${price:,.2f} (盈亏: {final_pnl:+.2f}, 资金: ${self.capital:.2f})")
            print(f"   持仓时长: {hold_time:.1f}小时")

            # 添加到历史记录用于学习
            self.trading_memory['historical_trades'].append(trade_record)

            # 限制历史记录大小
            if len(self.trading_memory['historical_trades']) > 1000:
                self.trading_memory['historical_trades'] = self.trading_memory['historical_trades'][-500:]

            # 触发学习
            if len(self.trades) % 5 == 0:  # 每5笔交易学习一次
                learning_result = self.analyze_trading_patterns()
                if learning_result:
                    self.optimize_strategy_parameters(learning_result)

            self.position = 0
            self.entry_price = 0
            self.entry_time = None
            self.margin_used = 0
            self.current_risk_params = None

        self.trades.append(trade_record)

    def calculate_funding_fee(self):
        """计算资金费率"""
        if self.position == 0 or not self.entry_time:
            return 0

        hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
        funding_periods = int(hold_hours / 8)

        if funding_periods > 0:
            position_value = abs(self.position) * self.entry_price
            funding_fee = position_value * self.funding_rate * funding_periods
            return -funding_fee if self.position > 0 else funding_fee
        return 0

    def update_equity(self, current_price, timestamp):
        """更新权益"""
        if self.position != 0:
            if self.position > 0:
                unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
            else:
                unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price

            unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
            funding_fee = self.calculate_funding_fee()
            current_equity = self.capital + self.margin_used + unrealized_pnl + funding_fee
        else:
            current_equity = self.capital

        self.equity_history.append({
            'timestamp': timestamp,
            'price': current_price,
            'equity': current_equity,
            'total_return': (current_equity - self.initial_capital) / self.initial_capital
        })

    def print_status(self, current_price=None, up_probability=None):
        """打印状态"""
        if self.equity_history:
            latest_equity = self.equity_history[-1]['equity']
            total_return = self.equity_history[-1]['total_return']
        else:
            latest_equity = self.capital
            total_return = 0

        completed_trades = [t for t in self.trades if t['action'] == 'CLOSE']
        profitable_trades = [t for t in completed_trades if t.get('final_pnl', 0) > 0]
        win_rate = len(profitable_trades) / len(completed_trades) if completed_trades else 0

        print(f"\n🧠 自学习永续合约交易状态")
        print("=" * 50)
        print(f"当前权益: ${latest_equity:.2f}")
        print(f"总收益率: {total_return:+.2%}")
        print(f"可用资金: ${self.capital:.2f}")
        print(f"占用保证金: ${self.margin_used:.2f}")

        if self.position != 0:
            position_type = "多头" if self.position > 0 else "空头"
            print(f"当前持仓: {position_type} {abs(self.position):.6f} BTC")
            print(f"入场价格: ${self.entry_price:,.2f}")

            if current_price:
                if self.position > 0:
                    unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
                else:
                    unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price

                unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
                print(f"未实现盈亏: {unrealized_pnl:+.2f} ({unrealized_pnl_ratio * self.leverage:+.2%})")
        else:
            print(f"当前持仓: 空仓")

        print(f"完成交易: {len(completed_trades)}")
        print(f"胜率: {win_rate:.2%}")

        # 显示学习状态
        if self.learning_log:
            latest_learning = self.learning_log[-1]
            print(f"\n🧠 学习状态:")
            print(f"学习样本: {latest_learning['sample_size']} 笔交易")
            print(f"学习胜率: {latest_learning['win_rate']:.2%}")
            print(f"盈亏比: {latest_learning['profit_loss_ratio']:.2f}")

        # 显示当前自适应参数
        thresholds = self.adaptive_params['base_thresholds']
        print(f"\n📊 当前自适应阈值:")
        print(f"强做多: >{thresholds['strong_long']:.2f}")
        print(f"弱做多: >{thresholds['weak_long']:.2f}")
        print(f"弱做空: <{thresholds['weak_short']:.2f}")
        print(f"强做空: <{thresholds['strong_short']:.2f}")

        if current_price and up_probability:
            adjusted_probability = self.apply_learning_adjustments(up_probability)

            print(f"\n📈 当前市场分析:")
            print(f"BTC永续价格: ${current_price:,.2f}")
            print(f"原始概率: {up_probability:.1%}")
            print(f"学习调整概率: {adjusted_probability:.1%}")

            # 确定信号
            if adjusted_probability > thresholds['strong_long']:
                signal = "🟢 强烈看涨"
            elif adjusted_probability > thresholds['weak_long']:
                signal = "🟡 轻微看涨"
            elif adjusted_probability < thresholds['strong_short']:
                signal = "🔴 强烈看跌"
            elif adjusted_probability < thresholds['weak_short']:
                signal = "🟡 轻微看跌"
            else:
                signal = "⚪ 中性"

            print(f"学习调整信号: {signal}")

def run_self_learning_simulation(check_interval=300, leverage=2):
    """运行自学习模拟"""
    print("🧠 启动自学习永续合约模拟交易系统")
    print("=" * 60)
    print("特点: 交易记录学习、策略自动优化、参数动态调整")
    print(f"杠杆倍数: {leverage}x")
    print(f"检查间隔: {check_interval}秒")
    print("")

    trader = SelfLearningFuturesTrader(initial_capital=50, leverage=leverage)

    try:
        while True:
            current_time = datetime.now()
            print(f"\n⏰ {current_time.strftime('%Y-%m-%d %H:%M:%S')} - 自学习市场分析...")

            up_prob, current_price, _ = trader.get_current_prediction()

            if up_prob is None:
                print("❌ 无法获取预测，跳过")
                time.sleep(check_interval)
                continue

            trader.update_equity(current_price, current_time)

            # 检查平仓
            if trader.position != 0:
                should_close, close_reason = trader.should_close_position(up_prob, current_price)
                if should_close:
                    trader.execute_trade('CLOSE', 0, 0, current_price, current_time, up_prob, close_reason)

            # 检查开仓
            should_open, direction, position_size, risk_params, open_reason = trader.should_open_position(up_prob)
            if should_open:
                trader.execute_trade('OPEN', direction, position_size, current_price, current_time, up_prob, open_reason, risk_params)

            trader.print_status(current_price, up_prob)

            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟...")
            time.sleep(check_interval)

    except KeyboardInterrupt:
        print(f"\n🛑 停止自学习交易")
        trader.print_status(current_price, up_prob)
        trader.save_trading_memory()
        print(f"💾 交易记忆已保存")

if __name__ == "__main__":
    import sys

    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300
    leverage = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    leverage = min(max(leverage, 1), 3)

    print("🧠 自学习交易系统说明:")
    print("- 从每笔交易中学习和优化")
    print("- 动态调整阈值、仓位、风险参数")
    print("- 识别最佳交易条件和模式")
    print("- 持续改进交易策略")
    print("- 交易记忆持久化保存")
    print("")

    run_self_learning_simulation(interval, leverage)