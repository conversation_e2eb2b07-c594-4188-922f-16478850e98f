#!/usr/bin/env python3
"""
实时模拟交易系统 - 基于币安永续合约规则
初始资金: $50, 实时AI增强交易
"""

import pandas as pd
import numpy as np
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer
from enhanced_display import print_enhanced_status, print_trade_execution
from enhanced_risk_management import EnhancedRiskManager
from multi_timeframe_analysis import MultiTimeFrameAnalyzer

class RealTimeSimulationTrader:
    """
    实时模拟交易器 - 基于币安永续合约规则
    """
    
    def __init__(self, initial_capital: float = 50.0, leverage: int = 2):
        """
        初始化实时模拟交易器
        """
        # 账户设置
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = leverage
        
        # 币安永续合约规则
        self.contract_rules = {
            'min_order_size': 0.001,  # 最小订单 0.001 BTC
            'min_notional': 5.0,      # 最小名义价值 5 USDT
            'maker_fee': 0.0002,      # Maker手续费 0.02%
            'taker_fee': 0.0004,      # Taker手续费 0.04%
            'funding_rate': 0.0001,   # 资金费率 0.01%
            'maintenance_margin': 0.004,  # 维持保证金率 0.4%
            'max_leverage': 125,      # 最大杠杆
            'funding_interval': 8     # 资金费率间隔(小时)
        }
        
        # 持仓状态
        self.position = {
            'size': 0.0,              # 持仓数量 (BTC)
            'side': None,             # 'LONG' 或 'SHORT'
            'entry_price': 0.0,       # 入场价格
            'entry_time': None,       # 入场时间
            'margin_used': 0.0,       # 占用保证金
            'unrealized_pnl': 0.0,    # 未实现盈亏
            'last_funding_time': None # 上次资金费率时间
        }
        
        # 交易记录
        self.trades = []
        self.equity_history = []
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'max_equity': initial_capital,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_factor': 0.0
        }
        
        # 数据获取
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()

        # 增强模块初始化
        self.risk_manager = EnhancedRiskManager(initial_capital)
        self.mtf_analyzer = MultiTimeFrameAnalyzer()

        # 加载AI模型
        self.ai_model = None
        self.scaler = None
        self._load_ai_model()
        
        # 状态文件
        self.state_file = "real_time_simulation_state.json"
        self._load_state()
        
        print(f"🚀 实时模拟交易系统启动")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   当前资金: ${self.capital:.2f}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   AI模型: {'已加载' if self.ai_model else '使用模拟'}")
        print(f"   合约规则: 币安永续合约标准")
    
    def _load_ai_model(self):
        """加载AI模型"""
        try:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if model_files:
                model_path = max(model_files, key=lambda x: x.split('_')[-1])
                model_data = joblib.load(model_path)
                self.ai_model = model_data['model']
                self.scaler = model_data['scaler']
                print(f"✅ AI模型加载成功")
            else:
                print(f"⚠️ 未找到AI模型，使用模拟预测")
        except Exception as e:
            print(f"❌ AI模型加载失败: {str(e)}")
    
    def _load_state(self):
        """加载交易状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                
                self.capital = state.get('capital', self.initial_capital)
                self.position = state.get('position', self.position)
                self.trades = state.get('trades', [])
                self.equity_history = state.get('equity_history', [])
                self.performance_stats = state.get('performance_stats', self.performance_stats)
                
                # 转换时间字符串
                if self.position.get('entry_time'):
                    self.position['entry_time'] = datetime.fromisoformat(self.position['entry_time'])
                if self.position.get('last_funding_time'):
                    self.position['last_funding_time'] = datetime.fromisoformat(self.position['last_funding_time'])
                
                print(f"📂 加载历史状态: 资金${self.capital:.2f}, 交易{len(self.trades)}笔")
            except Exception as e:
                print(f"❌ 状态加载失败: {str(e)}")
    
    def _save_state(self):
        """保存交易状态"""
        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            else:
                return obj

        state = {
            'capital': float(self.capital),
            'position': {
                'size': float(self.position['size']),
                'side': self.position['side'],
                'entry_price': float(self.position['entry_price']),
                'entry_time': self.position['entry_time'].isoformat() if self.position['entry_time'] else None,
                'margin_used': float(self.position['margin_used']),
                'unrealized_pnl': float(self.position['unrealized_pnl']),
                'last_funding_time': self.position['last_funding_time'].isoformat() if self.position['last_funding_time'] else None
            },
            'trades': convert_numpy_types(self.trades),
            'equity_history': convert_numpy_types(self.equity_history),
            'performance_stats': convert_numpy_types(self.performance_stats),
            'last_saved': datetime.now().isoformat()
        }

        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
    
    def get_ai_prediction(self, symbol: str = 'BTCUSDT') -> Tuple[float, float]:
        """获取AI预测"""
        try:
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            
            if self.ai_model is None:
                # 使用模拟预测，加入一些随机性模拟真实情况
                base_prob = 0.372
                noise = np.random.normal(0, 0.08)  # 增加一些变化
                probability = np.clip(base_prob + noise, 0.15, 0.85)
                return probability, current_price
            
            # 使用真实AI模型
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                return 0.372, current_price
            
            # 特征工程
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            # 预测
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.ai_model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            return up_probability, current_price
            
        except Exception as e:
            print(f"❌ AI预测失败: {str(e)}")
            return 0.372, 104000.0  # 默认值
    
    def calculate_technical_indicators(self, symbol: str = 'BTCUSDT') -> Dict:
        """计算技术指标"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)  # 获取7天数据
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 50:
                return self._get_default_indicators()
            
            # 计算RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1] if not rsi.empty else 50
            
            # 计算MACD
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            
            # 计算布林带
            sma_20 = df['close'].rolling(20).mean()
            std_20 = df['close'].rolling(20).std()
            upper_band = sma_20 + (std_20 * 2)
            lower_band = sma_20 - (std_20 * 2)
            
            current_price = df['close'].iloc[-1]
            bb_position = (current_price - lower_band.iloc[-1]) / (upper_band.iloc[-1] - lower_band.iloc[-1])
            
            # 计算成交量比率
            volume_sma = df['volume'].rolling(20).mean()
            volume_ratio = df['volume'].iloc[-1] / volume_sma.iloc[-1] if volume_sma.iloc[-1] > 0 else 1
            
            return {
                'rsi': current_rsi,
                'macd_trend': 'bullish' if macd_line.iloc[-1] > signal_line.iloc[-1] else 'bearish',
                'bb_position': bb_position,
                'volume_ratio': volume_ratio,
                'price': current_price
            }
            
        except Exception as e:
            print(f"❌ 技术指标计算失败: {str(e)}")
            return self._get_default_indicators()
    
    def _get_default_indicators(self) -> Dict:
        """获取默认技术指标"""
        return {
            'rsi': 50.0,
            'macd_trend': 'neutral',
            'bb_position': 0.5,
            'volume_ratio': 1.0,
            'price': 104000.0
        }
    
    def generate_trading_signal(self, ai_probability: float, indicators: Dict) -> Dict:
        """生成交易信号"""
        signals = []
        current_price = indicators['price']
        
        # AI增强信号
        if ai_probability < 0.35:  # 强看跌
            confirmations = 0
            if indicators['rsi'] > 50:
                confirmations += 1
            if indicators['macd_trend'] == 'bearish':
                confirmations += 1
            if indicators['bb_position'] > 0.6:
                confirmations += 1
            
            if confirmations >= 2:
                signals.append({
                    'direction': 'SHORT',
                    'strength': 0.8,
                    'reason': f'AI强看跌({ai_probability:.1%})+{confirmations}个指标确认',
                    'confidence': 0.75
                })
        
        elif ai_probability > 0.65:  # 强看涨
            confirmations = 0
            if indicators['rsi'] < 50:
                confirmations += 1
            if indicators['macd_trend'] == 'bullish':
                confirmations += 1
            if indicators['bb_position'] < 0.4:
                confirmations += 1
            
            if confirmations >= 2:
                signals.append({
                    'direction': 'LONG',
                    'strength': 0.8,
                    'reason': f'AI强看涨({ai_probability:.1%})+{confirmations}个指标确认',
                    'confidence': 0.75
                })
        
        elif ai_probability < 0.45:  # 弱看跌
            confirmations = 0
            if indicators['rsi'] > 55:
                confirmations += 1
            if indicators['macd_trend'] == 'bearish':
                confirmations += 1
            if indicators['bb_position'] > 0.7:
                confirmations += 1
            if indicators['volume_ratio'] > 1.2:
                confirmations += 1
            
            if confirmations >= 3:
                signals.append({
                    'direction': 'SHORT',
                    'strength': 0.6,
                    'reason': f'AI弱看跌({ai_probability:.1%})+{confirmations}个指标强确认',
                    'confidence': 0.65
                })
        
        elif ai_probability > 0.55:  # 弱看涨
            confirmations = 0
            if indicators['rsi'] < 45:
                confirmations += 1
            if indicators['macd_trend'] == 'bullish':
                confirmations += 1
            if indicators['bb_position'] < 0.3:
                confirmations += 1
            if indicators['volume_ratio'] > 1.2:
                confirmations += 1
            
            if confirmations >= 3:
                signals.append({
                    'direction': 'LONG',
                    'strength': 0.6,
                    'reason': f'AI弱看涨({ai_probability:.1%})+{confirmations}个指标强确认',
                    'confidence': 0.65
                })
        
        # 返回最强信号
        if signals:
            best_signal = max(signals, key=lambda x: x['strength'] * x['confidence'])
            return best_signal
        else:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'reason': f'AI中性({ai_probability:.1%})，技术指标确认不足',
                'confidence': 0.5
            }

    def calculate_position_size(self, entry_price: float, confidence: float) -> Optional[Dict]:
        """根据币安规则计算仓位大小"""
        # 可用资金 (保留20%作为缓冲)
        available_capital = self.capital * 0.8

        # 基础风险金额 (最大2%资金风险)
        max_risk = self.capital * 0.02

        # 根据置信度调整风险
        confidence_adj = min(confidence * 1.5, 1.0)
        risk_amount = max_risk * confidence_adj

        # 计算仓位价值 (假设2.5%止损)
        stop_loss_pct = 0.025
        position_value = risk_amount / stop_loss_pct

        # 应用杠杆
        margin_required = position_value / self.leverage

        # 检查最小和最大限制
        if margin_required > available_capital:
            margin_required = available_capital
            position_value = margin_required * self.leverage

        # 计算BTC数量
        btc_size = position_value / entry_price

        # 检查币安最小订单限制
        min_notional = self.contract_rules['min_notional']
        min_size = self.contract_rules['min_order_size']

        if position_value < min_notional:
            print(f"⚠️ 仓位价值${position_value:.2f}小于最小要求${min_notional}")
            return None

        if btc_size < min_size:
            print(f"⚠️ BTC数量{btc_size:.6f}小于最小要求{min_size}")
            return None

        return {
            'btc_size': btc_size,
            'position_value': position_value,
            'margin_required': margin_required,
            'risk_amount': risk_amount,
            'confidence_multiplier': confidence_adj
        }

    def calculate_unrealized_pnl(self, current_price: float) -> float:
        """计算未实现盈亏"""
        if self.position['size'] == 0:
            return 0.0

        entry_price = self.position['entry_price']
        size = abs(self.position['size'])

        if self.position['side'] == 'LONG':
            price_diff = current_price - entry_price
        else:  # SHORT
            price_diff = entry_price - current_price

        # 计算盈亏 (考虑杠杆)
        pnl = (price_diff / entry_price) * self.position['margin_used'] * self.leverage

        return pnl

    def open_position(self, side: str, entry_price: float, size_info: Dict, reason: str, confidence: float) -> bool:
        """开仓"""
        if self.position['size'] != 0:
            print(f"❌ 已有持仓，无法开新仓")
            return False

        btc_size = size_info['btc_size']
        margin_required = size_info['margin_required']

        # 计算手续费
        position_value = btc_size * entry_price
        fee = position_value * self.contract_rules['taker_fee']

        # 检查资金充足
        if margin_required + fee > self.capital:
            print(f"❌ 资金不足: 需要${margin_required + fee:.2f}, 可用${self.capital:.2f}")
            return False

        # 扣除手续费
        self.capital -= fee

        # 设置持仓
        self.position = {
            'size': btc_size if side == 'LONG' else -btc_size,
            'side': side,
            'entry_price': entry_price,
            'entry_time': datetime.now(),
            'margin_used': margin_required,
            'unrealized_pnl': 0.0,
            'last_funding_time': datetime.now()
        }

        # 记录交易
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'OPEN',
            'side': side,
            'size': btc_size,
            'price': entry_price,
            'margin': margin_required,
            'fee': fee,
            'reason': reason,
            'confidence': confidence,
            'capital_after': self.capital
        }

        self.trades.append(trade_record)
        self.performance_stats['total_trades'] += 1

        # 使用增强版显示
        print_trade_execution("OPEN", {
            'side': side,
            'size': btc_size,
            'price': entry_price,
            'margin': margin_required,
            'fee': fee,
            'reason': reason,
            'confidence': confidence
        })
        print(f"   剩余资金: ${self.capital:.2f}")

        self._save_state()
        return True

    def close_position(self, current_price: float, reason: str) -> bool:
        """平仓"""
        if self.position['size'] == 0:
            print(f"❌ 无持仓可平")
            return False

        # 计算盈亏
        unrealized_pnl = self.calculate_unrealized_pnl(current_price)

        # 计算手续费
        position_value = abs(self.position['size']) * current_price
        closing_fee = position_value * self.contract_rules['taker_fee']

        # 最终盈亏
        final_pnl = unrealized_pnl - closing_fee

        # 释放保证金并结算盈亏
        self.capital += self.position['margin_used'] + final_pnl

        # 更新统计
        if final_pnl > 0:
            self.performance_stats['winning_trades'] += 1
        else:
            self.performance_stats['losing_trades'] += 1

        self.performance_stats['total_pnl'] += final_pnl

        # 记录交易
        hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600

        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'CLOSE',
            'side': self.position['side'],
            'size': abs(self.position['size']),
            'entry_price': self.position['entry_price'],
            'exit_price': current_price,
            'unrealized_pnl': unrealized_pnl,
            'closing_fee': closing_fee,
            'final_pnl': final_pnl,
            'hold_hours': hold_hours,
            'reason': reason,
            'capital_after': self.capital
        }

        self.trades.append(trade_record)

        # 使用增强版显示
        print_trade_execution("CLOSE", {
            'side': self.position['side'],
            'entry_price': self.position['entry_price'],
            'exit_price': current_price,
            'hold_hours': hold_hours,
            'final_pnl': final_pnl,
            'reason': reason
        })
        print(f"   当前资金: ${self.capital:.2f}")

        # 重置持仓
        self.position = {
            'size': 0.0,
            'side': None,
            'entry_price': 0.0,
            'entry_time': None,
            'margin_used': 0.0,
            'unrealized_pnl': 0.0,
            'last_funding_time': None
        }

        self._save_state()
        return True

    def check_stop_loss_take_profit(self, current_price: float) -> bool:
        """检查止损止盈"""
        if self.position['size'] == 0:
            return False

        entry_price = self.position['entry_price']
        side = self.position['side']

        # 计算止损止盈价格
        stop_loss_pct = 0.025  # 2.5%止损
        take_profit_pct = 0.05  # 5%止盈

        if side == 'LONG':
            stop_loss_price = entry_price * (1 - stop_loss_pct)
            take_profit_price = entry_price * (1 + take_profit_pct)

            if current_price <= stop_loss_price:
                self.close_position(current_price, f"止损触发 (${stop_loss_price:,.2f})")
                return True
            elif current_price >= take_profit_price:
                self.close_position(current_price, f"止盈触发 (${take_profit_price:,.2f})")
                return True

        else:  # SHORT
            stop_loss_price = entry_price * (1 + stop_loss_pct)
            take_profit_price = entry_price * (1 - take_profit_pct)

            if current_price >= stop_loss_price:
                self.close_position(current_price, f"止损触发 (${stop_loss_price:,.2f})")
                return True
            elif current_price <= take_profit_price:
                self.close_position(current_price, f"止盈触发 (${take_profit_price:,.2f})")
                return True

        return False

    def update_performance_stats(self):
        """更新绩效统计"""
        if self.performance_stats['total_trades'] > 0:
            self.performance_stats['win_rate'] = self.performance_stats['winning_trades'] / self.performance_stats['total_trades']

        # 计算平均盈亏
        winning_trades = [t for t in self.trades if t.get('action') == 'CLOSE' and t.get('final_pnl', 0) > 0]
        losing_trades = [t for t in self.trades if t.get('action') == 'CLOSE' and t.get('final_pnl', 0) < 0]

        if winning_trades:
            self.performance_stats['avg_win'] = sum(t['final_pnl'] for t in winning_trades) / len(winning_trades)

        if losing_trades:
            self.performance_stats['avg_loss'] = sum(t['final_pnl'] for t in losing_trades) / len(losing_trades)

        # 计算盈亏比
        if self.performance_stats['avg_loss'] != 0:
            self.performance_stats['profit_factor'] = abs(self.performance_stats['avg_win'] / self.performance_stats['avg_loss'])

        # 更新最大权益和回撤
        current_equity = self.capital + self.position['unrealized_pnl']
        if current_equity > self.performance_stats['max_equity']:
            self.performance_stats['max_equity'] = current_equity

        drawdown = (self.performance_stats['max_equity'] - current_equity) / self.performance_stats['max_equity']
        if drawdown > self.performance_stats['max_drawdown']:
            self.performance_stats['max_drawdown'] = drawdown

    def print_status(self, ai_probability: float, indicators: Dict, signal: Dict):
        """打印当前状态"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_price = indicators['price']

        print(f"\n⏰ {current_time}")
        print("=" * 80)

        # 账户状态
        unrealized_pnl = self.calculate_unrealized_pnl(current_price)
        total_equity = self.capital + unrealized_pnl
        total_return = (total_equity - self.initial_capital) / self.initial_capital * 100

        print(f"💰 账户状态:")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   可用资金: ${self.capital:.2f}")
        print(f"   未实现盈亏: ${unrealized_pnl:+.2f}")
        print(f"   总权益: ${total_equity:.2f}")
        print(f"   总收益率: {total_return:+.2f}%")

        # 持仓状态
        print(f"\n📊 持仓状态:")
        if self.position['size'] != 0:
            hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            print(f"   方向: {self.position['side']}")
            print(f"   数量: {abs(self.position['size']):.6f} BTC")
            print(f"   入场价: ${self.position['entry_price']:,.2f}")
            print(f"   当前价: ${current_price:,.2f}")
            print(f"   保证金: ${self.position['margin_used']:.2f}")
            print(f"   持仓时间: {hold_hours:.1f}小时")
            print(f"   未实现盈亏: ${unrealized_pnl:+.2f}")
        else:
            print(f"   当前: 空仓")
            print(f"   BTC价格: ${current_price:,.2f}")

        # AI分析
        print(f"\n🤖 AI分析:")
        print(f"   上涨概率: {ai_probability:.1%}")
        print(f"   下跌概率: {1-ai_probability:.1%}")

        # 技术指标
        print(f"\n📈 技术指标:")
        print(f"   RSI: {indicators['rsi']:.1f}")
        print(f"   MACD趋势: {indicators['macd_trend']}")
        print(f"   布林带位置: {indicators['bb_position']:.1%}")
        print(f"   成交量比率: {indicators['volume_ratio']:.1f}")

        # 交易信号
        print(f"\n🎯 交易信号:")
        signal_emoji = {"LONG": "🟢", "SHORT": "🔴", "WAIT": "⏸️"}
        print(f"   {signal_emoji.get(signal['direction'], '❓')} 方向: {signal['direction']}")
        print(f"   强度: {signal['strength']:.1%}")
        print(f"   置信度: {signal['confidence']:.1%}")
        print(f"   理由: {signal['reason']}")

        # 绩效统计
        self.update_performance_stats()
        stats = self.performance_stats
        print(f"\n📊 绩效统计:")
        print(f"   总交易数: {stats['total_trades']}")
        print(f"   胜率: {stats['win_rate']:.1%}")
        print(f"   盈利交易: {stats['winning_trades']}")
        print(f"   亏损交易: {stats['losing_trades']}")
        print(f"   总盈亏: ${stats['total_pnl']:+.2f}")
        print(f"   最大回撤: {stats['max_drawdown']:.1%}")

        if stats['avg_win'] > 0 and stats['avg_loss'] < 0:
            print(f"   平均盈利: ${stats['avg_win']:+.2f}")
            print(f"   平均亏损: ${stats['avg_loss']:+.2f}")
            print(f"   盈亏比: {stats['profit_factor']:.2f}")

        print("=" * 80)

    def run_single_cycle(self) -> bool:
        """运行单次交易循环"""
        try:
            # 1. 获取AI预测
            ai_probability, current_price = self.get_ai_prediction()

            # 2. 计算技术指标
            indicators = self.calculate_technical_indicators()
            indicators['price'] = current_price  # 确保价格一致

            # 3. 生成交易信号
            signal = self.generate_trading_signal(ai_probability, indicators)

            # 4. 检查止损止盈
            if self.position['size'] != 0:
                if self.check_stop_loss_take_profit(current_price):
                    # 如果触发止损止盈，重新生成信号
                    signal = self.generate_trading_signal(ai_probability, indicators)

            # 5. 执行交易决策
            if signal['direction'] in ['LONG', 'SHORT'] and self.position['size'] == 0:
                # 开新仓
                size_info = self.calculate_position_size(current_price, signal['confidence'])
                if size_info:
                    self.open_position(
                        signal['direction'],
                        current_price,
                        size_info,
                        signal['reason'],
                        signal['confidence']
                    )

            # 6. 打印状态 - 使用增强版显示
            print_enhanced_status(self, ai_probability, indicators, signal)

            # 7. 记录权益历史
            unrealized_pnl = self.calculate_unrealized_pnl(current_price)
            total_equity = self.capital + unrealized_pnl

            self.equity_history.append({
                'timestamp': datetime.now().isoformat(),
                'capital': self.capital,
                'unrealized_pnl': unrealized_pnl,
                'total_equity': total_equity,
                'btc_price': current_price,
                'ai_probability': ai_probability
            })

            # 8. 保存状态
            self._save_state()

            return True

        except Exception as e:
            print(f"❌ 交易循环执行失败: {str(e)}")
            return False

def run_real_time_simulation(check_interval: int = 300):
    """
    运行实时模拟交易系统
    """
    print("🚀 启动实时模拟交易系统")
    print("=" * 80)
    print("系统特点:")
    print("• 基于币安永续合约规则")
    print("• 初始资金 $50, 2x杠杆")
    print("• AI模型 + 技术指标确认")
    print("• 自动止损止盈 (2.5% / 5%)")
    print("• 实时状态保存和恢复")
    print("• 完整的绩效统计")
    print("")
    print("⚠️ 这是模拟交易，不涉及真实资金")
    print("🎯 让我们看看AI增强系统的真实表现！")
    print("")

    # 初始化交易器
    trader = RealTimeSimulationTrader(initial_capital=50.0, leverage=2)

    print(f"⏰ 交易循环间隔: {check_interval/60:.1f}分钟")
    print(f"🔄 按 Ctrl+C 停止系统")
    print("")

    cycle_count = 0

    try:
        while True:
            cycle_count += 1
            print(f"\n🔄 第 {cycle_count} 次交易循环")

            # 执行交易循环
            success = trader.run_single_cycle()

            if not success:
                print(f"❌ 交易循环失败，等待下次重试...")

            # 检查是否爆仓
            if trader.capital < 5:  # 资金低于5美元
                print(f"\n💥 资金不足，停止交易")
                print(f"最终资金: ${trader.capital:.2f}")
                break

            # 等待下次循环
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟后继续...")
            time.sleep(check_interval)

    except KeyboardInterrupt:
        print(f"\n🛑 用户停止交易系统")

    except Exception as e:
        print(f"\n❌ 系统异常: {str(e)}")

    finally:
        # 最终统计
        print(f"\n📊 最终交易统计:")
        print("=" * 60)

        final_equity = trader.capital + trader.calculate_unrealized_pnl(
            trader.fetcher.get_current_price('BTCUSDT', is_futures=True)
        )

        total_return = (final_equity - trader.initial_capital) / trader.initial_capital * 100

        print(f"初始资金: ${trader.initial_capital}")
        print(f"最终权益: ${final_equity:.2f}")
        print(f"总收益率: {total_return:+.2f}%")
        print(f"总交易数: {trader.performance_stats['total_trades']}")
        print(f"胜率: {trader.performance_stats['win_rate']:.1%}")
        print(f"最大回撤: {trader.performance_stats['max_drawdown']:.1%}")

        if trader.performance_stats['total_trades'] > 0:
            if total_return > 0:
                print(f"🎉 恭喜！AI增强系统盈利了！")
            else:
                print(f"📈 继续优化，AI系统在学习中...")

        print(f"\n💾 交易记录已保存到: {trader.state_file}")
        print(f"🔄 重新运行可继续之前的交易状态")

if __name__ == "__main__":
    import sys

    # 解析命令行参数
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300  # 默认5分钟

    print("🎯 实时模拟交易系统")
    print("=" * 60)
    print("这个系统将展示AI增强后的真实交易能力:")
    print("")
    print("📊 AI模型集成:")
    print("  • 实时概率预测")
    print("  • 技术指标确认")
    print("  • 多层信号过滤")
    print("")
    print("💰 资金管理:")
    print("  • 初始资金: $50")
    print("  • 杠杆: 2x")
    print("  • 最大风险: 2%/笔")
    print("  • 止损: 2.5%")
    print("  • 止盈: 5%")
    print("")
    print("🔄 交易规则:")
    print("  • 基于币安永续合约")
    print("  • 自动止损止盈")
    print("  • 实时状态保存")
    print("  • 完整绩效跟踪")
    print("")

    # 启动系统
    run_real_time_simulation(interval)
