#!/usr/bin/env python
# coding: utf-8
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
import logging
from pathlib import Path
from datetime import datetime, timedelta
import requests # 保持，可能用于其他目的或备用
import json # 保持
from textblob import TextBlob
import tweepy
from newsapi import NewsApiClient
import time
from concurrent.futures import ThreadPoolExecutor, as_completed # 确保导入 as_completed
import matplotlib.pyplot as plt
import seaborn as sns
import praw # 用于 Reddit
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer # 用于 VADER

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SentimentAnalyzer:
    """
    加密货币市场情绪分析器
    功能：
    1. Twitter情绪分析
    2. 新闻情绪分析
    3. Reddit情绪分析 (新增)
    4. 综合情绪指标计算 (使用VADER或TextBlob)
    """
    
    def __init__(self,
                 twitter_api_key: Optional[str] = None,
                 twitter_api_secret: Optional[str] = None,
                 twitter_access_token: Optional[str] = None,
                 twitter_access_secret: Optional[str] = None,
                 newsapi_key: Optional[str] = None,
                 reddit_client_id: Optional[str] = None, 
                 reddit_client_secret: Optional[str] = None, 
                 reddit_user_agent: Optional[str] = None, 
                 output_dir: Union[str, Path] = 'sentiment_results',
                 default_sentiment_analyzer: str = 'vader' 
                 ):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.twitter_api = None
        self.newsapi = None
        self.reddit_api = None 
        self.vader_analyzer = SentimentIntensityAnalyzer()
        self.default_analyzer_type = default_sentiment_analyzer.lower()
        if self.default_analyzer_type not in ['vader', 'textblob']:
            logger.warning(f"不支持的默认分析器 {self.default_analyzer_type}, 将回退到 'vader'")
            self.default_analyzer_type = 'vader'

        # 初始化 Twitter API
        if all([twitter_api_key, twitter_api_secret, twitter_access_token, twitter_access_secret]):
            try:
                auth = tweepy.OAuthHandler(twitter_api_key, twitter_api_secret)
                auth.set_access_token(twitter_access_token, twitter_access_secret)
                self.twitter_api = tweepy.API(auth, wait_on_rate_limit=True)
                logger.info("Twitter API 初始化成功")
            except Exception as e:
                logger.error(f"Twitter API 初始化失败: {e}")
        else:
            logger.info("Twitter API密钥未完全提供，将无法使用Twitter情绪分析。")

        # 初始化 NewsAPI
        if newsapi_key:
            try:
                self.newsapi = NewsApiClient(api_key=newsapi_key)
                logger.info("NewsAPI 初始化成功")
            except Exception as e:
                logger.error(f"NewsAPI 初始化失败: {e}")
        else:
            logger.info("NewsAPI密钥未提供，将无法使用新闻情绪分析。")

        # 初始化 Reddit API
        if reddit_client_id and reddit_client_secret and reddit_user_agent:
            try:
                self.reddit_api = praw.Reddit(
                    client_id=reddit_client_id,
                    client_secret=reddit_client_secret,
                    user_agent=reddit_user_agent,
                    read_only=True # 明确设置为只读模式
                )
                # 测试连接 (可选)
                # self.reddit_api.user.me() # 如果需要验证凭据，但这需要非只读权限
                logger.info("Reddit API 初始化成功 (只读模式)")
            except Exception as e:
                logger.error(f"Reddit API 初始化失败: {e}", exc_info=True)
        else:
            logger.info("Reddit API凭据未完全提供，将无法使用Reddit情绪分析。")
            
        self.sentiment_history: List[Dict[str, Any]] = [] # 存储历史记录

    def _get_sentiment_from_text(self, text: Optional[str], analyzer_type: Optional[str] = None) -> float:
        if not text or not text.strip():
            return 0.0 # 空文本返回中性
            
        analyzer_to_use = (analyzer_type or self.default_analyzer_type).lower()
        
        try:
            if analyzer_to_use == 'vader':
                return self.vader_analyzer.polarity_scores(text)['compound']
            elif analyzer_to_use == 'textblob':
                return TextBlob(text).sentiment.polarity
            else:
                logger.warning(f"未知的情绪分析器类型: {analyzer_to_use}. 使用VADER作为备用。")
                return self.vader_analyzer.polarity_scores(text)['compound']
        except Exception as e:
            logger.error(f"文本情绪分析失败 ({analyzer_to_use}) for text: \"{text[:50]}...\": {e}")
            return 0.0 # 错误时返回中性

    def _process_sentiments_list(self, sentiments: List[float], analyzer_type_used: str) -> Dict[str, float]:
        if not sentiments:
            return {
                'positive_ratio': 0.0, 'negative_ratio': 0.0, 'neutral_ratio': 0.0,
                'average_score': 0.0, 'std_score': 0.0, 'count': 0
            }
        
        threshold = 0.05 if analyzer_type_used == 'vader' else 0.0
        
        positive_count = sum(1 for s in sentiments if s > threshold)
        negative_count = sum(1 for s in sentiments if s < -threshold)
        neutral_count = len(sentiments) - positive_count - negative_count
        
        return {
            'positive_ratio': positive_count / len(sentiments),
            'negative_ratio': negative_count / len(sentiments),
            'neutral_ratio': neutral_count / len(sentiments),
            'average_score': np.mean(sentiments),
            'std_score': np.std(sentiments),
            'count': len(sentiments)
        }

    def analyze_twitter_sentiment(self, 
                                query: str,
                                count: int = 100,
                                lang: str = 'en',
                                analyzer: Optional[str] = None) -> Dict[str, float]:
        effective_analyzer = (analyzer or self.default_analyzer_type).lower()
        if self.twitter_api is None:
            logger.error("Twitter API未初始化")
            return self._process_sentiments_list([], effective_analyzer)
            
        try:
            logger.info(f"分析Twitter情绪 - 关键词: '{query}', 分析器: {effective_analyzer}, 数量: {count}")
            # search_tweets 已被移除，使用 search_recent_tweets (需要v2 API权限) 或其他方法
            # 作为示例，我们假设 search_tweets 仍然可用或有替代品
            # 注意: tweepy.API.search_tweets 对应 Twitter API v1.1, 它正在被逐步淘汰
            # 如果遇到问题, 可能需要迁移到 Twitter API v2 和对应的 tweepy 方法如 client.search_recent_tweets
            tweets_obj = self.twitter_api.search_tweets(
                q=query, count=count, lang=lang, tweet_mode='extended', result_type='recent'
            )
            if not tweets_obj:
                 logger.info(f"Twitter API没有为查询 '{query}' 返回推文。")
                 return self._process_sentiments_list([], effective_analyzer)

            sentiments = [self._get_sentiment_from_text(tweet.full_text, effective_analyzer) for tweet in tweets_obj]
            result = self._process_sentiments_list(sentiments, effective_analyzer)
            logger.info(f"Twitter情绪分析完成 - 平均分: {result['average_score']:.4f}, 推文数: {result['count']}")
            return result
        except tweepy.TweepyException as e:
            logger.error(f"分析Twitter情绪时发生Tweepy API错误: {e}")
            return self._process_sentiments_list([], effective_analyzer)
        except Exception as e:
            logger.error(f"分析Twitter情绪时发生未知错误: {str(e)}", exc_info=True)
            return self._process_sentiments_list([], effective_analyzer)
            
    def analyze_news_sentiment(self,
                             query: str,
                             days: int = 7,
                             page_size: int = 100, # NewsAPI 每页最多100条
                             analyzer: Optional[str] = None) -> Dict[str, float]:
        effective_analyzer = (analyzer or self.default_analyzer_type).lower()
        if self.newsapi is None:
            logger.error("NewsAPI未初始化")
            return self._process_sentiments_list([], effective_analyzer)
            
        try:
            logger.info(f"分析新闻情绪 - 关键词: '{query}', 分析器: {effective_analyzer}, 天数: {days}")
            from_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            articles_response = self.newsapi.get_everything(
                q=query, from_param=from_date, language='en', 
                sort_by='relevancy', page_size=page_size
            )
            sentiments = []
            processed_articles_count = 0
            if articles_response and articles_response['articles']:
                processed_articles_count = len(articles_response['articles'])
                for article in articles_response['articles']:
                    title = article.get('title', '')
                    description = article.get('description')
                    text_to_analyze = title # 优先分析标题
                    if description: # 如果有描述，附加描述
                        text_to_analyze += ". " + description
                    
                    if text_to_analyze.strip():
                        sentiment_score = self._get_sentiment_from_text(text_to_analyze, effective_analyzer)
                        sentiments.append(sentiment_score)
            
            result = self._process_sentiments_list(sentiments, effective_analyzer)
            logger.info(f"新闻情绪分析完成 - 平均分: {result['average_score']:.4f}, 文章数: {result['count']} (API返回: {processed_articles_count})")
            return result
        except Exception as e:
            logger.error(f"分析新闻情绪时发生错误: {str(e)}", exc_info=True)
            return self._process_sentiments_list([], effective_analyzer)

    def analyze_reddit_sentiment(self,
                               subreddit_name: str,
                               query: Optional[str] = None, 
                               limit: int = 25, 
                               time_filter: str = 'day', 
                               analyzer: Optional[str] = None,
                               include_comments: bool = False,
                               comment_limit_per_post: int = 5
                               ) -> Dict[str, float]:
        effective_analyzer = (analyzer or self.default_analyzer_type).lower()
        if self.reddit_api is None:
            logger.error("Reddit API未初始化")
            return self._process_sentiments_list([], effective_analyzer)
        
        try:
            logger.info(f"分析Reddit情绪 - r/{subreddit_name}, 查询: '{query or "N/A"}', 分析器: {effective_analyzer}, 限制: {limit}")
            subreddit = self.reddit_api.subreddit(subreddit_name)
            sentiments = []
            fetched_items_count = 0

            if query:
                posts = subreddit.search(query, sort='relevance', time_filter=time_filter, limit=limit)
            else:
                posts = subreddit.top(time_filter=time_filter, limit=limit) # Can also use .hot() or .new()
            
            for post in posts:
                fetched_items_count += 1
                title_score = self._get_sentiment_from_text(post.title, effective_analyzer)
                sentiments.append(title_score) # 至少包含标题情绪
                
                if post.selftext:
                    selftext_score = self._get_sentiment_from_text(post.selftext[:2000], effective_analyzer) # 限制长度
                    # 可以选择如何合并标题和正文的情绪，例如平均或只用一个
                    sentiments.append(selftext_score) # 这里简单地都加入列表，后续_process_sentiments_list会平均
                
                if include_comments:
                    post.comments.replace_more(limit=0) # 展开顶层评论
                    comment_count = 0
                    for comment in post.comments.list():
                        if comment_count >= comment_limit_per_post: break
                        sentiments.append(self._get_sentiment_from_text(comment.body[:2000], effective_analyzer))
                        comment_count += 1
            
            result = self._process_sentiments_list(sentiments, effective_analyzer)
            logger.info(f"Reddit情绪 (r/{subreddit_name}) 分析完成 - 平均分: {result['average_score']:.4f}, 分析文本片段数: {result['count']} (来自约 {fetched_items_count} 个帖子)")
            return result
        except Exception as e:
            logger.error(f"分析Reddit情绪 (r/{subreddit_name}) 时发生错误: {str(e)}", exc_info=True)
            return self._process_sentiments_list([], effective_analyzer)

    def get_combined_sentiment(self, 
                                 crypto_query: str = 'BTC OR Bitcoin OR crypto OR cryptocurrency',
                                 twitter_params: Optional[Dict[str, Any]] = None,
                                 news_params: Optional[Dict[str, Any]] = None,
                                 reddit_params: Optional[Dict[str, Any]] = None,
                                 weights: Optional[Dict[str, float]] = None,
                                 analyzer: Optional[str] = None,
                                 timeout_seconds: int = 60
                                 ) -> Dict[str, Any]:
        if weights is None:
            weights = {'twitter': 0.33, 'news': 0.34, 'reddit': 0.33}
        
        # 默认参数
        twitter_p = {'query': crypto_query, 'count': 50, **(twitter_params or {})}
        news_p = {'query': crypto_query, 'days': 3, 'page_size': 50, **(news_params or {})}
        # 对于Reddit, query通常在subreddit内，或者不使用全局query而只用subreddit主题
        reddit_default_query = crypto_query.split(' OR ')[0] # 取第一个关键词作为subreddit内搜索词
        reddit_p = {'subreddit_name': 'CryptoCurrency', 'query': reddit_default_query, 'limit': 20, 'time_filter': 'day', **(reddit_params or {})}

        all_sentiments_data: Dict[str, Optional[Dict[str, float]]] = {}
        futures = {}
        effective_analyzer = analyzer or self.default_analyzer_type

        with ThreadPoolExecutor(max_workers=3) as executor:
            if self.twitter_api and weights.get('twitter', 0) > 0:
                futures[executor.submit(self.analyze_twitter_sentiment, **twitter_p, analyzer=effective_analyzer)] = 'twitter'
            if self.newsapi and weights.get('news', 0) > 0:
                futures[executor.submit(self.analyze_news_sentiment, **news_p, analyzer=effective_analyzer)] = 'news'
            if self.reddit_api and weights.get('reddit', 0) > 0:
                futures[executor.submit(self.analyze_reddit_sentiment, **reddit_p, analyzer=effective_analyzer)] = 'reddit'

            for future in as_completed(futures):
                source_name = futures[future]
                try:
                    all_sentiments_data[source_name] = future.result(timeout=timeout_seconds)
                except Exception as e:
                    logger.error(f"获取源 {source_name} 情绪时出错: {e}", exc_info=True)
                    all_sentiments_data[source_name] = self._process_sentiments_list([], effective_analyzer) # 错误时填默认值
        
        # 计算综合情绪
        weighted_scores = []
        total_weight_applied = 0
        source_details_for_result = {}

        for source, data in all_sentiments_data.items():
            source_details_for_result[source] = data # 存储每个源的完整分析结果
            if data and data.get('count', 0) > 0:
                score = data['average_score']
                weight = weights.get(source, 0)
                if weight > 0:
                    weighted_scores.append(score * weight)
                    total_weight_applied += weight
            elif weights.get(source, 0) > 0: # 如果某个源被赋予权重但没有数据
                 logger.warning(f"源 {source} 被赋予权重但未成功获取或分析数据。")
        
        combined_avg_score = sum(weighted_scores) / total_weight_applied if total_weight_applied > 0 else 0.0
        sentiment_label = self._get_sentiment_label(combined_avg_score, analyzer_type=effective_analyzer)
        
        timestamp = datetime.now()
        result_summary = {
            'timestamp': timestamp.isoformat(),
            'combined_average_score': combined_avg_score,
            'combined_sentiment_label': sentiment_label,
            'analyzer_used': effective_analyzer,
            'weights_used': {s: w for s, w in weights.items() if w > 0 and s in all_sentiments_data and all_sentiments_data[s]['count'] > 0},
            'source_details': source_details_for_result 
        }
        self.sentiment_history.append(result_summary)
        logger.info(f"综合情绪: {result_summary['combined_average_score']:.4f} ({result_summary['combined_sentiment_label']}), 使用分析器: {effective_analyzer}")
        return result_summary

    def _get_sentiment_label(self, score: float, analyzer_type: Optional[str] = None) -> str:
        current_analyzer = (analyzer_type or self.default_analyzer_type).lower()
        if current_analyzer == 'vader':
            if score >= 0.05: return "积极 (VADER)"
            elif score <= -0.05: return "消极 (VADER)"
            else: return "中性 (VADER)"
        else: # TextBlob
            if score > 0.1: return "积极 (TextBlob)"
            elif score < -0.1: return "消极 (TextBlob)"
            else: return "中性 (TextBlob)"

    def plot_sentiment_history(self, save: bool = True, last_n_records: Optional[int] = None) -> None:
        """绘制情绪历史图表"""
        # 解决中文字体显示问题
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei'] # 指定默认字体为黑体
            plt.rcParams['axes.unicode_minus'] = False # 解决保存图像是负号'-'显示为方块的问题
        except Exception as e:
            logger.warning(f"设置中文字体失败，图表中的中文可能无法正确显示: {e}")

        if not self.sentiment_history:
            logger.warning("无情绪历史数据可供绘制。")
            return
        
        history_df = pd.DataFrame(self.sentiment_history)
        if history_df.empty:
            logger.warning("情绪历史数据为空DataFrame。")
            return
            
        history_df['timestamp'] = pd.to_datetime(history_df['timestamp'])
        history_df.set_index('timestamp', inplace=True)
        
        if last_n_records and last_n_records > 0:
            history_df = history_df.tail(last_n_records)
            if history_df.empty:
                logger.warning(f"最近 {last_n_records} 条记录中无情绪数据。")
                return

        plt.figure(figsize=(15, 7))
        if 'combined_average_score' in history_df.columns:
            plt.plot(history_df.index, history_df['combined_average_score'], label='综合平均情绪', marker='.', linewidth=2)
        
        # 可选：绘制单个来源的情绪（如果需要）
        # Example: Plotting Twitter sentiment if available and details are stored
        # if 'source_details' in history_df.columns:
        #     twitter_scores = history_df['source_details'].apply(lambda x: x.get('twitter', {}).get('average_score') if isinstance(x.get('twitter'), dict) else None)
        #     if not twitter_scores.isnull().all():
        # plt.plot(history_df.index, twitter_scores, label='Twitter 平均情绪', linestyle='--', alpha=0.7)

        plt.title(f'市场情绪趋势 (分析器: {history_df["analyzer_used"].iloc[-1] if "analyzer_used" in history_df.columns and not history_df.empty else self.default_analyzer_type})')
        plt.xlabel('时间')
        plt.ylabel('情绪得分 (-1 到 1)')
        plt.axhline(0, color='grey', linestyle='--', alpha=0.5)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save:
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = self.output_dir / f'sentiment_trend_{timestamp_str}.png'
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"情绪趋势图已保存到: {save_path}")
            except Exception as e:
                logger.error(f"保存情绪趋势图失败: {e}")
        if not save:
                plt.show()
        plt.close()

    def save_sentiment_history(self, filename: Optional[str] = None) -> Path:
        """将情绪历史保存到JSON文件，将datetime转换为ISO格式字符串"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sentiment_history_{timestamp}.json"
        save_path = self.output_dir / filename

        try:
            history_to_save = []
            for record in self.sentiment_history:
                serializable_record = record.copy()
                if isinstance(serializable_record.get('timestamp'), datetime):
                    serializable_record['timestamp'] = serializable_record['timestamp'].isoformat()
                history_to_save.append(serializable_record)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(history_to_save, f, ensure_ascii=False, indent=4)
            logger.info(f"情绪历史已保存到: {save_path}")
            return save_path
        except Exception as e:
            logger.error(f"保存情绪历史失败: {e}", exc_info=True)
            raise

    def load_sentiment_history(self, filepath: Union[str, Path]) -> bool:
        """从JSON文件加载情绪历史，将ISO格式字符串转换回datetime"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                loaded_history = json.load(f)
            
            self.sentiment_history = []
            for record in loaded_history:
                deserialized_record = record.copy()
                if isinstance(deserialized_record.get('timestamp'), str):
                    try:
                        deserialized_record['timestamp'] = datetime.fromisoformat(deserialized_record['timestamp'])
                    except ValueError:
                        logger.warning(f"无法将时间戳字符串 '{deserialized_record['timestamp']}' 解析为datetime对象，保留为字符串。")
                self.sentiment_history.append(deserialized_record)

            logger.info(f"情绪历史已从 {filepath} 加载，共 {len(self.sentiment_history)} 条记录")
            return True
        except FileNotFoundError:
            logger.error(f"情绪历史文件未找到: {filepath}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"解析情绪历史文件JSON失败: {filepath}, 错误: {e}")
            return False
        except Exception as e:
            logger.error(f"加载情绪历史时发生未知错误: {e}", exc_info=True)
            return False

if __name__ == '__main__':
    # 配置API密钥 (从环境变量或config.py加载)
    # 确保在运行前设置这些环境变量
    TWITTER_API_KEY = os.getenv("TWITTER_API_KEY")
    TWITTER_API_SECRET = os.getenv("TWITTER_API_SECRET")
    TWITTER_ACCESS_TOKEN = os.getenv("TWITTER_ACCESS_TOKEN")
    TWITTER_ACCESS_SECRET = os.getenv("TWITTER_ACCESS_SECRET")
    NEWSAPI_KEY = os.getenv("NEWSAPI_KEY")
    REDDIT_CLIENT_ID = os.getenv("REDDIT_CLIENT_ID")
    REDDIT_CLIENT_SECRET = os.getenv("REDDIT_CLIENT_SECRET")
    REDDIT_USER_AGENT = os.getenv("REDDIT_USER_AGENT")

    logger.info("开始情绪分析器测试...")

    analyzer_vader = SentimentAnalyzer(
        twitter_api_key=TWITTER_API_KEY,
        twitter_api_secret=TWITTER_API_SECRET,
        twitter_access_token=TWITTER_ACCESS_TOKEN,
        twitter_access_secret=TWITTER_ACCESS_SECRET,
        newsapi_key=NEWSAPI_KEY,
        reddit_client_id=REDDIT_CLIENT_ID,
        reddit_client_secret=REDDIT_CLIENT_SECRET,
        reddit_user_agent=REDDIT_USER_AGENT,
        default_sentiment_analyzer='vader'
    )

    analyzer_textblob = SentimentAnalyzer(
        twitter_api_key=TWITTER_API_KEY,
        twitter_api_secret=TWITTER_API_SECRET,
        twitter_access_token=TWITTER_ACCESS_TOKEN,
        twitter_access_secret=TWITTER_ACCESS_SECRET,
        newsapi_key=NEWSAPI_KEY,
        reddit_client_id=REDDIT_CLIENT_ID,
        reddit_client_secret=REDDIT_CLIENT_SECRET,
        reddit_user_agent=REDDIT_USER_AGENT,
        default_sentiment_analyzer='textblob'
    )

    crypto_query = "Solana OR SOL OR Ethereum OR ETH"
    common_twitter_params = {'query': crypto_query, 'count': 20} # 减少数量以加快测试
    common_news_params = {'query': crypto_query, 'days': 2, 'page_size': 20}
    common_reddit_params = {'subreddit_name': 'CryptoMarkets', 'query': 'Solana', 'limit': 10, 'time_filter': 'week', 'include_comments': False}

    if analyzer_vader.twitter_api:
        logger.info("--- 测试VADER Twitter情绪 ---")
        vader_twitter_sentiment = analyzer_vader.analyze_twitter_sentiment(**common_twitter_params)
        print(f"VADER Twitter Sentiment for '{crypto_query}': {vader_twitter_sentiment}")
    
    if analyzer_textblob.twitter_api:
        logger.info("--- 测试TextBlob Twitter情绪 ---")
        textblob_twitter_sentiment = analyzer_textblob.analyze_twitter_sentiment(**common_twitter_params, analyzer='textblob')
        print(f"TextBlob Twitter Sentiment for '{crypto_query}': {textblob_twitter_sentiment}")

    if analyzer_vader.newsapi:
        logger.info("--- 测试VADER News情绪 ---")
        vader_news_sentiment = analyzer_vader.analyze_news_sentiment(**common_news_params)
        print(f"VADER News Sentiment for '{crypto_query}': {vader_news_sentiment}")

    if analyzer_textblob.newsapi:
        logger.info("--- 测试TextBlob News情绪 ---")
        textblob_news_sentiment = analyzer_textblob.analyze_news_sentiment(**common_news_params, analyzer='textblob')
        print(f"TextBlob News Sentiment for '{crypto_query}': {textblob_news_sentiment}")

    if analyzer_vader.reddit_api:
        logger.info("--- 测试VADER Reddit情绪 ---")
        vader_reddit_sentiment = analyzer_vader.analyze_reddit_sentiment(**common_reddit_params)
        print(f"VADER Reddit Sentiment for r/{common_reddit_params['subreddit_name']} (Query: {common_reddit_params['query']}): {vader_reddit_sentiment}")

    if analyzer_textblob.reddit_api:
        logger.info("--- 测试TextBlob Reddit情绪 ---")
        textblob_reddit_sentiment = analyzer_textblob.analyze_reddit_sentiment(**common_reddit_params, analyzer='textblob')
        print(f"TextBlob Reddit Sentiment for r/{common_reddit_params['subreddit_name']} (Query: {common_reddit_params['query']}): {textblob_reddit_sentiment}")

    logger.info("--- 测试VADER综合情绪 ---")
    combined_vader = analyzer_vader.get_combined_sentiment(
        crypto_query=crypto_query,
        twitter_params={'count': 15}, 
        news_params={'days':1, 'page_size': 15}, 
        reddit_params={'subreddit_name': 'altcoin', 'query': 'AI crypto', 'limit': 5, 'include_comments': False},
        weights={'twitter': 0.4, 'news': 0.3, 'reddit': 0.3}
    )
    print(f"Combined VADER Sentiment: {combined_vader}")
    analyzer_vader.plot_sentiment_history(last_n_records=10)
    vader_history_path = analyzer_vader.save_sentiment_history()
    analyzer_vader.load_sentiment_history(vader_history_path)

    logger.info("--- 测试TextBlob综合情绪 ---")
    combined_textblob = analyzer_textblob.get_combined_sentiment(
        crypto_query=crypto_query,
        analyzer='textblob',
        twitter_params={'count': 15}, 
        news_params={'days':1, 'page_size': 15}, 
        reddit_params={'subreddit_name': 'ethtrader', 'query': 'staking', 'limit': 5}
    )
    print(f"Combined TextBlob Sentiment: {combined_textblob}")    
    analyzer_textblob.plot_sentiment_history(last_n_records=10)
    
    logger.info("情绪分析器测试完成。") 