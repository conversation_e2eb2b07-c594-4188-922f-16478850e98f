#!/usr/bin/env python3
"""
实盘交易准备脚本 - 风险管理和监控系统
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

class LiveTradingManager:
    """
    实盘交易管理器
    """
    
    def __init__(self, initial_capital=1000, max_risk_per_trade=0.02):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_risk_per_trade = max_risk_per_trade
        self.positions = {}
        self.trade_history = []
        self.daily_stats = []
        
        # 风险控制参数
        self.max_daily_loss = 0.05  # 最大日亏损5%
        self.max_drawdown_limit = 0.15  # 最大回撤15%
        self.min_confidence_threshold = 0.65  # 最低置信度
        
        # 监控指标
        self.peak_capital = initial_capital
        self.current_drawdown = 0
        self.daily_pnl = 0
        self.trade_count_today = 0
        
        print(f"🚀 实盘交易管理器初始化完成")
        print(f"   初始资金: ${initial_capital:,.2f}")
        print(f"   单笔最大风险: {max_risk_per_trade:.1%}")
        print(f"   最大日亏损: {self.max_daily_loss:.1%}")
        print(f"   最大回撤限制: {self.max_drawdown_limit:.1%}")
    
    def calculate_position_size(self, confidence, current_price, stop_loss_price):
        """
        根据风险管理规则计算仓位大小
        """
        # 基础仓位大小（基于置信度）
        if confidence >= 0.8:
            base_size = 0.8
        elif confidence >= 0.7:
            base_size = 0.5
        elif confidence >= 0.65:
            base_size = 0.3
        else:
            return 0  # 置信度太低，不交易
        
        # 基于止损距离调整仓位
        risk_per_share = abs(current_price - stop_loss_price) / current_price
        max_shares = (self.current_capital * self.max_risk_per_trade) / (current_price * risk_per_share)
        target_shares = (self.current_capital * base_size) / current_price
        
        # 取较小值确保风险控制
        final_shares = min(max_shares, target_shares)
        
        return final_shares
    
    def check_risk_limits(self):
        """
        检查风险限制
        """
        # 检查日亏损限制
        if self.daily_pnl < -self.max_daily_loss * self.initial_capital:
            return False, "达到日亏损限制"
        
        # 检查最大回撤限制
        if self.current_drawdown > self.max_drawdown_limit:
            return False, "达到最大回撤限制"
        
        # 检查交易频率（防止过度交易）
        if self.trade_count_today > 10:
            return False, "今日交易次数过多"
        
        return True, "风险检查通过"
    
    def execute_trade(self, symbol, action, price, confidence, stop_loss=None):
        """
        执行交易
        """
        # 风险检查
        can_trade, reason = self.check_risk_limits()
        if not can_trade:
            print(f"⚠️  交易被拒绝: {reason}")
            return False
        
        timestamp = datetime.now()
        
        if action == 'BUY':
            # 计算仓位大小
            if stop_loss is None:
                stop_loss = price * 0.97  # 默认3%止损
            
            position_size = self.calculate_position_size(confidence, price, stop_loss)
            
            if position_size <= 0:
                print(f"⚠️  仓位大小为0，跳过交易")
                return False
            
            # 执行买入
            cost = position_size * price * 1.001  # 包含手续费
            
            if cost > self.current_capital:
                print(f"⚠️  资金不足，无法执行交易")
                return False
            
            self.positions[symbol] = {
                'shares': position_size,
                'entry_price': price,
                'stop_loss': stop_loss,
                'entry_time': timestamp,
                'confidence': confidence
            }
            
            self.current_capital -= cost
            self.trade_count_today += 1
            
            trade_record = {
                'timestamp': timestamp,
                'symbol': symbol,
                'action': 'BUY',
                'price': price,
                'shares': position_size,
                'cost': cost,
                'confidence': confidence,
                'stop_loss': stop_loss
            }
            
            self.trade_history.append(trade_record)
            
            print(f"✅ 买入 {symbol}: {position_size:.4f} @ ${price:.2f} (置信度: {confidence:.2%})")
            
        elif action == 'SELL' and symbol in self.positions:
            # 执行卖出
            position = self.positions[symbol]
            shares = position['shares']
            entry_price = position['entry_price']
            
            proceeds = shares * price * 0.999  # 扣除手续费
            pnl = proceeds - (shares * entry_price)
            pnl_ratio = pnl / (shares * entry_price)
            
            self.current_capital += proceeds
            self.daily_pnl += pnl
            self.trade_count_today += 1
            
            trade_record = {
                'timestamp': timestamp,
                'symbol': symbol,
                'action': 'SELL',
                'price': price,
                'shares': shares,
                'proceeds': proceeds,
                'pnl': pnl,
                'pnl_ratio': pnl_ratio,
                'hold_time': timestamp - position['entry_time']
            }
            
            self.trade_history.append(trade_record)
            
            # 移除持仓
            del self.positions[symbol]
            
            print(f"✅ 卖出 {symbol}: {shares:.4f} @ ${price:.2f} (盈亏: ${pnl:.2f}, {pnl_ratio:.2%})")
        
        # 更新统计
        self.update_stats()
        return True
    
    def update_stats(self):
        """
        更新统计数据
        """
        # 更新峰值资金和回撤
        if self.current_capital > self.peak_capital:
            self.peak_capital = self.current_capital
        
        self.current_drawdown = (self.peak_capital - self.current_capital) / self.peak_capital
    
    def get_daily_report(self):
        """
        生成日报
        """
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        
        # 计算今日交易统计
        today = datetime.now().date()
        today_trades = [t for t in self.trade_history 
                       if t['timestamp'].date() == today and 'pnl' in t]
        
        today_pnl = sum(t['pnl'] for t in today_trades)
        today_trades_count = len(today_trades)
        profitable_trades = len([t for t in today_trades if t['pnl'] > 0])
        
        win_rate = profitable_trades / today_trades_count if today_trades_count > 0 else 0
        
        report = {
            'date': today.strftime('%Y-%m-%d'),
            'current_capital': self.current_capital,
            'total_return': total_return,
            'daily_pnl': today_pnl,
            'daily_return': today_pnl / self.initial_capital,
            'current_drawdown': self.current_drawdown,
            'today_trades': today_trades_count,
            'win_rate': win_rate,
            'active_positions': len(self.positions)
        }
        
        return report
    
    def print_status(self):
        """
        打印当前状态
        """
        report = self.get_daily_report()
        
        print(f"\n📊 实盘交易状态报告")
        print("=" * 40)
        print(f"当前资金: ${report['current_capital']:,.2f}")
        print(f"总收益率: {report['total_return']:+.2%}")
        print(f"今日盈亏: ${report['daily_pnl']:+,.2f} ({report['daily_return']:+.2%})")
        print(f"当前回撤: {report['current_drawdown']:.2%}")
        print(f"今日交易: {report['today_trades']} 笔")
        print(f"今日胜率: {report['win_rate']:.2%}")
        print(f"持仓数量: {report['active_positions']}")
        
        if self.positions:
            print(f"\n📈 当前持仓:")
            for symbol, pos in self.positions.items():
                print(f"   {symbol}: {pos['shares']:.4f} @ ${pos['entry_price']:.2f}")

def create_trading_config():
    """
    创建交易配置文件
    """
    config = {
        "trading_parameters": {
            "initial_capital": 1000,
            "max_risk_per_trade": 0.02,
            "max_daily_loss": 0.05,
            "max_drawdown_limit": 0.15,
            "min_confidence_threshold": 0.65,
            "stop_loss_ratio": 0.03
        },
        "symbols": [
            "BTCUSDT",
            "ETHUSDT",
            "BNBUSDT"
        ],
        "model_settings": {
            "model_update_frequency": "daily",
            "prediction_interval": "1h",
            "lookback_period": 24
        },
        "risk_management": {
            "max_positions": 3,
            "max_trades_per_day": 10,
            "emergency_stop_loss": 0.10
        },
        "monitoring": {
            "report_frequency": "daily",
            "alert_thresholds": {
                "daily_loss": 0.03,
                "drawdown": 0.10
            }
        }
    }
    
    config_path = "live_trading_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"📝 交易配置文件已创建: {config_path}")
    return config

def simulate_live_trading():
    """
    模拟实盘交易流程
    """
    print("🎯 模拟实盘交易流程")
    print("=" * 40)
    
    # 创建交易管理器
    manager = LiveTradingManager(initial_capital=1000)
    
    # 模拟一些交易
    print(f"\n📈 模拟交易序列:")
    
    # 模拟买入
    manager.execute_trade('BTCUSDT', 'BUY', 67000, 0.75, 65000)
    
    # 模拟价格变化后卖出
    manager.execute_trade('BTCUSDT', 'SELL', 68500, 0.6)
    
    # 再次买入
    manager.execute_trade('ETHUSDT', 'BUY', 3500, 0.8, 3400)
    
    # 打印状态
    manager.print_status()
    
    return manager

if __name__ == "__main__":
    print("🚀 实盘交易准备")
    print("=" * 50)
    
    # 创建配置文件
    config = create_trading_config()
    
    # 模拟交易流程
    manager = simulate_live_trading()
    
    print(f"\n🎯 实盘交易准备完成!")
    print(f"下一步: 根据参数优化结果调整配置，然后开始小额实盘测试。")
