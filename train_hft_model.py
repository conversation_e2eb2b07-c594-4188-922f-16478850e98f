#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高频交易专用AI模型训练脚本
专门为5分钟K线数据和30分钟内预测优化
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
from sklearn.metrics import classification_report, accuracy_score
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append('.')
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class HFTModelTrainer:
    """高频交易模型训练器"""
    
    def __init__(self):
        self.data_fetcher = BinanceDataFetcher()
        self.feature_engineer = FeatureEngineer()
        self.scaler = StandardScaler()
        
    def create_hft_labels(self, df: pd.DataFrame, target_pct: float = 0.015) -> pd.Series:
        """
        创建高频交易标签
        预测未来5-30分钟内是否达到目标涨跌幅
        """
        labels = []
        
        for i in range(len(df) - 6):  # 预测未来6个5分钟周期(30分钟)
            current_price = df['close'].iloc[i]
            
            # 查看未来6个周期的价格变动
            future_prices = df['close'].iloc[i+1:i+7]
            
            # 计算最大涨幅和最大跌幅
            max_gain = (future_prices.max() - current_price) / current_price
            max_loss = (current_price - future_prices.min()) / current_price
            
            # 标签定义：
            # 0: 大跌 (>1.5%跌幅)
            # 1: 小跌 (0.8%-1.5%跌幅) 
            # 2: 震荡 (<0.8%变动)
            # 3: 小涨 (0.8%-1.5%涨幅)
            # 4: 大涨 (>1.5%涨幅)
            
            if max_gain > 0.015:  # >1.5%涨幅
                if max_gain > 0.025:  # >2.5%大涨
                    label = 4
                else:  # 1.5%-2.5%小涨
                    label = 3
            elif max_loss > 0.015:  # >1.5%跌幅
                if max_loss > 0.025:  # >2.5%大跌
                    label = 0
                else:  # 1.5%-2.5%小跌
                    label = 1
            elif max_gain > 0.008 or max_loss > 0.008:  # 0.8%-1.5%变动
                if max_gain > max_loss:
                    label = 3  # 小涨
                else:
                    label = 1  # 小跌
            else:  # <0.8%变动
                label = 2  # 震荡
                
            labels.append(label)
        
        # 补齐最后6个数据点
        labels.extend([2] * 6)  # 默认为震荡
        
        return pd.Series(labels, index=df.index)
    
    def create_hft_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建高频交易专用特征"""
        features_df = pd.DataFrame(index=df.index)
        
        # 基础价格特征
        features_df['price_change_1'] = df['close'].pct_change(1)
        features_df['price_change_3'] = df['close'].pct_change(3)
        features_df['price_change_6'] = df['close'].pct_change(6)
        features_df['price_change_12'] = df['close'].pct_change(12)
        
        # 波动率特征
        features_df['volatility_6'] = df['close'].rolling(6).std() / df['close'].rolling(6).mean()
        features_df['volatility_12'] = df['close'].rolling(12).std() / df['close'].rolling(12).mean()
        
        # 技术指标（短周期）
        # RSI (短周期)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=6).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=6).mean()
        rs = gain / loss
        features_df['rsi_6'] = 100 - (100 / (1 + rs))
        
        # 移动平均
        features_df['sma_6'] = df['close'].rolling(6).mean()
        features_df['sma_12'] = df['close'].rolling(12).mean()
        features_df['sma_24'] = df['close'].rolling(24).mean()
        
        # 价格相对位置
        features_df['price_vs_sma6'] = df['close'] / features_df['sma_6'] - 1
        features_df['price_vs_sma12'] = df['close'] / features_df['sma_12'] - 1
        
        # 成交量特征
        features_df['volume_ma_6'] = df['volume'].rolling(6).mean()
        features_df['volume_ma_12'] = df['volume'].rolling(12).mean()
        features_df['volume_ratio'] = df['volume'] / features_df['volume_ma_12']
        
        # 价量关系
        features_df['price_volume_corr'] = df['close'].rolling(12).corr(df['volume'])
        
        # 高低点特征
        features_df['high_low_ratio'] = (df['high'] - df['low']) / df['close']
        features_df['close_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # 动量特征
        features_df['momentum_3'] = df['close'] / df['close'].shift(3) - 1
        features_df['momentum_6'] = df['close'] / df['close'].shift(6) - 1
        
        # 布林带
        bb_period = 12
        bb_middle = df['close'].rolling(bb_period).mean()
        bb_std = df['close'].rolling(bb_period).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)
        features_df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
        
        # 时间特征
        features_df['hour'] = df.index.hour
        features_df['minute'] = df.index.minute
        features_df['is_market_open'] = ((df.index.hour >= 9) & (df.index.hour <= 16)).astype(int)
        
        return features_df.fillna(0)
    
    def train_hft_model(self, symbol: str = 'BTCUSDT', days: int = 30):
        """训练高频交易模型"""
        print(f"🚀 开始训练高频交易模型...")
        print(f"📊 交易对: {symbol}")
        print(f"📅 训练数据: 最近{days}天的5分钟K线")
        
        # 1. 获取5分钟数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        print(f"📡 获取{symbol} 5分钟K线数据...")
        df = self.data_fetcher.get_historical_data(
            symbol, '5m', start_date.strftime('%Y-%m-%d'), 
            is_futures=True, force_refresh=True
        )
        
        if df is None or len(df) < 1000:
            raise Exception(f"数据不足，需要至少1000条5分钟K线数据")
        
        print(f"✅ 获取到 {len(df)} 条5分钟K线数据")
        
        # 2. 创建特征
        print(f"🔧 创建高频交易专用特征...")
        features_df = self.create_hft_features(df)
        
        # 3. 创建标签
        print(f"🎯 创建高频交易标签...")
        labels = self.create_hft_labels(df)
        
        # 4. 准备训练数据
        # 移除前24个数据点（特征计算需要）
        features_df = features_df.iloc[24:]
        labels = labels.iloc[24:]
        
        # 移除包含NaN的行
        valid_mask = ~(features_df.isna().any(axis=1) | labels.isna())
        features_df = features_df[valid_mask]
        labels = labels[valid_mask]
        
        print(f"📊 有效训练样本: {len(features_df)}")
        print(f"📊 标签分布:")
        label_counts = labels.value_counts().sort_index()
        for label, count in label_counts.items():
            label_names = {0: '大跌', 1: '小跌', 2: '震荡', 3: '小涨', 4: '大涨'}
            print(f"   {label} ({label_names[label]}): {count} ({count/len(labels)*100:.1f}%)")
        
        # 5. 重新映射标签确保连续性
        unique_labels = sorted(labels.unique())
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        labels_mapped = labels.map(label_mapping)

        print(f"📊 标签映射: {label_mapping}")

        # 分割训练测试集
        X_train, X_test, y_train, y_test = train_test_split(
            features_df, labels_mapped, test_size=0.2, random_state=42, stratify=labels_mapped
        )
        
        # 6. 标准化特征
        print(f"🔧 标准化特征...")
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 7. 训练模型
        print(f"🤖 训练XGBoost模型...")
        model = XGBClassifier(
            n_estimators=200,
            max_depth=6,
            learning_rate=0.1,
            random_state=42,
            eval_metric='mlogloss'
        )
        
        model.fit(X_train_scaled, y_train)
        
        # 8. 评估模型
        print(f"📊 评估模型性能...")
        y_pred = model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"✅ 模型准确率: {accuracy:.3f}")
        print(f"\n📊 详细分类报告:")
        print(classification_report(y_test, y_pred))
        
        # 9. 保存模型
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_name = f"hft_xgb_{symbol}_{timestamp}"
        
        model_path = f"models/{model_name}.joblib"
        scaler_path = f"models/{model_name}_scaler.joblib"
        
        joblib.dump(model, model_path)
        joblib.dump(self.scaler, scaler_path)
        
        print(f"💾 模型已保存:")
        print(f"   模型文件: {model_path}")
        print(f"   标准化器: {scaler_path}")
        
        # 10. 保存特征名称
        feature_names_path = f"models/{model_name}_features.txt"
        with open(feature_names_path, 'w') as f:
            for feature in features_df.columns:
                f.write(f"{feature}\n")
        
        print(f"   特征列表: {feature_names_path}")
        
        return model_name, accuracy

if __name__ == "__main__":
    trainer = HFTModelTrainer()
    
    try:
        model_name, accuracy = trainer.train_hft_model('BTCUSDT', days=30)
        print(f"\n🎉 高频交易模型训练完成!")
        print(f"📦 模型名称: {model_name}")
        print(f"📊 准确率: {accuracy:.1%}")
        print(f"\n💡 使用方法:")
        print(f"   在smart_ai_trader.py中将模型名称改为: {model_name}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
