import pandas as pd
import numpy as np
from sklearn.model_selection import TimeSeriesSplit, cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_curve, auc, confusion_matrix, log_loss, roc_auc_score
)
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
import xgboost as xgb
import lightgbm as lgb
import optuna
import joblib
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
from pathlib import Path
import platform
import json
import sklearn
import matplotlib.font_manager as fm
from binance.client import Client
import re
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.regularizers import l1_l2
import time
import random
from tensorflow.keras.metrics import F1Score
import gc
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
optuna.logging.set_verbosity(optuna.logging.WARNING)

# 配置中文字体
try:
    if platform.system().lower() == 'windows':
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 微软雅黑
    else:
        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei']  # Linux/Mac 文泉驿微米黑
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
except Exception as e:
    logger.warning(f"设置中文字体时发生错误: {str(e)}")

# 使用 register_keras_serializable 装饰器确保自定义度量可以被 Keras 正确保存和加载
@tf.keras.utils.register_keras_serializable(package='Custom', name='F1Score')
class F1Score(tf.keras.metrics.Metric):
    def __init__(self, name='f1_score', **kwargs):
        super().__init__(name=name, **kwargs)
        self.precision = tf.keras.metrics.Precision()
        self.recall = tf.keras.metrics.Recall()

    def update_state(self, y_true, y_pred, sample_weight=None):
        self.precision.update_state(y_true, y_pred, sample_weight)
        self.recall.update_state(y_true, y_pred, sample_weight)

    def result(self):
        p = self.precision.result()
        r = self.recall.result()
        return 2 * ((p * r) / (p + r + tf.keras.backend.epsilon()))

    def reset_state(self):
        self.precision.reset_state()
        self.recall.reset_state()

class ModelTrainer:
    def __init__(self, 
                 model_type: str = 'xgb',
                 n_splits: int = 5,
                 random_state: int = 42,
                 output_dir: Union[str, Path] = 'models',
                 save_plots: bool = True,
                 use_optuna: bool = True,
                 optuna_n_trials: int = 50, 
                 scaler_type: str = 'standard',
                 model_path: Optional[Union[str, Path]] = None,
                 scaler_path: Optional[Union[str, Path]] = None,
                 # 添加LSTM特定参数的占位符
                 lstm_units: int = 50,
                 lstm_dropout: float = 0.2,
                 lstm_recurrent_dropout: float = 0.2,
                 lstm_learning_rate: float = 0.001,
                 lstm_epochs: int = 50,
                 lstm_batch_size: int = 32,
                 lstm_timesteps: int = 1, # 默认时间步长为1，后续需要根据数据调整
                 num_classes: int = 8 # 新增：类别数量，默认为8对应8个市场状态
                 ):
        """
        初始化模型训练器
        
        参数:
            model_type (str): 模型类型 ('rf', 'gb', 'xgb', 'lgb', 'ensemble', 'lstm')
            n_splits (int): 交叉验证折数
            random_state (int): 随机种子
            output_dir (Union[str, Path]): 输出目录
            save_plots (bool): 是否保存图表
            use_optuna (bool): 是否使用Optuna进行超参数优化
            optuna_n_trials (int): Optuna的试验次数
            scaler_type (str): 数据缩放器类型 ('standard', 'minmax', 'robust')
            model_path (Optional[Union[str, Path]]): 直接加载模型的路径 (可选)
            scaler_path (Optional[Union[str, Path]]): 直接加载scaler的路径 (可选)
            lstm_units (int): LSTM模型的单元数量
            lstm_dropout (float): LSTM模型的dropout率
            lstm_recurrent_dropout (float): LSTM模型的recurrent dropout率
            lstm_learning_rate (float): LSTM模型的学习率
            lstm_epochs (int): LSTM模型的训练周期数
            lstm_batch_size (int): LSTM模型的批量大小
            lstm_timesteps (int): LSTM模型的时间步长
            num_classes (int): 模型的类别数量 (用于多分类)
        """
        self.model_type = model_type
        self.n_splits = n_splits
        self.random_state = random_state
        self.output_dir = Path(output_dir)
        self.save_plots = save_plots
        self.use_optuna = use_optuna
        self.optuna_n_trials = optuna_n_trials
        self.scaler_type = scaler_type
        
        # LSTM 特定参数
        self.lstm_units = lstm_units
        self.lstm_dropout = lstm_dropout
        self.lstm_recurrent_dropout = lstm_recurrent_dropout
        self.lstm_learning_rate = lstm_learning_rate
        self.lstm_epochs = lstm_epochs
        self.lstm_batch_size = lstm_batch_size
        self.lstm_timesteps = lstm_timesteps
        self.num_classes = num_classes # 保存类别数量
        
        self.model: Optional[Any] = None
        self.scaler: Union[StandardScaler, MinMaxScaler, RobustScaler] = None
        # _set_scaler 应该在加载scaler之前或如果scaler_path未提供时调用
        # self._set_scaler() 
        
        self.best_params: Optional[Dict[str, Any]] = None
        self.feature_importance: Optional[pd.Series] = None
        
        self.output_dir.mkdir(parents=True, exist_ok=True)
        # logger.info(f"初始化 ModelTrainer - 模型: {model_type}, CV折数: {n_splits}, Optuna: {use_optuna}, Scaler: {scaler_type}")

        # 如果提供了路径，则直接加载
        if model_path:
            self.load_model(specific_model_path=Path(model_path))
            # 如果模型加载成功，尝试从模型类型推断 self.model_type (如果需要)
            # 例如，可以检查 self.model.__class__.__name__ 并映射回 'xgb', 'rf' 等
            # 但这可能比较复杂，暂时依赖传入的 model_type
        else:
            # 如果没有提供model_path，则可能需要基于model_type初始化一个新模型
            # 或者依赖后续的train方法来初始化和训练
             logger.info(f"未提供model_path，将根据model_type ({self.model_type}) 初始化新模型或等待训练。")

        if scaler_path:
            self.load_scaler(specific_scaler_path=Path(scaler_path))
        else:
            # 如果没有提供scaler_path，则根据scaler_type初始化一个新的scaler
            self._set_scaler()
            logger.info(f"未提供scaler_path，根据scaler_type ({self.scaler_type}) 初始化新scaler。")
        
        # 确保即使从路径加载，scaler也已设置
        if self.scaler is None:
             self._set_scaler()

        logger.info(f"ModelTrainer 初始化完成. 模型类型: {self.model_type}, Scaler类型: {self.scaler_type}. "
                    f"模型已加载: {'是' if self.model else '否'}, Scaler已加载: {'是' if self.scaler and not isinstance(self.scaler, (StandardScaler, MinMaxScaler, RobustScaler)) or (hasattr(self.scaler, 'n_features_in_') if self.scaler else False) else '否 (或为新实例)'}")

    def _enhanced_data_cleaning(self, X: pd.DataFrame) -> pd.DataFrame:
        """增强的数据清理方法"""
        logger.info("开始增强数据清理...")
        X_clean = X.copy()

        # 1. 处理无穷大值
        inf_cols = X_clean.columns[np.isinf(X_clean).any()].tolist()
        if inf_cols:
            logger.warning(f"检测到包含无穷大值的列: {inf_cols}")
            X_clean.replace([np.inf, -np.inf], np.nan, inplace=True)

        # 2. 异常值检测和处理（使用IQR方法）
        numeric_cols = X_clean.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            Q1 = X_clean[col].quantile(0.25)
            Q3 = X_clean[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 3 * IQR  # 使用3倍IQR，比1.5倍更宽松
            upper_bound = Q3 + 3 * IQR

            outlier_mask = (X_clean[col] < lower_bound) | (X_clean[col] > upper_bound)
            outlier_count = outlier_mask.sum()

            if outlier_count > 0:
                logger.debug(f"列 {col} 检测到 {outlier_count} 个异常值")
                # 使用Winsorization处理异常值
                X_clean.loc[X_clean[col] < lower_bound, col] = lower_bound
                X_clean.loc[X_clean[col] > upper_bound, col] = upper_bound

        # 3. 智能NaN值处理
        nan_cols = X_clean.columns[X_clean.isna().any()].tolist()
        if nan_cols:
            logger.warning(f"检测到包含NaN值的列: {len(nan_cols)} 个")

            for col in nan_cols:
                nan_ratio = X_clean[col].isna().sum() / len(X_clean)

                if nan_ratio > 0.5:
                    # 如果超过50%的值是NaN，考虑删除该特征
                    logger.warning(f"列 {col} 有 {nan_ratio:.2%} 的NaN值，考虑删除")
                    X_clean.drop(columns=[col], inplace=True)
                    continue

                # 根据特征类型选择填充策略
                if any(keyword in col.lower() for keyword in ['return', 'momentum', 'change', 'pct']):
                    # 收益率类特征用0填充
                    X_clean[col].fillna(0, inplace=True)
                elif any(keyword in col.lower() for keyword in ['ma', 'sma', 'ema', 'price']):
                    # 价格和移动平均类特征用前向填充
                    X_clean[col].fillna(method='ffill', inplace=True)
                    X_clean[col].fillna(method='bfill', inplace=True)
                elif any(keyword in col.lower() for keyword in ['rsi', 'stoch', 'williams']):
                    # 振荡器指标用中位数填充
                    X_clean[col].fillna(X_clean[col].median(), inplace=True)
                elif any(keyword in col.lower() for keyword in ['volume', 'obv']):
                    # 成交量指标用前向填充
                    X_clean[col].fillna(method='ffill', inplace=True)
                    X_clean[col].fillna(0, inplace=True)
                else:
                    # 其他特征用中位数填充
                    X_clean[col].fillna(X_clean[col].median(), inplace=True)

        # 4. 最终检查
        remaining_nan = X_clean.isna().sum().sum()
        if remaining_nan > 0:
            logger.warning(f"仍有 {remaining_nan} 个NaN值，使用0填充")
            X_clean.fillna(0, inplace=True)

        logger.info(f"数据清理完成。原始特征数: {X.shape[1]}, 清理后特征数: {X_clean.shape[1]}")
        return X_clean

    def _advanced_feature_selection(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """高级特征选择方法"""
        logger.info("开始高级特征选择...")
        X_selected = X.copy()

        # 1. 移除低方差特征
        from sklearn.feature_selection import VarianceThreshold
        variance_selector = VarianceThreshold(threshold=0.01)
        X_variance = variance_selector.fit_transform(X_selected)
        selected_features = X_selected.columns[variance_selector.get_support()]
        X_selected = pd.DataFrame(X_variance, columns=selected_features, index=X_selected.index)
        logger.info(f"方差过滤后保留特征数: {X_selected.shape[1]}")

        # 2. 移除高相关性特征
        corr_matrix = X_selected.corr().abs()
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        high_corr_features = [column for column in upper_triangle.columns
                             if any(upper_triangle[column] > 0.95)]
        X_selected = X_selected.drop(columns=high_corr_features)
        logger.info(f"相关性过滤后保留特征数: {X_selected.shape[1]}")

        # 3. 基于重要性的特征选择
        if X_selected.shape[1] > 100:  # 只有特征数量较多时才进行
            from sklearn.feature_selection import SelectKBest, f_classif
            from sklearn.ensemble import RandomForestClassifier

            # 使用随机森林进行特征重要性评估
            rf_selector = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
            rf_selector.fit(X_selected, y)

            # 选择重要性最高的特征
            feature_importance = pd.Series(rf_selector.feature_importances_, index=X_selected.columns)
            top_features = feature_importance.nlargest(min(50, X_selected.shape[1])).index
            X_selected = X_selected[top_features]
            logger.info(f"重要性过滤后保留特征数: {X_selected.shape[1]}")

        return X_selected

    def _create_ensemble_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """创建集成特征"""
        logger.info("创建集成特征...")
        X_ensemble = X.copy()

        # 1. 技术指标组合
        if all(col in X_ensemble.columns for col in ['RSI_14', 'K', 'D']):
            X_ensemble['momentum_composite'] = (
                X_ensemble['RSI_14'] * 0.4 +
                X_ensemble['K'] * 0.3 +
                X_ensemble['D'] * 0.3
            )

        # 2. 趋势强度指标
        ma_cols = [col for col in X_ensemble.columns if 'MA_' in col]
        if len(ma_cols) >= 3:
            # 计算多个MA的一致性
            ma_data = X_ensemble[ma_cols[:3]]
            X_ensemble['ma_trend_consistency'] = (
                (ma_data.diff() > 0).sum(axis=1) / len(ma_cols[:3])
            )

        # 3. 波动性复合指标
        if all(col in X_ensemble.columns for col in ['ATR_14', 'BB_bandwidth']):
            X_ensemble['volatility_composite'] = (
                X_ensemble['ATR_14'] * 0.6 +
                X_ensemble['BB_bandwidth'] * 0.4
            )

        # 4. 成交量价格关系
        if all(col in X_ensemble.columns for col in ['volume_change', 'log_return']):
            X_ensemble['volume_price_sync'] = (
                X_ensemble['volume_change'] * X_ensemble['log_return']
            )

        logger.info(f"集成特征创建完成，新增特征数: {X_ensemble.shape[1] - X.shape[1]}")
        return X_ensemble

    def _set_scaler(self):
        """根据 scaler_type 初始化数据缩放器"""
        if self.scaler_type == 'minmax':
            self.scaler = MinMaxScaler()
        elif self.scaler_type == 'robust':
            self.scaler = RobustScaler()
        else: # 默认 standard
            self.scaler = StandardScaler()
        logger.info(f"使用 {self.scaler_type} scaler.")

    def _get_base_models(self, params: Optional[Dict[str, Dict[str, Any]]] = None) -> List[Tuple[str, Any]]:
        """获取基础模型列表，可选择性地使用优化后的参数"""
        params = params or {}
        models = [
            ('rf', RandomForestClassifier(**params.get('rf', {}), random_state=self.random_state, class_weight='balanced')),
            ('gb', GradientBoostingClassifier(**params.get('gb', {}), random_state=self.random_state)),
            ('xgb', xgb.XGBClassifier(**params.get('xgb', {}), random_state=self.random_state, eval_metric='logloss')),
            ('lgb', lgb.LGBMClassifier(**params.get('lgb', {}), random_state=self.random_state, class_weight='balanced')),
            ('lstm', None)  # 占位符，后续替换为LSTM模型实例化
        ]
        return models

    def _optuna_objective(self, trial: optuna.Trial, X: pd.DataFrame, y: pd.Series, model_name: str) -> float:
        """
        Optuna优化目标函数。
        """
        if model_name == 'lstm':
            # 获取当前试验的超参数
            lstm_units = trial.suggest_int('lstm_units', 32, 128)
            lstm_dropout = trial.suggest_float('lstm_dropout', 0.0, 0.5)
            lstm_recurrent_dropout = trial.suggest_float('lstm_recurrent_dropout', 0.0, 0.5)
            learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)
            batch_size = trial.suggest_categorical('batch_size', [16, 32, 64])
            epochs = trial.suggest_int('lstm_epochs_optuna', 10, 100)
            
            # 准备数据
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 重塑数据为LSTM格式
            X_train_reshaped, y_train_reshaped = self._reshape_data_for_lstm(X_train.values, y_train)
            X_val_reshaped, y_val_reshaped = self._reshape_data_for_lstm(X_val.values, y_val)
            
            # 将目标变量转换为 one-hot 编码
            y_train_one_hot = tf.keras.utils.to_categorical(y_train_reshaped, num_classes=self.num_classes)
            y_val_one_hot = tf.keras.utils.to_categorical(y_val_reshaped, num_classes=self.num_classes)
            
            # 构建和训练模型
            lstm_model_optuna = self._build_lstm_model(
                input_shape=(X_train_reshaped.shape[1], X_train_reshaped.shape[2]),
                units=lstm_units,
                dropout=lstm_dropout,
                recurrent_dropout=lstm_recurrent_dropout,
                learning_rate=learning_rate
            )
            
            # 设置早停回调
            early_stopping_optuna = tf.keras.callbacks.EarlyStopping(
                monitor='val_accuracy' if self.num_classes > 1 else 'val_f1_score',
                patience=5,
                restore_best_weights=True
            )
            
            # 训练模型
            history = lstm_model_optuna.fit(
                X_train_reshaped, y_train_one_hot,
                epochs=epochs,
                batch_size=batch_size,
                validation_data=(X_val_reshaped, y_val_one_hot),
                callbacks=[early_stopping_optuna, optuna.integration.TFKerasPruningCallback(
                    trial, 'val_accuracy' if self.num_classes > 1 else 'val_f1_score')],
                verbose=0
            )
            
            # 返回验证集上的性能指标
            return history.history['val_accuracy'][-1] if self.num_classes > 1 else history.history['val_f1_score'][-1]
            
        else:
            raise ValueError(f"不支持的模型类型: {model_name}")
            
        return 0.0  # 如果出现任何错误，返回0

    def _initialize_model(self, optimized_params: Optional[Dict[str, Any]] = None, X_shape_for_lstm: Optional[Tuple[int, ...]] = None):
        """根据模型类型初始化模型，可选择性地使用优化后的参数"""
        
        if optimized_params is None:
            optimized_params = {} 

        default_rf_params = {'random_state': self.random_state, 'class_weight': 'balanced'}
        default_gb_params = {'random_state': self.random_state}
        default_xgb_params = {'random_state': self.random_state, 'eval_metric': 'logloss', 'use_label_encoder': False}
        default_lgb_params = {'random_state': self.random_state, 'class_weight': 'balanced'}

        if self.model_type == 'lstm':
            current_lstm_units = optimized_params.get('lstm_units', self.lstm_units)
            current_lstm_dropout = optimized_params.get('lstm_dropout', self.lstm_dropout)
            current_lstm_recurrent_dropout = optimized_params.get('lstm_recurrent_dropout', self.lstm_recurrent_dropout)
            current_learning_rate = optimized_params.get('learning_rate', self.lstm_learning_rate)
            current_batch_size_optuna = optimized_params.get('batch_size', None) 

            log_msg_parts = [f"LSTM模型将使用以下参数构建:"]
            param_details = {
                "Units": (current_lstm_units, self.lstm_units, 'lstm_units'),
                "Dropout": (current_lstm_dropout, self.lstm_dropout, 'lstm_dropout'),
                "Recurrent Dropout": (current_lstm_recurrent_dropout, self.lstm_recurrent_dropout, 'lstm_recurrent_dropout'),
                "Learning Rate": (current_learning_rate, self.lstm_learning_rate, 'learning_rate')
            }
            for name, (current_val, default_val, opt_key) in param_details.items():
                source = "Optuna优化" if opt_key in optimized_params else "默认值"
                log_msg_parts.append(f"  {name}: {current_val} ({source})")
            
            if current_batch_size_optuna is not None:
                self.lstm_batch_size = current_batch_size_optuna 
                log_msg_parts.append(f"  Batch Size (for training): {self.lstm_batch_size} (Optuna优化)")
            else:
                log_msg_parts.append(f"  Batch Size (for training): {self.lstm_batch_size} (默认值)")
            logger.info("\n".join(log_msg_parts))

            if X_shape_for_lstm:
                model_input_shape = (X_shape_for_lstm[1], X_shape_for_lstm[2])
                logger.info(f"Building LSTM model with derived input shape (timesteps, features): {model_input_shape}")
                self.model = self._build_lstm_model(
                    input_shape=model_input_shape,
                    units=current_lstm_units,
                    dropout=current_lstm_dropout,
                    recurrent_dropout=current_lstm_recurrent_dropout,
                    learning_rate=current_learning_rate
                )
            else:
                logger.warning("X_shape_for_lstm 未提供，LSTM模型结构此时无法构建。可能在加载模型或Optuna的早期阶段。")
                self.model = None
            
            if self.model:
                summary_list = []
                self.model.summary(print_fn=lambda x: summary_list.append(x))
                logger.info("LSTM 模型结构:\n" + "\n".join(summary_list))
            elif X_shape_for_lstm:
                logger.error("LSTM 模型构建失败，即使提供了X_shape_for_lstm。请检查_build_lstm_model的日志。")

        elif self.model_type == 'rf':
            # optimized_params 是 Optuna 为 rf 找到的参数，如 {'n_estimators': ..., 'max_depth': ...}
            # 将它们与默认参数合并，优化参数优先
            final_params = {**default_rf_params, **optimized_params}
            self.model = RandomForestClassifier(**final_params)
            logger.info(f"模型 RandomForestClassifier 使用参数: {final_params} 进行初始化。 Optuna优化部分: {optimized_params if optimized_params else '无'}")
        
        elif self.model_type == 'gb':
            final_params = {**default_gb_params, **optimized_params}
            self.model = GradientBoostingClassifier(**final_params)
            logger.info(f"模型 GradientBoostingClassifier 使用参数: {final_params} 进行初始化。 Optuna优化部分: {optimized_params if optimized_params else '无'}")

        elif self.model_type == 'xgb':
            final_params = {**default_xgb_params, **optimized_params}
            self.model = xgb.XGBClassifier(**final_params)
            logger.info(f"模型 XGBClassifier 使用参数: {final_params} 进行初始化。 Optuna优化部分: {optimized_params if optimized_params else '无'}")

        elif self.model_type == 'lgb':
            final_params = {**default_lgb_params, **optimized_params}
            self.model = lgb.LGBMClassifier(**final_params)
            logger.info(f"模型 LGBMClassifier 使用参数: {final_params} 进行初始化。 Optuna优化部分: {optimized_params if optimized_params else '无'}")
        
        elif self.model_type == 'ensemble':
            logger.info("初始化集成模型...")
            # optimized_params 对于 ensemble 可能是包含各基础模型参数的字典
            # 例如: {'rf': rf_best_params, 'lgb': lgb_best_params}
            # 或者是一个指示使用哪些模型的列表等，具体取决于 optimize_hyperparameters 如何为 ensemble 设置 best_params
            
            # 确保 optimized_params 是字典，即使为空
            # if not isinstance(optimized_params, dict): # optimized_params 已经保证是dict了
            #     logger.warning(f"集成模型的 optimized_params 不是字典: {optimized_params}。将使用默认基础模型。")
            #     optimized_params = {}

            # 获取各基础模型的参数，如果 optimized_params 中没有，则使用默认值
            rf_ens_params = {**default_rf_params, **optimized_params.get('rf', {})}
            lgb_ens_params = {**default_lgb_params, **optimized_params.get('lgb', {})}
            xgb_ens_params = {**default_xgb_params, **optimized_params.get('xgb', {})} # 添加XGB到集成

            base_models_for_ensemble = [
                ('rf', RandomForestClassifier(**rf_ens_params)),
                ('lgb', lgb.LGBMClassifier(**lgb_ens_params)),
                ('xgb', xgb.XGBClassifier(**xgb_ens_params)) # 添加XGB
            ]
            # 可以根据需要添加更多模型，例如 GradientBoostingClassifier
            # gb_ens_params = {**default_gb_params, **optimized_params.get('gb', {})}
            # base_models_for_ensemble.append(('gb', GradientBoostingClassifier(**gb_ens_params)))

            self.model = VotingClassifier(estimators=base_models_for_ensemble, voting='soft') # 'soft' 通常更好
            logger.info(f"集成模型 VotingClassifier 初始化，使用基础模型参数: RF={rf_ens_params}, LGBM={lgb_ens_params}, XGB={xgb_ens_params}")

        else:
            logger.error(f"未知的模型类型: {self.model_type}，无法初始化。")
            raise ValueError(f"未知的模型类型: {self.model_type}")

        if self.model:
            logger.info(f"模型 {self.model_type} 初始化成功。")
        else:
            # 此处错误主要用于捕获 _build_lstm_model 返回 None 或未知模型类型的情况
            logger.error(f"模型 {self.model_type} 初始化失败。检查模型类型或构建逻辑。")

    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """
        使用Optuna优化超参数
        
        参数:
            X (pd.DataFrame): 特征数据
            y (pd.Series): 目标变量
        返回:
            Dict[str, Any]: 优化后的最佳参数字典
        """
        if not self.use_optuna:
            logger.info("Optuna优化未启用，跳过超参数优化。")
            return {}

        optimized_params: Dict[str, Any] = {}

        if self.model_type == 'ensemble':
            logger.info("为集成模型优化基础模型的超参数...")
            base_model_names = [name for name, _ in self._get_base_models()]
            best_base_params: Dict[str, Dict[str, Any]] = {}

            for model_name in base_model_names:
                logger.info(f"开始为基础模型 {model_name} 优化超参数...")
                study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
                
                objective_func = lambda trial: self._optuna_objective(trial, X, y, model_name)
                
                study.optimize(objective_func, n_trials=self.optuna_n_trials, n_jobs=-1)
                
                best_base_params[model_name] = study.best_params
                logger.info(f"基础模型 {model_name} 的最佳参数: {study.best_params}, 最佳 F1 值: {study.best_value:.4f}")
            
            optimized_params['base_models'] = best_base_params
            self.best_params = optimized_params
        else:
            logger.info(f"开始为 {self.model_type} 模型优化超参数...")
            study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
            
            objective_func = lambda trial: self._optuna_objective(trial, X, y, self.model_type)
            
            study.optimize(objective_func, n_trials=self.optuna_n_trials, n_jobs=-1)
            
            optimized_params[self.model_type] = study.best_params
            self.best_params = study.best_params
            logger.info(f"模型 {self.model_type} 的最佳参数: {study.best_params}, 最佳 F1 值: {study.best_value:.4f}")
            
        return optimized_params

    def train(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """
        训练模型并返回评估结果。
        """
        logger.info("开始训练模型...")
        
        # 增强的数据质量检查和处理
        X = self._enhanced_data_cleaning(X)

        # 创建集成特征
        X = self._create_ensemble_features(X)

        # 高级特征选择
        X = self._advanced_feature_selection(X, y)

        try:
            logger.info("开始训练模型...")
            
            assert len(X) == len(y), f"特征和目标变量长度不一致: X={len(X)}, y={len(y)}"
            
            # --- 在缩放前检查 X 中的 inf 和 NaN ---
            if not isinstance(X, pd.DataFrame):
                logger.warning("输入到训练的 X 不是 DataFrame，某些检查可能无法执行或结果不准确。")
            else:
                cols_with_inf_X = X.columns[np.isinf(X).any()].tolist()
                if cols_with_inf_X:
                    logger.error(f"!!!!!! Model Trainer: X 在缩放前检测到以下列包含无穷大值: {cols_with_inf_X}")
                    for col_inf in cols_with_inf_X:
                        logger.debug(f"列 {col_inf} 中的无穷大值详情:\\n{X[X[col_inf] == np.inf][col_inf]}")
                        logger.debug(f"列 {col_inf} 中的负无穷大值详情:\\n{X[X[col_inf] == -np.inf][col_inf]}")
                    # 可以选择在这里抛出错误或者尝试替换
                    # X.replace([np.inf, -np.inf], np.nan, inplace=True)
                    # logger.warning("已将 X 中的无穷大值替换为 NaN，然后再进行缩放。")
                
                cols_with_nan_X = X.columns[X.isnull().any()].tolist()
                if cols_with_nan_X:
                    logger.warning(f"Model Trainer: X 在缩放前检测到以下列包含NaN值: {cols_with_nan_X}")
                    # for col_nan in cols_with_nan_X:
                    #     logger.debug(f"列 {col_nan} 中NaN值的数量: {X[col_nan].isnull().sum()}")
                    # X.fillna(X.median(), inplace=True) # 或者其他填充策略
                    # logger.warning("已使用中位数填充 X 中的 NaN 值，然后再进行缩放。")
            # --- 检查结束 ---
            
            # 首先进行数据缩放，这对Optuna和实际训练都通用
            X_scaled_np = self.scaler.fit_transform(X)
            X_scaled_df = pd.DataFrame(X_scaled_np, columns=X.columns, index=X.index)

            # 如果启用了Optuna，首先进行超参数优化
            if self.use_optuna:
                # 注意：optimize_hyperparameters内部会设置 self.best_params
                # 它可能使用 X_scaled_df 或原始 X，取决于其内部实现，当前是原始X
                # 对于LSTM的Optuna目标函数，它内部会再次进行缩放和重塑
                logger.info("Optuna已启用，开始超参数优化...")
                # 将缩放后的数据传递给 optimize_hyperparameters，因为它更通用
                # _optuna_objective 对于 LSTM 会自己处理缩放和重塑
                # 对于其他sklearn模型，它们通常期望缩放后的数据
                optimized_params = self.optimize_hyperparameters(X_scaled_df, y) 
                # self.best_params 应该已被 optimize_hyperparameters 设置
                if not self.best_params:
                    logger.warning("Optuna 优化未返回最佳参数，或未更新 self.best_params。将使用默认参数。")
                    # self.best_params 默认为 None 或 {}，_initialize_model 会处理
            else:
                logger.info("Optuna未启用，将使用默认或指定的参数进行训练。")
                #确保 self.best_params 在未使用optuna时为空字典，以便_initialize_model正确处理
                self.best_params = {} 

            # --- 后续的训练逻辑 ---
            if self.model_type == 'lstm':
                # LSTM的重塑需要缩放后的数据 (X_scaled_np)
                X_reshaped, y_reshaped = self._reshape_data_for_lstm(X_scaled_np, y, self.lstm_timesteps)
                if X_reshaped.shape[0] == 0:
                    logger.error("重塑后的X数据为空，无法训练LSTM模型。请检查lstm_timesteps和数据长度。")
                    return {'error': 'Reshaped X data is empty for LSTM'}
                
                # 模型初始化：使用 self.best_params (如果Optuna运行过) 或默认参数
                # _initialize_model 会从 self.best_params 中查找 'lstm' 键
                self._initialize_model(optimized_params=self.best_params, X_shape_for_lstm=X_reshaped.shape)

                if self.model is None: # 双重检查，确保模型已构建
                    logger.error("LSTM模型未能成功构建。")
                    return {'error': 'LSTM model could not be built.'}

                # Keras的TimeSeriesSplit处理
                tscv = TimeSeriesSplit(n_splits=self.n_splits)
                cv_scores = defaultdict(list)
                all_fold_predictions = []
                all_fold_true_values = []
                
                # 获取当前时间戳用于保存模型
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                # 创建保存模型的目录
                model_save_dir = Path('models/BTCUSDT/1h') / f'lstm_{timestamp}'
                model_save_dir.mkdir(parents=True, exist_ok=True)
                
                # 初始化存储fold模型路径的列表
                fold_models_paths = []
                
                for fold, (train_index, test_index) in enumerate(tscv.split(X_scaled_df), 1):
                    logger.info(f"--- LSTM Fold {fold}/{self.n_splits} ---")
                    
                    # 获取当前折的训练集和测试集
                    X_train_fold = X_scaled_df.iloc[train_index]
                    X_test_fold = X_scaled_df.iloc[test_index]
                    y_train_fold = y[train_index] if isinstance(y, np.ndarray) else y.iloc[train_index]
                    y_test_fold = y[test_index] if isinstance(y, np.ndarray) else y.iloc[test_index]
                    
                    # 重塑数据为LSTM格式
                    X_train_reshaped, y_train_reshaped = self._reshape_data_for_lstm(X_train_fold.values, y_train_fold)
                    X_test_reshaped, y_test_reshaped = self._reshape_data_for_lstm(X_test_fold.values, y_test_fold)
                    
                    # 将目标变量转换为 one-hot 编码
                    y_train_one_hot = tf.keras.utils.to_categorical(y_train_reshaped, num_classes=self.num_classes)
                    y_test_one_hot = tf.keras.utils.to_categorical(y_test_reshaped, num_classes=self.num_classes)
                    
                    # 为每个折构建新的模型
                    current_fold_model = self._build_lstm_model(
                        input_shape=(X_train_reshaped.shape[1], X_train_reshaped.shape[2]),
                        units=self.lstm_units,
                        dropout=self.lstm_dropout,
                        recurrent_dropout=self.lstm_recurrent_dropout,
                        learning_rate=self.lstm_learning_rate
                    )
                    
                    # 设置回调
                    callbacks = [
                        tf.keras.callbacks.EarlyStopping(
                            monitor='val_accuracy',
                            patience=5,
                            restore_best_weights=True
                        ),
                        tf.keras.callbacks.ModelCheckpoint(
                            filepath=str(model_save_dir / f'lstm_fold_{fold}_best_model.keras'),
                            monitor='val_accuracy',
                            save_best_only=True,
                            save_weights_only=False  # 保存整个模型而不是只保存权重
                        )
                    ]
                    
                    # 记录当前fold的模型保存路径
                    fold_models_paths.append(str(model_save_dir / f'lstm_fold_{fold}_best_model.keras'))
                    
                    # 训练模型
                    history = current_fold_model.fit(
                        X_train_reshaped, y_train_one_hot,
                        epochs=self.lstm_epochs,
                        batch_size=self.lstm_batch_size,
                        validation_data=(X_test_reshaped, y_test_one_hot),
                        callbacks=callbacks,
                        verbose=0
                    )
                    
                    # 评估模型
                    evaluation_results = current_fold_model.evaluate(X_test_reshaped, y_test_one_hot, verbose=0)
                    
                    # 记录评估结果
                    metrics = ['loss', 'accuracy', 'precision', 'recall', 'f1_score']
                    for metric, value in zip(metrics, evaluation_results):
                        cv_scores[metric].append(value)
                    
                    # 获取预测结果
                    y_pred_proba = current_fold_model.predict(X_test_reshaped)
                    y_pred = np.argmax(y_pred_proba, axis=1)
                    y_true = np.argmax(y_test_one_hot, axis=1)
                    
                    # 保存预测结果和真实值
                    all_fold_predictions.extend(y_pred)
                    all_fold_true_values.extend(y_true)
                    
                    # 输出当前折的性能
                    logger.info(f"Fold {fold} - Accuracy: {cv_scores['accuracy'][-1]:.4f}, F1: {cv_scores['f1_score'][-1]:.4f}")
                    
                    # 清理内存
                    tf.keras.backend.clear_session()
                    gc.collect()

                # 训练结束后，选择一个模型作为self.model
                # 例如，可以选择最后一个fold的模型，或者在所有fold中验证损失最小的模型对应的那个
                # 这里简单地使用最后一个fold训练完成的current_fold_model（已恢复最佳权重）
                # 或者，如果开启了ModelCheckpoint，可以尝试加载在所有fold中val_loss最低的模型
                if fold_models_paths: # 如果保存了每个fold的模型
                    # 简单策略：加载最后一个fold的最佳模型 (如果 EarlyStopping(restore_best_weights=True) 行为符合预期)
                    # 或者可以实现更复杂的逻辑来选择所有fold中最好的一个
                    # 现在我们保存的是 .keras 文件
                    best_overall_model_path = fold_models_paths[-1] # 这是一个简化, 指向 .keras 文件
                    try:
                        logger.info(f"加载在最后一个fold中表现最佳的LSTM模型: {best_overall_model_path}")
                        self.model = tf.keras.models.load_model(best_overall_model_path)
                    except Exception as e:
                        logger.error(f"从 {best_overall_model_path} 加载模型失败: {e}. 将使用最后一个fold的内存中模型。")
                        self.model = current_fold_model # Fallback
                else: # 如果没有保存每个fold的模型，则最后一个fold的模型（已恢复最佳权重）就是我们能用的
                    self.model = current_fold_model
                
                # 计算平均分数等
                mean_scores = {metric: np.mean(scores) for metric, scores in cv_scores.items() if scores}
                std_scores = {metric: np.std(scores) for metric, scores in cv_scores.items() if scores}
                self.feature_importance = None # LSTM特征重要性不直接

                # 调用绘图，确保传递正确的数据
                self._generate_evaluation_plots(
                    cv_scores=cv_scores, # 传递每个fold的指标
                    feature_names=list(X.columns), # 原始特征名
                    y_true=np.array(all_fold_true_values),
                    y_pred=np.array(all_fold_predictions),
                    y_prob=np.array(all_fold_predictions) # 注意这里的y_prob是概率值
                )

                results = {
                    'cv_scores': cv_scores,
                    'mean_scores': mean_scores,
                    'std_scores': std_scores,
                    'best_params': self.best_params, # Optuna找到的参数
                    'feature_importance': self.feature_importance
                }
                logger.info("\nLSTM模型评估结果:")
                for metric, score in mean_scores.items():
                    logger.info(f"{metric}: {score:.4f} (+/- {std_scores.get(metric, 0):.4f})")
                return results

            else: # 非LSTM模型的现有逻辑
                self._initialize_model(optimized_params=self.best_params)
                if self.model is None:
                    logger.error(f"模型 {self.model_type} 未能初始化.")
                    return {'error': f'Model {self.model_type} could not be initialized'}

                # X_scaled = self.scaler.fit_transform(X) # 已在前面完成
                # X_scaled = pd.DataFrame(X_scaled, columns=X.columns) # 已在前面完成
            
            tscv = TimeSeriesSplit(n_splits=self.n_splits)
            cv_scores = {
                'accuracy': [],
                'precision': [],
                'recall': [],
                'f1': []
            }
            
            all_y_true = []
            all_y_pred = []
            all_y_prob = []
            
            for fold, (train_idx, test_idx) in enumerate(tscv.split(X_scaled_df), 1):
                X_train, X_test = X_scaled_df.iloc[train_idx], X_scaled_df.iloc[test_idx]
                y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
                
                assert len(X_train) == len(y_train), f"训练集长度不一致: X_train={len(X_train)}, y_train={len(y_train)}"
                assert len(X_test) == len(y_test), f"测试集长度不一致: X_test={len(X_test)}, y_test={len(y_test)}"
                
                self.model.fit(X_train, y_train)
                
                y_pred = self.model.predict(X_test)
                
                all_y_true.extend(y_test)
                all_y_pred.extend(y_pred)
                
                if hasattr(self.model, 'predict_proba'):
                    y_prob = self.model.predict_proba(X_test)[:, 1]
                    all_y_prob.extend(y_prob)
                
                cv_scores['accuracy'].append(accuracy_score(y_test, y_pred))
                cv_scores['precision'].append(precision_score(y_test, y_pred, average='weighted'))
                cv_scores['recall'].append(recall_score(y_test, y_pred, average='weighted'))
                cv_scores['f1'].append(f1_score(y_test, y_pred, average='weighted'))
                
                logger.info(f"Fold {fold} - Accuracy: {cv_scores['accuracy'][-1]:.4f}, "
                          f"F1: {cv_scores['f1'][-1]:.4f}")
            
            mean_scores = {metric: np.mean(scores) for metric, scores in cv_scores.items()}
            std_scores = {metric: np.std(scores) for metric, scores in cv_scores.items()}
            
            self.feature_importance = self._get_feature_importance(X.columns)
            
            self._generate_evaluation_plots(
                cv_scores=cv_scores,
                feature_names=X.columns,
                y_true=np.array(all_y_true),
                y_pred=np.array(all_y_pred),
                y_prob=np.array(all_y_prob) if all_y_prob else None
            )
            
            results = {
                'cv_scores': cv_scores,
                'mean_scores': mean_scores,
                'std_scores': std_scores,
                'best_params': self.best_params,
                'feature_importance': self.feature_importance
            }
            
            logger.info("\n模型评估结果:")
            for metric, score in mean_scores.items():
                logger.info(f"{metric}: {score:.4f} (+/- {std_scores[metric]:.4f})")
            
            return results
            
        except Exception as e:
            logger.error(f"模型训练过程中发生错误: {str(e)}")
            raise
            
    def _get_feature_importance(self, feature_names: List[str]) -> Optional[pd.Series]:
        """获取特征重要性"""
        if hasattr(self.model, 'feature_importances_'):
            importance = self.model.feature_importances_
        elif hasattr(self.model, 'coef_'):
            importance = np.abs(self.model.coef_[0])
        elif hasattr(self.model, 'estimators_'):
            importance_list = []
            for name, estimator in self.model.named_estimators_.items():
                if hasattr(estimator, 'feature_importances_'):
                    importance_list.append(estimator.feature_importances_)
            if importance_list:
                importance = np.mean(importance_list, axis=0)
            else:
                return None
        else:
            return None
            
        return pd.Series(importance, index=feature_names).sort_values(ascending=False)
        
    def _generate_evaluation_plots(self, cv_scores: Dict, feature_names: List[str], y_true: np.ndarray = None, y_pred: np.ndarray = None, y_prob: np.ndarray = None):
        """生成并保存评估图表，包括混淆矩阵、ROC曲线（如果适用）、特征重要性（如果适用）"""
        if not self.save_plots:
            return
            
        logger.info("开始生成评估图表...")
        plot_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_plot_dir = self.output_dir / f"{self.model_type}_{plot_timestamp}_plots"
        model_plot_dir.mkdir(parents=True, exist_ok=True)

        # 1. 混淆矩阵
        if y_true is not None and y_pred is not None and len(y_true) > 0 and len(y_pred) > 0:
            try:
                cm = confusion_matrix(y_true, y_pred)
                plt.figure(figsize=(8, 6))
                # Determine class labels. If num_classes is defined and > 1, use range(self.num_classes)
                # Otherwise, infer from unique values in y_true and y_pred.
                labels = None
                if hasattr(self, 'num_classes') and self.num_classes > 0:
                    labels = list(range(self.num_classes))
                
                sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=labels if labels else 'auto', yticklabels=labels if labels else 'auto')
                plt.title(f'{self.model_type.upper()} 混淆矩阵 (CV聚合预测)')
                plt.xlabel('预测标签')
                plt.ylabel('真实标签')
                plt.savefig(model_plot_dir / f"confusion_matrix_{plot_timestamp}.png")
                plt.close()
                logger.info(f"混淆矩阵已保存到: {model_plot_dir}")
            except Exception as e:
                logger.error(f"生成混淆矩阵时出错: {e}. y_true shape: {y_true.shape}, y_pred shape: {y_pred.shape}")
        else:
            logger.info("真实标签或预测标签为空，跳过混淆矩阵生成。")

        # 2. ROC曲线 和 AUC (y_prob 需要是概率值)
        # For multiclass, y_prob should be (n_samples, n_classes)
        # y_true needs to be binarized for each class if using one-vs-rest
        if y_true is not None and y_prob is not None and len(y_true) > 0 and y_prob.size > 0:
            try:
                plt.figure(figsize=(10, 8))
                if hasattr(self, 'num_classes') and self.num_classes > 1:
                    # 确保y_prob是二维数组
                    if y_prob.ndim == 1:
                        # 如果是一维数组，需要将其转换为独热编码形式
                        y_prob_reshaped = np.zeros((len(y_prob), self.num_classes))
                        for i in range(len(y_prob)):
                            y_prob_reshaped[i, int(y_prob[i])] = 1
                        y_prob = y_prob_reshaped

                    # Multiclass ROC: One-vs-Rest
                    for i in range(self.num_classes):
                        # Binarize y_true for class i vs rest
                        y_true_class_i = (y_true == i).astype(int)
                        # Get probabilities for class i
                        y_prob_class_i = y_prob[:, i]
                        fpr, tpr, _ = roc_curve(y_true_class_i, y_prob_class_i)
                        roc_auc = auc(fpr, tpr)
                        plt.plot(fpr, tpr, lw=2, label=f'类别 {i} (AUC = {roc_auc:.2f})')

                    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
                    plt.xlim([0.0, 1.0])
                    plt.ylim([0.0, 1.05])
                    plt.xlabel('假正例率 (FPR)')
                    plt.ylabel('真正例率 (TPR)')
                    plt.title(f'{self.model_type.upper()} ROC 曲线 (One-vs-Rest)')
                    plt.legend(loc="lower right")
                elif hasattr(self, 'num_classes') and self.num_classes == 1:  # Binary case
                    # Ensure y_prob is 1D for binary classification if it came from predict_proba[:,1] or similar
                    y_prob_binary = y_prob.ravel() if y_prob.ndim > 1 else y_prob
                    fpr, tpr, _ = roc_curve(y_true, y_prob_binary)
                    roc_auc = auc(fpr, tpr)
                    plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.2f})')
                    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
                    plt.xlim([0.0, 1.0])
                    plt.ylim([0.0, 1.05])
                    plt.xlabel('假正例率 (FPR)')
                    plt.ylabel('真正例率 (TPR)')
                    plt.title(f'{self.model_type.upper()} ROC 曲线')
                    plt.legend(loc="lower right")
                else:
                    logger.warning("num_classes未定义或无效，无法确定ROC曲线类型。")
                plt.savefig(model_plot_dir / f"roc_curve_{plot_timestamp}.png")
                plt.close()
                logger.info(f"ROC曲线已保存到: {model_plot_dir}")
            except Exception as e:
                logger.error(f"生成ROC曲线时出错: {e}")
        else:
            logger.info("真实标签或预测概率为空，跳过ROC曲线生成。")

        # 3. 特征重要性图 (如果可用)
        if self.feature_importance is not None and not self.feature_importance.empty:
            try:
                plt.figure(figsize=(12, 8))
                # 只显示Top N个特征
                top_n = min(len(self.feature_importance), 20)
                self.feature_importance.head(top_n).plot(kind='barh')
                plt.title(f'{self.model_type.upper()} 特征重要性 (Top {top_n})')
                plt.gca().invert_yaxis()  # 重要性高的在顶部
                plt.tight_layout()
                plt.savefig(model_plot_dir / f"feature_importance_{plot_timestamp}.png")
                plt.close()
                logger.info(f"特征重要性图已保存到: {model_plot_dir}")
            except Exception as e:
                logger.error(f"生成特征重要性图时出错: {e}")
        else:
            logger.info(f"模型 {self.model_type} 无可用特征重要性信息，跳过绘图。")
        
        # 4. CV 分数图
        if cv_scores and isinstance(cv_scores, dict) and any(isinstance(v, list) for v in cv_scores.values()):
            try:
                for metric_name, scores_list in cv_scores.items():
                    if isinstance(scores_list, list) and all(isinstance(s, (int, float)) for s in scores_list):
                        plt.figure(figsize=(8, 5))
                        plt.plot(range(1, len(scores_list) + 1), scores_list, marker='o', linestyle='-')
                        plt.title(f'{self.model_type.upper()} CV - {metric_name} per Fold')
                        plt.xlabel('Fold')
                        plt.ylabel(metric_name)
                        plt.xticks(range(1, len(scores_list) + 1))
                        plt.grid(True)
                        plt.tight_layout()
                        plt.savefig(model_plot_dir / f"cv_scores_{metric_name}_{plot_timestamp}.png")
                        plt.close()
                logger.info(f"详细CV分数图已保存到: {model_plot_dir}")
            except Exception as e:
                logger.error(f"生成详细CV分数图时出错: {e}")
        elif cv_scores and isinstance(cv_scores, dict):  # Fallback to summary bar chart
            try:
                simple_scores = {k: v for k, v in cv_scores.items() if isinstance(v, (int, float, np.number))}
                if simple_scores:
                    metrics_df = pd.DataFrame.from_dict(simple_scores, orient='index', columns=['Score']).sort_values(by='Score', ascending=False)
                    if not metrics_df.empty:
                        plt.figure(figsize=(10, 6))
                        metrics_df.plot(kind='bar', legend=None)
                        plt.title(f'{self.model_type.upper()} 平均交叉验证指标')
                        plt.ylabel('分数')
                        plt.xticks(rotation=45, ha='right')
                        plt.tight_layout()
                        plt.savefig(model_plot_dir / f"cv_metrics_summary_{plot_timestamp}.png")
                        plt.close()
                        logger.info(f"CV指标摘要图已保存到: {model_plot_dir}")
                else:
                    logger.info("未找到简单的CV平均分数用于绘制摘要图。")
            except Exception as e:
                logger.error(f"生成CV指标摘要图时出错: {e}")
        else:
            logger.info("CV分数数据为空或格式不正确，跳过CV分数图生成。")
        
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """使用训练好的模型进行预测"""
        if self.model is None:
            logger.error("模型未训练或加载，无法进行预测。")
            return np.array([]) 

        if self.scaler is None:
            logger.error("Scaler未初始化或加载，无法转换数据进行预测。")
            return np.array([])
        
        try:
            X_scaled = self.scaler.transform(X)
        except Exception as e:
            logger.error(f"预测时数据缩放失败: {e}")
            logger.error(f"X shape: {X.shape}, X head:\\n{X.head()}")
            logger.error(f"Scaler n_features_in: {self.scaler.n_features_in_ if hasattr(self.scaler, 'n_features_in_') else 'N/A'}")
            if hasattr(self.scaler, 'n_features_in_') and X.shape[1] != self.scaler.n_features_in_:
                 logger.error(f"输入特征数量 ({X.shape[1]}) 与训练时 ({self.scaler.n_features_in_}) 不匹配。")
            return np.array([])

        if self.model_type == 'lstm':
            X_scaled_reshaped = self._reshape_data_for_lstm(X_scaled, timesteps=self.lstm_timesteps)
            if X_scaled_reshaped.shape[0] == 0:
                logger.warning("LSTM预测：重塑后的数据为空，可能是因为输入数据行数不足。返回空预测。")
                return np.array([])
            
            predictions_proba = self.model.predict(X_scaled_reshaped)
            if self.num_classes > 1:
                predictions = np.argmax(predictions_proba, axis=1)
            else: # Binary
                predictions = (predictions_proba > 0.5).astype(int)
            return predictions
        else:
            return self.model.predict(X_scaled)
        
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """使用训练好的模型进行概率预测"""
        if self.model is None:
            logger.error("模型未训练或加载，无法进行概率预测。")
            if hasattr(self, 'num_classes') and self.num_classes > 1:
                return np.empty((0, self.num_classes))
            else:
                return np.empty((0,))

        if self.scaler is None:
            logger.error("Scaler未初始化或加载，无法转换数据进行概率预测。")
            if hasattr(self, 'num_classes') and self.num_classes > 1:
                return np.empty((0, self.num_classes))
            else:
                return np.empty((0,))

        try:
            X_scaled = self.scaler.transform(X)
        except Exception as e:
            logger.error(f"概率预测时数据缩放失败: {e}")
            if hasattr(self, 'num_classes') and self.num_classes > 1:
                return np.empty((0, self.num_classes))
            else:
                return np.empty((0,))

        if self.model_type == 'lstm':
            X_scaled_reshaped = self._reshape_data_for_lstm(X_scaled, timesteps=self.lstm_timesteps)
            if X_scaled_reshaped.shape[0] == 0:
                logger.warning("LSTM概率预测：重塑后的数据为空。返回空预测。")
                if hasattr(self, 'num_classes') and self.num_classes > 1:
                    return np.empty((0, self.num_classes))
                else:
                    return np.empty((0,1)) 
            
            probabilities = self.model.predict(X_scaled_reshaped)
            return probabilities
        else:
            if hasattr(self.model, 'predict_proba'):
                return self.model.predict_proba(X_scaled)
            else:
                logger.warning(f"模型 {self.model_type} 没有 predict_proba 方法。返回空数组。")
                if hasattr(self, 'num_classes') and self.num_classes > 1:
                     return np.empty((X_scaled.shape[0], self.num_classes)) 
                else:
                    # For binary or regression-like outputs where predict_proba might not be standard
                    # or if num_classes is not well-defined for this path.
                    # Return an empty 1D array as a fallback, or (N,1) if that's more appropriate.
                    return np.empty((X_scaled.shape[0],))

        
    def save_model(self) -> Tuple[Optional[Path], Optional[Path]]:
        """保存训练好的模型和scaler到文件。"""
        if self.model is None:
            logger.warning("模型未训练，无法保存。")
            return None, None

        # 创建模型保存的特定时间戳目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_save_dir = self.output_dir / f"{self.model_type}_{timestamp}"
        model_save_dir.mkdir(parents=True, exist_ok=True)

        model_path = None
        scaler_path = None

        try:
            if self.model_type == 'lstm':
                model_path = model_save_dir / f"model_{self.model_type}_{timestamp}.keras"
                self.model.save(model_path)
                logger.info(f"LSTM模型已保存到: {model_path}")
            else:
                model_path = model_save_dir / f"model_{self.model_type}_{timestamp}.joblib"
                joblib.dump(self.model, model_path)
                logger.info(f"模型已保存到: {model_path}")

            if self.scaler:
                    scaler_path = model_save_dir / f"scaler_{self.model_type}_{timestamp}.pkl"
                    joblib.dump(self.scaler, scaler_path)
                    logger.info(f"Scaler已保存到: {scaler_path}")
            else:
                    logger.warning("Scaler未定义，无法保存。")
                    scaler_path = None
                    
            # 保存模型信息（可选，但推荐）
            info_path = model_save_dir / f"model_info_{self.model_type}_{timestamp}.json"
            model_info = {
                "model_type": self.model_type,
                "timestamp": timestamp,
                "best_params": self.best_params,
                "scaler_type": self.scaler_type,
                "lstm_timesteps": self.lstm_timesteps if self.model_type == 'lstm' else None,
                "num_classes": self.num_classes
            }
            with open(info_path, 'w') as f:
                json.dump(model_info, f, indent=4)
            logger.info(f"模型信息已保存到: {info_path}")

        except Exception as e:
            logger.error(f"保存模型或scaler时出错: {str(e)}")
            # Clean up partially saved files if an error occurs
            if model_path and model_path.exists():
                model_path.unlink(missing_ok=True)
            if scaler_path and scaler_path.exists():
                scaler_path.unlink(missing_ok=True)
            if 'info_path' in locals() and info_path.exists():
                info_path.unlink(missing_ok=True) # type: ignore
            return None, None

    def load_model(self, specific_model_path: Optional[Union[str, Path]] = None, model_type: Optional[str] = None):
        """
        从文件加载模型。
        如果提供了 specific_model_path，则直接加载该文件。
        否则，尝试从 self.output_dir 中查找最新的匹配 model_type 的模型。
        """
        load_path: Optional[Path] = None
        loaded_model_type = model_type or self.model_type # Prioritize explicitly passed model_type

        if specific_model_path:
            load_path = Path(specific_model_path)
            if not load_path.exists():
                logger.error(f"指定的模型文件不存在: {load_path}")
                self.model = None
                return
            # Try to infer model_type from path if not provided or inconsistent
            # This logic might need to be more robust based on file naming conventions
            if loaded_model_type is None: # If model_type was not passed and self.model_type is also None (unlikely for class instance)
                match = re.search(r"model_([a-zA-Z0-9_]+)_\\d{8}_\\d{6}\\.(joblib|keras)", load_path.name)
                if match:
                    loaded_model_type = match.group(1)
                    logger.info(f"从路径推断模型类型为: {loaded_model_type}")
                else:
                    logger.warning(f"无法从路径 {load_path.name} 推断模型类型。")
        else:
            # Find the latest model if no specific path is given
            latest_model_file, found_model_type = self._find_latest_model_files(f"model_{loaded_model_type}_", ".joblib" if loaded_model_type != 'lstm' else ".keras")
            if latest_model_file:
                load_path = latest_model_file
                if found_model_type: loaded_model_type = found_model_type # Update with found type
            else:
                logger.warning(f"在 {self.output_dir} 中未找到 {loaded_model_type} 类型的模型文件。")
                self.model = None
                return

        if load_path and load_path.exists():
            try:
                logger.info(f"开始加载模型: {load_path} (类型: {loaded_model_type})")
                if loaded_model_type == 'lstm':
                    # 加载自定义对象，如果F1Score已注册则Keras会自动处理
                    custom_objects = {}
                    if 'F1Score' in tf.keras.utils.get_custom_objects():
                         custom_objects['F1Score'] = tf.keras.utils.get_custom_objects()['F1Score']
                    
                    self.model = tf.keras.models.load_model(load_path, custom_objects=custom_objects, compile=False)
                    # Optionally re-compile if needed, e.g., if optimizer state is important and not saved
                    # Or if you need to change the optimizer/loss after loading
                    # self.model.compile(optimizer=Adam(learning_rate=self.lstm_learning_rate), loss=..., metrics=[...])
                    logger.info(f"LSTM模型已成功加载: {load_path}")
                else:
                    self.model = joblib.load(load_path)
                    logger.info(f"模型已成功加载: {load_path}")
                
                self.model_type = loaded_model_type # Update the instance's model_type to what was loaded

                # 尝试加载相关的模型信息文件
                info_filename_stem = load_path.stem.replace("model_", "model_info_")
                info_path = load_path.parent / f"{info_filename_stem}.json"
                if info_path.exists():
                    try:
                        with open(info_path, 'r') as f:
                            model_info = json.load(f)
                        self.best_params = model_info.get("best_params")
                        # self.scaler_type = model_info.get("scaler_type", self.scaler_type) # Be careful with overriding
                        self.lstm_timesteps = model_info.get("lstm_timesteps", self.lstm_timesteps)
                        self.num_classes = model_info.get("num_classes", self.num_classes)
                        logger.info(f"相关的模型信息已从 {info_path} 加载。")
                    except Exception as e_info:
                        logger.warning(f"加载模型信息文件 {info_path} 失败: {e_info}")
                else:
                    logger.info(f"未找到相关的模型信息文件 {info_path}。")

            except Exception as e:
                logger.error(f"加载模型 {load_path} 时发生错误: {str(e)}")
                self.model = None
        else:
            logger.info("没有有效的模型路径可供加载。")
            self.model = None # Ensure model is None if loading fails

    def load_scaler(self, specific_scaler_path: Optional[Union[str, Path]] = None, model_path_for_scaler_ref: Optional[Path] = None):
        """
        从文件加载scaler。
        - 如果提供了 specific_scaler_path，则直接加载。
        - 否则，如果提供了 model_path_for_scaler_ref，则尝试从该模型相关的scaler文件加载。
        - 再否则，尝试从 self.output_dir 查找最新的匹配 self.model_type 的scaler。
        """
        load_path: Optional[Path] = None

        if specific_scaler_path:
            load_path = Path(specific_scaler_path)
            if not load_path.exists():
                logger.error(f"指定的scaler文件不存在: {load_path}")
                self._set_scaler() # Fallback to default scaler
                return
        elif model_path_for_scaler_ref:
            # Try to infer scaler path from the model_path
            # Assumes scaler is in the same directory and follows naming convention
            model_p = Path(model_path_for_scaler_ref)
            scaler_filename_stem = model_p.stem.replace("model_", "scaler_")
            inferred_scaler_path = model_p.parent / f"{scaler_filename_stem}.pkl" # Assuming .pkl for scalers
            if inferred_scaler_path.exists():
                load_path = inferred_scaler_path
                logger.info(f"根据模型路径推断出Scaler路径: {load_path}")
        else:
                logger.warning(f"未找到与模型 {model_p.name} 相关的Scaler文件 {inferred_scaler_path}。将尝试查找最新。")
                # Fall through to find latest generic scaler
        
        if not load_path: # If still no load_path, try to find the latest one
            latest_scaler_file, _ = self._find_latest_model_files(f"scaler_{self.model_type}_", ".pkl")
            if latest_scaler_file:
                load_path = latest_scaler_file
            else:
                logger.warning(f"在 {self.output_dir} 中未找到 {self.model_type} 类型的Scaler文件。将使用新初始化的Scaler。")
                self._set_scaler() # Fallback to default scaler if no specific or latest found
                return
        
        if load_path and load_path.exists():
            try:
                self.scaler = joblib.load(load_path)
                logger.info(f"Scaler已成功加载: {load_path}")
            except Exception as e:
                logger.error(f"加载Scaler {load_path} 时发生错误: {str(e)}。将使用新初始化的Scaler。")
                self._set_scaler() # Fallback to default
        else:
            if not specific_scaler_path and not model_path_for_scaler_ref: # Only log this if we weren't trying a specific path
                 logger.info("没有有效的Scaler路径可供加载。将使用新初始化的Scaler。")
            self._set_scaler() # Fallback

    def _find_latest_model_files(self, prefix: str, extension: str) -> Tuple[Optional[Path], Optional[str]]:
        """查找最新的模型或scaler文件"""
        try:
            files = list(self.output_dir.glob(f"{prefix}*{extension}"))
            if not files:
                # If exact prefix match fails, try a more generic search within subdirs
                # This is for cases where output_dir is like 'models/BTCUSDT/1h/'
                # and files are in 'models/BTCUSDT/1h/xgb_YYYYMMDD_HHMMSS/model_xgb_....joblib'
                potential_model_type_dirs = [d for d in self.output_dir.iterdir() if d.is_dir() and d.name.startswith(self.model_type)]
                if potential_model_type_dirs:
                    latest_type_dir = sorted(potential_model_type_dirs, key=os.path.getmtime, reverse=True)[0]
                    files = list(latest_type_dir.glob(f"{prefix}*{extension}"))


            if not files:
                logger.debug(f"在 {self.output_dir} (或其子目录 {self.model_type}_*) 中未找到与 '{prefix}*{extension}' 匹配的文件。")
                return None, None

            latest_file = sorted(files, key=os.path.getmtime, reverse=True)[0]
            
            # Try to extract model_type from filename if it was part of the prefix search logic
            # Example prefix: "model_" or "scaler_", filename: "model_xgb_date.joblib"
            # This part is tricky if prefix itself contains a model_type.
            # Let's assume prefix might be generic like "model_" and we infer from filename.
            # Example: model_xgb_... -> xgb; scaler_lgb_... -> lgb
            # If prefix was "model_xgb_", then this regex is not needed to find 'xgb'
            
            # Simplified: if the prefix was generic (e.g. "model_"), try to find type
            found_type = None
            if prefix == "model_" or prefix == "scaler_": # Only if prefix was generic
                match = re.search(r"^(?:model|scaler)_([a-zA-Z0-9_]+?)_\\d{8}_\\d{6}", filename_stem)
                if match:
                    found_type = match.group(1)
            elif self.model_type and prefix.startswith(f"model_{self.model_type}") or prefix.startswith(f"scaler_{self.model_type}"):
                 found_type = self.model_type # If prefix already had the type, use that.

            logger.info(f"找到最新文件: {latest_file} (推断类型: {found_type or '未知'})")
            return latest_file, found_type
        except Exception as e:
            logger.error(f"查找最新文件时发生错误 (prefix='{prefix}', ext='{extension}'): {e}")
            return None, None

    def _reshape_data_for_lstm(self, data: np.ndarray, y: Optional[Union[pd.Series, np.ndarray]] = None, timesteps: Optional[int] = None) -> Union[Tuple[np.ndarray, np.ndarray], np.ndarray]:
        """
        将数据重塑为LSTM期望的 [样本数, 时间步长, 特征数] 格式。
        
        参数:
            data (np.ndarray): 输入特征数据。
            y (Optional[Union[pd.Series, np.ndarray]]): 目标变量，如果提供，则一并处理。
            timesteps (Optional[int]): 要使用的时间步长。如果为None，则使用 self.lstm_timesteps。
            
        返回:
            如果提供了y，则返回 (X_reshaped, y_reshaped) 的元组。
            否则，仅返回 X_reshaped。
        """
        current_timesteps = timesteps if timesteps is not None else self.lstm_timesteps
        
        if data.ndim == 1: # 如果data是一维的 (例如单个特征)
            data = data.reshape(-1, 1)
            
        if data.shape[0] < current_timesteps:
            logger.warning(f"数据点 ({data.shape[0]}) 少于时间步长 ({current_timesteps})，无法为LSTM重塑。返回空数组。")
            if y is not None:
                return np.empty((0, current_timesteps, data.shape[1])), np.empty((0,))
            else:
                return np.empty((0, current_timesteps, data.shape[1]))

        X_reshaped = []
        y_reshaped_list = [] if y is not None else None
        
        # data.shape[0] 是总样本数
        # current_timesteps 是每个LSTM序列的长度
        # 我们需要从 data 中创建 data.shape[0] - current_timesteps + 1 个序列
        # 例如：数据 [1,2,3,4,5], timesteps=3
        # 序列1: [1,2,3] -> y_target for 3 (or 4 if predicting next)
        # 序列2: [2,3,4] -> y_target for 4 (or 5 if predicting next)
        # 序列3: [3,4,5] -> y_target for 5 (or 6 if predicting next)
        
        # 确保 y (如果提供) 是 numpy array
        if y is not None and isinstance(y, pd.Series):
            y_np = y.values
        elif y is not None:
            y_np = y
        else:
            y_np = None

        for i in range(data.shape[0] - current_timesteps + 1):
            X_reshaped.append(data[i:(i + current_timesteps)])
            if y_np is not None:
                # y 的目标应该是对应于序列末尾时间点的值
                # 例如，使用 X[t-timesteps : t] 来预测 y[t-1] 或 y[t]
                # 这里假设 y 的值与 X 的最后一个时间步对齐。
                # 如果预测未来，y_idx 应该是 i + current_timesteps -1 + forecast_horizon
                y_idx = i + current_timesteps - 1 
                if y_idx < len(y_np):
                     y_reshaped_list.append(y_np[y_idx]) # type: ignore
                else:
                    # This case should ideally not happen if loops correctly
                    # or if y is shorter than X implies an issue.
                    # For now, we might just stop or pad, but better to ensure y is long enough.
                    logger.warning(f"LSTM重塑：y的索引 ({y_idx}) 超出范围 (长度 {len(y_np)})。这可能表示数据不匹配。")
                    # To prevent error, we might skip this sample, or handle it as needed.
                    # For now, let's assume this loop structure is mostly for training where X and y align.
                    # If y_reshaped_list was used, then X_reshaped last element should be popped.
                    X_reshaped.pop() # Remove the X if y runs out
                    break # Stop processing further if y runs out
        
        X_reshaped_np = np.array(X_reshaped)
        
        if y_reshaped_list is not None:
            y_reshaped_np = np.array(y_reshaped_list)
            # 确保 X 和 y 的样本数量在重塑后仍然匹配
            if X_reshaped_np.shape[0] != y_reshaped_np.shape[0]:
                min_len = min(X_reshaped_np.shape[0], y_reshaped_np.shape[0])
                logger.warning(f"LSTM重塑后X和y的样本数量不匹配 ({X_reshaped_np.shape[0]} vs {y_reshaped_np.shape[0]})。将截断到 {min_len}。")
                X_reshaped_np = X_reshaped_np[:min_len]
                y_reshaped_np = y_reshaped_np[:min_len]
            return X_reshaped_np, y_reshaped_np
        else:
            return X_reshaped_np

    def _build_lstm_model(self, input_shape: Tuple[int, int], 
                            units: int, 
                            dropout: float, 
                            recurrent_dropout: float, 
                            learning_rate: float, 
                            loss_function: Optional[str] = None):
        """构建LSTM模型"""
        model = Sequential()
        model.add(LSTM(units=units, 
                       input_shape=input_shape, 
                       dropout=dropout, 
                       recurrent_dropout=recurrent_dropout,
                       kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4), # 添加L1/L2正则化
                       return_sequences=False # 最后一层LSTM不返回序列，除非有更多LSTM层
                       ))
        # 可以根据需要添加更多层
        # model.add(Dropout(0.2))
        # model.add(Dense(units // 2, activation='relu'))

        # 输出层
        if self.num_classes > 1: # 多分类
            model.add(Dense(self.num_classes, activation='softmax'))
            # 确定损失函数
            if loss_function is None:
                loss = 'categorical_crossentropy'
            else:
                loss = loss_function
            metrics = ['accuracy', F1Score(name='f1_score')] # 使用自定义的F1Score
        else: # 二分类 (或回归，但这里假设是分类)
            model.add(Dense(1, activation='sigmoid'))
            if loss_function is None:
                loss = 'binary_crossentropy'
            else:
                loss = loss_function
            metrics = ['accuracy', tf.keras.metrics.Precision(), tf.keras.metrics.Recall(), F1Score(name='f1_score')]

        optimizer = Adam(learning_rate=learning_rate)
        model.compile(optimizer=optimizer, loss=loss, metrics=metrics)
        
        logger.info("LSTM模型构建完成:")
        model.summary(print_fn=logger.info)
        return model

    def plot_model_performance(self, y_true=None, y_prob=None, cv_scores=None, model_plot_dir=None):
        """绘制模型性能相关的图表"""
        if model_plot_dir is None:
            model_plot_dir = Path('model_plots')
        model_plot_dir = Path(model_plot_dir)
        model_plot_dir.mkdir(parents=True, exist_ok=True)
        
        plot_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # y_true needs to be binarized for each class if using one-vs-rest
        if y_true is not None and y_prob is not None and len(y_true) > 0 and y_prob.size > 0:
            try:
                plt.figure(figsize=(10, 8))
                if hasattr(self, 'num_classes') and self.num_classes > 1:
                    # 确保y_prob是二维数组
                    if y_prob.ndim == 1:
                        # 如果是一维数组，需要将其转换为独热编码形式
                        y_prob_reshaped = np.zeros((len(y_prob), self.num_classes))
                        for i in range(len(y_prob)):
                            y_prob_reshaped[i, int(y_prob[i])] = 1
                        y_prob = y_prob_reshaped

                    # Multiclass ROC: One-vs-Rest
                    for i in range(self.num_classes):
                        # Binarize y_true for class i vs rest
                        y_true_class_i = (y_true == i).astype(int)
                        # Get probabilities for class i
                        y_prob_class_i = y_prob[:, i]
                        
                        fpr, tpr, _ = roc_curve(y_true_class_i, y_prob_class_i)
                        roc_auc = auc(fpr, tpr)
                        plt.plot(fpr, tpr, lw=2, label=f'类别 {i} (AUC = {roc_auc:.2f})')

                    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
                    plt.xlim([0.0, 1.0])
                    plt.ylim([0.0, 1.05])
                    plt.xlabel('假正例率 (FPR)')
                    plt.ylabel('真正例率 (TPR)')
                    plt.title(f'{self.model_type.upper()} ROC 曲线 (One-vs-Rest)')
                    plt.legend(loc="lower right")
                elif hasattr(self, 'num_classes') and self.num_classes == 1:  # Binary case
                    # Ensure y_prob is 1D for binary classification if it came from predict_proba[:,1] or similar
                    y_prob_binary = y_prob.ravel() if y_prob.ndim > 1 else y_prob
                    fpr, tpr, _ = roc_curve(y_true, y_prob_binary)
                    roc_auc = auc(fpr, tpr)
                    plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.2f})')
                    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
                    plt.xlim([0.0, 1.0])
                    plt.ylim([0.0, 1.05])
                    plt.xlabel('假正例率 (FPR)')
                    plt.ylabel('真正例率 (TPR)')
                    plt.title(f'{self.model_type.upper()} ROC 曲线')
                    plt.legend(loc="lower right")
                else:
                    logger.warning("num_classes未定义或无效，无法确定ROC曲线类型。")
                
                plt.savefig(model_plot_dir / f"roc_curve_{plot_timestamp}.png")
                plt.close()
                logger.info(f"ROC曲线已保存到: {model_plot_dir}")
            except Exception as e:
                logger.error(f"生成ROC曲线时出错: {e}")
        else:
            logger.info("真实标签或预测概率为空，跳过ROC曲线生成。")

        # 3. 特征重要性图 (如果可用)
        if self.feature_importance is not None and not self.feature_importance.empty:
            try:
                plt.figure(figsize=(12, 8))
                # 只显示Top N个特征
                top_n = min(len(self.feature_importance), 20)
                self.feature_importance.head(top_n).plot(kind='barh')
                plt.title(f'{self.model_type.upper()} 特征重要性 (Top {top_n})')
                plt.gca().invert_yaxis()  # 重要性高的在顶部
                plt.tight_layout()
                plt.savefig(model_plot_dir / f"feature_importance_{plot_timestamp}.png")
                plt.close()
                logger.info(f"特征重要性图已保存到: {model_plot_dir}")
            except Exception as e:
                logger.error(f"生成特征重要性图时出错: {e}")
        else:
            logger.info(f"模型 {self.model_type} 无可用特征重要性信息，跳过绘图。")
        
        # 4. CV 分数图
        if cv_scores and isinstance(cv_scores, dict) and any(isinstance(v, list) for v in cv_scores.values()):
            try:
                for metric_name, scores_list in cv_scores.items():
                    if isinstance(scores_list, list) and all(isinstance(s, (int, float)) for s in scores_list):
                        plt.figure(figsize=(8, 5))
                        plt.plot(range(1, len(scores_list) + 1), scores_list, marker='o', linestyle='-')
                        plt.title(f'{self.model_type.upper()} CV - {metric_name} per Fold')
                        plt.xlabel('Fold')
                        plt.ylabel(metric_name)
                        plt.xticks(range(1, len(scores_list) + 1))
                        plt.grid(True)
                        plt.tight_layout()
                        plt.savefig(model_plot_dir / f"cv_scores_{metric_name}_{plot_timestamp}.png")
                        plt.close()
                logger.info(f"详细CV分数图已保存到: {model_plot_dir}")
            except Exception as e:
                logger.error(f"生成详细CV分数图时出错: {e}")
        elif cv_scores and isinstance(cv_scores, dict):  # Fallback to summary bar chart
            try:
                simple_scores = {k: v for k, v in cv_scores.items() if isinstance(v, (int, float, np.number))}
                if simple_scores:
                    metrics_df = pd.DataFrame.from_dict(simple_scores, orient='index', columns=['Score']).sort_values(by='Score', ascending=False)
                    if not metrics_df.empty:
                        plt.figure(figsize=(10, 6))
                        metrics_df.plot(kind='bar', legend=None)
                        plt.title(f'{self.model_type.upper()} 平均交叉验证指标')
                        plt.ylabel('分数')
                        plt.xticks(rotation=45, ha='right')
                        plt.tight_layout()
                        plt.savefig(model_plot_dir / f"cv_metrics_summary_{plot_timestamp}.png")
                        plt.close()
                        logger.info(f"CV指标摘要图已保存到: {model_plot_dir}")
                else:
                    logger.info("未找到简单的CV平均分数用于绘制摘要图。")
            except Exception as e:
                logger.error(f"生成CV指标摘要图时出错: {e}")
        else:
            logger.info("CV分数数据为空或格式不正确，跳过CV分数图生成。")

if __name__ == "__main__":
    from data_processor import DataProcessor
    
    try:
        # --- 配置 DataProcessor ---
        # 定义K线时间周期列表，现在使用30分钟作为基础周期，并结合其他周期
        # 如果您之前有特定的多个15分钟周期，请根据需要调整
        # 例如，如果之前是 [15m, 1h, 4h]，现在可以是 [30m, 1h, 4h]
        # 或者如果您希望所有基于15分钟K线，那么target_interval也应该修改
        # 假设 DataProcessor 的 __init__ 接受 target_interval 参数，或者它内部基于 intervals[0]
        # 如果 DataProcessor 接受 target_interval，并且它原来是15分钟，那么现在也应该是30分钟
        # 此处我们假设DataProcessor的第一个interval是主要的，或者它有单独的target_interval参数
        
        # 假设原始的 intervals = [Client.KLINE_INTERVAL_15MINUTE, Client.KLINE_INTERVAL_1HOUR, Client.KLINE_INTERVAL_4HOUR]
        # 新的 intervals 应该类似如下：
        dp_intervals = [Client.KLINE_INTERVAL_30MINUTE, Client.KLINE_INTERVAL_1HOUR, Client.KLINE_INTERVAL_4HOUR]
        
        processor = DataProcessor(
            intervals=dp_intervals, # 使用更新后的intervals
            future_periods=2, 
            atr_multiplier=0.75, # 这个值是之前修改的
            # target_interval=Client.KLINE_INTERVAL_30MINUTE # 如果有此参数，也需要更新
        )
        
        symbol = 'BTCUSDT'
        # start_date = '1 Jan, 2023' 
        # end_date = '31 Jan, 2023'
        start_date = '1 Jan, 2021' # 修改开始日期
        if platform.system().lower() == 'windows':
            end_date = datetime.now().strftime('%#d %b, %Y') # Windows 兼容格式
        else:
            end_date = datetime.now().strftime('%-d %b, %Y') # 其他系统格式

        logger.info(f"开始为符号 {symbol} 处理从 {start_date} 到 {end_date} 的数据...")
        
        logger.info("正在准备数据...")
        X, y, _ = processor.prepare_data(
            symbol=symbol,
            start_str=start_date,
            end_str=end_date
            # mode='train' is likely default or handled by DataProcessor if not specified
        )
        
        if X.empty or y.empty:
            logger.error(f"数据准备失败或返回为空，无法继续进行训练。X: {X.shape}, y: {y.shape}")
        else:
            trainer = ModelTrainer(
                model_type='ensemble',
                n_splits=5,
                output_dir='trained_models',
                use_optuna=False # Setting to False for quicker test run if needed
            )
            results = trainer.train(X, y)
            
            saved_model_path, saved_scaler_path = trainer.save_model()

            if saved_model_path and saved_scaler_path:
                logger.info(f"模型和Scaler已保存。模型: {saved_model_path}, Scaler: {saved_scaler_path}")

                loader_trainer_instance = ModelTrainer(
                    model_type=trainer.model_type, 
                    output_dir=trainer.output_dir
                )

                model_loaded_successfully = loader_trainer_instance.load_model(model_path=saved_model_path)

                if model_loaded_successfully:
                    scaler_loaded_successfully = loader_trainer_instance.load_scaler(scaler_filename=saved_scaler_path.name)

                    if scaler_loaded_successfully:
                        logger.info("模型和Scaler已成功加载到新的实例中。")
                        if not X.empty:
                            X_test_sample = X.head()
                            test_pred = loader_trainer_instance.predict(X_test_sample) # predict handles scaling
                            logger.info(f"加载后的模型验证预测结果 (前{len(X_test_sample)}条): {test_pred}")
                        else:
                            logger.warning("X为空，无法进行加载后的模型验证预测。")
                    else:
                        logger.error("加载Scaler失败。")
                else:
                    logger.error("加载模型失败。")
            else:
                logger.error("保存模型或Scaler失败，无法进行加载测试。")
        
    except Exception as e:
        logger.error(f"测试代码执行时发生错误: {str(e)}", exc_info=True) # Added exc_info for more details