#!/usr/bin/env python3
"""
无TensorFlow依赖的训练脚本
专注于传统机器学习模型
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 只导入非TensorFlow相关的模块
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer, FeatureConfig

# 手动导入sklearn模型，避免model_trainer中的TensorFlow
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import TimeSeriesSplit, cross_validate
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score
import joblib
import optuna

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False
    print("警告: XGBoost未安装")

try:
    import lightgbm as lgb
    HAS_LGB = True
except ImportError:
    HAS_LGB = False
    print("警告: LightGBM未安装")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleModelTrainer:
    """简化的模型训练器，不依赖TensorFlow"""
    
    def __init__(self, model_type='rf', use_optuna=True, n_trials=50):
        self.model_type = model_type
        self.use_optuna = use_optuna
        self.n_trials = n_trials
        self.model = None
        self.scaler = RobustScaler()
        self.label_encoder = LabelEncoder()
        
    def _create_model(self, trial=None, num_classes=None):
        """创建模型"""
        if self.model_type == 'rf':
            if trial:
                return RandomForestClassifier(
                    n_estimators=trial.suggest_int('n_estimators', 100, 500),
                    max_depth=trial.suggest_int('max_depth', 3, 20),
                    min_samples_split=trial.suggest_int('min_samples_split', 2, 20),
                    min_samples_leaf=trial.suggest_int('min_samples_leaf', 1, 10),
                    random_state=42,
                    n_jobs=-1
                )
            else:
                return RandomForestClassifier(n_estimators=200, random_state=42, n_jobs=-1)

        elif self.model_type == 'gb':
            if trial:
                return GradientBoostingClassifier(
                    n_estimators=trial.suggest_int('n_estimators', 100, 300),
                    max_depth=trial.suggest_int('max_depth', 3, 10),
                    learning_rate=trial.suggest_float('learning_rate', 0.01, 0.3),
                    subsample=trial.suggest_float('subsample', 0.8, 1.0),
                    random_state=42
                )
            else:
                return GradientBoostingClassifier(n_estimators=200, random_state=42)

        elif self.model_type == 'xgb' and HAS_XGB:
            # XGBoost需要指定类别数量来避免类别不一致问题
            base_params = {
                'random_state': 42,
                'n_jobs': -1,
                'eval_metric': 'mlogloss',
                'objective': 'multi:softprob'
            }
            if num_classes:
                base_params['num_class'] = num_classes

            if trial:
                base_params.update({
                    'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'subsample': trial.suggest_float('subsample', 0.8, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0)
                })
            return xgb.XGBClassifier(**base_params)

        elif self.model_type == 'lgb' and HAS_LGB:
            base_params = {
                'random_state': 42,
                'n_jobs': -1,
                'verbose': -1,
                'objective': 'multiclass'
            }
            if num_classes:
                base_params['num_class'] = num_classes

            if trial:
                base_params.update({
                    'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'subsample': trial.suggest_float('subsample', 0.8, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0)
                })
            return lgb.LGBMClassifier(**base_params)
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
    
    def _objective(self, trial, X, y, num_classes):
        """Optuna优化目标函数"""
        model = self._create_model(trial, num_classes)

        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=3)
        scores = []

        for train_idx, val_idx in tscv.split(X):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

            # 检查训练集是否包含所有类别
            unique_train_classes = set(y_train.unique())
            unique_val_classes = set(y_val.unique())

            # 如果验证集包含训练集中没有的类别，跳过这个fold
            if not unique_val_classes.issubset(unique_train_classes):
                continue

            # 训练模型
            model.fit(X_train, y_train)

            # 预测和评分
            y_pred = model.predict(X_val)
            score = accuracy_score(y_val, y_pred)
            scores.append(score)

        # 如果没有有效的分数，返回一个较低的值
        return np.mean(scores) if scores else 0.5
    
    def train(self, X, y):
        """训练模型"""
        logger.info(f"开始训练 {self.model_type.upper()} 模型...")

        # 数据预处理
        X_clean = self._clean_data(X)
        y_clean = self._encode_labels(y)

        # 确保X和y的索引一致
        common_index = X_clean.index.intersection(y_clean.index)
        X_clean = X_clean.loc[common_index]
        y_clean = y_clean.loc[common_index]

        # 获取类别数量
        num_classes = len(y_clean.unique())
        logger.info(f"检测到 {num_classes} 个类别: {sorted(y_clean.unique())}")
        logger.info(f"有效样本数: {len(X_clean)}")

        # 特征缩放
        X_scaled = pd.DataFrame(
            self.scaler.fit_transform(X_clean),
            columns=X_clean.columns,
            index=X_clean.index
        )

        # 超参数优化
        if self.use_optuna:
            logger.info(f"开始Optuna超参数优化 ({self.n_trials} 次试验)...")
            study = optuna.create_study(direction='maximize')
            study.optimize(
                lambda trial: self._objective(trial, X_scaled, y_clean, num_classes),
                n_trials=self.n_trials
            )

            # 使用最佳参数创建模型
            self.model = self._create_model(study.best_trial, num_classes)
            logger.info(f"最佳参数: {study.best_params}")
        else:
            self.model = self._create_model(num_classes=num_classes)

        # 最终训练
        self.model.fit(X_scaled, y_clean)

        # 交叉验证评估
        results = self._evaluate_model(X_scaled, y_clean)

        logger.info(f"模型训练完成! 准确率: {results['accuracy']:.4f}")
        return results
    
    def _clean_data(self, X):
        """清理数据"""
        X_clean = X.copy()
        
        # 处理无穷大值
        X_clean.replace([np.inf, -np.inf], np.nan, inplace=True)
        
        # 处理NaN值
        for col in X_clean.columns:
            if X_clean[col].isna().any():
                if 'return' in col.lower() or 'change' in col.lower():
                    X_clean[col].fillna(0, inplace=True)
                else:
                    X_clean[col].fillna(X_clean[col].median(), inplace=True)
        
        return X_clean
    
    def _encode_labels(self, y):
        """编码标签 - 确保连续的类别标签"""
        # 先移除NaN值
        y_clean = y.dropna()

        # 使用LabelEncoder确保标签从0开始连续
        y_encoded = self.label_encoder.fit_transform(y_clean)

        # 创建映射关系用于调试
        original_classes = self.label_encoder.classes_
        encoded_classes = range(len(original_classes))

        logger.info(f"标签映射: {dict(zip(original_classes, encoded_classes))}")

        return pd.Series(y_encoded, index=y_clean.index)
    
    def _evaluate_model(self, X, y):
        """评估模型"""
        tscv = TimeSeriesSplit(n_splits=5)
        
        scoring = ['accuracy', 'precision_macro', 'recall_macro', 'f1_macro']
        cv_results = cross_validate(self.model, X, y, cv=tscv, scoring=scoring)
        
        results = {
            'accuracy': cv_results['test_accuracy'].mean(),
            'precision': cv_results['test_precision_macro'].mean(),
            'recall': cv_results['test_recall_macro'].mean(),
            'f1': cv_results['test_f1_macro'].mean(),
            'accuracy_std': cv_results['test_accuracy'].std(),
            'cv_scores': cv_results
        }
        
        return results
    
    def save_model(self, output_dir='models'):
        """保存模型"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        model_path = output_dir / f"model_{self.model_type}_{timestamp}.joblib"
        scaler_path = output_dir / f"scaler_{self.model_type}_{timestamp}.joblib"
        encoder_path = output_dir / f"encoder_{self.model_type}_{timestamp}.joblib"
        
        joblib.dump(self.model, model_path)
        joblib.dump(self.scaler, scaler_path)
        joblib.dump(self.label_encoder, encoder_path)
        
        logger.info(f"模型已保存到: {model_path}")
        return model_path, scaler_path, encoder_path

def train_crypto_model_simple(symbol='BTCUSDT', model_type='xgb', days_back=90):
    """简化的加密货币模型训练"""
    
    print(f"🚀 开始训练 {symbol} {model_type.upper()} 模型...")
    
    try:
        # 1. 获取数据
        print("📊 获取数据...")
        start_date = (datetime.now() - pd.Timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, '1h', start_date)
        print(f"✅ 获取到 {len(df)} 条数据")
        
        # 2. 特征工程
        print("🔧 特征工程...")
        config = FeatureConfig(
            ma_periods=[5, 10, 20, 50],
            rsi_periods=[14, 24],
            prediction_window=24
        )
        engineer = FeatureEngineer(config=config)
        df_features = engineer.create_features(df)
        
        # 分离特征和目标
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始列
        original_cols = ['open', 'high', 'low', 'close', 'volume']
        X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
        
        print(f"✅ 特征: {X.shape[1]} 个, 样本: {len(y)} 个, 类别: {len(y.unique())} 个")
        
        # 3. 训练模型
        print("🎯 训练模型...")
        trainer = SimpleModelTrainer(
            model_type=model_type,
            use_optuna=True,
            n_trials=30
        )
        
        results = trainer.train(X, y)
        
        # 4. 显示结果
        print("\n" + "="*50)
        print("🎉 训练完成!")
        print("="*50)
        print(f"准确率: {results['accuracy']:.4f} ± {results['accuracy_std']:.4f}")
        print(f"精确率: {results['precision']:.4f}")
        print(f"召回率: {results['recall']:.4f}")
        print(f"F1分数: {results['f1']:.4f}")
        
        # 5. 保存模型
        model_path, scaler_path, encoder_path = trainer.save_model()
        print(f"\n💾 模型已保存:")
        print(f"   模型: {model_path}")
        print(f"   缩放器: {scaler_path}")
        print(f"   编码器: {encoder_path}")
        
        return trainer, results
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        raise

if __name__ == "__main__":
    import sys
    
    # 默认参数
    symbol = 'BTCUSDT'
    model_type = 'xgb'
    
    # 从命令行参数获取
    if len(sys.argv) > 1:
        symbol = sys.argv[1]
    if len(sys.argv) > 2:
        model_type = sys.argv[2]
    
    # 检查模型类型可用性
    if model_type == 'xgb' and not HAS_XGB:
        print("XGBoost未安装，使用随机森林替代")
        model_type = 'rf'
    elif model_type == 'lgb' and not HAS_LGB:
        print("LightGBM未安装，使用随机森林替代")
        model_type = 'rf'
    
    # 执行训练
    train_crypto_model_simple(symbol, model_type)
