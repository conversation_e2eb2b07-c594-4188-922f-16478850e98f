#!/usr/bin/env python3
"""
增强风险控制机制
为剥头皮交易系统提供全面的风险管理功能
"""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """风险等级"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

@dataclass
class RiskEvent:
    """风险事件"""
    timestamp: datetime
    event_type: str
    risk_level: RiskLevel
    description: str
    current_value: float
    threshold: float
    action_taken: str

class EnhancedRiskManager:
    """增强风险控制管理器"""
    
    def __init__(self, initial_balance: float, max_daily_loss_pct: float = 0.05):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        
        # 风险限制参数
        self.max_daily_loss_pct = max_daily_loss_pct  # 5%日亏损限制
        self.max_daily_loss_amount = initial_balance * max_daily_loss_pct
        self.max_consecutive_losses = 5  # 连续亏损限制
        self.min_balance_pct = 0.30  # 最低余额30%
        self.max_position_risk_pct = 0.025  # 单笔最大风险2.5%
        
        # 异常检测参数
        self.max_price_volatility = 0.02  # 2%异常波动
        self.max_slippage_pct = 0.001  # 0.1%最大滑点
        self.network_timeout_threshold = 10  # 10秒网络超时
        
        # 状态跟踪
        self.consecutive_losses = 0
        self.daily_loss = 0.0
        self.last_reset_date = datetime.now().date()
        self.emergency_stop = False
        self.trading_paused = False
        self.pause_until = None
        
        # 风险事件记录
        self.risk_events: List[RiskEvent] = []
        self.network_errors = 0
        self.last_successful_request = datetime.now()
        
        # 价格监控
        self.price_history = []
        self.last_price = None
        self.price_update_time = None
        
    def reset_daily_counters(self):
        """重置日计数器"""
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.daily_loss = 0.0
            self.last_reset_date = current_date
            self.consecutive_losses = 0  # 可选：每日重置连续亏损
            logger.info("📅 日风险计数器已重置")
    
    def update_balance(self, new_balance: float):
        """更新余额"""
        self.reset_daily_counters()
        
        # 计算日亏损
        balance_change = new_balance - self.current_balance
        if balance_change < 0:
            self.daily_loss += abs(balance_change)
        
        self.current_balance = new_balance
    
    def record_trade_result(self, pnl: float, is_winner: bool):
        """记录交易结果"""
        self.update_balance(self.current_balance + pnl)
        
        if is_winner:
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1
            
            # 检查连续亏损风险
            if self.consecutive_losses >= self.max_consecutive_losses:
                self.trigger_risk_event(
                    "CONSECUTIVE_LOSSES",
                    RiskLevel.HIGH,
                    f"连续亏损{self.consecutive_losses}次",
                    self.consecutive_losses,
                    self.max_consecutive_losses,
                    "暂停交易30分钟"
                )
                self.pause_trading(30)  # 暂停30分钟
    
    def check_daily_loss_limit(self) -> bool:
        """检查日亏损限制"""
        self.reset_daily_counters()
        
        if self.daily_loss >= self.max_daily_loss_amount:
            self.trigger_risk_event(
                "DAILY_LOSS_LIMIT",
                RiskLevel.CRITICAL,
                f"达到日亏损限制",
                self.daily_loss,
                self.max_daily_loss_amount,
                "紧急停止交易"
            )
            self.emergency_stop = True
            return False
        
        return True
    
    def check_minimum_balance(self) -> bool:
        """检查最低余额"""
        min_balance = self.initial_balance * self.min_balance_pct
        
        if self.current_balance < min_balance:
            self.trigger_risk_event(
                "MINIMUM_BALANCE",
                RiskLevel.CRITICAL,
                f"余额低于最低限制",
                self.current_balance,
                min_balance,
                "紧急停止交易"
            )
            self.emergency_stop = True
            return False
        
        return True
    
    def check_position_risk(self, position_value: float) -> bool:
        """检查单笔仓位风险"""
        max_position_value = self.current_balance * self.max_position_risk_pct
        
        if position_value > max_position_value:
            self.trigger_risk_event(
                "POSITION_RISK",
                RiskLevel.HIGH,
                f"单笔仓位风险过大",
                position_value,
                max_position_value,
                "拒绝交易"
            )
            return False
        
        return True
    
    def check_price_volatility(self, current_price: float) -> bool:
        """检查价格异常波动"""
        if self.last_price is None:
            self.last_price = current_price
            self.price_update_time = datetime.now()
            return True
        
        # 计算价格变化
        price_change = abs(current_price - self.last_price) / self.last_price
        
        if price_change > self.max_price_volatility:
            self.trigger_risk_event(
                "PRICE_VOLATILITY",
                RiskLevel.HIGH,
                f"检测到异常价格波动",
                price_change,
                self.max_price_volatility,
                "暂停交易5分钟"
            )
            self.pause_trading(5)  # 暂停5分钟
            
        # 更新价格历史
        self.price_history.append({
            'timestamp': datetime.now(),
            'price': current_price,
            'change': price_change
        })
        
        # 保持最近100个价格记录
        if len(self.price_history) > 100:
            self.price_history.pop(0)
        
        self.last_price = current_price
        self.price_update_time = datetime.now()
        
        return price_change <= self.max_price_volatility
    
    def check_network_health(self) -> bool:
        """检查网络健康状况"""
        # 检查网络超时
        time_since_last = (datetime.now() - self.last_successful_request).total_seconds()
        
        if time_since_last > self.network_timeout_threshold:
            self.trigger_risk_event(
                "NETWORK_TIMEOUT",
                RiskLevel.MEDIUM,
                f"网络连接超时",
                time_since_last,
                self.network_timeout_threshold,
                "暂停交易直到网络恢复"
            )
            return False
        
        # 检查网络错误频率
        if self.network_errors > 10:
            self.trigger_risk_event(
                "NETWORK_ERRORS",
                RiskLevel.HIGH,
                f"网络错误过多",
                self.network_errors,
                10,
                "暂停交易10分钟"
            )
            self.pause_trading(10)
            self.network_errors = 0  # 重置计数器
            return False
        
        return True
    
    def record_network_success(self):
        """记录网络请求成功"""
        self.last_successful_request = datetime.now()
        if self.network_errors > 0:
            self.network_errors = max(0, self.network_errors - 1)  # 逐渐减少错误计数
    
    def record_network_error(self):
        """记录网络错误"""
        self.network_errors += 1
    
    def check_slippage(self, expected_price: float, actual_price: float) -> bool:
        """检查滑点"""
        slippage = abs(actual_price - expected_price) / expected_price
        
        if slippage > self.max_slippage_pct:
            self.trigger_risk_event(
                "HIGH_SLIPPAGE",
                RiskLevel.MEDIUM,
                f"检测到高滑点",
                slippage,
                self.max_slippage_pct,
                "记录警告"
            )
            return False
        
        return True
    
    def pause_trading(self, minutes: int):
        """暂停交易指定分钟"""
        self.trading_paused = True
        self.pause_until = datetime.now() + timedelta(minutes=minutes)
        logger.warning(f"⏸️ 交易已暂停 {minutes} 分钟")
    
    def is_trading_paused(self) -> bool:
        """检查交易是否暂停"""
        if self.trading_paused and self.pause_until:
            if datetime.now() >= self.pause_until:
                self.trading_paused = False
                self.pause_until = None
                logger.info("▶️ 交易暂停已解除")
                return False
            return True
        return False
    
    def can_trade(self) -> Tuple[bool, str]:
        """综合检查是否可以交易"""
        # 检查紧急停止
        if self.emergency_stop:
            return False, "系统紧急停止"
        
        # 检查交易暂停
        if self.is_trading_paused():
            remaining = (self.pause_until - datetime.now()).total_seconds() / 60
            return False, f"交易暂停中，剩余{remaining:.1f}分钟"
        
        # 检查日亏损限制
        if not self.check_daily_loss_limit():
            return False, "达到日亏损限制"
        
        # 检查最低余额
        if not self.check_minimum_balance():
            return False, "余额低于最低限制"
        
        # 检查网络健康
        if not self.check_network_health():
            return False, "网络连接异常"
        
        return True, "可以交易"
    
    def trigger_risk_event(self, event_type: str, risk_level: RiskLevel, 
                          description: str, current_value: float, 
                          threshold: float, action_taken: str):
        """触发风险事件"""
        event = RiskEvent(
            timestamp=datetime.now(),
            event_type=event_type,
            risk_level=risk_level,
            description=description,
            current_value=current_value,
            threshold=threshold,
            action_taken=action_taken
        )
        
        self.risk_events.append(event)
        
        # 根据风险等级记录日志
        if risk_level == RiskLevel.CRITICAL:
            logger.critical(f"🚨 {description}: {current_value:.4f} > {threshold:.4f} - {action_taken}")
        elif risk_level == RiskLevel.HIGH:
            logger.error(f"⚠️ {description}: {current_value:.4f} > {threshold:.4f} - {action_taken}")
        elif risk_level == RiskLevel.MEDIUM:
            logger.warning(f"⚡ {description}: {current_value:.4f} > {threshold:.4f} - {action_taken}")
        else:
            logger.info(f"ℹ️ {description}: {current_value:.4f} > {threshold:.4f} - {action_taken}")
    
    def get_risk_summary(self) -> Dict:
        """获取风险摘要"""
        return {
            'current_balance': self.current_balance,
            'daily_loss': self.daily_loss,
            'daily_loss_pct': self.daily_loss / self.initial_balance,
            'consecutive_losses': self.consecutive_losses,
            'emergency_stop': self.emergency_stop,
            'trading_paused': self.trading_paused,
            'network_errors': self.network_errors,
            'total_risk_events': len(self.risk_events),
            'critical_events': len([e for e in self.risk_events if e.risk_level == RiskLevel.CRITICAL]),
            'high_risk_events': len([e for e in self.risk_events if e.risk_level == RiskLevel.HIGH])
        }
    
    def save_risk_report(self):
        """保存风险报告"""
        filename = f"risk_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'summary': self.get_risk_summary(),
            'events': [
                {
                    'timestamp': event.timestamp.isoformat(),
                    'event_type': event.event_type,
                    'risk_level': event.risk_level.value,
                    'description': event.description,
                    'current_value': event.current_value,
                    'threshold': event.threshold,
                    'action_taken': event.action_taken
                }
                for event in self.risk_events
            ],
            'price_history': self.price_history[-50:] if self.price_history else []  # 最近50个价格
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 风险报告已保存: {filename}")
    
    def emergency_shutdown(self, reason: str):
        """紧急关闭"""
        self.emergency_stop = True
        self.trigger_risk_event(
            "EMERGENCY_SHUTDOWN",
            RiskLevel.CRITICAL,
            f"紧急关闭: {reason}",
            0,
            0,
            "立即停止所有交易"
        )
        logger.critical(f"🚨 紧急关闭: {reason}")
        self.save_risk_report()

if __name__ == "__main__":
    # 测试风险管理器
    print("🛡️ 增强风险控制机制测试")
    
    risk_manager = EnhancedRiskManager(initial_balance=1000.0)
    
    # 模拟一些风险场景
    print("\n📊 风险摘要:")
    summary = risk_manager.get_risk_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 测试价格波动检测
    print("\n🔍 测试价格波动检测:")
    risk_manager.check_price_volatility(100.0)
    risk_manager.check_price_volatility(103.0)  # 3%波动，应该触发警告
    
    # 测试连续亏损
    print("\n📉 测试连续亏损:")
    for i in range(6):
        risk_manager.record_trade_result(-10, False)
        can_trade, reason = risk_manager.can_trade()
        print(f"  亏损{i+1}: 可交易={can_trade}, 原因={reason}")
    
    print("\n🎉 风险管理器测试完成")
