#!/usr/bin/env python3
"""
大数据训练脚本 - 获取更多历史数据进行训练
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, balanced_accuracy_score
import joblib

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_large_dataset(symbol='BTCUSDT', months_back=12, interval='1h'):
    """
    获取大量历史数据 - 简化版本
    """
    print(f"📊 获取 {symbol} 最近 {months_back} 个月的 {interval} 数据...")

    fetcher = BinanceDataFetcher()

    # 计算开始日期
    start_date = (datetime.now() - timedelta(days=months_back*30)).strftime('%Y-%m-%d')

    print(f"  获取从 {start_date} 到现在的数据...")

    try:
        # 直接获取所有数据
        df = fetcher.get_historical_data(
            symbol=symbol,
            interval=interval,
            start_date=start_date,
            force_refresh=True
        )

        if df.empty:
            raise ValueError("获取到的数据为空")

        print(f"✅ 成功获取到 {len(df)} 条历史数据")
        print(f"📅 时间范围: {df.index.min()} 到 {df.index.max()}")

        return df

    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        raise

def big_data_train(symbol='BTCUSDT', model_type='xgb', months_back=12, interval='1h'):
    """
    大数据训练
    """
    print(f"🚀 开始大数据训练 {symbol} {model_type.upper()} 模型...")
    print(f"📊 目标: 获取 {months_back} 个月的 {interval} 数据")
    
    try:
        # 1. 获取大量历史数据
        df = get_large_dataset(symbol, months_back, interval)
        
        if len(df) < 5000:
            print(f"⚠️  数据量较少 ({len(df)} 条)，建议增加时间范围")
        
        # 2. 特征工程
        print("🔧 特征工程...")
        engineer = FeatureEngineer()
        df_features = engineer.create_features(df)
        
        # 3. 数据准备
        print("📋 数据准备...")
        
        if 'target' not in df_features.columns:
            raise ValueError("目标列 'target' 未找到")
        
        # 分离特征和目标
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始列
        original_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
        
        # 数据清理
        print("🧹 数据清理...")
        before_clean = len(X)
        
        # 移除NaN行
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]
        
        # 处理无穷大值
        X.replace([np.inf, -np.inf], np.nan, inplace=True)
        X.fillna(X.median(), inplace=True)
        
        print(f"数据清理: {before_clean} -> {len(X)} 样本")
        
        # 编码标签
        label_encoder = LabelEncoder()
        y_encoded = label_encoder.fit_transform(y)
        
        # 分析类别分布
        unique, counts = np.unique(y_encoded, return_counts=True)
        total_samples = len(y_encoded)
        
        print(f"\n📊 数据统计:")
        print(f"   总样本数: {total_samples:,}")
        print(f"   特征数量: {X.shape[1]}")
        print(f"   类别数量: {len(unique)}")
        print(f"   时间跨度: {months_back} 个月")
        
        print(f"\n📈 类别分布:")
        for i, (class_id, count) in enumerate(zip(unique, counts)):
            percentage = count / total_samples * 100
            print(f"   类别 {class_id}: {count:,} 样本 ({percentage:.1f}%)")
        
        # 检查数据质量
        min_samples_per_class = min(counts)
        print(f"\n🔍 数据质量检查:")
        print(f"   最少类别样本数: {min_samples_per_class:,}")
        
        if min_samples_per_class < 100:
            print("   ⚠️  某些类别样本数较少，可能影响模型性能")
        elif min_samples_per_class < 500:
            print("   ⚠️  类别样本数中等，建议获取更多数据")
        else:
            print("   ✅ 类别样本数充足")
        
        if total_samples < 10000:
            print("   ⚠️  总样本数较少，建议增加时间范围")
        elif total_samples < 50000:
            print("   ✅ 样本数中等，适合训练")
        else:
            print("   ✅ 样本数充足，适合复杂模型")
        
        # 4. 时间序列分割
        print("\n✂️  时间序列分割...")
        
        # 使用80/20分割，但保持时间顺序
        split_point = int(len(X) * 0.8)
        
        X_train = X.iloc[:split_point]
        X_test = X.iloc[split_point:]
        y_train = y_encoded[:split_point]
        y_test = y_encoded[split_point:]
        
        print(f"   训练集: {len(X_train):,} 样本")
        print(f"   测试集: {len(X_test):,} 样本")
        
        # 检查训练集和测试集的类别分布
        train_unique, train_counts = np.unique(y_train, return_counts=True)
        test_unique, test_counts = np.unique(y_test, return_counts=True)
        
        print(f"\n📊 训练集类别分布:")
        for class_id, count in zip(train_unique, train_counts):
            print(f"   类别 {class_id}: {count:,} 样本")
        
        print(f"\n📊 测试集类别分布:")
        for class_id, count in zip(test_unique, test_counts):
            print(f"   类别 {class_id}: {count:,} 样本")
        
        # 5. 特征缩放
        print("\n📏 特征缩放...")
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 6. 创建模型
        print(f"\n🎯 创建 {model_type.upper()} 模型...")
        
        # 根据数据量调整模型复杂度
        if total_samples > 50000:
            # 大数据集，可以使用更复杂的模型
            if model_type == 'xgb' and HAS_XGB:
                model = xgb.XGBClassifier(
                    n_estimators=200,
                    max_depth=8,
                    learning_rate=0.1,
                    subsample=0.9,
                    colsample_bytree=0.9,
                    reg_alpha=0.1,
                    reg_lambda=0.1,
                    random_state=42,
                    n_jobs=-1,
                    eval_metric='mlogloss'
                )
            else:
                model = RandomForestClassifier(
                    n_estimators=200,
                    max_depth=15,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42,
                    n_jobs=-1
                )
        else:
            # 中等数据集，使用保守参数
            if model_type == 'xgb' and HAS_XGB:
                model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    reg_alpha=0.5,
                    reg_lambda=0.5,
                    random_state=42,
                    n_jobs=-1,
                    eval_metric='mlogloss'
                )
            else:
                model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    min_samples_split=10,
                    min_samples_leaf=5,
                    random_state=42,
                    n_jobs=-1
                )
        
        # 7. 训练模型
        print("🏃 训练模型...")
        model.fit(X_train_scaled, y_train)
        
        # 8. 评估模型
        print("📊 评估模型...")
        
        # 预测
        y_train_pred = model.predict(X_train_scaled)
        y_test_pred = model.predict(X_test_scaled)
        
        # 计算指标
        train_accuracy = accuracy_score(y_train, y_train_pred)
        test_accuracy = accuracy_score(y_test, y_test_pred)
        train_balanced = balanced_accuracy_score(y_train, y_train_pred)
        test_balanced = balanced_accuracy_score(y_test, y_test_pred)
        
        # 9. 显示结果
        print("\n" + "="*60)
        print("🎉 大数据训练完成!")
        print("="*60)
        print(f"数据规模: {total_samples:,} 样本, {X.shape[1]} 特征, {len(unique)} 类别")
        print(f"训练集准确率:     {train_accuracy:.4f}")
        print(f"测试集准确率:     {test_accuracy:.4f}")
        print(f"训练集平衡准确率: {train_balanced:.4f}")
        print(f"测试集平衡准确率: {test_balanced:.4f}")
        
        # 过拟合检查
        overfitting = train_accuracy - test_accuracy
        print(f"\n📊 模型健康度:")
        print(f"过拟合程度: {overfitting:.4f}")
        if overfitting > 0.1:
            print("⚠️  存在过拟合，建议增加正则化")
        elif overfitting > 0.05:
            print("⚠️  轻微过拟合")
        else:
            print("✅ 泛化能力良好")
        
        # 分类报告
        print("\n📋 详细分类报告:")
        print(classification_report(y_test, y_test_pred))
        
        # 10. 保存模型
        print("💾 保存模型...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path("models")
        model_dir.mkdir(exist_ok=True)
        
        model_path = model_dir / f"bigdata_{model_type}_{symbol}_{months_back}m_{timestamp}.joblib"
        scaler_path = model_dir / f"bigdata_scaler_{model_type}_{symbol}_{months_back}m_{timestamp}.joblib"
        encoder_path = model_dir / f"bigdata_encoder_{model_type}_{symbol}_{months_back}m_{timestamp}.joblib"
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        joblib.dump(label_encoder, encoder_path)
        
        print(f"✅ 模型已保存: {model_path}")
        
        return {
            'model': model,
            'scaler': scaler,
            'label_encoder': label_encoder,
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'balanced_accuracy': test_balanced,
            'total_samples': total_samples,
            'num_features': X.shape[1],
            'num_classes': len(unique),
            'overfitting': overfitting,
            'model_path': model_path
        }
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    model_type = sys.argv[2] if len(sys.argv) > 2 else 'xgb'
    months = int(sys.argv[3]) if len(sys.argv) > 3 else 12
    
    if model_type == 'xgb' and not HAS_XGB:
        print("XGBoost不可用，使用随机森林")
        model_type = 'rf'
    
    big_data_train(symbol, model_type, months)
