#!/usr/bin/env python3
"""
币安实盘剥头皮交易系统
基于成功验证的策略，集成真实交易API
包含完整的风险控制和安全管理
"""

import pandas as pd
import numpy as np
import logging
import time
import requests
import hmac
import hashlib
import json
import os
import base64
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import threading
import queue
from urllib.parse import urlencode
import getpass

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from cryptography.fernet import Fernet
except ImportError:
    logger.warning("cryptography库未安装，API密钥加密功能不可用")
    Fernet = None

class SecureAPIManager:
    """安全API密钥管理"""

    def __init__(self):
        self.key_file = "api_keys.enc"
        self.cipher_key = None

    def generate_key(self) -> bytes:
        """生成加密密钥"""
        return Fernet.generate_key()

    def save_encrypted_keys(self, api_key: str, api_secret: str, password: str):
        """保存加密的API密钥"""
        # 使用密码生成密钥
        key = hashlib.pbkdf2_hmac('sha256', password.encode(), b'salt_', 100000)
        cipher_key = base64.urlsafe_b64encode(key)

        fernet = Fernet(cipher_key)

        # 加密API密钥
        data = {
            'api_key': api_key,
            'api_secret': api_secret,
            'created': datetime.now().isoformat()
        }

        encrypted_data = fernet.encrypt(json.dumps(data).encode())

        with open(self.key_file, 'wb') as f:
            f.write(encrypted_data)

        logger.info("✅ API密钥已安全保存")

    def load_encrypted_keys(self, password: str) -> Tuple[str, str]:
        """加载加密的API密钥"""
        if not os.path.exists(self.key_file):
            raise FileNotFoundError("API密钥文件不存在")

        # 使用密码生成密钥
        key = hashlib.pbkdf2_hmac('sha256', password.encode(), b'salt_', 100000)
        cipher_key = base64.urlsafe_b64encode(key)

        fernet = Fernet(cipher_key)

        with open(self.key_file, 'rb') as f:
            encrypted_data = f.read()

        try:
            decrypted_data = fernet.decrypt(encrypted_data)
            data = json.loads(decrypted_data.decode())

            return data['api_key'], data['api_secret']
        except Exception as e:
            raise ValueError("密码错误或密钥文件损坏")

class BinanceLiveAPI:
    """币安实盘API客户端"""

    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        # API端点
        if testnet:
            self.base_url = "https://testnet.binance.vision"
            self.futures_url = "https://testnet.binancefuture.com"
        else:
            self.base_url = "https://api.binance.com"
            self.futures_url = "https://fapi.binance.com"

        self.api_key = api_key
        self.api_secret = api_secret

        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        })

        # 连接状态
        self.connected = False
        self.server_time_offset = 0

    def _get_server_time(self) -> int:
        """获取服务器时间"""
        try:
            response = self.session.get(f"{self.futures_url}/fapi/v1/time", timeout=5)
            response.raise_for_status()
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            self.server_time_offset = server_time - local_time
            return server_time
        except Exception as e:
            logger.error(f"获取服务器时间失败: {e}")
            return int(time.time() * 1000)

    def _generate_signature(self, params: dict) -> str:
        """生成API签名"""
        query_string = urlencode(params)
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _make_request(self, method: str, endpoint: str, params: dict = None, signed: bool = False) -> Optional[dict]:
        """发送API请求"""
        if params is None:
            params = {}

        if signed:
            timestamp = int(time.time() * 1000) + self.server_time_offset
            params['timestamp'] = timestamp
            params['recvWindow'] = 60000  # 60秒接收窗口
            params['signature'] = self._generate_signature(params)

        url = f"{self.futures_url}{endpoint}"

        try:
            if method == "GET":
                response = self.session.get(url, params=params, timeout=10)
            elif method == "POST":
                response = self.session.post(url, params=params, timeout=10)
            elif method == "DELETE":
                response = self.session.delete(url, params=params, timeout=10)
            else:
                return None

            response.raise_for_status()
            return response.json()

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                logger.error("❌ API认证失败，请检查密钥")
            elif e.response.status_code == 429:
                logger.warning("⚠️ API请求频率限制")
                time.sleep(1)
            else:
                logger.error(f"API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"网络请求失败: {e}")
            return None

    def test_connectivity(self) -> bool:
        """测试连接"""
        try:
            # 测试基础连接
            result = self._make_request("GET", "/fapi/v1/ping")
            if result == {}:
                logger.info("✅ 币安期货API连接成功")

                # 获取服务器时间
                self._get_server_time()

                # 测试账户连接
                account = self.get_account_info()
                if account:
                    logger.info("✅ 账户认证成功")
                    self.connected = True
                    return True
                else:
                    logger.error("❌ 账户认证失败")
                    return False
            return False
        except Exception as e:
            logger.error(f"❌ 连接测试失败: {e}")
            return False

    def get_account_info(self) -> Optional[dict]:
        """获取账户信息"""
        return self._make_request("GET", "/fapi/v2/account", signed=True)

    def get_balance(self) -> Optional[dict]:
        """获取余额信息"""
        account = self.get_account_info()
        if account and 'assets' in account:
            balances = {}
            for asset in account['assets']:
                if float(asset['walletBalance']) > 0:
                    balances[asset['asset']] = {
                        'balance': float(asset['walletBalance']),
                        'unrealized_pnl': float(asset['unrealizedProfit']),
                        'margin_balance': float(asset['marginBalance'])
                    }
            return balances
        return None

    def get_position_info(self, symbol: str = "ADAUSDT") -> Optional[dict]:
        """获取持仓信息"""
        result = self._make_request("GET", "/fapi/v2/positionRisk", {"symbol": symbol}, signed=True)
        if result and len(result) > 0:
            pos = result[0]
            return {
                'symbol': pos['symbol'],
                'position_amt': float(pos['positionAmt']),
                'entry_price': float(pos['entryPrice']),
                'mark_price': float(pos['markPrice']),
                'unrealized_pnl': float(pos['unRealizedProfit']),
                'percentage': float(pos['percentage'])
            }
        return None

    def get_price(self, symbol: str = "ADAUSDT") -> Optional[float]:
        """获取当前价格"""
        result = self._make_request("GET", "/fapi/v1/ticker/price", {"symbol": symbol})
        if result and 'price' in result:
            return float(result['price'])
        return None

    def get_klines(self, symbol: str = "ADAUSDT", interval: str = "1m", limit: int = 100) -> Optional[list]:
        """获取K线数据"""
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        return self._make_request("GET", "/fapi/v1/klines", params)

    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """设置杠杆"""
        params = {
            "symbol": symbol,
            "leverage": leverage
        }
        result = self._make_request("POST", "/fapi/v1/leverage", params, signed=True)
        if result and 'leverage' in result:
            logger.info(f"✅ {symbol} 杠杆设置为 {leverage}x")
            return True
        return False

    def set_margin_type(self, symbol: str, margin_type: str = "ISOLATED") -> bool:
        """设置保证金模式"""
        params = {
            "symbol": symbol,
            "marginType": margin_type
        }
        result = self._make_request("POST", "/fapi/v1/marginType", params, signed=True)
        if result:
            logger.info(f"✅ {symbol} 保证金模式设置为 {margin_type}")
            return True
        return False

    def place_market_order(self, symbol: str, side: str, quantity: float) -> Optional[dict]:
        """下市价单"""
        params = {
            "symbol": symbol,
            "side": side,  # BUY or SELL
            "type": "MARKET",
            "quantity": f"{quantity:.6f}"
        }

        logger.info(f"📝 下单: {side} {quantity} {symbol}")
        result = self._make_request("POST", "/fapi/v1/order", params, signed=True)

        if result and 'orderId' in result:
            logger.info(f"✅ 订单成功: ID {result['orderId']}")
            return result
        else:
            logger.error("❌ 下单失败")
            return None

    def place_stop_order(self, symbol: str, side: str, quantity: float, stop_price: float) -> Optional[dict]:
        """下止损单"""
        params = {
            "symbol": symbol,
            "side": side,
            "type": "STOP_MARKET",
            "quantity": f"{quantity:.6f}",
            "stopPrice": f"{stop_price:.6f}"
        }

        result = self._make_request("POST", "/fapi/v1/order", params, signed=True)
        if result and 'orderId' in result:
            logger.info(f"✅ 止损单设置: {stop_price:.6f}")
            return result
        return None

    def place_take_profit_order(self, symbol: str, side: str, quantity: float, stop_price: float) -> Optional[dict]:
        """下止盈单"""
        params = {
            "symbol": symbol,
            "side": side,
            "type": "TAKE_PROFIT_MARKET",
            "quantity": f"{quantity:.6f}",
            "stopPrice": f"{stop_price:.6f}"
        }

        result = self._make_request("POST", "/fapi/v1/order", params, signed=True)
        if result and 'orderId' in result:
            logger.info(f"✅ 止盈单设置: {stop_price:.6f}")
            return result
        return None

    def cancel_all_orders(self, symbol: str) -> bool:
        """取消所有订单"""
        result = self._make_request("DELETE", "/fapi/v1/allOpenOrders", {"symbol": symbol}, signed=True)
        if result:
            logger.info(f"✅ 已取消 {symbol} 所有订单")
            return True
        return False

    def close_position(self, symbol: str) -> bool:
        """平仓"""
        position = self.get_position_info(symbol)
        if position and position['position_amt'] != 0:
            quantity = abs(position['position_amt'])
            side = "SELL" if position['position_amt'] > 0 else "BUY"

            result = self.place_market_order(symbol, side, quantity)
            if result:
                logger.info(f"✅ {symbol} 平仓成功")
                return True
        return False

class LiveScalpingSystem:
    """实盘剥头皮交易系统"""

    def __init__(self, api_key: str, api_secret: str, initial_balance: float = 1000.0, testnet: bool = False):
        self.symbol = "ADAUSDT"
        self.initial_balance = initial_balance
        self.current_balance = initial_balance

        # 成功验证的剥头皮参数
        self.position_risk = 0.025  # 2.5%风险
        self.stop_loss_ratio = 0.0004  # 0.04%止损
        self.take_profit_ratio = 0.0005  # 0.05%止盈
        self.min_confidence = 0.65  # 65%置信度
        self.leverage = 125  # 125x杠杆

        # 实盘API
        self.api = BinanceLiveAPI(api_key, api_secret, testnet)

        # 交易状态
        self.current_position = None
        self.last_trade_time = None
        self.total_trades = 0
        self.winning_trades = 0
        self.trade_history = []

        # 风险控制
        self.daily_loss_limit = initial_balance * 0.05  # 5%日亏损限制
        self.consecutive_losses = 0
        self.max_consecutive_losses = 5
        self.emergency_stop = False

        # 实时监控
        self.is_running = False
        self.price_monitor_thread = None
        self.price_queue = queue.Queue()

        # 订单管理
        self.stop_loss_order_id = None
        self.take_profit_order_id = None

    def initialize_live_trading(self) -> bool:
        """初始化实盘交易"""
        logger.info("🔧 初始化币安实盘交易系统...")

        # 测试连接
        if not self.api.test_connectivity():
            logger.error("❌ 实盘API连接失败")
            return False

        # 获取账户信息
        account_info = self.api.get_account_info()
        if not account_info:
            logger.error("❌ 无法获取账户信息")
            return False

        # 显示账户余额
        balances = self.api.get_balance()
        if balances:
            logger.info("✅ 账户余额:")
            for asset, info in balances.items():
                logger.info(f"   {asset}: {info['balance']:.6f} (可用保证金: {info['margin_balance']:.6f})")

        # 设置杠杆和保证金模式
        if not self.api.set_leverage(self.symbol, self.leverage):
            logger.warning("⚠️ 杠杆设置失败，使用当前设置")

        if not self.api.set_margin_type(self.symbol, "ISOLATED"):
            logger.warning("⚠️ 保证金模式设置失败，使用当前设置")

        # 检查现有持仓
        position = self.api.get_position_info(self.symbol)
        if position and position['position_amt'] != 0:
            logger.warning(f"⚠️ 检测到现有持仓: {position['position_amt']} {self.symbol}")
            choice = input("是否平仓现有持仓? (y/n): ").lower()
            if choice == 'y':
                self.api.close_position(self.symbol)

        logger.info("✅ 实盘交易系统初始化完成")
        return True

    def start_price_monitor(self):
        """启动价格监控"""
        def price_monitor():
            while self.is_running:
                try:
                    price = self.api.get_price(self.symbol)
                    if price:
                        self.price_queue.put({
                            'timestamp': datetime.now(),
                            'price': price
                        })

                    # 有持仓时更频繁监控
                    time.sleep(1 if self.current_position else 3)

                except Exception as e:
                    logger.error(f"价格监控错误: {e}")
                    time.sleep(3)

        self.price_monitor_thread = threading.Thread(target=price_monitor, daemon=True)
        self.price_monitor_thread.start()
        logger.info("✅ 实盘价格监控已启动")

    def get_latest_price(self) -> Optional[float]:
        """获取最新价格"""
        try:
            latest_price = None
            while not self.price_queue.empty():
                price_data = self.price_queue.get_nowait()
                latest_price = price_data['price']

            return latest_price if latest_price else self.api.get_price(self.symbol)

        except Exception:
            return self.api.get_price(self.symbol)

    def calculate_signal(self) -> Tuple[str, float]:
        """计算交易信号（保持成功的策略）"""
        try:
            klines = self.api.get_klines(self.symbol, "1m", 20)
            if not klines or len(klines) < 10:
                return "HOLD", 0.0

            # 转换价格数据
            closes = [float(k[4]) for k in klines]

            current_price = closes[-1]
            prev_price_1 = closes[-2]
            prev_price_3 = closes[-4] if len(closes) >= 4 else closes[-2]

            # 计算变化率
            change_1 = (current_price - prev_price_1) / prev_price_1
            change_3 = (current_price - prev_price_3) / prev_price_3

            # 简单移动平均
            ma_5 = np.mean(closes[-5:])
            ma_10 = np.mean(closes[-10:])

            # 评分系统（保持成功的逻辑）
            score = 0
            confidence_factors = []

            # 1分钟动量
            if change_1 > 0.0002:  # 0.02%
                score += 3
                confidence_factors.append(0.25)
            elif change_1 < -0.0002:
                score -= 3
                confidence_factors.append(0.25)

            # 3分钟趋势
            if change_3 > 0.0003:  # 0.03%
                score += 2
                confidence_factors.append(0.15)
            elif change_3 < -0.0003:
                score -= 2
                confidence_factors.append(0.15)

            # 移动平均
            if current_price > ma_5 * 1.0001:  # 0.01%
                score += 1
                confidence_factors.append(0.10)
            elif current_price < ma_5 * 0.9999:
                score -= 1
                confidence_factors.append(0.10)

            # 决策
            if score >= 3:
                direction = "LONG"
                base_confidence = 0.70
            elif score <= -3:
                direction = "SHORT"
                base_confidence = 0.70
            else:
                direction = "HOLD"
                base_confidence = 0.5

            # 计算最终置信度
            if direction != "HOLD":
                confidence_boost = sum(confidence_factors)
                final_confidence = min(0.90, base_confidence + confidence_boost)
            else:
                final_confidence = base_confidence

            return direction, final_confidence

        except Exception as e:
            logger.error(f"信号计算失败: {e}")
            return "HOLD", 0.0

    def can_trade(self) -> bool:
        """检查是否可以交易（增强风险控制）"""
        # 紧急停止检查
        if self.emergency_stop:
            return False

        # 检查是否有持仓
        if self.current_position:
            return False

        # 检查交易间隔
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < 30:
                return False

        # 检查连续亏损
        if self.consecutive_losses >= self.max_consecutive_losses:
            logger.warning(f"⚠️ 连续亏损{self.consecutive_losses}次，暂停交易")
            return False

        # 检查日亏损限制
        daily_loss = self.initial_balance - self.current_balance
        if daily_loss >= self.daily_loss_limit:
            logger.warning(f"⚠️ 达到日亏损限制 ${daily_loss:.2f}")
            self.emergency_stop = True
            return False

        # 检查账户余额
        balances = self.api.get_balance()
        if balances and 'USDT' in balances:
            available_balance = balances['USDT']['margin_balance']
            if available_balance < self.initial_balance * 0.3:
                logger.warning("⚠️ 可用余额不足，停止交易")
                return False

        return True

    def execute_live_trade(self, direction: str, confidence: float, entry_price: float):
        """执行实盘交易"""
        # 计算仓位大小
        risk_amount = self.current_balance * self.position_risk
        position_value = risk_amount * self.leverage
        position_size = position_value / entry_price

        # 币安最小交易数量检查
        min_qty = 0.1  # ADAUSDT最小数量
        if position_size < min_qty:
            logger.warning(f"⚠️ 仓位大小 {position_size:.6f} 小于最小数量 {min_qty}")
            return

        # 计算止损止盈价格
        if direction == "LONG":
            stop_loss_price = entry_price * (1 - self.stop_loss_ratio)
            take_profit_price = entry_price * (1 + self.take_profit_ratio)
            side = "BUY"
            stop_side = "SELL"
        else:
            stop_loss_price = entry_price * (1 + self.stop_loss_ratio)
            take_profit_price = entry_price * (1 - self.take_profit_ratio)
            side = "SELL"
            stop_side = "BUY"

        # 执行开仓
        logger.info(f"🚀 执行实盘开仓: {direction} @ {entry_price:.4f}")
        order_result = self.api.place_market_order(self.symbol, side, position_size)

        if not order_result:
            logger.error("❌ 开仓失败")
            return

        # 设置止损单
        stop_order = self.api.place_stop_order(self.symbol, stop_side, position_size, stop_loss_price)
        if stop_order:
            self.stop_loss_order_id = stop_order['orderId']

        # 设置止盈单
        tp_order = self.api.place_take_profit_order(self.symbol, stop_side, position_size, take_profit_price)
        if tp_order:
            self.take_profit_order_id = tp_order['orderId']

        # 创建持仓记录
        self.current_position = {
            'direction': direction,
            'entry_price': entry_price,
            'entry_time': datetime.now(),
            'position_size': position_size,
            'stop_loss': stop_loss_price,
            'take_profit': take_profit_price,
            'confidence': confidence,
            'order_id': order_result['orderId'],
            'stop_loss_order_id': self.stop_loss_order_id,
            'take_profit_order_id': self.take_profit_order_id
        }

        self.total_trades += 1
        self.last_trade_time = datetime.now()

        logger.info(f"✅ 实盘开仓成功:")
        logger.info(f"   订单ID: {order_result['orderId']}")
        logger.info(f"   置信度: {confidence:.1%}")
        logger.info(f"   止损: {stop_loss_price:.4f} ({self.stop_loss_ratio:.2%})")
        logger.info(f"   止盈: {take_profit_price:.4f} ({self.take_profit_ratio:.2%})")

    def monitor_position(self):
        """监控持仓状态"""
        if not self.current_position:
            return

        # 获取实时持仓信息
        position_info = self.api.get_position_info(self.symbol)
        if not position_info or position_info['position_amt'] == 0:
            # 持仓已平仓，更新状态
            self.handle_position_closed()
            return

        # 检查异常价格波动
        current_price = self.get_latest_price()
        if current_price:
            pos = self.current_position
            price_change = abs(current_price - pos['entry_price']) / pos['entry_price']

            # 异常波动检测（超过2%）
            if price_change > 0.02:
                logger.warning(f"⚠️ 检测到异常价格波动: {price_change:.2%}")
                # 可以选择紧急平仓
                # self.emergency_close_position()

        # 检查最大持仓时间
        duration = (datetime.now() - pos['entry_time']).total_seconds()
        if duration > 300:  # 5分钟强制平仓
            logger.info("⏰ 达到最大持仓时间，强制平仓")
            self.emergency_close_position()

    def handle_position_closed(self):
        """处理持仓平仓"""
        if not self.current_position:
            return

        pos = self.current_position

        # 获取最终价格（从持仓信息或当前价格）
        position_info = self.api.get_position_info(self.symbol)
        if position_info and 'unrealized_pnl' in position_info:
            pnl_amount = position_info['unrealized_pnl']
        else:
            # 估算盈亏
            current_price = self.get_latest_price()
            if pos['direction'] == "LONG":
                pnl_pct = (current_price - pos['entry_price']) / pos['entry_price']
            else:
                pnl_pct = (pos['entry_price'] - current_price) / pos['entry_price']

            leveraged_pnl = pnl_pct * self.leverage
            position_value = pos['position_size'] * pos['entry_price']
            margin_used = position_value / self.leverage
            pnl_amount = margin_used * leveraged_pnl

        # 更新余额
        self.current_balance += pnl_amount

        # 更新统计
        is_winner = pnl_amount > 0
        if is_winner:
            self.winning_trades += 1
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1

        # 记录交易
        holding_time = (datetime.now() - pos['entry_time']).total_seconds()

        trade_record = {
            'trade_id': self.total_trades,
            'entry_time': pos['entry_time'],
            'exit_time': datetime.now(),
            'direction': pos['direction'],
            'entry_price': pos['entry_price'],
            'exit_price': self.get_latest_price(),
            'pnl_amount': pnl_amount,
            'holding_time': holding_time,
            'is_winner': is_winner,
            'exit_reason': "自动平仓",
            'confidence': pos['confidence'],
            'order_id': pos['order_id']
        }

        self.trade_history.append(trade_record)
        self.current_position = None
        self.stop_loss_order_id = None
        self.take_profit_order_id = None

        # 显示结果
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance

        status = "✅" if is_winner else "❌"
        logger.info(f"📈 实盘平仓: {pos['direction']} @ {trade_record['exit_price']:.4f}")
        logger.info(f"   {status} 盈亏: ${pnl_amount:+.2f}")
        logger.info(f"   持仓时间: {holding_time:.0f}秒")
        logger.info(f"   当前余额: ${self.current_balance:.2f}")
        logger.info(f"   胜率: {win_rate:.1%}, 收益: {total_return:+.1%}")
        logger.info(f"   连续亏损: {self.consecutive_losses}")

    def emergency_close_position(self):
        """紧急平仓"""
        if not self.current_position:
            return

        logger.warning("🚨 执行紧急平仓")

        # 取消所有挂单
        self.api.cancel_all_orders(self.symbol)

        # 市价平仓
        success = self.api.close_position(self.symbol)
        if success:
            logger.info("✅ 紧急平仓成功")
            self.handle_position_closed()
        else:
            logger.error("❌ 紧急平仓失败")

    def run_live_trading(self, duration_minutes: int = 60):
        """运行实盘交易"""
        logger.info("🚀 启动币安实盘剥头皮交易系统")
        logger.info(f"💰 初始资金: ${self.initial_balance}")
        logger.info(f"📊 成功验证参数: 止损{self.stop_loss_ratio:.2%}, 止盈{self.take_profit_ratio:.2%}")
        logger.info(f"🛡️ 风险控制: 日亏损限制{self.daily_loss_limit:.2f}, 连续亏损限制{self.max_consecutive_losses}")
        logger.info(f"⚡ 杠杆: {self.leverage}x")
        logger.info(f"⏰ 运行时间: {duration_minutes} 分钟")

        # 初始化实盘交易
        if not self.initialize_live_trading():
            logger.error("❌ 实盘交易初始化失败")
            return

        self.is_running = True
        self.start_price_monitor()

        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)

        try:
            while datetime.now() < end_time and self.is_running and not self.emergency_stop:
                current_price = self.get_latest_price()
                if current_price is None:
                    time.sleep(3)
                    continue

                # 监控持仓
                if self.current_position:
                    self.monitor_position()
                    time.sleep(2)
                else:
                    # 寻找交易机会
                    if self.can_trade():
                        direction, confidence = self.calculate_signal()

                        if direction in ["LONG", "SHORT"] and confidence >= self.min_confidence:
                            self.execute_live_trade(direction, confidence, current_price)

                    time.sleep(5)

        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断实盘交易")
        except Exception as e:
            logger.error(f"❌ 实盘交易异常: {e}")
        finally:
            self.is_running = False

            # 紧急平仓
            if self.current_position:
                self.emergency_close_position()

            self.show_live_results()

    def show_live_results(self):
        """显示实盘结果"""
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance

        print("\n" + "="*70)
        print("🎉 币安实盘剥头皮交易完成")
        print("="*70)
        print(f"📊 交易统计:")
        print(f"  总交易数: {self.total_trades}")
        print(f"  胜率: {win_rate:.1%}")
        print(f"  收益率: {total_return:+.1%}")
        print(f"  连续亏损: {self.consecutive_losses}")
        print(f"💰 财务表现:")
        print(f"  初始余额: ${self.initial_balance:.2f}")
        print(f"  最终余额: ${self.current_balance:.2f}")
        print(f"  净盈亏: ${self.current_balance - self.initial_balance:+.2f}")
        print(f"🛡️ 风险控制:")
        print(f"  紧急停止: {'是' if self.emergency_stop else '否'}")
        print(f"  最大连续亏损: {self.max_consecutive_losses}")

        # 保存交易记录
        self.save_trade_history()

    def save_trade_history(self):
        """保存交易历史"""
        if not self.trade_history:
            return

        filename = f"live_trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        # 计算统计数据
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance

        results = {
            'system_info': {
                'symbol': self.symbol,
                'timestamp': datetime.now().isoformat(),
                'initial_balance': self.initial_balance,
                'final_balance': self.current_balance,
                'leverage': self.leverage,
                'strategy': 'LIVE_SCALPING'
            },
            'performance': {
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'win_rate': win_rate,
                'total_return': total_return,
                'consecutive_losses': self.consecutive_losses,
                'emergency_stop': self.emergency_stop
            },
            'trades': [
                {
                    'trade_id': trade['trade_id'],
                    'entry_time': trade['entry_time'].isoformat(),
                    'exit_time': trade['exit_time'].isoformat(),
                    'direction': trade['direction'],
                    'entry_price': trade['entry_price'],
                    'exit_price': trade['exit_price'],
                    'pnl_amount': trade['pnl_amount'],
                    'holding_time': trade['holding_time'],
                    'is_winner': trade['is_winner'],
                    'exit_reason': trade['exit_reason'],
                    'confidence': trade['confidence'],
                    'order_id': trade['order_id']
                }
                for trade in self.trade_history
            ]
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 交易记录已保存: {filename}")

def main():
    """主函数"""
    print("🚀 币安实盘剥头皮交易系统")
    print("📊 基于成功验证的策略 (60.3%胜率, 13.3%收益率)")
    print("🛡️ 包含完整风险控制和安全管理")

    # 选择模式
    print("\n请选择模式:")
    print("1. 测试网模式 (推荐)")
    print("2. 实盘模式 (谨慎使用)")

    mode = input("请输入选择 (1/2): ").strip()
    testnet = mode != "2"

    if not testnet:
        print("\n⚠️ 警告: 您选择了实盘模式！")
        print("⚠️ 这将使用真实资金进行交易！")
        confirm = input("确认继续? (输入 'YES' 确认): ")
        if confirm != "YES":
            print("已取消")
            return

    # API密钥管理
    api_manager = SecureAPIManager()

    try:
        # 尝试加载已保存的密钥
        if os.path.exists(api_manager.key_file):
            password = getpass.getpass("请输入密钥密码: ")
            api_key, api_secret = api_manager.load_encrypted_keys(password)
            print("✅ 已加载保存的API密钥")
        else:
            raise FileNotFoundError()

    except (FileNotFoundError, ValueError):
        # 输入新的API密钥
        print("\n请输入API密钥:")
        api_key = input("API Key: ").strip()
        api_secret = getpass.getpass("API Secret: ").strip()

        if Fernet and api_key and api_secret:
            save_keys = input("是否安全保存密钥? (y/n): ").lower() == 'y'
            if save_keys:
                password = getpass.getpass("设置密钥密码: ")
                api_manager.save_encrypted_keys(api_key, api_secret, password)

    if not api_key or not api_secret:
        print("❌ API密钥不能为空")
        return

    # 创建交易系统
    initial_balance = float(input("\n请输入初始资金 (默认1000): ") or "1000")
    trader = LiveScalpingSystem(api_key, api_secret, initial_balance, testnet)

    # 运行交易
    try:
        duration = int(input("请输入运行时间(分钟，默认30): ") or "30")
        trader.run_live_trading(duration_minutes=duration)
    except:
        print("使用默认30分钟运行...")
        trader.run_live_trading(duration_minutes=30)

if __name__ == "__main__":
    main()