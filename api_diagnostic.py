#!/usr/bin/env python3
"""
API诊断工具
帮助排查币安API连接问题
"""

import requests
import hmac
import hashlib
import time
from urllib.parse import urlencode
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class APIDiagnostic:
    """API诊断工具"""
    
    def __init__(self, api_key: str, api_secret: str):
        self.api_key = api_key
        self.api_secret = api_secret
        
        # 测试网和主网端点
        self.testnet_url = "https://testnet.binance.vision"
        self.mainnet_url = "https://api.binance.com"
        
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        })
    
    def _generate_signature(self, params: dict) -> str:
        """生成API签名"""
        query_string = urlencode(params)
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def test_basic_connectivity(self, base_url: str, network_name: str):
        """测试基础连接"""
        logger.info(f"\n🔍 测试{network_name}基础连接...")
        
        try:
            # 测试ping
            response = self.session.get(f"{base_url}/api/v3/ping", timeout=10)
            if response.status_code == 200 and response.json() == {}:
                logger.info(f"✅ {network_name} Ping 成功")
            else:
                logger.error(f"❌ {network_name} Ping 失败")
                return False
            
            # 测试服务器时间
            response = self.session.get(f"{base_url}/api/v3/time", timeout=10)
            if response.status_code == 200:
                server_time = response.json()['serverTime']
                local_time = int(time.time() * 1000)
                offset = server_time - local_time
                logger.info(f"✅ {network_name} 服务器时间获取成功，偏移: {offset}ms")
            else:
                logger.error(f"❌ {network_name} 服务器时间获取失败")
                return False
            
            # 测试交易对信息
            response = self.session.get(f"{base_url}/api/v3/ticker/price", 
                                      params={"symbol": "ADAUSDT"}, timeout=10)
            if response.status_code == 200:
                price_data = response.json()
                logger.info(f"✅ {network_name} ADAUSDT价格: {price_data['price']}")
            else:
                logger.error(f"❌ {network_name} 价格获取失败")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ {network_name} 连接异常: {e}")
            return False
    
    def test_api_authentication(self, base_url: str, network_name: str):
        """测试API认证"""
        logger.info(f"\n🔐 测试{network_name}API认证...")
        
        try:
            # 准备签名请求
            params = {
                'timestamp': int(time.time() * 1000),
                'recvWindow': 60000
            }
            params['signature'] = self._generate_signature(params)
            
            # 测试账户信息
            response = self.session.get(f"{base_url}/api/v3/account", 
                                      params=params, timeout=10)
            
            if response.status_code == 200:
                account_data = response.json()
                logger.info(f"✅ {network_name} API认证成功")
                
                # 显示账户信息
                logger.info(f"   账户类型: {account_data.get('accountType', 'N/A')}")
                logger.info(f"   权限: {account_data.get('permissions', [])}")
                
                # 显示余额
                balances = account_data.get('balances', [])
                non_zero_balances = [b for b in balances if float(b['free']) > 0]
                if non_zero_balances:
                    logger.info(f"   非零余额:")
                    for balance in non_zero_balances[:5]:  # 只显示前5个
                        logger.info(f"     {balance['asset']}: {balance['free']}")
                else:
                    logger.info(f"   所有余额为零")
                
                return True
                
            else:
                error_data = response.json() if response.content else {}
                logger.error(f"❌ {network_name} API认证失败")
                logger.error(f"   状态码: {response.status_code}")
                logger.error(f"   错误信息: {error_data}")
                
                # 分析常见错误
                if response.status_code == 401:
                    error_code = error_data.get('code', 0)
                    if error_code == -2015:
                        logger.error("   🔍 可能原因: API密钥无效、IP未加白名单或权限不足")
                    elif error_code == -1021:
                        logger.error("   🔍 可能原因: 时间戳超出接收窗口")
                    elif error_code == -2014:
                        logger.error("   🔍 可能原因: API密钥格式错误")
                
                return False
                
        except Exception as e:
            logger.error(f"❌ {network_name} API认证异常: {e}")
            return False
    
    def test_trading_permissions(self, base_url: str, network_name: str):
        """测试交易权限"""
        logger.info(f"\n📊 测试{network_name}交易权限...")
        
        try:
            # 准备签名请求
            params = {
                'timestamp': int(time.time() * 1000),
                'recvWindow': 60000
            }
            params['signature'] = self._generate_signature(params)
            
            # 测试获取订单历史（需要交易权限）
            response = self.session.get(f"{base_url}/api/v3/allOrders", 
                                      params={**params, 'symbol': 'ADAUSDT', 'limit': 1}, 
                                      timeout=10)
            
            if response.status_code == 200:
                logger.info(f"✅ {network_name} 交易权限正常")
                return True
            else:
                error_data = response.json() if response.content else {}
                logger.error(f"❌ {network_name} 交易权限不足")
                logger.error(f"   错误信息: {error_data}")
                return False
                
        except Exception as e:
            logger.error(f"❌ {network_name} 交易权限测试异常: {e}")
            return False
    
    def get_ip_info(self):
        """获取当前IP信息"""
        logger.info(f"\n🌐 获取当前IP信息...")
        
        try:
            response = requests.get("https://httpbin.org/ip", timeout=10)
            if response.status_code == 200:
                ip_data = response.json()
                current_ip = ip_data.get('origin', 'Unknown')
                logger.info(f"✅ 当前IP地址: {current_ip}")
                logger.info(f"   请确保此IP已添加到币安API白名单中")
                return current_ip
            else:
                logger.error("❌ 无法获取IP信息")
                return None
        except Exception as e:
            logger.error(f"❌ 获取IP信息异常: {e}")
            return None
    
    def run_full_diagnostic(self):
        """运行完整诊断"""
        logger.info("🔧 开始API完整诊断...")
        logger.info("="*60)
        
        # 获取IP信息
        self.get_ip_info()
        
        # 测试测试网
        logger.info("\n" + "="*60)
        logger.info("🧪 测试网诊断")
        logger.info("="*60)
        
        testnet_basic = self.test_basic_connectivity(self.testnet_url, "测试网")
        testnet_auth = False
        testnet_trading = False
        
        if testnet_basic:
            testnet_auth = self.test_api_authentication(self.testnet_url, "测试网")
            if testnet_auth:
                testnet_trading = self.test_trading_permissions(self.testnet_url, "测试网")
        
        # 测试主网
        logger.info("\n" + "="*60)
        logger.info("🌐 主网诊断")
        logger.info("="*60)
        
        mainnet_basic = self.test_basic_connectivity(self.mainnet_url, "主网")
        mainnet_auth = False
        mainnet_trading = False
        
        if mainnet_basic:
            mainnet_auth = self.test_api_authentication(self.mainnet_url, "主网")
            if mainnet_auth:
                mainnet_trading = self.test_trading_permissions(self.mainnet_url, "主网")
        
        # 总结报告
        logger.info("\n" + "="*60)
        logger.info("📋 诊断总结")
        logger.info("="*60)
        
        logger.info(f"测试网:")
        logger.info(f"  基础连接: {'✅' if testnet_basic else '❌'}")
        logger.info(f"  API认证: {'✅' if testnet_auth else '❌'}")
        logger.info(f"  交易权限: {'✅' if testnet_trading else '❌'}")
        
        logger.info(f"主网:")
        logger.info(f"  基础连接: {'✅' if mainnet_basic else '❌'}")
        logger.info(f"  API认证: {'✅' if mainnet_auth else '❌'}")
        logger.info(f"  交易权限: {'✅' if mainnet_trading else '❌'}")
        
        # 建议
        logger.info(f"\n💡 建议:")
        
        if not testnet_auth and not mainnet_auth:
            logger.info("  1. 检查API密钥是否正确")
            logger.info("  2. 确认IP地址已添加到白名单")
            logger.info("  3. 检查API权限设置")
        elif mainnet_auth and not testnet_auth:
            logger.info("  1. 当前密钥是主网密钥，需要测试网密钥")
            logger.info("  2. 请到 https://testnet.binance.vision 创建测试网密钥")
        elif testnet_auth and not mainnet_auth:
            logger.info("  1. 当前密钥是测试网密钥")
            logger.info("  2. 可以用于测试网交易")
        
        if testnet_auth or mainnet_auth:
            if not (testnet_trading or mainnet_trading):
                logger.info("  3. 需要启用现货交易权限")
        
        return {
            'testnet': {'basic': testnet_basic, 'auth': testnet_auth, 'trading': testnet_trading},
            'mainnet': {'basic': mainnet_basic, 'auth': mainnet_auth, 'trading': mainnet_trading}
        }

if __name__ == "__main__":
    print("🔧 币安API诊断工具")
    print("📊 帮助排查API连接问题")
    
    # 使用提供的API密钥
    API_KEY = "WO0FoiEOvpN996J38gHKZa6314Cdq0N8JuEy3hr0awGA9ISB0yY5fVpwtW5NEunS"
    API_SECRET = "fTHGkdJJZX5oooKJ89MUlED0WKtTn8gWEqxBQ4Fc5ykiUlrfirJonRlv58mCoBL4"
    
    diagnostic = APIDiagnostic(API_KEY, API_SECRET)
    results = diagnostic.run_full_diagnostic()
    
    print("\n🎉 诊断完成！")
    print("请根据上述建议解决API问题。")
