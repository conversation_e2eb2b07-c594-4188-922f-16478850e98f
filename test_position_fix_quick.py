#!/usr/bin/env python3
"""
Quick test to verify position size is now reasonable
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader

def test_position_fix_quick():
    """Quick test for reasonable position size"""
    print("🧪 Quick Position Size Test")
    print("=" * 40)
    
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    # Test position calculation
    mock_signal = {
        'direction': 'LONG',
        'confidence': 0.4,
        'trading_style': 'left_side',
        'signal_count': 2,
        'reasons': ['Test']
    }
    
    mock_market_data = {
        'current_price': 102000.0,
        'volatility': 0.005,
        'volume_ratio': 1.0
    }
    
    print(f"💰 Balance: ${trader.account['balance']:.2f}")
    print(f"⚡ Leverage: {trader.leverage}x")
    
    position_size = trader.calculate_dynamic_position_size(mock_signal, mock_market_data)
    margin_required = position_size * mock_market_data['current_price']
    margin_usage = (margin_required / trader.account['balance']) * 100
    
    print(f"\n✅ Results:")
    print(f"   📊 Position: {position_size:.6f} BTC")
    print(f"   💰 Margin: ${margin_required:.2f}")
    print(f"   📊 Usage: {margin_usage:.1f}%")
    
    if margin_usage > 100:
        print(f"   ❌ STILL BROKEN: Margin usage > 100%")
    elif margin_usage > 50:
        print(f"   ✅ FIXED: Reasonable margin usage")
    else:
        print(f"   ✅ CONSERVATIVE: Low margin usage")
    
    # Compare with your expectation
    max_theoretical = (50 * 125) / 102000  # $6250 / $102000
    utilization = (position_size / max_theoretical) * 100
    
    print(f"\n📊 Comparison:")
    print(f"   🎯 Max Theoretical: {max_theoretical:.6f} BTC")
    print(f"   📈 Utilization: {utilization:.1f}%")
    
    if utilization > 5:  # At least 5% utilization
        print(f"   ✅ GOOD: Reasonable leverage utilization")
    else:
        print(f"   ⚠️ LOW: Still conservative, but safe")

if __name__ == "__main__":
    test_position_fix_quick()
