import pandas as pd
import numpy as np
from ta import add_all_ta_features
from ta.trend import SMAIndicator, MACD, IchimokuIndicator, ADXIndicator
from ta.volatility import BollingerBands, KeltnerChannel, DonchianChannel
from ta.momentum import RSIIndicator, StochasticOscillator, WilliamsRIndicator, AwesomeOscillatorIndicator
from ta.volume import ChaikinMoneyFlowIndicator, MFIIndicator, OnBalanceVolumeIndicator
from ta.volatility import AverageTrueRange
from ta.utils import dropna
import logging
from datetime import datetime
from typing import Tuple, Dict, Optional, List, Union, Any, Callable # Added Callable
from pathlib import Path
import pickle
import hashlib
from concurrent.futures import ProcessPoolExecutor, as_completed
from functools import partial
import psutil
import warnings
from tqdm import tqdm
import gc
from dataclasses import dataclass, field, fields # Added fields
from typing import Dict, List, Optional, Union
from sklearn.feature_selection import VarianceThreshold, SelectKBest, f_classif, mutual_info_classif, RFE, RFECV # Added RFE and RFECV
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression # Added for RFE estimator option
from sklearn.model_selection import TimeSeriesSplit
import os
import json # For saving data quality reports
import time

try:
    from statsmodels.stats.outliers_influence import variance_inflation_factor
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    logger.warning("statsmodels库未安装，VIF计算功能将不可用。请运行 'pip install statsmodels' 进行安装。")

try:
    from statsmodels.tsa.seasonal import STL
    STATSMODELS_STL_AVAILABLE = True
except ImportError:
    STATSMODELS_STL_AVAILABLE = False
    # logger is not defined yet at this global level, so can't log here directly
    # We will log within the methods that use it.

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

logger = logging.getLogger(__name__)
if not logger.hasHandlers():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Define a type alias for feature functions for clarity
FeatureFunctionType = Callable[[pd.DataFrame, Optional['FeatureConfig']], pd.DataFrame]

@dataclass
class FeatureConfig:
    """特征工程配置类"""
    # 基础配置
    prediction_window: int = 24
    cache_dir: str = 'data/feature_cache'
    data_quality_report_dir: str = 'data/quality_reports' # New: For data quality reports
    memory_threshold: float = 0.8
    chunk_size: int = 10000
    
    # 数据类型优化
    use_float32: bool = True
    
    # 缓存配置
    cache_ttl_days: int = 7  # 缓存文件的生命周期（天）
    max_cache_size_gb: float = 10.0  # 最大缓存大小（GB）
    
    # 特征选择配置 (增强)
    enable_variance_threshold_selection: bool = True
    variance_threshold_value: float = 0.01 # 原始默认值，现在可配置
    enable_correlation_selection: bool = True
    correlation_threshold: float = 0.95  # VIF阈值 -  这个注释有误，应该是相关性阈值
    enable_vif_selection: bool = True
    vif_threshold: float = 10.0  # VIF阈值 - 这个注释正确
    enable_importance_selection: bool = True # 基于随机森林/模型的特征重要性选择
    max_features_after_importance: int = 200  # 通过重要性选择后的最大特征数 (原max_features)
    # 可以考虑添加 selector_model_n_estimators: int = 100 用于随机森林
    
    # 异常值处理
    outlier_std_threshold: float = 3.0  # 标准差倍数阈值
    winsorize_quantile: float = 0.01  # winsorization分位数
    
    # 时间序列特征配置
    ma_periods: List[int] = field(default_factory=lambda: [5, 10, 20, 50, 120, 200])
    rsi_periods: List[int] = field(default_factory=lambda: [6, 12, 14, 24])
    bb_window: int = 20 # Bollinger Bands window
    bb_std_dev: int = 2  # Bollinger Bands std dev
    macd_fast_period: int = 12
    macd_slow_period: int = 26
    macd_signal_period: int = 9
    adx_period: int = 14
    atr_period: int = 14
    
    volume_ma_periods: List[int] = field(default_factory=lambda: [6, 12, 24, 72, 168])
    stl_decomposition_period: int = 24 # Period for STL decomposition
    lag_target_columns: List[str] = field(default_factory=lambda: ['close', 'volume', 'log_return']) # Columns for lag features
    lag_periods: List[int] = field(default_factory=lambda: [1, 2, 3, 6, 12, 24, 48, 72, 168]) # Lag periods
    rolling_window_sizes: List[int] = field(default_factory=lambda: [6, 12, 24, 72, 168]) # Rolling window sizes
    rolling_target_columns: List[str] = field(default_factory=lambda: ['close', 'volume', 'log_return']) # Columns for rolling features
    
    # --- 新增：市场状态定义相关参数 ---
    rsi_strong_duration: int = 3       # RSI 持续超买/超卖的K线数 (用于强趋势)
    adx_strong_duration: int = 3       # ADX 持续强势的K线数 (用于强趋势)
    adx_trend_threshold: float = 25.0  # ADX 判断趋势强度的阈值
    adx_choppy_threshold_low: float = 20.0 # ADX 判断盘整/震荡的下阈值
    adx_choppy_threshold_high: float = 30.0 # ADX 判断震荡的阈值上限 (例如ADX在20-30之间但DI频繁交叉)
    rsi_overbought_threshold: float = 70.0
    rsi_oversold_threshold: float = 30.0
    rsi_mid_zone_low: float = 40.0     # RSI 中间区域下限 (用于盘整)
    rsi_mid_zone_high: float = 60.0    # RSI 中间区域上限 (用于盘整)
    macd_zero_proximity_threshold: float = 0.0005 # 根据MACD实际数值范围调整 (用于盘整时MACD接近0轴)
    atr_consolidation_multiplier: float = 2.0 # 盘整状态ATR倍数 ((High-Low) <= X * ATR)
    volume_increase_factor_breakout: float = 1.5 # 突破时成交量放大倍数 (与滚动平均成交量比)
    bb_squeeze_threshold_factor: float = 0.8 # 布林带宽度与ATR或价格的比例，用于判断squeeze后突破
    # --- 结束：市场状态定义相关参数 ---
    
    # --- 新增：盘整状态量化相关参数 ---
    consolidation_ma_spread_pct_threshold: float = 0.01       # MA相对价差阈值 (占收盘价百分比)
    consolidation_ma_spread_atr_threshold: float = 0.3        # MA相对价差阈值 (ATR倍数)
    consolidation_ma_std_pct_threshold: float = 0.005       # MA横截面标准差阈值 (占收盘价百分比)
    consolidation_ma_std_atr_threshold: float = 0.15        # MA横截面标准差阈值 (ATR倍数)
    consolidation_slope_calculation_window: int = 10          # 用于计算MA斜率的回看窗口期
    consolidation_ma_slope_atr_threshold: float = 0.01      # MA斜率阈值 (ATR倍数)
    consolidation_macd_norm_atr_threshold: float = 0.1        # MACD归一化(ATR)后绝对值阈值
    consolidation_macd_zscore_window: int = 20              # 用于计算MACD Z-score的滚动窗口期
    consolidation_macd_zscore_threshold: float = 0.8          # MACD Z-score绝对值阈值
    # --- 结束：盘整状态量化相关参数 ---
    
    # 特征组配置
    feature_groups: Dict[str, bool] = field(default_factory=lambda: {
        'technical': True,
        'time': True,
        'price': True,
        'volume': True,
        'candlestick': True,
        'seasonal': True,
        'trend_decomposition': True,
        'lag': True, # Added lag features group
        'rolling_stats': True # Added rolling stats group
    })
    
    # 数据预处理配置
    fillna_method: str = 'median'  # 'mean', 'median', 'ffill', 'bfill'
    scaling_method: str = 'standard'  # 'standard', 'minmax', 'robust'
    
    # 并行处理配置
    enable_parallel_processing: bool = False # Default to False to avoid pickling errors on Windows temporarily
    max_workers: Optional[int] = None # None 通常表示使用CPU核心数
    
    # 自定义特征配置
    enable_custom_features: bool = True
    custom_feature_groups_to_run: List[str] = field(default_factory=list) # 空列表表示运行所有自定义特征组
    
    # New: Univariate feature selection parameters
    enable_univariate_selection: bool = False # Default to False
    univariate_selection_k: int = 50
    univariate_selection_score_func: str = 'f_classif' # Options: 'f_classif', 'mutual_info_classif'
    
    # New: RFE parameters
    enable_rfe_selection: bool = False # Default to False
    rfe_estimator_type: str = 'random_forest' # Options: 'random_forest', 'logistic_regression'
    rfe_n_features_to_select: Optional[int] = None # None means 0.5 * n_features
    rfe_step: Union[int, float] = 1 # Number of features to remove at each iteration or percentage
    # Consider adding estimator-specific params for RFE, e.g., rfe_rf_n_estimators, rfe_lr_C

    # New: RFECV specific parameters
    rfe_use_cv: bool = True # Default to True now, if RFE is enabled
    rfe_cv_folds: int = 3 # Number of folds for RFECV
    rfe_cv_scoring: Optional[str] = 'accuracy' # Scoring metric for RFECV, e.g., 'roc_auc', 'f1'
    rfe_min_features_to_select: int = 1 # Minimum number of features for RFECV

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'FeatureConfig':
        """从字典创建 FeatureConfig 实例。"""
        # 获取dataclass所有字段的名称和类型
        known_fields = {f.name: f.type for f in fields(cls)}
        config_args = {}

        for key, value in config_dict.items():
            if key in known_fields:
                # TODO: 更复杂的类型转换或校验可以在这里添加，例如对于List[int]等
                # For now, assume direct assignment is okay or types are compatible.
                # A more robust solution might inspect known_fields[key] and cast value.
                config_args[key] = value
            else:
                logger.warning(f"在 FeatureConfig 定义中未找到配置项 '{key}'，将被忽略。")
        
        try:
            return cls(**config_args)
        except TypeError as e:
            logger.error(f"从字典创建FeatureConfig时发生类型错误: {e}. "
                         f"提供的参数: {config_args}")
            raise ValueError(f"无法从提供的字典创建FeatureConfig: {e}")

    @classmethod
    def from_yaml(cls, yaml_path: Union[str, Path]) -> 'FeatureConfig':
        """从YAML文件加载FeatureConfig。"""
        if not YAML_AVAILABLE:
            logger.error("PyYAML库不可用。请安装它 (pip install pyyaml) 以从YAML加载配置。")
            raise ImportError("PyYAML is not installed. Cannot load config from YAML.")
        
        yaml_path = Path(yaml_path)
        if not yaml_path.exists():
            logger.error(f"YAML配置文件未找到: {yaml_path}")
            raise FileNotFoundError(f"YAML configuration file not found: {yaml_path}")

        logger.info(f"从YAML文件加载FeatureConfig: {yaml_path}")
        try:
            with open(yaml_path, 'r', encoding='utf-8') as f: # Added encoding
                config_dict = yaml.safe_load(f)
            if not isinstance(config_dict, dict):
                raise ValueError("YAML文件内容未解析为字典。")
            return cls.from_dict(config_dict)
        except yaml.YAMLError as e:
            logger.error(f"解析YAML文件 '{yaml_path}' 时出错: {e}")
            raise ValueError(f"Error parsing YAML file '{yaml_path}': {e}")
        except Exception as e:
            logger.error(f"从YAML文件 '{yaml_path}' 加载FeatureConfig时发生未知错误: {e}")
            raise
            
    def to_dict(self) -> Dict[str, Any]:
        """将 FeatureConfig 实例转换为字典。"""
        return {f.name: getattr(self, f.name) for f in fields(self)}

    def to_yaml(self, yaml_path: Union[str, Path]):
        """将当前 FeatureConfig 保存到YAML文件。"""
        if not YAML_AVAILABLE:
            logger.error("PyYAML库不可用。请安装它 (pip install pyyaml) 以保存配置到YAML。")
            raise ImportError("PyYAML is not installed. Cannot save config to YAML.")

        yaml_path = Path(yaml_path)
        yaml_path.parent.mkdir(parents=True, exist_ok=True) # Ensure parent directory exists
        logger.info(f"将FeatureConfig保存到YAML文件: {yaml_path}")
        try:
            config_dict = self.to_dict()
            with open(yaml_path, 'w', encoding='utf-8') as f: # Added encoding
                yaml.dump(config_dict, f, sort_keys=False, default_flow_style=False, allow_unicode=True) # Added allow_unicode
            logger.info(f"FeatureConfig成功保存到: {yaml_path}")
        except Exception as e:
            logger.error(f"保存FeatureConfig到YAML文件 '{yaml_path}' 时发生错误: {e}")
            raise

    def __post_init__(self):
        """验证配置参数"""
        if self.prediction_window <= 0:
            raise ValueError("prediction_window must be positive")
        if self.memory_threshold <= 0 or self.memory_threshold >= 1:
            raise ValueError("memory_threshold must be between 0 and 1")
        if self.chunk_size <= 0:
            raise ValueError("chunk_size must be positive")
        if self.max_cache_size_gb <= 0:
            raise ValueError("max_cache_size_gb must be positive")
        if self.cache_ttl_days <= 0:
            raise ValueError("cache_ttl_days must be positive")
        if not isinstance(self.custom_feature_groups_to_run, list):
            raise ValueError("custom_feature_groups_to_run must be a list.")
        if self.variance_threshold_value < 0:
            raise ValueError("variance_threshold_value must be non-negative.")
        if not (0 < self.correlation_threshold <= 1):
            raise ValueError("correlation_threshold must be between 0 (exclusive) and 1 (inclusive).")
        if self.vif_threshold <= 1:
            raise ValueError("vif_threshold must be greater than 1.")
        if self.max_features_after_importance <= 0:
            raise ValueError("max_features_after_importance must be positive.")
        
        # Validations for new univariate selection parameters
        if not isinstance(self.enable_univariate_selection, bool):
            raise ValueError("enable_univariate_selection must be a boolean.")
        if not isinstance(self.univariate_selection_k, int) or self.univariate_selection_k <= 0:
            raise ValueError("univariate_selection_k must be a positive integer.")
        if self.univariate_selection_score_func not in ['f_classif', 'mutual_info_classif']:
            raise ValueError("univariate_selection_score_func must be 'f_classif' or 'mutual_info_classif'.")
        
        # Validations for new RFE parameters
        if not isinstance(self.enable_rfe_selection, bool):
            raise ValueError("enable_rfe_selection must be a boolean.")
        if self.rfe_estimator_type not in ['random_forest', 'logistic_regression']:
            raise ValueError("rfe_estimator_type must be 'random_forest' or 'logistic_regression'.")
        if self.rfe_n_features_to_select is not None and (not isinstance(self.rfe_n_features_to_select, int) or self.rfe_n_features_to_select <= 0):
            raise ValueError("rfe_n_features_to_select must be a positive integer or None.")
        if not isinstance(self.rfe_step, (int, float)) or self.rfe_step <= 0:
            raise ValueError("rfe_step must be a positive number (int or float).")
        if isinstance(self.rfe_step, float) and not (0 < self.rfe_step < 1):
            raise ValueError("If rfe_step is a float, it must be between 0 and 1 (exclusive).")
        
        # Ensure cache directory exists
        try:
            Path(self.cache_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            logger.warning(f"无法创建缓存目录 {self.cache_dir}: {e}")
        # pass # Remove pass if validations are added above it

        # Validations for RFECV parameters
        if not isinstance(self.rfe_use_cv, bool):
            raise ValueError("rfe_use_cv must be a boolean.")
        if not isinstance(self.rfe_cv_folds, int) or self.rfe_cv_folds <= 1:
            raise ValueError("rfe_cv_folds must be an integer greater than 1.")
        # Scoring can be None, or a string. Add more valid scoring options if necessary.
        if self.rfe_cv_scoring is not None and not isinstance(self.rfe_cv_scoring, str):
            raise ValueError("rfe_cv_scoring must be a string or None.")
        if not isinstance(self.rfe_min_features_to_select, int) or self.rfe_min_features_to_select <= 0:
            raise ValueError("rfe_min_features_to_select must be a positive integer.")

        Path(self.data_quality_report_dir).mkdir(parents=True, exist_ok=True) # Create dir for reports

@dataclass
class RegisteredFeature:
    name: str
    func: FeatureFunctionType
    group: str = "custom" # Default group for custom features
    description: Optional[str] = None
    # One could add dependencies, default parameters for the function, etc.

class FeatureRegistry:
    """特征函数的注册中心。"""
    def __init__(self):
        self._features: Dict[str, RegisteredFeature] = {}
        logger.info("特征注册中心已初始化。")

    def register(
        self,
        name: str,
        func: FeatureFunctionType,
        group: str = "custom",
        description: Optional[str] = None,
        overwrite: bool = False
    ):
        """
        注册一个新的特征计算函数。

        参数:
            name (str): 特征的唯一名称。
            func (Callable): 特征计算函数。它应该接受一个DataFrame和可选的FeatureConfig，
                             并返回一个带有新特征的DataFrame。
            group (str): 特征所属的组，用于选择性执行 (例如, 'custom_technical', 'custom_fundamental')。
            description (Optional[str]): 特征的简短描述。
            overwrite (bool): 如果为True，则允许覆盖同名的已注册特征。
        """
        if name in self._features and not overwrite:
            logger.error(f"特征 '{name}' 已经注册。设置 overwrite=True 以覆盖。")
            raise ValueError(f"特征 '{name}' 已经注册。")
        
        if not callable(func):
            logger.error(f"提供的特征函数 '{name}' ({func}) 不是可调用的。")
            raise TypeError(f"特征函数 '{name}' 必须是可调用的。")

        self._features[name] = RegisteredFeature(name=name, func=func, group=group, description=description)
        logger.info(f"特征 '{name}' (组: '{group}') 已成功注册。描述: {description or 'N/A'}")

    def unregister(self, name: str):
        """注销一个特征。"""
        if name in self._features:
            del self._features[name]
            logger.info(f"特征 '{name}' 已注销。")
        else:
            logger.warning(f"尝试注销一个未注册的特征: '{name}'。")

    def get_feature(self, name: str) -> Optional[RegisteredFeature]:
        """按名称获取已注册的特征。"""
        return self._features.get(name)

    def get_features_by_group(self, group: str) -> List[RegisteredFeature]:
        """按组获取已注册的特征。"""
        return [rf for rf in self._features.values() if rf.group == group]

    def get_all_features(self) -> List[RegisteredFeature]:
        """获取所有已注册的特征。"""
        return list(self._features.values())

    def list_registered_features(self) -> List[Dict[str, str]]:
        """列出所有已注册特征的摘要信息。"""
        return [
            {"name": rf.name, "group": rf.group, "description": rf.description or "N/A"}
            for rf in self._features.values()
        ]

class FeatureEngineer:
    def __init__(self, config: Optional[FeatureConfig] = None, registry: Optional[FeatureRegistry] = None):
        """
        初始化特征工程类
        
        参数:
            config (FeatureConfig, optional): 特征工程配置对象。
            registry (FeatureRegistry, optional): 特征注册中心实例。
        """
        self.config = config or FeatureConfig()
        self.registry = registry or FeatureRegistry() 
        self.cache_dir = Path(self.config.cache_dir)
        self.data_quality_report_dir = Path(self.config.data_quality_report_dir) # New
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.data_quality_report_dir.mkdir(parents=True, exist_ok=True) # New
        self._setup_memory_management()
        logger.info(f"FeatureEngineer 初始化完成。自定义特征启用: {self.config.enable_custom_features}, 待运行组: {self.config.custom_feature_groups_to_run or '所有'}")
        logger.info(f"报告目录: {self.data_quality_report_dir}")
        if not STATSMODELS_AVAILABLE: # Log VIF unavailability here after logger is set up
            logger.warning("statsmodels库未安装，VIF计算功能将不可用。请运行 'pip install statsmodels' 进行安装。")
        if not STATSMODELS_STL_AVAILABLE: # Log STL unavailability here
            logger.warning("statsmodels.tsa.seasonal.STL 不可用，趋势分解功能将受限。")
        
    def _setup_memory_management(self):
        """设置内存管理参数"""
        self.memory_threshold = self.config.memory_threshold
        self.chunk_size = self.config.chunk_size
        
    def _manage_cache(self):
        """管理缓存文件，删除过期或超出大小限制的缓存"""
        try:
            current_time = datetime.now()
            total_size = 0
            cache_files = []
            
            # 收集缓存文件信息
            for cache_file in self.cache_dir.glob("*.pkl"):
                stats = cache_file.stat()
                age_days = (current_time - datetime.fromtimestamp(stats.st_mtime)).days
                size_gb = stats.st_size / (1024 ** 3)  # 转换为GB
                cache_files.append({
                    'path': cache_file,
                    'age_days': age_days,
                    'size_gb': size_gb
                })
                total_size += size_gb
            
            # 按年龄排序
            cache_files.sort(key=lambda x: x['age_days'], reverse=True)
            
            # 删除过期文件
            for file_info in cache_files:
                if file_info['age_days'] > self.config.cache_ttl_days:
                    file_info['path'].unlink()
                    total_size -= file_info['size_gb']
                    logger.info(f"删除过期缓存文件: {file_info['path']}")
                elif total_size > self.config.max_cache_size_gb:
                    file_info['path'].unlink()
                    total_size -= file_info['size_gb']
                    logger.info(f"删除超出大小限制的缓存文件: {file_info['path']}")
                else:
                    break
                    
        except Exception as e:
            logger.error(f"管理缓存文件时发生错误: {str(e)}")
            
    def _optimize_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame的内存使用"""
        try:
            start_mem = df.memory_usage().sum() / 1024**2  # MB
            
            # 转换数值类型
            if self.config.use_float32:
                float_cols = df.select_dtypes(include=['float64']).columns
                df[float_cols] = df[float_cols].astype('float32')
                
            int_cols = df.select_dtypes(include=['int64']).columns
            for col in int_cols:
                df[col] = pd.to_numeric(df[col], downcast='integer')
                
            # 转换分类数据
            categorical_threshold = 0.5  # 如果唯一值比例小于此值，转换为category
            obj_cols = df.select_dtypes(include=['object']).columns
            for col in obj_cols:
                unique_ratio = df[col].nunique() / len(df)
                if unique_ratio < categorical_threshold:
                    df[col] = df[col].astype('category')
                    
            end_mem = df.memory_usage().sum() / 1024**2  # MB
            reduction = (start_mem - end_mem) / start_mem * 100
            
            logger.info(f"内存优化: {start_mem:.2f}MB -> {end_mem:.2f}MB ({reduction:.1f}% 减少)")
            
            return df
            
        except Exception as e:
            logger.error(f"优化内存使用时发生错误: {str(e)}")
            return df
            
    def _check_and_optimize_memory(self):
        """检查并优化内存使用"""
        try:
            memory_usage = psutil.virtual_memory().percent / 100
            if memory_usage > self.memory_threshold:
                logger.warning(f"内存使用率 ({memory_usage*100:.1f}%) 超过阈值 ({self.memory_threshold*100:.1f}%)")
                gc.collect()  # 强制垃圾回收
                
        except Exception as e:
            logger.error(f"检查内存使用时发生错误: {str(e)}")
        
    def _get_cache_key(self, df: pd.DataFrame, feature_type: str) -> str:
        """生成缓存键"""
        # 添加配置信息到缓存键
        config_str = f"{self.config.prediction_window}_{feature_type[:10]}" 
        data_hash = hashlib.md5(pd.util.hash_pandas_object(df.head(10) if not df.empty else df, index=True).values).hexdigest()[:10]
        return f"{data_hash}_{config_str}"
        
    def _get_cache_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.pkl"
        
    def _load_from_cache(self, cache_key: str, force_refresh: bool = False) -> Optional[pd.DataFrame]:
        """从缓存加载特征"""
        if force_refresh:
            logger.info(f"强制刷新模式: 跳过特征缓存加载 for key {cache_key}")
            return None

        cache_path = self._get_cache_path(cache_key)
        if cache_path.exists():
            try:
                # Check cache TTL
                if self.config.cache_ttl_days > 0:  # Only check TTL if it's a positive value
                    cache_file_mtime = cache_path.stat().st_mtime
                    cache_age_seconds = time.time() - cache_file_mtime
                    cache_age_days = cache_age_seconds / (24 * 3600)
                    if cache_age_days > self.config.cache_ttl_days:
                        logger.info(f"特征缓存文件 {cache_path.name} (key: {cache_key}) 已过期 (年龄: {cache_age_days:.2f} 天, TTL: {self.config.cache_ttl_days} 天)，将重新计算。")
                        # Optionally, delete the expired cache file
                        # try:
                        #     cache_path.unlink()
                        #     logger.info(f"已删除过期缓存文件: {cache_path.name}")
                        # except OSError as e_del:
                        #     logger.warning(f"删除过期缓存文件 {cache_path.name} 失败: {e_del}")
                        return None
                
                # If not expired or TTL is not set, load it
                with cache_path.open('rb') as f:
                    loaded_data = pickle.load(f)
                logger.info(f"成功从缓存加载特征: {cache_key} (路径: {cache_path.name})")
                return loaded_data
            except Exception as e:
                logger.warning(f"加载缓存 {cache_key} (路径: {cache_path.name}) 失败: {str(e)}")
        else:
            logger.debug(f"特征缓存文件未找到: {cache_path.name} (key: {cache_key})")
        return None
        
    def _save_to_cache(self, df: pd.DataFrame, cache_key: str):
        """保存特征到缓存"""
        try:
            # 检查并管理缓存
            self._manage_cache()
            
            cache_path = self._get_cache_path(cache_key)
            with cache_path.open('wb') as f:
                pickle.dump(df, f)
                
            # 优化内存
            self._check_and_optimize_memory()
            
        except Exception as e:
            logger.warning(f"保存缓存失败: {str(e)}")
            
    def _check_memory_usage(self) -> bool:
        """检查内存使用情况"""
        memory_usage = psutil.virtual_memory().percent / 100
        return memory_usage > self.memory_threshold
        
    def _process_in_chunks(self, df: pd.DataFrame, func, desc: str = "Processing chunks") -> pd.DataFrame:
        """分块处理数据，支持并行。"""
        try:
            # 如果数据量不大或未启用并行，则直接处理
            if not self.config.enable_parallel_processing or len(df) <= self.chunk_size * 1.5:  # 乘以1.5避免为略大于chunk_size的数据启动并行
                logger.info(f"数据量较小或已禁用并行处理，将直接处理 '{desc}'。")
                result = func(df)
                return self._optimize_memory(result)
            
            # 并行处理逻辑
            chunks = [df[i:i + self.chunk_size] for i in range(0, len(df), self.chunk_size)]
            results = []
            
            num_workers = self.config.max_workers
            if num_workers is None:
                try:
                    num_workers = min(os.cpu_count() or 1, 8)  # 限制最大worker数量为8，避免资源过度消耗
                except NotImplementedError:
                    num_workers = 2  # 默认值
                logger.info(f"max_workers 未指定, 自动设置为: {num_workers}")
            else:
                num_workers = max(1, num_workers)
            
            logger.info(f"开始并行处理 '{desc}'，共 {len(chunks)} 个数据块，使用 {num_workers} 个 workers。")

            with ProcessPoolExecutor(max_workers=num_workers) as executor:
                future_to_chunk_index = {executor.submit(func, chunk): i for i, chunk in enumerate(chunks)}
                processed_results_list = [None] * len(chunks)  # Pre-allocate list to maintain order

                for future in tqdm(as_completed(future_to_chunk_index), total=len(chunks), desc=desc):
                    chunk_index = future_to_chunk_index[future]
                    try:
                        processed_results_list[chunk_index] = future.result()
                    except Exception as exc:
                        logger.error(f'{desc} - 数据块 {chunk_index} (原始索引 {chunks[chunk_index].index[0] if not chunks[chunk_index].empty else "N/A"}) 处理失败: {exc}', exc_info=True)

                for result_chunk in processed_results_list:
                    if result_chunk is not None:  # Only append if successfully processed
                        optimized_chunk = self._optimize_memory(result_chunk)
                        results.append(optimized_chunk)
                        self._check_and_optimize_memory()
                
                if not results:  # If all chunks failed or no chunks to process
                    logger.warning(f"并行处理 '{desc}' 后没有有效结果。返回空DataFrame。")
                    return pd.DataFrame()  # Return empty DataFrame if no results

                final_result = pd.concat(results)
                return self._optimize_memory(final_result)
            
        except Exception as e:
            logger.error(f"分块并行处理数据 ('{desc}') 时发生错误: {str(e)}", exc_info=True)
            logger.warning(f"并行处理失败，尝试以串行方式处理 '{desc}'...")
            try:
                chunks_fallback = [df[i:i + self.chunk_size] for i in range(0, len(df), self.chunk_size)]
                results_fallback = []
                for chunk_fb in tqdm(chunks_fallback, desc=f"{desc} (串行回退)"):
                    result_fb = func(chunk_fb)
                    result_fb = self._optimize_memory(result_fb)
                    results_fallback.append(result_fb)
                    self._check_and_optimize_memory()
                
                if not results_fallback:
                    logger.warning(f"串行回退处理 '{desc}' 后没有有效结果。返回空DataFrame。")
                    return pd.DataFrame()

                final_result_fallback = pd.concat(results_fallback)
                return self._optimize_memory(final_result_fallback)
            except Exception as fallback_e:
                logger.error(f"串行回退处理 ('{desc}') 也失败: {fallback_e}", exc_info=True)
                raise fallback_e  # 再次抛出，让调用者处理
    
    # 在调用 _process_in_chunks 的地方传递 desc 参数
    def add_time_features(self, df: pd.DataFrame, force_refresh: bool = False) -> pd.DataFrame:
        """计算时间特征 (v2)"""
        # Use a more descriptive cache key if logic has significantly changed.
        cache_key = self._get_cache_key(df, 'time_v2') 
        cached_df = self._load_from_cache(cache_key, force_refresh=force_refresh)
        if cached_df is not None:
            logger.info(f"从缓存加载时间特征 (v2): {cache_key}")
            return cached_df
            
        def process_chunk(chunk):
            chunk = chunk.copy()
            # 基础时间特征
            chunk['hour'] = chunk.index.hour
            chunk['day_of_week'] = chunk.index.dayofweek
            chunk['month'] = chunk.index.month
            chunk['is_weekend'] = chunk['day_of_week'].isin([5, 6]).astype(int)
            
            # 新增：更细粒度的时间特征 (基于分钟和秒)
            temp_minute_col = chunk.index.minute # 用于计算衍生特征
            chunk['second'] = chunk.index.second # 如果数据有秒级精度

            chunk['is_minute_0'] = (temp_minute_col == 0).astype(int)
            chunk['is_minute_15_interval'] = (temp_minute_col % 15 == 0).astype(int)
            chunk['is_minute_30_interval'] = (temp_minute_col % 30 == 0).astype(int)
            
            # 当前小时内的5分钟区间 (0-11)
            chunk['five_minute_interval_of_hour'] = temp_minute_col // 5
            
            # 周期性时间特征
            chunk['hour_sin'] = np.sin(2 * np.pi * chunk['hour']/24)
            chunk['hour_cos'] = np.cos(2 * np.pi * chunk['hour']/24)
            chunk['day_sin'] = np.sin(2 * np.pi * chunk['day_of_week']/7)
            chunk['day_cos'] = np.cos(2 * np.pi * chunk['day_of_week']/7)
            chunk['minute_sin'] = np.sin(2 * np.pi * temp_minute_col/60)
            chunk['minute_cos'] = np.cos(2 * np.pi * temp_minute_col/60)
            chunk['second_sin'] = np.sin(2 * np.pi * chunk['second']/60) # 如果数据有秒级精度
            chunk['second_cos'] = np.cos(2 * np.pi * chunk['second']/60) # 如果数据有秒级精度
            
            # 交易时间特征
            chunk['is_asia_session'] = ((chunk['hour'] >= 1) & (chunk['hour'] < 9)).astype(int)
            chunk['is_europe_session'] = ((chunk['hour'] >= 8) & (chunk['hour'] < 16)).astype(int)
            chunk['is_us_session'] = ((chunk['hour'] >= 14) & (chunk['hour'] < 22)).astype(int)
            
            return chunk
            
        result = self._process_in_chunks(df, process_chunk, desc="Adding Time Features")
        self._save_to_cache(result, cache_key)
        return result
        
    def add_price_features(self, df: pd.DataFrame, force_refresh: bool = False) -> pd.DataFrame:
        """
        计算价格相关特征。
        包括对数收益率、价格动量、价格比率、波动性指标等。
        """
        cache_key = self._get_cache_key(df, 'price_v2') # Changed key for v2
        cached_df = self._load_from_cache(cache_key, force_refresh=force_refresh)
        if cached_df is not None:
            logger.info(f"从缓存加载价格特征 (v2): {cache_key}")
            return cached_df
            
        original_columns = df.columns.tolist() # 保存原始列名，以便后续只处理新增的

        def process_chunk(chunk_df):
            # 确保使用原始数据的副本进行修改，避免 SettingWithCopyWarning
            chunk = chunk_df.copy()

            # 检查必需的列是否存在
            required_cols = ['open', 'high', 'low', 'close']
            if not all(col in chunk.columns for col in required_cols):
                logger.warning(f"process_chunk in add_price_features is missing one of {required_cols}. Skipping price feature calculation for this chunk.")
                return chunk
            
            # 对数收益率
            chunk['log_return'] = np.log(chunk['close'] / chunk['close'].shift(1))
            chunk['log_return_24h'] = np.log(chunk['close'] / chunk['close'].shift(24))
            chunk['log_return_7d'] = np.log(chunk['close'] / chunk['close'].shift(168))
            
            # 价格动量
            for period in [6, 12, 24, 72, 168]:
                chunk[f'momentum_{period}h'] = chunk['close'] / chunk['close'].shift(period) - 1
            
            # 价格比率
            chunk['high_low_ratio'] = chunk['high'] / chunk['low']
            # chunk['close_open_ratio'] = chunk['close'] / chunk['open'] # open_div_close 会替代它

            # 新增：将 open, high, low 转换为相对于 close 的特征
            # 先处理 close 可能为0的情况，避免除零错误
            safe_close = chunk['close'].replace(0, np.nan) # 替换0为NaN，除以NaN结果还是NaN

            chunk['high_div_close'] = chunk['high'] / safe_close
            chunk['low_div_close'] = chunk['low'] / safe_close
            chunk['open_div_close'] = chunk['open'] / safe_close
            
            # 波动性指标
            chunk['true_range'] = pd.DataFrame({
                'hl': chunk['high'] - chunk['low'],
                'hc': abs(chunk['high'] - chunk['close'].shift(1)),
                'lc': abs(chunk['low'] - chunk['close'].shift(1))
            }).max(axis=1)
            
            # 波动率
            for period in [24, 72, 168]:
                chunk[f'volatility_{period}h'] = chunk['true_range'].rolling(window=period).std()
            
            # 新增：短期/长期波动率比率
            if all(col in chunk.columns for col in ['volatility_24h', 'volatility_168h']):
                # 安全除法，避免除以0或NaN
                denominator = chunk['volatility_168h'].replace(0, np.nan)
                chunk['volatility_ratio_24_168'] = chunk['volatility_24h'] / denominator
            else:
                chunk['volatility_ratio_24_168'] = np.nan # 如果基础列不存在，则填充NaN
                
            return chunk
            
        result = self._process_in_chunks(df, process_chunk, desc="Adding Price Features")
        self._save_to_cache(result, cache_key)
        
        # 在 create_features 中，我们会先调用 calculate_technical_indicators, 
        # 然后 add_time_features, add_price_features, etc.
        # 最终的 X, y 分割会在 create_features 的最后进行。
        # OHLC列如果在这里被移除，后续TA计算可能会失败。
        # 所以，这里的策略应该是：不直接移除OHL，而是由调用者 (create_features) 在最后选择哪些列进入X
        # 但为了VIF分析，我们需要一个不包含原始OHL的X。 
        # 因此，在 get_feature_target_split 中，需要排除原始的 open, high, low (如果新版特征已创建)
        # 但为了VIF分析，我们需要一个不包含原始OHL的X。 
        # 因此，在 get_feature_target_split 中，需要排除原始的 open, high, low (如果新版特征已创建)
        return result
        
    def add_volume_features(self, df: pd.DataFrame, force_refresh: bool = False) -> pd.DataFrame:
        """计算成交量相关特征"""
        cache_key = self._get_cache_key(df, 'volume_v2') # Ensure a unique key for this version
        cached_df = self._load_from_cache(cache_key, force_refresh=force_refresh)
        if cached_df is not None:
            logger.info(f"从缓存加载成交量特征 (v2): {cache_key}")
            return cached_df
            
        def process_chunk(chunk):
            chunk = chunk.copy()
            # 交易量变化
            chunk['volume_change'] = chunk['volume'].pct_change()
            
            # 移动平均
            for period in [6, 12, 24, 72, 168]:
                chunk[f'volume_ma_{period}h'] = chunk['volume'].rolling(window=period).mean()
                chunk[f'volume_std_{period}h'] = chunk['volume'].rolling(window=period).std()
            
            # 成交量RSI
            chunk['volume_rsi_24h'] = self._calculate_rsi(chunk['volume'], 24)
            
            # 价格成交量相关性
            chunk['price_volume_corr_24h'] = (
                chunk['close'].rolling(24)
                .corr(chunk['volume'])
            )
            
            # 新增：成交量趋势指标
            chunk['volume_trend'] = (
                chunk['volume'] / chunk['volume'].rolling(24).mean()
            )
            
            # 新增：异常交易量检测
            volume_std = chunk['volume'].rolling(24).std()
            chunk['volume_zscore'] = (
                (chunk['volume'] - chunk['volume'].rolling(24).mean()) / 
                volume_std.replace(0, 1)  # 避免除以0
            )
            
            return chunk
            
        result = self._process_in_chunks(df, process_chunk, desc="Adding Volume Features")
        self._save_to_cache(result, cache_key)
        return result
        
    def _calculate_fibonacci_levels(self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 20) -> pd.DataFrame:
        """计算基于固定回看窗口的斐波那契相关特征"""
        rolling_high = high.rolling(window=window).max()
        rolling_low = low.rolling(window=window).min()
        price_range = rolling_high - rolling_low
        
        fib_levels = pd.DataFrame(index=high.index)
        # 相对于当前收盘价的位置
        fib_levels[f'fib_0.236_dist'] = (close - (rolling_high - price_range * 0.236)) / close
        fib_levels[f'fib_0.382_dist'] = (close - (rolling_high - price_range * 0.382)) / close
        fib_levels[f'fib_0.500_dist'] = (close - (rolling_high - price_range * 0.500)) / close
        fib_levels[f'fib_0.618_dist'] = (close - (rolling_high - price_range * 0.618)) / close
        fib_levels[f'fib_0.786_dist'] = (close - (rolling_high - price_range * 0.786)) / close
        
        # 当前价格是否在特定斐波那契区间内 (示例)
        fib_levels[f'is_above_fib_0.618'] = (close > (rolling_high - price_range * 0.618)).astype(int)
        fib_levels[f'is_below_fib_0.382'] = (close < (rolling_high - price_range * 0.382)).astype(int)
        return fib_levels

    def calculate_technical_indicators(self, df: pd.DataFrame, force_refresh: bool = False) -> pd.DataFrame:
        """计算技术指标（并行处理），包含新增指标"""
        # 确保输入DataFrame有datetime索引
        if not isinstance(df.index, pd.DatetimeIndex):
            try:
                df.index = pd.to_datetime(df.index)
            except Exception as e:
                logger.error(f"无法将索引转换为DatetimeIndex: {e}. TA计算可能失败。")
                # Consider returning df or raising error based on strictness
        
        cache_key = self._get_cache_key(df, 'technical_indicators_v2')
        cached_df = self._load_from_cache(cache_key, force_refresh=force_refresh)
        if cached_df is not None:
            logger.info(f"从缓存加载技术指标 (v2): {cache_key}")
            return cached_df
            
        def process_chunk(chunk_df):
            chunk = chunk_df.copy()
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                # 移动平均线 (SMA)
                for period in self.config.ma_periods: # 使用 FeatureConfig 中的定义
                    indicator = SMAIndicator(close=chunk['close'], window=period, fillna=True)
                    chunk[f'MA_{period}'] = indicator.sma_indicator() # 确保MA和period之间有下划线
                
                # MACD
                macd_indicator = MACD(close=chunk['close'], window_slow=self.config.macd_slow_period, window_fast=self.config.macd_fast_period, window_sign=self.config.macd_signal_period, fillna=True)
                chunk['MACD_line'] = macd_indicator.macd() 
                chunk['MACD_signal_line'] = macd_indicator.macd_signal()
                chunk['MACD_hist'] = macd_indicator.macd_diff()
                
                # 布林带 (Bollinger Bands)
                bb_indicator = BollingerBands(close=chunk['close'], window=self.config.bb_window, window_dev=self.config.bb_std_dev, fillna=True)
                chunk['BB_upperband'] = bb_indicator.bollinger_hband()
                chunk['BB_middleband'] = bb_indicator.bollinger_mavg()
                chunk['BB_lowerband'] = bb_indicator.bollinger_lband()
                chunk['BB_percent'] = bb_indicator.bollinger_pband()
                chunk['BB_bandwidth'] = bb_indicator.bollinger_wband()
                
                # RSI
                for period in self.config.rsi_periods: # Using config periods
                    indicator = RSIIndicator(close=chunk['close'], window=period, fillna=True)
                    chunk[f'RSI_{period}'] = indicator.rsi() # Changed to RSI_period
                
                # KDJ (Stochastic Oscillator)
                indicator = StochasticOscillator(high=chunk['high'], low=chunk['low'], close=chunk['close'], fillna=True)
                chunk['K'] = indicator.stoch()
                chunk['D'] = indicator.stoch_signal()
                chunk['J'] = 3 * chunk['K'] - 2 * chunk['D']
                
                # ATR (Average True Range)
                indicator = AverageTrueRange(high=chunk['high'], low=chunk['low'], close=chunk['close'], window=14, fillna=True)
                chunk['ATR_14'] = indicator.average_true_range() # Changed name from ATR to ATR_14
                # ATR 变化率 (例如10周期)
                chunk['ATR_roc_10'] = chunk['ATR_14'].pct_change(periods=10) * 100
                
                # --- 新增指标 ---
                # 长周期 ATR (用于MTFA)
                long_atr_window = 120 # 假设1h数据，约5天
                indicator_long_atr = AverageTrueRange(high=chunk['high'], low=chunk['low'], close=chunk['close'], window=long_atr_window, fillna=True)
                chunk['ATR_long'] = indicator_long_atr.average_true_range()

                # 长周期布林带 (用于MTFA)
                long_boll_window = 120
                temp_boll_long = BollingerBands(close=chunk['close'], window=long_boll_window, window_dev=2, fillna=True)
                temp_boll_upper_long = temp_boll_long.bollinger_hband()
                temp_boll_lower_long = temp_boll_long.bollinger_lband()

                # 一目均衡表 (Ichimoku Cloud)
                ichimoku = IchimokuIndicator(high=chunk['high'], low=chunk['low'], window1=9, window2=26, window3=52, fillna=True)
                temp_ichimoku_A = ichimoku.ichimoku_a()
                temp_ichimoku_B = ichimoku.ichimoku_b()
                chunk['price_vs_cloud_A'] = chunk['close'] - temp_ichimoku_A
                chunk['price_vs_cloud_B'] = chunk['close'] - temp_ichimoku_B
                chunk['cloud_thickness'] = abs(temp_ichimoku_A - temp_ichimoku_B)

                # 斐波那契相关特征 (基于20周期高低点)
                fib_features = self._calculate_fibonacci_levels(chunk['high'], chunk['low'], chunk['close'], window=20)
                for col in fib_features.columns:
                    chunk[col] = fib_features[col]
                
                # 更多波动性指标
                # Keltner Channels
                # keltner = KeltnerChannel(high=chunk['high'], low=chunk['low'], close=chunk['close'], window=20, window_atr=10, fillna=True)
                # chunk['keltner_hband'] = keltner.keltner_channel_hband()
                # chunk['keltner_lband'] = keltner.keltner_channel_lband()
                # chunk['keltner_mband'] = keltner.keltner_channel_mband()
                # chunk['keltner_pband'] = keltner.keltner_channel_pband()

                # Donchian Channels
                donchian = DonchianChannel(high=chunk['high'], low=chunk['low'], close=chunk['close'], window=20, offset=0, fillna=True)
                chunk['donchian_hband'] = donchian.donchian_channel_hband()
                chunk['donchian_lband'] = donchian.donchian_channel_lband()
                chunk['donchian_pband'] = donchian.donchian_channel_pband()
                chunk['donchian_wband'] = donchian.donchian_channel_wband()

                # 更多动量指标
                # Williams %R
                williams_r = WilliamsRIndicator(high=chunk['high'], low=chunk['low'], close=chunk['close'], lbp=14, fillna=True)
                chunk['williams_%R'] = williams_r.williams_r()

                # Awesome Oscillator
                awesome_osc = AwesomeOscillatorIndicator(high=chunk['high'], low=chunk['low'], window1=5, window2=34, fillna=True)
                chunk['awesome_osc'] = awesome_osc.awesome_oscillator()

                # 市场强度/资金流指标
                # Chaikin Money Flow (CMF)
                cmf = ChaikinMoneyFlowIndicator(high=chunk['high'], low=chunk['low'], close=chunk['close'], volume=chunk['volume'], window=20, fillna=True)
                chunk['cmf'] = cmf.chaikin_money_flow()
                
                # Money Flow Index (MFI)
                mfi = MFIIndicator(high=chunk['high'], low=chunk['low'], close=chunk['close'], volume=chunk['volume'], window=14, fillna=True)
                chunk['mfi'] = mfi.money_flow_index()
                
                # On-Balance Volume (OBV)
                obv = OnBalanceVolumeIndicator(close=chunk['close'], volume=chunk['volume'], fillna=True)
                chunk['OBV'] = obv.on_balance_volume()
                chunk['OBV_MA10'] = chunk['OBV'].rolling(window=10, min_periods=1).mean() # OBV的10周期均线

                # ADX, +DI, -DI
                adx_indicator = ADXIndicator(high=chunk['high'], low=chunk['low'], close=chunk['close'], window=14, fillna=True)
                chunk['ADX_14'] = adx_indicator.adx()
                chunk['ADX_pos_14'] = adx_indicator.adx_pos() # DI+ , changed name from DMP_14
                chunk['ADX_neg_14'] = adx_indicator.adx_neg() # DI- , changed name from DMN_14

                # --- 开始添加组合特征 ---
                logger.debug(f"Chunk columns before combined features: {chunk.columns.tolist()}")

                # 1. MA Spread (差值)
                if all(col in chunk.columns for col in ['MA5', 'MA20', 'MA10', 'MA50', 'MA120']):
                    chunk['MA_spread_5_20'] = chunk['MA5'] - chunk['MA20']
                    chunk['MA_spread_10_50'] = chunk['MA10'] - chunk['MA50']
                    chunk['MA_spread_20_120'] = chunk['MA20'] - chunk['MA120']
                else:
                    logger.warning("Skipping MA spread features due to missing base MA columns in chunk.")

                # 2. Price vs MA (Normalized by ATR)
                if all(col in chunk.columns for col in ['close', 'MA_20', 'ATR_14']):
                    safe_ATR = chunk['ATR_14'].replace(0, np.nan).ffill().fillna(1e-9) # Changed from .fillna(method='ffill')
                    chunk['Price_MA20_norm_ATR'] = (chunk['close'] - chunk['MA_20']) / safe_ATR
                else:
                    logger.warning("Skipping Price_MA20_norm_ATR due to missing base columns (close, MA_20, or ATR_14) in chunk.")

                # 3. RSI vs MA of RSI
                if 'RSI_14' in chunk.columns: # Changed from RSI14
                    chunk['RSI14_MA5'] = chunk['RSI_14'].rolling(window=5, min_periods=1).mean()
                    chunk['RSI14_vs_MA5'] = chunk['RSI_14'] - chunk['RSI14_MA5']
                else:
                    logger.warning("Skipping RSI14_vs_MA5 due to missing RSI_14 column in chunk.") # Updated warning message

                # 4. Stochastic Crossover Signal
                if all(col in chunk.columns for col in ['K', 'D']):
                    chunk['K_cross_D_up'] = ((chunk['K'].shift(1) < chunk['D'].shift(1)) & (chunk['K'] > chunk['D'])).astype(int)
                    chunk['K_cross_D_down'] = ((chunk['K'].shift(1) > chunk['D'].shift(1)) & (chunk['K'] < chunk['D'])).astype(int)
                else:
                    logger.warning("Skipping Stochastic Crossover signals due to missing K or D columns in chunk.")
                
                # 5. BB_PERCENT relative to thresholds
                if 'BB_percent' in chunk.columns: # Changed from BOLL_%B
                    chunk['BB_extrem_oversold'] = (chunk['BB_percent'] < 0.05).astype(int)
                    chunk['BB_extrem_overbought'] = (chunk['BB_percent'] > 0.95).astype(int)
                else:
                    logger.warning("Skipping BB_extrem signals due to missing BB_percent column in chunk.") # Updated warning message

                # 6. ADX trend strength
                if 'ADX_14' in chunk.columns:
                    chunk['ADX_is_trending'] = (chunk['ADX_14'] > 25).astype(int)
                    chunk['ADX_is_strong_trending'] = (chunk['ADX_14'] > 40).astype(int) # 新增更强趋势判断
                    chunk['ADX_DMP_dominant'] = (chunk['ADX_pos_14'] > chunk['ADX_neg_14']).astype(int) # 判断是否多头占优
                else:
                    logger.warning("Skipping ADX_is_trending due to missing base ADX column (e.g., ADX_14) in chunk. Ensure ADX is calculated first.")

                # 7. MACD Histogram change direction
                if 'MACD_hist' in chunk.columns:
                    macd_hist_diff = chunk['MACD_hist'].diff()
                    chunk['MACD_hist_increasing'] = (macd_hist_diff > 0).astype(int)
                    chunk['MACD_hist_decreasing'] = (macd_hist_diff < 0).astype(int)
                else:
                    logger.warning("Skipping MACD Histogram change signals due to missing MACD_hist column in chunk.")

                # 8. Price relative to Ichimoku Cloud
                if all(col in chunk.columns for col in ['close']): # ichimoku_A, ichimoku_B 不再是列，而是用 temp_ichimoku_A/B
                    price_above_A = chunk['close'] > temp_ichimoku_A
                    price_above_B = chunk['close'] > temp_ichimoku_B
                    price_below_A = chunk['close'] < temp_ichimoku_A
                    price_below_B = chunk['close'] < temp_ichimoku_B
                    
                    chunk['Price_above_cloud'] = (price_above_A & price_above_B).astype(int)
                    chunk['Price_below_cloud'] = (price_below_A & price_below_B).astype(int)
                    chunk['Price_in_cloud'] = (~(price_above_A & price_above_B) & ~(price_below_A & price_below_B)).astype(int)
                else:
                    # logger.warning("Skipping Price relative to Ichimoku Cloud features due to missing base columns (close, ichimoku_A, or ichimoku_B) in chunk.")
                    # 更新日志或确保此路径不会轻易进入，因为 temp_ichimoku_A/B 应该总是可计算
                    if 'close' not in chunk.columns:
                         logger.warning("Skipping Price relative to Ichimoku Cloud features due to missing 'close' column.")
                    else: # A或B计算失败 (不太可能如果ichimoku对象创建成功)
                         logger.warning("Skipping Price relative to Ichimoku Cloud features, temp_ichimoku_A/B might not be available.")
                         chunk['Price_above_cloud'] = np.nan
                         chunk['Price_below_cloud'] = np.nan
                         chunk['Price_in_cloud'] = np.nan

                # --- 新增：多时间框架分析 (MTFA) 特征 ---
                # Ensure MA_120 is calculated if 120 is in ma_periods
                ma_120_col = f'MA_{120}' # Assuming 120 is a standard period we rely on, or take from config
                rsi_14_col = f'RSI_{14}' # Assuming 14 is a standard period

                if (all(col in chunk.columns for col in ['close', 'ATR_long', rsi_14_col, ma_120_col]) and 
                    all(col in locals() for col in ['temp_boll_upper_long', 'temp_boll_lower_long'])):
                    # temp_boll_upper_long and temp_boll_lower_long are local variables, not columns
                    # The original check for BOLL_UPPER_long, BOLL_LOWER_long in chunk.columns was incorrect.
                    
                    safe_ATR_long = chunk['ATR_long'].replace(0, np.nan).ffill().fillna(1e-9) # Changed

                    # 特征 MTFA 1: 长周期布林上轨压力 + 短周期RSI顶背离/反转
                    near_long_boll_upper = chunk['close'] > (temp_boll_upper_long - 0.5 * safe_ATR_long) # 使用临时变量
                    rsi_turn_down = (chunk[rsi_14_col].shift(1) > 68) & (chunk[rsi_14_col] < 68) # RSI从大于68回落到小于68
                    chunk['mtfa_long_boll_upper_rsi_turn'] = (near_long_boll_upper & rsi_turn_down).astype(int)

                    # 特征 MTFA 2: 长周期布林下轨支撑 + 短周期RSI底背离/反转
                    near_long_boll_lower = chunk['close'] < (temp_boll_lower_long + 0.5 * safe_ATR_long) # 使用临时变量
                    rsi_turn_up = (chunk[rsi_14_col].shift(1) < 32) & (chunk[rsi_14_col] > 32) # RSI从小于32回升到大于32
                    chunk['mtfa_long_boll_lower_rsi_turn'] = (near_long_boll_lower & rsi_turn_up).astype(int)

                    # 特征 MTFA 3: 长周期MA120阻力 + 价格短期反转下跌
                    # 条件: 前一期收盘价在MA120之上，当期收盘价跌破MA120，并且当期收盘价比前一期低
                    resistance_MA120 = (
                        (chunk['close'].shift(1) > chunk[ma_120_col].shift(1)) & \
                        (chunk['close'] < chunk[ma_120_col]) & \
                        (chunk['close'] < chunk['close'].shift(1))
                    )
                    chunk['mtfa_resistance_MA120_price_reversal'] = resistance_MA120.astype(int)
                    
                    # 特征 MTFA 4: 长周期MA120支撑 + 价格短期反弹上涨
                    # 条件: 前一期收盘价在MA120之下，当期收盘价突破MA120，并且当期收盘价比前一期高
                    support_MA120 = (
                        (chunk['close'].shift(1) < chunk[ma_120_col].shift(1)) & \
                        (chunk['close'] > chunk[ma_120_col]) & \
                        (chunk['close'] > chunk['close'].shift(1))
                    )
                    chunk['mtfa_support_MA120_price_rebound'] = support_MA120.astype(int)
                else:
                    logger.warning(f"Skipping MTFA features due to missing one or more base columns (close, ATR_long, {rsi_14_col}, {ma_120_col}) or local vars in chunk.")

                logger.debug(f"Chunk columns after combined features: {chunk.columns.tolist()}")
                # --- 组合特征结束 ---
                
                # 移除所有包含 NaN 的行 (这个操作可能需要谨慎，或者在更高层级处理)
                # chunk.dropna(inplace=True) # 暂时注释掉，在create_features方法末尾有统一处理
                return chunk
                
        result_df = self._process_in_chunks(df, process_chunk, desc="Calculating Technical Indicators")
        self._save_to_cache(result_df, cache_key)
        logger.info(f"技术指标计算完成 (v2)。缓存已保存到: {cache_key}")
        return result_df
    
    def _calculate_rsi(self, series: pd.Series, period: int) -> pd.Series:
        """计算RSI指标"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
        
    def analyze_market_trend(self, df: pd.DataFrame) -> dict:
        """分析市场趋势并生成详细报告"""
        try:
            latest = df.iloc[-1]
            current_price = latest['close']
            
            # 计算24小时价格变化 (假设df的频率是1小时)
            price_change_24h = np.nan # 默认为NaN
            if len(df) >= 25: # 确保有足够数据
                price_24h_ago = df['close'].iloc[-25] # -25 是为了获取24小时前的那个收盘价，当前是-1
                if price_24h_ago != 0: # 避免除以零
                    price_change_24h = ((current_price - price_24h_ago) / price_24h_ago) * 100
            
            # --- 信号定义 ---
            # 均线信号
            ma_bullish_strong = latest.get('MA5', np.nan) > latest.get('MA10', np.nan) and latest.get('MA10', np.nan) > latest.get('MA20', np.nan)
            ma_bullish_mild = latest.get('MA5', np.nan) > latest.get('MA20', np.nan)
            
            ma_bearish_strong = latest.get('MA5', np.nan) < latest.get('MA10', np.nan) and latest.get('MA10', np.nan) < latest.get('MA20', np.nan)
            ma_bearish_mild = latest.get('MA5', np.nan) < latest.get('MA20', np.nan)

            # MACD信号
            macd_bullish_strong = latest.get('MACD', np.nan) > latest.get('MACD_signal', np.nan) and latest.get('MACD_hist', np.nan) > 0
            macd_bullish_mild = latest.get('MACD', np.nan) > latest.get('MACD_signal', np.nan) or latest.get('MACD_hist', np.nan) > 0

            macd_bearish_strong = latest.get('MACD', np.nan) < latest.get('MACD_signal', np.nan) and latest.get('MACD_hist', np.nan) < 0
            macd_bearish_mild = latest.get('MACD', np.nan) < latest.get('MACD_signal', np.nan) or latest.get('MACD_hist', np.nan) < 0
            
            # 价格位置信号
            price_bullish_strong = latest.get('close', np.nan) > latest.get('ichimoku_A', np.nan) and latest.get('close', np.nan) > latest.get('ichimoku_B', np.nan) # Above cloud
            price_bullish_mild = latest.get('close', np.nan) > latest.get('BOLL_MIDDLE', np.nan) # Above middle BB

            price_bearish_strong = latest.get('close', np.nan) < latest.get('ichimoku_A', np.nan) and latest.get('close', np.nan) < latest.get('ichimoku_B', np.nan) # Below cloud
            price_bearish_mild = latest.get('close', np.nan) < latest.get('BOLL_MIDDLE', np.nan) # Below middle BB

            trend = "盘整" # Default

            # --- 趋势判定 ---
            if ma_bullish_strong and macd_bullish_strong and price_bullish_strong:
                trend = "强劲上涨"
            elif ma_bearish_strong and macd_bearish_strong and price_bearish_strong:
                trend = "强劲下跌"
            else:
                bullish_signals = sum(filter(None,[
                    ma_bullish_mild,
                    macd_bullish_mild,
                    price_bullish_mild 
                ]))
                bearish_signals = sum(filter(None,[
                    ma_bearish_mild,
                    macd_bearish_mild,
                    price_bearish_mild
                ]))

                if bullish_signals >= 2 and bearish_signals == 0:
                    if ma_bullish_mild and macd_bullish_mild and price_bullish_mild:
                        trend = "上涨 (指标共振)"
                    else:
                        trend = "偏强震荡"
                elif bearish_signals >= 2 and bullish_signals == 0:
                    if ma_bearish_mild and macd_bearish_mild and price_bearish_mild:
                        trend = "下跌 (指标共振)"
                    else:
                        trend = "偏弱震荡"
            
            if trend == "盘整":
                # 使用 .get() 避免KeyError，如果指标不存在则条件为False
                ma_intertwined = abs(latest.get('MA5', 0) - latest.get('MA20', 1)) / latest.get('MA20', 1) < 0.005 if latest.get('MA20') else False
                price_neutral_boll = latest.get('BOLL_%B', 0.5) > 0.4 and latest.get('BOLL_%B', 0.5) < 0.6
                macd_hist_val = latest.get('MACD_hist', 0)
                atr_val = latest.get('ATR', 1) # Default ATR to 1 to avoid division by zero if not present
                macd_hist_small = abs(macd_hist_val) < (atr_val * 0.1) if atr_val > 0 else False

                if ma_intertwined and price_neutral_boll:
                    trend = "盘整 (均线纠缠, 中轨附近)"
                elif ma_intertwined and macd_hist_small:
                    trend = "盘整 (均线纠缠, 动能不足)"
                elif price_neutral_boll and macd_hist_small:
                    trend = "盘整 (中轨徘徊, 动能不足)"
                elif ma_intertwined:
                    trend = "盘整 (均线纠缠)"
                elif price_neutral_boll:
                    trend = "盘整 (中轨徘徊)"
                elif macd_hist_small:
                    trend = "盘整 (动能不足)"
                
            # 获取技术指标
            technical_analysis = {
                'moving_averages': {
                    'MA5': latest.get('MA5'),
                    'MA10': latest.get('MA10'),
                    'MA20': latest.get('MA20'),
                    'MA50': latest.get('MA50'),
                    'MA100': latest.get('MA100'),
                    'MA120': latest.get('MA120'),
                    'MA200': latest.get('MA200')
                },
                'MACD': {
                    'DIF': latest.get('MACD'),
                    'DEA': latest.get('MACD_signal'),
                    'HIST': latest.get('MACD_hist')
                },
                'BOLL': {
                    'UPPER': latest.get('BOLL_UPPER'),
                    'LOWER': latest.get('BOLL_LOWER'),
                    '%B': latest.get('BOLL_%B')
                },
                'RSI': {
                    'RSI6': latest.get('RSI6'),
                    'RSI12': latest.get('RSI12'),
                    'RSI14': latest.get('RSI14'),
                    'RSI24': latest.get('RSI24')
                },
                'KDJ': {
                    'K': latest.get('K'),
                    'D': latest.get('D'),
                    'J': latest.get('J')
                },
                'ATR': latest.get('ATR'),
                'Ichimoku': {
                    'SenkouA': latest.get('ichimoku_A'),
                    'SenkouB': latest.get('ichimoku_B')
                }
            }
            
            # 计算支撑和阻力位
            support_resistance = {
                'S1_BollLower': latest.get('BOLL_LOWER'),
                'S2_BollLower_ATR': latest.get('BOLL_LOWER', np.nan) - latest.get('ATR', np.nan),
                'R1_BollUpper': latest.get('BOLL_UPPER'),
                'R2_BollUpper_ATR': latest.get('BOLL_UPPER', np.nan) + latest.get('ATR', np.nan),
                'PivotPoint': latest.get('PP'),
                'S1_Pivot': latest.get('S1'),
                'R1_Pivot': latest.get('R1')
            }
            
            # 分析成交量
            volume_analysis = {
                'current_volume': latest.get('volume'),
                'volume_ma_6h': latest.get('volume_ma_6h'),
                'volume_ma_24h': latest.get('volume_ma_24h'),
                'cmf': latest.get('cmf'),
                'mfi': latest.get('mfi')
            }
            
            return {
                'current_price': current_price,
                'price_change_24h': price_change_24h,
                'trend': trend,
                'technical_analysis': technical_analysis,
                'support_resistance': support_resistance,
                'volume_analysis': volume_analysis
            }
            
        except Exception as e:
            logger.error(f"分析市场趋势时发生错误: {str(e)}")
            raise
            
    def _create_multiclass_market_state_target(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建多分类市场状态目标变量
        """
        df = df.copy()  # 创建副本以避免 SettingWithCopyWarning
        
        # 初始化目标变量列，使用数值类型而不是分类类型
        df['target'] = pd.Series(-1, index=df.index, dtype='int64')
        
        # 定义市场状态检测函数及其优先级顺序
        state_functions = [
            self._is_strong_uptrend,
            self._is_strong_downtrend,
            self._is_consolidation,
            self._is_bullish_reversal,
            self._is_bearish_reversal,
            self._is_bullish_breakout,
            self._is_bearish_breakout,
            self._is_volatile_choppy
        ]
        
        # 按优先级顺序检测每种市场状态
        for i, state_func in enumerate(state_functions):
            state_conditions = state_func(df)
            # 只更新尚未分配状态的样本（target == -1）
            mask = (state_conditions) & (df['target'] == -1)
            df.loc[mask, 'target'] = i
        
        # 将任何剩余的未分类样本标记为震荡市场（最后一类）
        df.loc[df['target'] == -1, 'target'] = len(state_functions) - 1
        
        return df

    def _is_strong_uptrend(self, df: pd.DataFrame) -> pd.Series:
        """判断是否为强劲上涨趋势"""
        conditions = pd.Series(True, index=df.index) # Start with all true

        # 1. 均线 (MA)
        # 短期均线持续位于中期均线之上，中期均线持续位于长期均线之上
        # MA(5) > MA(10) > MA(20) > MA(50) (using config values)
        ma_short1 = f'MA_{self.config.ma_periods[0]}' # e.g., 5
        ma_short2 = f'MA_{self.config.ma_periods[1]}' # e.g., 10
        ma_mid = f'MA_{self.config.ma_periods[2]}'    # e.g., 20
        ma_long = f'MA_{self.config.ma_periods[3]}'   # e.g., 50 (index 3 from default [5,10,20,50,120,200])
        
        conditions &= (df[ma_short1] > df[ma_short2])
        conditions &= (df[ma_short2] > df[ma_mid])
        conditions &= (df[ma_mid] > df[ma_long])
        # 价格持续在短期均线之上运行
        conditions &= (df['close'] > df[ma_short1])

        # 2. 布林带 (Bollinger Bands)
        # 价格持续沿布林带上轨运行或突破上轨
        conditions &= (df['close'] >= df['BB_upperband'] * 0.995) # Allow slight touch below upper band
        # 布林带开口向上扩张 (BB_bandwidth 增加 or 稳定在高位)
        # This is harder to quantify as a single point condition.
        # We can check if bandwidth is relatively high or has increased recently.
        # For now, let's use a simpler condition: bandwidth is above its rolling mean
        bb_bandwidth_mean = df['BB_bandwidth'].rolling(window=self.config.rsi_strong_duration).mean()
        conditions &= (df['BB_bandwidth'] > bb_bandwidth_mean * 0.9) # Bandwidth is not shrinking significantly

        # 3. RSI (Relative Strength Index)
        rsi_col = 'RSI_14' # Assuming this is the column name, now with underscore
        # RSI(14) 持续在 60-80 之间运行, 或持续在 70 以上 N 根K线
        # For simplicity, let's check if RSI is above a threshold for N bars
        is_rsi_strong = df[rsi_col].rolling(window=self.config.rsi_strong_duration).apply(lambda x: (x >= self.config.rsi_overbought_threshold -10).all(), raw=True).fillna(False)
        conditions &= is_rsi_strong
        conditions &= (df[rsi_col] > self.config.rsi_mid_zone_high) # RSI consistently above 60

        # 4. MACD (Moving Average Convergence Divergence)
        # MACD 线和信号线都在零轴上方运行
        conditions &= (df['MACD_line'] > 0)
        conditions &= (df['MACD_signal_line'] > 0)
        # MACD 线持续在信号线上方
        conditions &= (df['MACD_line'] > df['MACD_signal_line'])
        # MACD 柱状图持续为正且可能扩大 (hist > 0)
        conditions &= (df['MACD_hist'] > 0)
        # MACD hist increasing for N bars (optional, adds strength)
        macd_hist_increasing = df['MACD_hist'].rolling(window=self.config.rsi_strong_duration).apply(lambda x: (np.diff(x) >= 0).all(), raw=True).fillna(False)
        conditions &= macd_hist_increasing


        # 5. ADX (Average Directional Index)
        adx_col = 'ADX_14'
        adx_pos_col = 'ADX_pos_14'
        adx_neg_col = 'ADX_neg_14'
        # ADX(14) 持续在 25 以上（甚至 > 40）
        is_adx_strong = df[adx_col].rolling(window=self.config.adx_strong_duration).apply(lambda x: (x >= self.config.adx_trend_threshold).all(), raw=True).fillna(False)
        conditions &= is_adx_strong
        # 同时 +DI 持续在 -DI 之上，且两者间距扩大
        conditions &= (df[adx_pos_col] > df[adx_neg_col])
        di_spread_increasing = (df[adx_pos_col] - df[adx_neg_col]).rolling(window=self.config.adx_strong_duration).apply(lambda x: (np.diff(x) >= 0).all(), raw=True).fillna(False)
        conditions &= di_spread_increasing


        # 6. 成交量 (Volume) - This is trickier for a point-in-time classification
        # 上涨时成交量明显放大，下跌回调时成交量萎缩
        # For a strong uptrend point, volume should be above average
        # Using one of the volume MA periods from config
        vol_ma_period = self.config.volume_ma_periods[2] if len(self.config.volume_ma_periods) > 2 else 20 # e.g. 24 from default
        df[f'Volume_MA_{vol_ma_period}'] = df['volume'].rolling(window=vol_ma_period).mean()
        conditions &= (df['volume'] > df[f'Volume_MA_{vol_ma_period}'])
        
        # For a K-line to be marked as "strong uptrend", all above must be true.
        # However, some conditions might be relaxed or weighted.
        # For now, it's a strict AND of all primary conditions.
        
        return conditions

    def _is_strong_downtrend(self, df: pd.DataFrame) -> pd.Series:
        """判断是否为强劲下跌趋势"""
        conditions = pd.Series(True, index=df.index)

        # 1. 均线 (MA)
        ma_short1 = f'MA_{self.config.ma_periods[0]}'
        ma_short2 = f'MA_{self.config.ma_periods[1]}'
        ma_mid = f'MA_{self.config.ma_periods[2]}'
        ma_long = f'MA_{self.config.ma_periods[3]}'
        
        conditions &= (df[ma_short1] < df[ma_short2])
        conditions &= (df[ma_short2] < df[ma_mid])
        conditions &= (df[ma_mid] < df[ma_long])
        conditions &= (df['close'] < df[ma_short1])

        # 2. 布林带 (Bollinger Bands)
        conditions &= (df['close'] <= df['BB_lowerband'] * 1.005) # Allow slight touch above lower band
        bb_bandwidth_mean = df['BB_bandwidth'].rolling(window=self.config.rsi_strong_duration).mean()
        conditions &= (df['BB_bandwidth'] > bb_bandwidth_mean * 0.9) # Bandwidth not shrinking significantly

        # 3. RSI (Relative Strength Index)
        rsi_col = 'RSI_14' # now with underscore
        is_rsi_weak = df[rsi_col].rolling(window=self.config.rsi_strong_duration).apply(lambda x: (x <= self.config.rsi_oversold_threshold + 10).all(), raw=True).fillna(False)
        conditions &= is_rsi_weak
        conditions &= (df[rsi_col] < self.config.rsi_mid_zone_low) # RSI consistently below 40

        # 4. MACD (Moving Average Convergence Divergence)
        conditions &= (df['MACD_line'] < 0)
        conditions &= (df['MACD_signal_line'] < 0)
        conditions &= (df['MACD_line'] < df['MACD_signal_line'])
        conditions &= (df['MACD_hist'] < 0)
        macd_hist_decreasing = df['MACD_hist'].rolling(window=self.config.rsi_strong_duration).apply(lambda x: (np.diff(x) <= 0).all(), raw=True).fillna(False)
        conditions &= macd_hist_decreasing

        # 5. ADX (Average Directional Index)
        adx_col = 'ADX_14'
        adx_pos_col = 'ADX_pos_14'
        adx_neg_col = 'ADX_neg_14'
        is_adx_strong = df[adx_col].rolling(window=self.config.adx_strong_duration).apply(lambda x: (x >= self.config.adx_trend_threshold).all(), raw=True).fillna(False)
        conditions &= is_adx_strong
        conditions &= (df[adx_neg_col] > df[adx_pos_col])
        di_spread_increasing_neg = (df[adx_neg_col] - df[adx_pos_col]).rolling(window=self.config.adx_strong_duration).apply(lambda x: (np.diff(x) >= 0).all(), raw=True).fillna(False)
        conditions &= di_spread_increasing_neg

        # 6. 成交量 (Volume)
        vol_ma_period = self.config.volume_ma_periods[2] if len(self.config.volume_ma_periods) > 2 else 20
        # Ensure Volume_MA column exists or is calculated here if not already
        if f'Volume_MA_{vol_ma_period}' not in df.columns:
             df[f'Volume_MA_{vol_ma_period}'] = df['volume'].rolling(window=vol_ma_period).mean()
        conditions &= (df['volume'] > df[f'Volume_MA_{vol_ma_period}'])
        
        return conditions

    def _is_consolidation(self, df: pd.DataFrame) -> pd.Series:
        """检测盘整/横盘整理模式"""
        conditions = pd.Series(False, index=df.index)
        if df.empty:
            return conditions

        # 获取安全的 ATR 值（避免除零错误）
        safe_atr = (df['ATR_14']
                   .replace(0, np.nan)
                   .ffill()
                   .bfill()
                   .fillna(1e-9))

        # 1. 价格波动范围相对较小
        high_low_range = df['high'] - df['low']
        cond_small_range = high_low_range <= (safe_atr * self.config.atr_consolidation_multiplier)

        # 2. 移动平均线趋于水平
        ma_periods = [20, 50, 120]  # 短中长期均线
        ma_cols = [f'MA_{period}' for period in ma_periods]
        
        cond_ma_tight = pd.Series(False, index=df.index) # Default to False
        cond_ma_aligned = pd.Series(False, index=df.index) # Default to False

        # 计算MA之间的最大相对价差
        ma_spreads_values = [] # Changed name to avoid conflict
        for i in range(len(ma_cols)):
            for j in range(i+1, len(ma_cols)):
                if ma_cols[i] in df.columns and ma_cols[j] in df.columns:
                    spread = abs(df[ma_cols[i]] - df[ma_cols[j]]) / df['close'].replace(0, np.nan) # Avoid division by zero for close
                    ma_spreads_values.append(spread)

        if ma_spreads_values: # Check if list is not empty
            max_ma_spread = pd.concat(ma_spreads_values, axis=1).max(axis=1)
            cond_ma_tight = max_ma_spread < self.config.consolidation_ma_spread_pct_threshold

            # 计算MA的横截面标准差
            # Ensure all ma_cols actually exist in df before trying to access them
            existing_ma_cols = [col for col in ma_cols if col in df.columns]
            if existing_ma_cols: # Only proceed if there are MA columns to process
                ma_values = pd.concat([df[col] for col in existing_ma_cols], axis=1)
                ma_std = ma_values.std(axis=1) / df['close'].replace(0, np.nan) # Avoid division by zero
                cond_ma_aligned = ma_std < self.config.consolidation_ma_std_pct_threshold

        # 3. MACD在零轴附近波动
        if 'MACD_hist' in df.columns:
            macd_norm = df['MACD_hist'] / safe_atr
            cond_macd_near_zero = abs(macd_norm) < self.config.consolidation_macd_norm_atr_threshold
        else:
            cond_macd_near_zero = pd.Series(False, index=df.index)

        # 4. ADX显示趋势较弱
        cond_weak_trend = (df['ADX_14'] < self.config.adx_choppy_threshold_low)

        # 5. RSI在中间区域
        cond_rsi_neutral = ((df['RSI_14'] > self.config.rsi_mid_zone_low) & 
                           (df['RSI_14'] < self.config.rsi_mid_zone_high))

        # 综合所有条件
        conditions = (cond_small_range & 
                     cond_ma_tight & 
                     cond_ma_aligned & 
                     cond_macd_near_zero & 
                     cond_weak_trend & 
                     cond_rsi_neutral)

        return conditions

    def _is_bullish_reversal(self, df: pd.DataFrame) -> pd.Series:
        """判断是否为看涨反转"""
        conditions = pd.Series(False, index=df.index) # Default to False
        if df.empty: return conditions

        rsi_col = 'RSI_14' # now with underscore
        macd_line_col = 'MACD_line'
        macd_signal_col = 'MACD_signal_line'
        ma_short1 = f'MA_{self.config.ma_periods[0]}' # e.g., MA5
        ma_short2 = f'MA_{self.config.ma_periods[1]}' # e.g., MA10

        # Common conditions for reversal
        # Was in a downtrend (simplified: price below longer MA, ADX indicated some trend or was recently high)
        # For simplicity, we can check if MA_long was recently declining or price was below it.
        # This part can be complex and might need a more robust prior trend detection.
        # For now, let's focus on the reversal signals themselves.

        # 1. Price Behavior & MA Crossover
        # Price from below MA5 crosses above MA5, MA5 crosses above MA10 (golden cross)
        cond_price_cross_ma5 = (df['close'].shift(1) < df[ma_short1].shift(1)) & (df['close'] > df[ma_short1])
        cond_ma5_cross_ma10 = (df[ma_short1].shift(1) < df[ma_short2].shift(1)) & (df[ma_short1] > df[ma_short2])
        conditions |= (cond_price_cross_ma5 & cond_ma5_cross_ma10)

        # 2. RSI
        # RSI from oversold (<30) crosses up, or bullish divergence
        cond_rsi_cross_up_30 = (df[rsi_col].shift(1) < self.config.rsi_oversold_threshold) & \
                               (df[rsi_col] > self.config.rsi_oversold_threshold)
        cond_rsi_cross_up_50 = (df[rsi_col].shift(1) < 50) & (df[rsi_col] > 50) & \
                               (df[rsi_col].rolling(window=self.config.rsi_strong_duration).min() < self.config.rsi_oversold_threshold + 5) # ensure it came from low
        
        # Bullish divergence (simplified: price makes lower low, RSI makes higher low over ~5-10 periods)
        # This is harder to capture robustly in a single pass without more complex state. Placeholder for now.
        # For now, focus on RSI crossover from low levels.
        conditions |= cond_rsi_cross_up_30
        conditions |= cond_rsi_cross_up_50

        # 3. MACD
        # MACD line crosses signal line from below (golden cross), preferably below zero or hist turns positive
        cond_macd_gold_cross = (df[macd_line_col].shift(1) < df[macd_signal_col].shift(1)) & \
                               (df[macd_line_col] > df[macd_signal_col])
        cond_macd_hist_turn_pos = (df['MACD_hist'].shift(1) < 0) & (df['MACD_hist'] > 0)
        conditions |= (cond_macd_gold_cross & (df[macd_line_col] < self.config.macd_zero_proximity_threshold * -0.5)) # MACD cross below or near zero
        conditions |= cond_macd_hist_turn_pos

        # 4. Bollinger Bands
        # Price from lower band crosses up middle band
        cond_bb_cross_mid_from_low = (df['close'].shift(1) < df['BB_middleband'].shift(1)) & \
                                     (df['close'] > df['BB_middleband']) & \
                                     (df['close'].rolling(window=self.config.rsi_strong_duration).min() < df['BB_lowerband']) # ensure it came from lower band area
        conditions |= cond_bb_cross_mid_from_low

        # 5. Volume
        # Reversal上涨时成交量明显放大
        vol_ma_period = self.config.volume_ma_periods[1] if len(self.config.volume_ma_periods) > 1 else 10
        if f'Volume_MA_{vol_ma_period}' not in df.columns:
            df[f'Volume_MA_{vol_ma_period}'] = df['volume'].rolling(window=vol_ma_period).mean()
        cond_volume_increase = df['volume'] > df[f'Volume_MA_{vol_ma_period}'] * self.config.volume_increase_factor_breakout
        
        # Combine: Bullish reversal should have at least some of these signals, and volume confirmation
        # This is a heuristic: require at least 2 of the primary signals (MA cross, RSI cross, MACD cross, BB cross)
        # plus volume confirmation.
        primary_signals = pd.concat([
            (cond_price_cross_ma5 & cond_ma5_cross_ma10),
            cond_rsi_cross_up_30,
            (cond_macd_gold_cross & (df[macd_line_col] < 0)),
            cond_bb_cross_mid_from_low
        ], axis=1)
        
        # Count how many primary signals are true for each row
        num_primary_signals = primary_signals.sum(axis=1)
        
        # Final condition for bullish reversal
        final_conditions = (num_primary_signals >= 2) & cond_volume_increase
        return final_conditions

    def _is_bearish_reversal(self, df: pd.DataFrame) -> pd.Series:
        """判断是否为看跌反转"""
        conditions = pd.Series(False, index=df.index)
        if df.empty: return conditions

        rsi_col = 'RSI_14' # now with underscore
        macd_line_col = 'MACD_line'
        macd_signal_col = 'MACD_signal_line'
        ma_short1 = f'MA_{self.config.ma_periods[0]}'
        ma_short2 = f'MA_{self.config.ma_periods[1]}'

        # 1. Price Behavior & MA Crossover
        cond_price_cross_ma5_down = (df['close'].shift(1) > df[ma_short1].shift(1)) & (df['close'] < df[ma_short1])
        cond_ma5_cross_ma10_down = (df[ma_short1].shift(1) > df[ma_short2].shift(1)) & (df[ma_short1] < df[ma_short2])
        conditions |= (cond_price_cross_ma5_down & cond_ma5_cross_ma10_down)

        # 2. RSI
        cond_rsi_cross_down_70 = (df[rsi_col].shift(1) > self.config.rsi_overbought_threshold) & \
                                 (df[rsi_col] < self.config.rsi_overbought_threshold)
        cond_rsi_cross_down_50 = (df[rsi_col].shift(1) > 50) & (df[rsi_col] < 50) & \
                                 (df[rsi_col].rolling(window=self.config.rsi_strong_duration).max() > self.config.rsi_overbought_threshold - 5)
        conditions |= cond_rsi_cross_down_70
        conditions |= cond_rsi_cross_down_50

        # 3. MACD
        cond_macd_death_cross = (df[macd_line_col].shift(1) > df[macd_signal_col].shift(1)) & \
                                (df[macd_line_col] < df[macd_signal_col])
        cond_macd_hist_turn_neg = (df['MACD_hist'].shift(1) > 0) & (df['MACD_hist'] < 0)
        conditions |= (cond_macd_death_cross & (df[macd_line_col] > self.config.macd_zero_proximity_threshold * -0.5))
        conditions |= cond_macd_hist_turn_neg

        # 4. Bollinger Bands
        cond_bb_cross_mid_from_high = (df['close'].shift(1) > df['BB_middleband'].shift(1)) & \
                                      (df['close'] < df['BB_middleband']) & \
                                      (df['close'].rolling(window=self.config.rsi_strong_duration).max() > df['BB_upperband'])
        conditions |= cond_bb_cross_mid_from_high

        # 5. Volume
        vol_ma_period = self.config.volume_ma_periods[1] if len(self.config.volume_ma_periods) > 1 else 10
        if f'Volume_MA_{vol_ma_period}' not in df.columns:
            df[f'Volume_MA_{vol_ma_period}'] = df['volume'].rolling(window=vol_ma_period).mean()
        cond_volume_increase = df['volume'] > df[f'Volume_MA_{vol_ma_period}'] * self.config.volume_increase_factor_breakout
        
        primary_signals = pd.concat([
            (cond_price_cross_ma5_down & cond_ma5_cross_ma10_down),
            cond_rsi_cross_down_70,
            (cond_macd_death_cross & (df[macd_line_col] > 0)),
            cond_bb_cross_mid_from_high
        ], axis=1)
        num_primary_signals = primary_signals.sum(axis=1)

        final_conditions = (num_primary_signals >= 2) & cond_volume_increase
        return final_conditions

    def _is_bullish_breakout(self, df: pd.DataFrame) -> pd.Series:
        """检测看涨突破模式"""
        conditions = pd.Series(False, index=df.index)
        if df.empty:
            return conditions

        # 获取 ATR 值，确保没有 0 值
        valid_atr_shifted = (pd.to_numeric(df['ATR_14'].shift(1), errors='coerce')
                           .bfill()
                           .fillna(1e-6))

        # 1. 价格突破上轨
        cond_price_above_upper = df['close'] > df['BB_upperband']

        # 2. 成交量放大
        volume_ma = df['volume'].rolling(window=20).mean()
        cond_volume_surge = df['volume'] > (volume_ma * self.config.volume_increase_factor_breakout)

        # 3. RSI强势但未到极端超买
        cond_rsi_strong = ((df['RSI_14'] > 60) & (df['RSI_14'] < 85))

        # 4. MACD柱状图转为正值且放大
        cond_macd_bullish = ((df['MACD_hist'] > 0) & 
                            (df['MACD_hist'] > df['MACD_hist'].shift(1)))

        # 5. ADX显示趋势增强
        cond_adx_rising = ((df['ADX_14'] > self.config.adx_trend_threshold) & 
                          (df['ADX_14'] > df['ADX_14'].shift(1)))

        # 6. 价格突破幅度显著
        breakout_size = (df['close'] - df['BB_upperband']) / valid_atr_shifted
        cond_significant_breakout = breakout_size > 0.5

        # 综合所有条件
        conditions = (cond_price_above_upper & 
                     cond_volume_surge & 
                     cond_rsi_strong & 
                     cond_macd_bullish & 
                     cond_adx_rising & 
                     cond_significant_breakout)

        return conditions

    def _is_bearish_breakout(self, df: pd.DataFrame) -> pd.Series:
        """检测看跌突破模式"""
        conditions = pd.Series(False, index=df.index)
        if df.empty:
            return conditions

        # 获取 ATR 值，确保没有 0 值
        valid_atr_shifted = (pd.to_numeric(df['ATR_14'].shift(1), errors='coerce')
                           .bfill()
                           .fillna(1e-6))

        # 1. 价格突破下轨
        cond_price_below_lower = df['close'] < df['BB_lowerband']

        # 2. 成交量放大
        volume_ma = df['volume'].rolling(window=20).mean()
        cond_volume_surge = df['volume'] > (volume_ma * self.config.volume_increase_factor_breakout)

        # 3. RSI弱势但未到极端超卖
        cond_rsi_weak = ((df['RSI_14'] < 40) & (df['RSI_14'] > 15))

        # 4. MACD柱状图转为负值且继续下降
        cond_macd_bearish = ((df['MACD_hist'] < 0) & 
                            (df['MACD_hist'] < df['MACD_hist'].shift(1)))

        # 5. ADX显示趋势增强
        cond_adx_rising = ((df['ADX_14'] > self.config.adx_trend_threshold) & 
                          (df['ADX_14'] > df['ADX_14'].shift(1)))

        # 6. 价格突破幅度显著
        breakout_size = (df['BB_lowerband'] - df['close']) / valid_atr_shifted
        cond_significant_breakout = breakout_size > 0.5

        # 综合所有条件
        conditions = (cond_price_below_lower & 
                     cond_volume_surge & 
                     cond_rsi_weak & 
                     cond_macd_bearish & 
                     cond_adx_rising & 
                     cond_significant_breakout)

        return conditions

    def _is_volatile_choppy(self, df: pd.DataFrame) -> pd.Series:
        """检测高波动性震荡市场"""
        conditions = pd.Series(False, index=df.index)
        if df.empty:
            return conditions

        # 1. Bollinger Bands: 带宽较宽，价格在上下轨之间波动
        cond_bb_wide = (df['BB_bandwidth'] > df['BB_bandwidth'].rolling(window=50).mean() * 1.2)
        cond_price_not_hugging_bands = (df['BB_percent'] > 0.10) & (df['BB_percent'] < 0.90)

        # 2. RSI: 频繁进出超买超卖区域，或在中间区域但波动性高
        rsi_col = 'RSI_14'
        rsi_std_short = df[rsi_col].rolling(window=self.config.rsi_strong_duration * 2).std()
        rsi_std_mean_long = (rsi_std_short
                           .rolling(window=50)
                           .mean()
                           .replace(0, np.nan)
                           .ffill()
                           .bfill()
                           .fillna(1e-6))
        cond_rsi_volatile = (rsi_std_short > rsi_std_mean_long * 1.3)

        # 3. MACD: 频繁穿越，柱状图在零线附近波动
        macd_hist_col = 'MACD_hist'
        hist_sign_changes = (df[macd_hist_col]
                           .rolling(window=10)
                           .apply(lambda x: np.sum(np.diff(np.sign(x)) != 0), raw=True)
                           .fillna(0))
        cond_macd_flipping = (hist_sign_changes >= 2)

        # 4. ADX: ADX在中等水平，DI线交叉或都较低，表示无明显趋势
        adx_col = 'ADX_14'
        adx_pos_col = 'ADX_pos_14'
        adx_neg_col = 'ADX_neg_14'
        cond_adx_mid_level_indecisive_di = ((df[adx_col] > self.config.adx_choppy_threshold_low - 5) & 
                                           (df[adx_col] < self.config.adx_choppy_threshold_high + 10) & 
                                           ((df[adx_pos_col] - df[adx_neg_col]).abs() < 8))

        # 5. ATR: ATR较高，表示波动性增加
        atr_col = 'ATR_14'
        atr_mean_long = (df[atr_col]
                        .rolling(window=50)
                        .mean()
                        .replace(0, np.nan)
                        .ffill()
                        .bfill()
                        .fillna(1e-6))
        cond_atr_high = (df[atr_col] > atr_mean_long * 1.15)

        # 综合多个信号
        choppy_signals = pd.concat([
            (cond_bb_wide & cond_price_not_hugging_bands),
            cond_rsi_volatile,
            cond_macd_flipping,
            cond_adx_mid_level_indecisive_di
        ], axis=1)
        num_choppy_signals = choppy_signals.sum(axis=1)

        # 最终条件：ATR高且至少有2个震荡信号
        conditions = cond_atr_high & (num_choppy_signals >= 2)
        return conditions

    def select_features(self, df_features: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """
        根据配置执行特征选择。
        
        Args:
            df_features: 特征DataFrame
            y: 目标变量Series
            
        Returns:
            选择后的特征DataFrame
        """
        logger.info("开始特征选择...")
        df_selected = df_features.copy()
        
        # 方差阈值选择
        if self.config.enable_variance_threshold_selection:
            selector = VarianceThreshold(threshold=self.config.variance_threshold_value)
            selected_features = selector.fit_transform(df_selected)
            selected_cols = df_selected.columns[selector.get_support()].tolist()
            df_selected = df_selected[selected_cols]
            logger.info(f"方差阈值选择后保留 {len(selected_cols)} 个特征")
        
        # 相关性选择
        if self.config.enable_correlation_selection:
            correlation_matrix = df_selected.corr().abs()
            upper = correlation_matrix.where(np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool))
            to_drop = [column for column in upper.columns if any(upper[column] > self.config.correlation_threshold)]
            df_selected = df_selected.drop(columns=to_drop)
            logger.info(f"相关性选择后保留 {df_selected.shape[1]} 个特征")
        
        # VIF选择
        if self.config.enable_vif_selection:
            vif_features = []
            for column in df_selected.columns:
                vif = variance_inflation_factor(df_selected[vif_features + [column]].values, -1) if vif_features else 1
                if vif < self.config.vif_threshold:
                    vif_features.append(column)
            df_selected = df_selected[vif_features]
            logger.info(f"VIF选择后保留 {len(vif_features)} 个特征")
        
        # 特征重要性选择
        if self.config.enable_importance_selection:
            rf = RandomForestClassifier(n_estimators=100, random_state=42)
            rf.fit(df_selected, y)
            importances = pd.Series(rf.feature_importances_, index=df_selected.columns)
            selected_features = importances.nlargest(self.config.max_features_after_importance).index
            df_selected = df_selected[selected_features]
            logger.info(f"特征重要性选择后保留 {len(selected_features)} 个特征")
        
        logger.info(f"特征选择完成。最终保留 {df_selected.shape[1]} 个特征")
        return df_selected

    def create_features(self, df: pd.DataFrame, force_refresh: bool = False) -> pd.DataFrame:
        """
        创建所有特征。
        
        Args:
            df: 输入的原始数据DataFrame
            force_refresh: 是否强制刷新所有特征缓存
            
        Returns:
            包含所有特征和目标变量的DataFrame
        """
        logger.info(f"开始创建特征... 强制刷新模式: {force_refresh}")
        df_copy = df.copy()
        
        # 确保索引是DatetimeIndex，这对于很多时间序列特征至关重要
        if not isinstance(df_copy.index, pd.DatetimeIndex):
            try:
                original_name = df_copy.index.name
                df_copy.index = pd.to_datetime(df_copy.index)
                df_copy.index.name = original_name
                logger.info("成功将索引转换为 DatetimeIndex。")
            except Exception as e:
                logger.error(f"在 create_features 开始时转换索引为 DatetimeIndex 失败: {e}")
                # Depending on strictness, might raise an error or try to proceed
                # For now, proceeding, but downstream features might fail.

        # 添加各类特征
        if self.config.feature_groups.get('price', True): # Default to True if not specified
            df_copy = self.add_price_features(df_copy, force_refresh=force_refresh)
        
        if self.config.feature_groups.get('technical', True):
            df_copy = self.calculate_technical_indicators(df_copy, force_refresh=force_refresh)
        
        if self.config.feature_groups.get('time', True):
            df_copy = self.add_time_features(df_copy, force_refresh=force_refresh)
        
        if self.config.feature_groups.get('volume', True):
            df_copy = self.add_volume_features(df_copy, force_refresh=force_refresh)

        if self.config.feature_groups.get('candlestick', False): # Assuming default False if not primary
             if hasattr(self, 'add_candlestick_features'):
                df_copy = self.add_candlestick_features(df_copy, force_refresh=force_refresh)
             else:
                logger.warning("Candlestick features enabled in config, but 'add_candlestick_features' method not found in FeatureEngineer.")
        
        if self.config.feature_groups.get('seasonal', False):
            if hasattr(self, 'add_seasonal_features'):
                df_copy = self.add_seasonal_features(df_copy, force_refresh=force_refresh)
            else:
                logger.warning("Seasonal features enabled in config, but 'add_seasonal_features' method not found.")

        if self.config.feature_groups.get('trend_decomposition', False):
            if hasattr(self, 'add_trend_decomposition_features'):
                df_copy = self.add_trend_decomposition_features(df_copy, force_refresh=force_refresh)
            else:
                logger.warning("Trend decomposition features enabled, but 'add_trend_decomposition_features' not found.")
        
        if self.config.feature_groups.get('lag', False):
            if hasattr(self, 'add_lag_features'):
                df_copy = self.add_lag_features(df_copy, force_refresh=force_refresh)
            else:
                logger.warning("Lag features enabled, but 'add_lag_features' not found.")

        if self.config.feature_groups.get('rolling_stats', False):
            if hasattr(self, 'add_rolling_stats_features'):
                df_copy = self.add_rolling_stats_features(df_copy, force_refresh=force_refresh)
            else:
                logger.warning("Rolling stats features enabled, but 'add_rolling_stats_features' not found.")
        
        # Custom features from registry
        if self.config.enable_custom_features and hasattr(self.registry, 'apply_custom_features'):
            logger.info("应用自定义特征...")
            df_copy = self.registry.apply_custom_features(
                df_copy, 
                self.config, 
                groups_to_run=self.config.custom_feature_groups_to_run,
                force_refresh_cache=force_refresh # Assuming FeatureRegistry's apply_custom_features handles this
            )
        elif self.config.enable_custom_features:
            logger.warning("自定义特征已启用，但 FeatureRegistry 或其 'apply_custom_features' 方法不可用。")
        
        # 创建目标变量
        df_copy = self._create_multiclass_market_state_target(df_copy)
        
        # 在检查无穷值之前，先将分类型列转换为数值型
        categorical_columns = df_copy.select_dtypes(include=['category']).columns
        for col in categorical_columns:
            df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
        
        # 检查并处理无穷值
        cols_with_inf = df_copy.columns[np.isinf(df_copy).any()].tolist()
        if cols_with_inf:
            logger.warning(f"发现包含无穷值的列: {cols_with_inf}")
            for col in cols_with_inf:
                mask = np.isinf(df_copy[col])
                if mask.any():
                    logger.warning(f"列 {col} 中有 {mask.sum()} 个无穷值，将被替换为 NaN")
                    df_copy.loc[mask, col] = np.nan
        
        # 记录目标变量分布
        target_counts = df_copy['target'].value_counts()
        total_samples = len(df_copy)
        target_percentages = (target_counts / total_samples) * 100
        
        logger.info(f"Absolute counts of each target category: {target_counts.to_dict()}")
        logger.info(f"Normalized (percentage) counts of each target category: {target_percentages.to_dict()}")
        
        logger.info(f"特征工程完成。处理了 {len(df_copy)} 行数据。NaN值将在模型训练前处理。")
        
        return df_copy