#!/usr/bin/env python3
"""
第二阶段集成测试 - 验证策略扩展与智能化功能
"""

import sys
import os
import numpy as np
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.getcwd())

def test_multi_strategy_library():
    """测试多策略库"""
    print("🧪 测试多策略库...")
    
    try:
        from multi_strategy_library import StrategyManager
        
        # 创建策略管理器
        manager = StrategyManager()
        
        # 模拟市场数据
        test_data = {
            'price': 104500.0,
            'rsi': 78.5,
            'bb_position': 0.92,
            'volume_ratio': 2.1,
            'atr_percentage': 0.035,
            'macd_trend': 'bearish',
            'price_history': [104000 + i*10 for i in range(30)]
        }
        
        # 生成组合信号
        combined_signal = manager.generate_combined_signal(test_data)
        
        print(f"✅ 多策略库测试成功")
        print(f"   策略数量: {len(manager.strategies)}")
        print(f"   组合信号: {combined_signal['direction']}")
        print(f"   信号强度: {combined_signal['strength']:.1%}")
        print(f"   市场制度: {combined_signal['market_regime']}")
        print(f"   活跃策略: {combined_signal['active_strategies']}/{combined_signal['total_strategies']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多策略库测试失败: {str(e)}")
        return False

def test_sentiment_analysis():
    """测试情绪分析"""
    print("\n🧪 测试情绪分析...")
    
    try:
        from sentiment_analysis import SentimentAnalyzer
        
        # 创建情绪分析器
        analyzer = SentimentAnalyzer()
        
        # 获取综合情绪
        sentiment = analyzer.get_comprehensive_sentiment()
        
        print(f"✅ 情绪分析测试成功")
        print(f"   数据源数量: {sentiment['sources_used']}")
        print(f"   总体情绪: {sentiment['sentiment_classification']}")
        print(f"   情绪分数: {sentiment['overall_sentiment_score']:.2f}")
        print(f"   交易建议: {sentiment['trading_signal']['direction']}")
        print(f"   信号类型: {sentiment['trading_signal']['signal_type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 情绪分析测试失败: {str(e)}")
        return False

def test_ai_model_monitor():
    """测试AI模型监控"""
    print("\n🧪 测试AI模型监控...")
    
    try:
        from ai_model_monitor import AIModelMonitor
        
        # 创建监控器
        monitor = AIModelMonitor()
        
        # 模拟预测记录
        for i in range(20):
            features = np.random.randn(6)
            prediction = np.random.uniform(0.2, 0.8)
            actual = np.random.choice([0, 1])
            confidence = np.random.uniform(0.5, 0.9)
            
            monitor.record_prediction(features, prediction, actual, confidence)
        
        # 获取监控报告
        report = monitor.get_monitoring_report()
        
        print(f"✅ AI模型监控测试成功")
        print(f"   总预测数: {report['total_predictions']}")
        print(f"   性能评估: {report['performance_evaluations']}")
        print(f"   重训练建议: {report['retrain_recommendation']['recommendation']}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI模型监控测试失败: {str(e)}")
        return False

def test_phase2_integration():
    """测试第二阶段集成"""
    print("\n🧪 测试第二阶段集成...")
    
    try:
        # 模拟第二阶段系统核心功能
        print("📊 模拟第二阶段信号融合...")
        
        # 模拟各信号源
        original_signal = {
            'direction': 'SHORT',
            'strength': 0.7,
            'confidence': 0.65,
            'reason': 'AI看跌+技术指标确认'
        }
        
        strategy_signal = {
            'direction': 'SHORT',
            'strength': 0.8,
            'confidence': 0.72,
            'agreement_score': 0.85,
            'active_strategies': 3,
            'total_strategies': 4
        }
        
        sentiment_signal = {
            'direction': 'LONG',
            'strength': 0.6,
            'confidence': 0.55,
            'signal_type': 'contrarian'
        }
        
        # 模拟权重融合
        original_weight = 0.45
        strategy_weight = 0.4
        sentiment_weight = 0.15
        
        # 计算加权信号
        weighted_short = (original_signal['strength'] * original_weight + 
                         strategy_signal['strength'] * strategy_weight)
        weighted_long = sentiment_signal['strength'] * sentiment_weight
        
        total_weight = original_weight + strategy_weight + sentiment_weight
        
        if weighted_short > weighted_long:
            final_direction = 'SHORT'
            final_strength = weighted_short / (original_weight + strategy_weight)
            signal_agreement = weighted_short / (weighted_short + weighted_long)
        else:
            final_direction = 'LONG'
            final_strength = weighted_long / sentiment_weight
            signal_agreement = weighted_long / (weighted_short + weighted_long)
        
        final_confidence = (original_signal['confidence'] * original_weight + 
                          strategy_signal['confidence'] * strategy_weight + 
                          sentiment_signal['confidence'] * sentiment_weight) / total_weight
        
        final_confidence *= signal_agreement
        
        print(f"   原始信号: {original_signal['direction']} ({original_signal['strength']:.1%})")
        print(f"   策略信号: {strategy_signal['direction']} ({strategy_signal['strength']:.1%})")
        print(f"   情绪信号: {sentiment_signal['direction']} ({sentiment_signal['strength']:.1%})")
        print(f"   最终融合: {final_direction} (强度:{final_strength:.1%}, 置信度:{final_confidence:.1%})")
        print(f"   信号一致性: {signal_agreement:.1%}")
        
        print(f"✅ 第二阶段集成逻辑测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 第二阶段集成测试失败: {str(e)}")
        return False

def test_textblob_dependency():
    """测试TextBlob依赖"""
    print("\n🧪 测试TextBlob依赖...")
    
    try:
        from textblob import TextBlob
        
        # 测试情绪分析
        text = "Bitcoin is showing strong bullish momentum"
        blob = TextBlob(text)
        sentiment = blob.sentiment.polarity
        
        print(f"✅ TextBlob测试成功")
        print(f"   测试文本: {text}")
        print(f"   情绪极性: {sentiment:.2f}")
        
        return True
        
    except ImportError:
        print(f"⚠️ TextBlob未安装，使用模拟情绪分析")
        print(f"   安装命令: pip install textblob")
        return True  # 不影响主要功能
        
    except Exception as e:
        print(f"❌ TextBlob测试失败: {str(e)}")
        return False

def create_phase2_sample_data():
    """创建第二阶段示例数据"""
    print("\n📝 创建第二阶段示例数据...")
    
    import json
    
    sample_data = {
        "phase2_config": {
            "enable_multi_strategy": True,
            "enable_sentiment_analysis": True,
            "enable_ai_monitoring": True,
            "sentiment_weight": 0.15,
            "multi_strategy_weight": 0.4,
            "original_signal_weight": 0.45
        },
        "strategy_performance": {
            "grid": {
                "signals_generated": 5,
                "successful_signals": 3,
                "success_rate": 0.6,
                "total_return": 1.25
            },
            "arbitrage": {
                "signals_generated": 8,
                "successful_signals": 5,
                "success_rate": 0.625,
                "total_return": 2.15
            },
            "reversal": {
                "signals_generated": 12,
                "successful_signals": 7,
                "success_rate": 0.583,
                "total_return": 1.85
            },
            "breakout": {
                "signals_generated": 6,
                "successful_signals": 4,
                "success_rate": 0.667,
                "total_return": 2.45
            }
        },
        "sentiment_history": [
            {
                "timestamp": "2025-06-20T14:00:00",
                "overall_sentiment_score": 0.65,
                "sentiment_classification": "Bullish",
                "trading_signal": {
                    "direction": "LONG",
                    "strength": 0.6,
                    "confidence": 0.7,
                    "signal_type": "momentum"
                }
            },
            {
                "timestamp": "2025-06-20T15:00:00",
                "overall_sentiment_score": 0.35,
                "sentiment_classification": "Bearish",
                "trading_signal": {
                    "direction": "SHORT",
                    "strength": 0.7,
                    "confidence": 0.65,
                    "signal_type": "contrarian"
                }
            }
        ],
        "ai_monitoring": {
            "total_predictions": 45,
            "performance_evaluations": 8,
            "last_accuracy": 0.612,
            "drift_score": 0.045,
            "retrain_recommendation": "CONTINUE_MONITORING"
        },
        "decision_breakdown": [
            {
                "timestamp": "2025-06-20T14:30:00",
                "original_signal": {"direction": "SHORT", "strength": 0.7, "confidence": 0.65},
                "strategy_signal": {"direction": "SHORT", "strength": 0.8, "confidence": 0.72},
                "sentiment_signal": {"direction": "LONG", "strength": 0.6, "confidence": 0.55},
                "final_signal": {"direction": "SHORT", "strength": 0.75, "confidence": 0.68}
            }
        ]
    }
    
    with open("phase2_sample_data.json", "w", encoding="utf-8") as f:
        json.dump(sample_data, f, indent=2, ensure_ascii=False)
    
    print("✅ 第二阶段示例数据创建成功: phase2_sample_data.json")

def main():
    """主测试函数"""
    print("🎯 第二阶段集成测试")
    print("=" * 80)
    
    test_results = []
    
    # 测试各个模块
    test_results.append(("多策略库", test_multi_strategy_library()))
    test_results.append(("情绪分析", test_sentiment_analysis()))
    test_results.append(("AI模型监控", test_ai_model_monitor()))
    test_results.append(("TextBlob依赖", test_textblob_dependency()))
    test_results.append(("第二阶段集成", test_phase2_integration()))
    
    # 创建示例数据
    create_phase2_sample_data()
    
    # 汇总结果
    print(f"\n📊 第二阶段测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 第二阶段集成测试全部通过！")
        print("✅ 系统已准备好进行第二阶段实际运行测试")
    elif passed >= total * 0.8:
        print("📈 第二阶段集成测试基本通过")
        print("🔧 部分功能需要进一步调试")
    else:
        print("🔧 第二阶段集成测试需要重大修复")
        print("❌ 建议检查模块依赖和配置")
    
    print(f"\n💡 下一步:")
    print(f"1. 如果测试通过，可以运行: python phase2_integrated_system.py")
    print(f"2. 安装缺失依赖: pip install textblob")
    print(f"3. 查看示例数据: phase2_sample_data.json")
    print(f"4. 准备进入第三阶段开发")
    
    print(f"\n🎯 第二阶段核心成就:")
    print(f"✅ 多策略库: 4个专业交易策略")
    print(f"✅ 情绪分析: 4个数据源综合分析")
    print(f"✅ AI监控: 实时性能和漂移检测")
    print(f"✅ 信号融合: 智能加权决策机制")

if __name__ == "__main__":
    main()
