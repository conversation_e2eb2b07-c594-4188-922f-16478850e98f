#!/usr/bin/env python3
"""
多策略库 - 第二阶段策略扩展
包含网格交易、统计套利、动量反转、突破跟踪等策略
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')

class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str, weight: float = 0.25):
        self.name = name
        self.weight = weight
        self.enabled = True
        self.performance_history = []
        
    @abstractmethod
    def generate_signal(self, data: Dict) -> Dict:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def update_parameters(self, market_state: Dict):
        """根据市场状态更新参数"""
        pass
    
    def get_performance_score(self) -> float:
        """获取策略表现评分"""
        if not self.performance_history:
            return 0.5
        
        recent_performance = self.performance_history[-10:]  # 最近10次
        return np.mean([p['success_rate'] for p in recent_performance])

class GridTradingStrategy(BaseStrategy):
    """
    网格交易策略 - 适用于震荡市场
    """
    
    def __init__(self, weight: float = 0.25):
        super().__init__("网格交易", weight)
        self.grid_size = 0.015  # 1.5%网格间距
        self.max_grids = 8      # 最大网格数
        self.grid_levels = []
        self.active_orders = []
        
    def generate_signal(self, data: Dict) -> Dict:
        """生成网格交易信号"""
        current_price = data['price']
        volatility = data.get('atr_percentage', 0.02)
        volume_ratio = data.get('volume_ratio', 1.0)
        
        # 动态调整网格大小
        dynamic_grid_size = self.grid_size * (1 + volatility)
        
        # 检查是否适合网格交易
        market_condition = self._analyze_market_condition(data)
        
        if market_condition != 'sideways':
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'reason': f'市场{market_condition}，不适合网格交易',
                'strategy': self.name
            }
        
        # 生成网格信号
        signal = self._generate_grid_signal(current_price, dynamic_grid_size, volume_ratio)
        signal['strategy'] = self.name
        
        return signal
    
    def _analyze_market_condition(self, data: Dict) -> str:
        """分析市场状态"""
        # 简化的市场状态判断
        rsi = data.get('rsi', 50)
        bb_position = data.get('bb_position', 0.5)
        macd_trend = data.get('macd_trend', 'neutral')
        
        # 震荡市场特征
        if 30 < rsi < 70 and 0.2 < bb_position < 0.8 and macd_trend == 'neutral':
            return 'sideways'
        elif rsi > 70 or bb_position > 0.8:
            return 'overbought'
        elif rsi < 30 or bb_position < 0.2:
            return 'oversold'
        else:
            return 'trending'
    
    def _generate_grid_signal(self, current_price: float, grid_size: float, volume_ratio: float) -> Dict:
        """生成具体的网格信号"""
        # 计算网格水平
        base_price = current_price
        upper_levels = [base_price * (1 + grid_size * i) for i in range(1, self.max_grids//2 + 1)]
        lower_levels = [base_price * (1 - grid_size * i) for i in range(1, self.max_grids//2 + 1)]
        
        # 网格交易逻辑
        if volume_ratio > 1.2:  # 有成交量支持
            # 在网格中间，等待触发
            confidence = 0.6 + min(volume_ratio - 1, 0.3)
            
            return {
                'direction': 'GRID_SETUP',
                'strength': 0.7,
                'confidence': confidence,
                'reason': f'设置网格交易，间距{grid_size:.1%}',
                'grid_levels': {
                    'upper': upper_levels,
                    'lower': lower_levels,
                    'base': base_price
                }
            }
        else:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.4,
                'reason': '成交量不足，等待网格触发条件'
            }
    
    def update_parameters(self, market_state: Dict):
        """根据市场状态更新网格参数"""
        volatility = market_state.get('volatility', 0.02)
        
        # 高波动率时增大网格间距
        if volatility > 0.04:
            self.grid_size = 0.025  # 2.5%
            self.max_grids = 6
        elif volatility < 0.015:
            self.grid_size = 0.01   # 1%
            self.max_grids = 10
        else:
            self.grid_size = 0.015  # 1.5%
            self.max_grids = 8

class StatisticalArbitrageStrategy(BaseStrategy):
    """
    统计套利策略 - 基于价格偏差
    """
    
    def __init__(self, weight: float = 0.2):
        super().__init__("统计套利", weight)
        self.lookback_period = 20
        self.z_score_threshold = 2.0
        self.mean_reversion_strength = 0.7
        
    def generate_signal(self, data: Dict) -> Dict:
        """生成统计套利信号"""
        current_price = data['price']
        price_history = data.get('price_history', [])
        
        if len(price_history) < self.lookback_period:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'reason': '历史数据不足',
                'strategy': self.name
            }
        
        # 计算Z-Score
        z_score = self._calculate_z_score(current_price, price_history)
        
        # 生成套利信号
        signal = self._generate_arbitrage_signal(z_score, current_price)
        signal['strategy'] = self.name
        
        return signal
    
    def _calculate_z_score(self, current_price: float, price_history: List[float]) -> float:
        """计算价格Z-Score"""
        recent_prices = price_history[-self.lookback_period:]
        mean_price = np.mean(recent_prices)
        std_price = np.std(recent_prices)
        
        if std_price == 0:
            return 0
        
        z_score = (current_price - mean_price) / std_price
        return z_score
    
    def _generate_arbitrage_signal(self, z_score: float, current_price: float) -> Dict:
        """生成套利信号"""
        abs_z_score = abs(z_score)
        
        if abs_z_score > self.z_score_threshold:
            # 强烈偏离均值，预期回归
            direction = 'SHORT' if z_score > 0 else 'LONG'
            strength = min(abs_z_score / 3, 1.0)  # 标准化强度
            confidence = 0.6 + min((abs_z_score - 2) * 0.1, 0.3)
            
            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'reason': f'价格偏离均值{abs_z_score:.1f}σ，预期回归',
                'z_score': z_score
            }
        
        elif abs_z_score > 1.5:
            # 中等偏离，谨慎信号
            direction = 'SHORT' if z_score > 0 else 'LONG'
            strength = 0.5
            confidence = 0.5
            
            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'reason': f'价格中等偏离{abs_z_score:.1f}σ',
                'z_score': z_score
            }
        
        else:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.4,
                'reason': f'价格接近均值，Z-Score={z_score:.1f}',
                'z_score': z_score
            }
    
    def update_parameters(self, market_state: Dict):
        """更新套利参数"""
        volatility = market_state.get('volatility', 0.02)
        
        # 高波动率时提高阈值
        if volatility > 0.04:
            self.z_score_threshold = 2.5
            self.lookback_period = 15
        elif volatility < 0.015:
            self.z_score_threshold = 1.5
            self.lookback_period = 25
        else:
            self.z_score_threshold = 2.0
            self.lookback_period = 20

class MomentumReversalStrategy(BaseStrategy):
    """
    动量反转策略 - 捕捉超买超卖反转
    """
    
    def __init__(self, weight: float = 0.25):
        super().__init__("动量反转", weight)
        self.rsi_overbought = 75
        self.rsi_oversold = 25
        self.volume_threshold = 1.5
        
    def generate_signal(self, data: Dict) -> Dict:
        """生成动量反转信号"""
        rsi = data.get('rsi', 50)
        volume_ratio = data.get('volume_ratio', 1.0)
        bb_position = data.get('bb_position', 0.5)
        macd_trend = data.get('macd_trend', 'neutral')
        
        signal = self._analyze_reversal_conditions(rsi, volume_ratio, bb_position, macd_trend)
        signal['strategy'] = self.name
        
        return signal
    
    def _analyze_reversal_conditions(self, rsi: float, volume_ratio: float, 
                                   bb_position: float, macd_trend: str) -> Dict:
        """分析反转条件"""
        
        # 超买反转信号
        if rsi > self.rsi_overbought and bb_position > 0.8:
            confirmations = []
            
            if volume_ratio > self.volume_threshold:
                confirmations.append("放量确认")
            
            if macd_trend == 'bearish':
                confirmations.append("MACD转向")
            
            strength = 0.6 + len(confirmations) * 0.15
            confidence = 0.65 + len(confirmations) * 0.1
            
            return {
                'direction': 'SHORT',
                'strength': min(strength, 1.0),
                'confidence': min(confidence, 0.9),
                'reason': f'超买反转信号，RSI={rsi:.1f}，{len(confirmations)}个确认',
                'confirmations': confirmations
            }
        
        # 超卖反转信号
        elif rsi < self.rsi_oversold and bb_position < 0.2:
            confirmations = []
            
            if volume_ratio > self.volume_threshold:
                confirmations.append("放量确认")
            
            if macd_trend == 'bullish':
                confirmations.append("MACD转向")
            
            strength = 0.6 + len(confirmations) * 0.15
            confidence = 0.65 + len(confirmations) * 0.1
            
            return {
                'direction': 'LONG',
                'strength': min(strength, 1.0),
                'confidence': min(confidence, 0.9),
                'reason': f'超卖反转信号，RSI={rsi:.1f}，{len(confirmations)}个确认',
                'confirmations': confirmations
            }
        
        # 中等反转信号
        elif rsi > 70 and bb_position > 0.7 and volume_ratio > 1.2:
            return {
                'direction': 'SHORT',
                'strength': 0.5,
                'confidence': 0.6,
                'reason': f'中等超买，RSI={rsi:.1f}，有成交量支持'
            }
        
        elif rsi < 30 and bb_position < 0.3 and volume_ratio > 1.2:
            return {
                'direction': 'LONG',
                'strength': 0.5,
                'confidence': 0.6,
                'reason': f'中等超卖，RSI={rsi:.1f}，有成交量支持'
            }
        
        else:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.4,
                'reason': f'未达到反转条件，RSI={rsi:.1f}'
            }
    
    def update_parameters(self, market_state: Dict):
        """更新反转参数"""
        volatility = market_state.get('volatility', 0.02)
        
        # 高波动率时调整阈值
        if volatility > 0.04:
            self.rsi_overbought = 80
            self.rsi_oversold = 20
            self.volume_threshold = 2.0
        elif volatility < 0.015:
            self.rsi_overbought = 70
            self.rsi_oversold = 30
            self.volume_threshold = 1.2
        else:
            self.rsi_overbought = 75
            self.rsi_oversold = 25
            self.volume_threshold = 1.5

class BreakoutFollowStrategy(BaseStrategy):
    """
    突破跟踪策略 - 趋势确认后的跟进
    """
    
    def __init__(self, weight: float = 0.3):
        super().__init__("突破跟踪", weight)
        self.breakout_threshold = 0.02  # 2%突破阈值
        self.volume_confirmation = 1.5
        self.trend_strength_min = 0.6
        
    def generate_signal(self, data: Dict) -> Dict:
        """生成突破跟踪信号"""
        current_price = data['price']
        bb_position = data.get('bb_position', 0.5)
        volume_ratio = data.get('volume_ratio', 1.0)
        macd_trend = data.get('macd_trend', 'neutral')
        rsi = data.get('rsi', 50)
        
        signal = self._analyze_breakout(current_price, bb_position, volume_ratio, macd_trend, rsi)
        signal['strategy'] = self.name
        
        return signal
    
    def _analyze_breakout(self, price: float, bb_position: float, volume_ratio: float, 
                         macd_trend: str, rsi: float) -> Dict:
        """分析突破情况"""
        
        # 向上突破
        if bb_position > 0.9 and volume_ratio > self.volume_confirmation:
            confirmations = []
            
            if macd_trend == 'bullish':
                confirmations.append("MACD确认")
            
            if 50 < rsi < 80:  # RSI在合理范围
                confirmations.append("RSI支持")
            
            if volume_ratio > 2.0:
                confirmations.append("强放量")
            
            if len(confirmations) >= 2:
                strength = 0.7 + len(confirmations) * 0.1
                confidence = 0.7 + len(confirmations) * 0.05
                
                return {
                    'direction': 'LONG',
                    'strength': min(strength, 1.0),
                    'confidence': min(confidence, 0.9),
                    'reason': f'向上突破确认，{len(confirmations)}个信号支持',
                    'confirmations': confirmations
                }
        
        # 向下突破
        elif bb_position < 0.1 and volume_ratio > self.volume_confirmation:
            confirmations = []
            
            if macd_trend == 'bearish':
                confirmations.append("MACD确认")
            
            if 20 < rsi < 50:  # RSI在合理范围
                confirmations.append("RSI支持")
            
            if volume_ratio > 2.0:
                confirmations.append("强放量")
            
            if len(confirmations) >= 2:
                strength = 0.7 + len(confirmations) * 0.1
                confidence = 0.7 + len(confirmations) * 0.05
                
                return {
                    'direction': 'SHORT',
                    'strength': min(strength, 1.0),
                    'confidence': min(confidence, 0.9),
                    'reason': f'向下突破确认，{len(confirmations)}个信号支持',
                    'confirmations': confirmations
                }
        
        # 假突破识别
        elif (bb_position > 0.85 or bb_position < 0.15) and volume_ratio < 1.2:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'reason': '疑似假突破，成交量不足'
            }
        
        else:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.4,
                'reason': '未检测到有效突破信号'
            }
    
    def update_parameters(self, market_state: Dict):
        """更新突破参数"""
        volatility = market_state.get('volatility', 0.02)
        
        # 根据波动率调整突破阈值
        if volatility > 0.04:
            self.breakout_threshold = 0.03
            self.volume_confirmation = 2.0
        elif volatility < 0.015:
            self.breakout_threshold = 0.015
            self.volume_confirmation = 1.2
        else:
            self.breakout_threshold = 0.02
            self.volume_confirmation = 1.5

class StrategyManager:
    """
    策略管理器 - 统一管理多个交易策略
    """

    def __init__(self):
        self.strategies = {
            'grid': GridTradingStrategy(weight=0.2),
            'arbitrage': StatisticalArbitrageStrategy(weight=0.2),
            'reversal': MomentumReversalStrategy(weight=0.3),
            'breakout': BreakoutFollowStrategy(weight=0.3)
        }

        self.market_state_history = []
        self.strategy_performance = {}
        self.adaptive_weights = True

        # 初始化策略性能跟踪
        for name in self.strategies.keys():
            self.strategy_performance[name] = {
                'signals_generated': 0,
                'successful_signals': 0,
                'total_return': 0.0,
                'recent_performance': []
            }

    def generate_combined_signal(self, data: Dict) -> Dict:
        """
        生成组合策略信号
        """
        # 1. 分析市场状态
        market_state = self._analyze_market_state(data)

        # 2. 更新策略参数
        self._update_strategy_parameters(market_state)

        # 3. 获取各策略信号
        strategy_signals = {}
        for name, strategy in self.strategies.items():
            if strategy.enabled:
                try:
                    signal = strategy.generate_signal(data)
                    strategy_signals[name] = signal
                except Exception as e:
                    print(f"⚠️ 策略{name}信号生成失败: {str(e)}")
                    continue

        # 4. 组合策略信号
        combined_signal = self._combine_strategy_signals(strategy_signals, market_state)

        # 5. 记录市场状态
        self.market_state_history.append({
            'timestamp': datetime.now(),
            'market_state': market_state,
            'strategy_signals': strategy_signals,
            'combined_signal': combined_signal
        })

        # 保持历史记录在合理范围内
        if len(self.market_state_history) > 100:
            self.market_state_history = self.market_state_history[-100:]

        return combined_signal

    def _analyze_market_state(self, data: Dict) -> Dict:
        """分析当前市场状态"""
        rsi = data.get('rsi', 50)
        bb_position = data.get('bb_position', 0.5)
        volume_ratio = data.get('volume_ratio', 1.0)
        volatility = data.get('atr_percentage', 0.02)
        macd_trend = data.get('macd_trend', 'neutral')

        # 市场状态分类
        if volatility > 0.04:
            volatility_state = 'high'
        elif volatility < 0.015:
            volatility_state = 'low'
        else:
            volatility_state = 'normal'

        # 趋势状态
        if 30 < rsi < 70 and 0.3 < bb_position < 0.7:
            trend_state = 'sideways'
        elif rsi > 70 or bb_position > 0.8:
            trend_state = 'overbought'
        elif rsi < 30 or bb_position < 0.2:
            trend_state = 'oversold'
        else:
            trend_state = 'trending'

        # 成交量状态
        if volume_ratio > 1.5:
            volume_state = 'high'
        elif volume_ratio < 0.8:
            volume_state = 'low'
        else:
            volume_state = 'normal'

        return {
            'volatility': volatility,
            'volatility_state': volatility_state,
            'trend_state': trend_state,
            'volume_state': volume_state,
            'macd_trend': macd_trend,
            'market_regime': self._determine_market_regime(volatility_state, trend_state, volume_state)
        }

    def _determine_market_regime(self, vol_state: str, trend_state: str, volume_state: str) -> str:
        """确定市场制度"""
        if trend_state == 'sideways' and vol_state == 'low':
            return 'consolidation'  # 盘整
        elif trend_state in ['overbought', 'oversold'] and volume_state == 'high':
            return 'reversal_zone'  # 反转区域
        elif trend_state == 'trending' and volume_state == 'high':
            return 'trending'  # 趋势
        elif vol_state == 'high':
            return 'volatile'  # 高波动
        else:
            return 'neutral'  # 中性

    def _update_strategy_parameters(self, market_state: Dict):
        """更新策略参数"""
        for strategy in self.strategies.values():
            strategy.update_parameters(market_state)

    def _combine_strategy_signals(self, strategy_signals: Dict, market_state: Dict) -> Dict:
        """组合策略信号"""
        if not strategy_signals:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'reason': '无可用策略信号',
                'strategy_breakdown': {}
            }

        # 根据市场制度调整策略权重
        adjusted_weights = self._get_adjusted_weights(market_state)

        # 收集有效信号
        valid_signals = {}
        for name, signal in strategy_signals.items():
            if signal['direction'] != 'WAIT' and signal['strength'] > 0:
                valid_signals[name] = signal

        if not valid_signals:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.4,
                'reason': '所有策略建议等待',
                'strategy_breakdown': strategy_signals
            }

        # 计算加权信号
        weighted_long = 0
        weighted_short = 0
        total_weight = 0
        confidence_sum = 0

        signal_details = []

        for name, signal in valid_signals.items():
            weight = adjusted_weights.get(name, self.strategies[name].weight)
            strength = signal['strength']
            confidence = signal['confidence']

            total_weight += weight
            confidence_sum += confidence * weight

            if signal['direction'] == 'LONG':
                weighted_long += weight * strength
                signal_details.append(f"{name}看涨({strength:.1%})")
            elif signal['direction'] == 'SHORT':
                weighted_short += weight * strength
                signal_details.append(f"{name}看跌({strength:.1%})")

        if total_weight == 0:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'reason': '策略权重为零',
                'strategy_breakdown': strategy_signals
            }

        # 计算最终信号
        avg_confidence = confidence_sum / total_weight

        if weighted_long > weighted_short:
            final_direction = 'LONG'
            final_strength = weighted_long / total_weight
            agreement = weighted_long / (weighted_long + weighted_short)
        elif weighted_short > weighted_long:
            final_direction = 'SHORT'
            final_strength = weighted_short / total_weight
            agreement = weighted_short / (weighted_long + weighted_short)
        else:
            final_direction = 'WAIT'
            final_strength = 0
            agreement = 0.5

        # 调整置信度
        final_confidence = avg_confidence * agreement

        return {
            'direction': final_direction,
            'strength': final_strength,
            'confidence': final_confidence,
            'reason': f"多策略组合: {', '.join(signal_details)}",
            'strategy_breakdown': strategy_signals,
            'market_regime': market_state['market_regime'],
            'agreement_score': agreement,
            'active_strategies': len(valid_signals),
            'total_strategies': len(strategy_signals)
        }

    def _get_adjusted_weights(self, market_state: Dict) -> Dict:
        """根据市场状态调整策略权重"""
        market_regime = market_state['market_regime']
        base_weights = {name: strategy.weight for name, strategy in self.strategies.items()}

        # 根据市场制度调整权重
        if market_regime == 'consolidation':
            # 盘整市场：网格交易和统计套利权重增加
            return {
                'grid': base_weights['grid'] * 1.5,
                'arbitrage': base_weights['arbitrage'] * 1.3,
                'reversal': base_weights['reversal'] * 0.8,
                'breakout': base_weights['breakout'] * 0.5
            }

        elif market_regime == 'trending':
            # 趋势市场：突破跟踪权重增加
            return {
                'grid': base_weights['grid'] * 0.5,
                'arbitrage': base_weights['arbitrage'] * 0.7,
                'reversal': base_weights['reversal'] * 0.8,
                'breakout': base_weights['breakout'] * 1.5
            }

        elif market_regime == 'reversal_zone':
            # 反转区域：动量反转权重增加
            return {
                'grid': base_weights['grid'] * 0.7,
                'arbitrage': base_weights['arbitrage'] * 1.1,
                'reversal': base_weights['reversal'] * 1.4,
                'breakout': base_weights['breakout'] * 0.8
            }

        elif market_regime == 'volatile':
            # 高波动：降低所有策略权重，更加谨慎
            return {
                'grid': base_weights['grid'] * 0.6,
                'arbitrage': base_weights['arbitrage'] * 0.8,
                'reversal': base_weights['reversal'] * 0.7,
                'breakout': base_weights['breakout'] * 0.9
            }

        else:  # neutral
            return base_weights

    def update_strategy_performance(self, strategy_name: str, signal_result: Dict):
        """更新策略表现"""
        if strategy_name not in self.strategy_performance:
            return

        perf = self.strategy_performance[strategy_name]
        perf['signals_generated'] += 1

        if signal_result.get('success', False):
            perf['successful_signals'] += 1

        pnl = signal_result.get('pnl', 0)
        perf['total_return'] += pnl

        # 记录最近表现
        perf['recent_performance'].append({
            'timestamp': datetime.now(),
            'success': signal_result.get('success', False),
            'pnl': pnl,
            'signal_strength': signal_result.get('signal_strength', 0)
        })

        # 保持最近50次记录
        if len(perf['recent_performance']) > 50:
            perf['recent_performance'] = perf['recent_performance'][-50:]

    def get_strategy_report(self) -> Dict:
        """获取策略表现报告"""
        report = {
            'total_strategies': len(self.strategies),
            'enabled_strategies': len([s for s in self.strategies.values() if s.enabled]),
            'strategy_performance': {}
        }

        for name, perf in self.strategy_performance.items():
            if perf['signals_generated'] > 0:
                success_rate = perf['successful_signals'] / perf['signals_generated']
                avg_return = perf['total_return'] / perf['signals_generated']

                # 计算最近表现
                recent_success = 0
                if len(perf['recent_performance']) > 0:
                    recent_success = sum(1 for p in perf['recent_performance'][-10:] if p['success']) / min(10, len(perf['recent_performance']))

                report['strategy_performance'][name] = {
                    'signals_generated': perf['signals_generated'],
                    'success_rate': success_rate,
                    'avg_return': avg_return,
                    'recent_success_rate': recent_success,
                    'total_return': perf['total_return'],
                    'enabled': self.strategies[name].enabled
                }

        return report

    def enable_strategy(self, strategy_name: str):
        """启用策略"""
        if strategy_name in self.strategies:
            self.strategies[strategy_name].enabled = True

    def disable_strategy(self, strategy_name: str):
        """禁用策略"""
        if strategy_name in self.strategies:
            self.strategies[strategy_name].enabled = False

if __name__ == "__main__":
    # 测试多策略库
    print("🧪 测试多策略库")

    # 创建策略管理器
    manager = StrategyManager()

    # 模拟市场数据
    test_data = {
        'price': 104500.0,
        'rsi': 78.5,
        'bb_position': 0.92,
        'volume_ratio': 2.1,
        'atr_percentage': 0.035,
        'macd_trend': 'bearish',
        'price_history': [104000 + i*10 for i in range(30)]
    }

    # 生成组合信号
    combined_signal = manager.generate_combined_signal(test_data)

    print(f"📊 组合信号结果:")
    print(f"   方向: {combined_signal['direction']}")
    print(f"   强度: {combined_signal['strength']:.1%}")
    print(f"   置信度: {combined_signal['confidence']:.1%}")
    print(f"   理由: {combined_signal['reason']}")
    print(f"   市场制度: {combined_signal['market_regime']}")
    print(f"   策略一致性: {combined_signal['agreement_score']:.1%}")
    print(f"   活跃策略: {combined_signal['active_strategies']}/{combined_signal['total_strategies']}")

    # 显示各策略详情
    print(f"\n📈 各策略信号详情:")
    for name, signal in combined_signal['strategy_breakdown'].items():
        print(f"   {name}: {signal['direction']} (强度:{signal['strength']:.1%}, 置信度:{signal['confidence']:.1%})")
        print(f"      理由: {signal['reason']}")

    print(f"\n✅ 多策略库测试完成")
