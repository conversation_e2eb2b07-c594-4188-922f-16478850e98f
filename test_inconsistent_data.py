#!/usr/bin/env python3
"""
Test script to verify the fix for inconsistent trading data display
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader
from datetime import datetime

def test_inconsistent_data_fix():
    """Test the fix for inconsistent trading data"""
    print("🧪 Testing Inconsistent Data Fix")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 Simulating Your Exact Scenario:")
    print("Initial Balance: $50.00")
    print("Current Balance: $62.81 (somehow increased)")
    print("Trade Record: 1 losing trade of -$2.01")
    print("Expected Issue: Data inconsistency")
    
    # Simulate the exact scenario you encountered
    # 1. Set current balance to $62.81 (as shown in your output)
    trader.account['balance'] = 62.81
    trader.account['equity'] = 62.81
    trader.account['unrealized_pnl'] = 0.00
    trader.account['margin_used'] = 0.00
    trader.account['available_margin'] = 62.81
    
    # 2. Add the losing trade record
    trader.trade_history.append({
        'action': 'CLOSE',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'side': 'LONG',
        'price': 102000,
        'net_pnl': -2.01,
        'roi_percent': -12.9,
        'hold_time': 0.007,  # 0.4 minutes
        'reason': '止损'
    })
    
    print("\n🔍 Testing Enhanced Display (Should Detect Inconsistency):")
    trader._print_enhanced_account_status()
    trader._print_enhanced_trading_statistics()
    
    print("\n" + "="*60)
    print("🎯 Analysis of the Issue:")
    print("✅ The enhanced display now shows:")
    print("   - Trade Record P&L: -$2.01 (from trade history)")
    print("   - Actual Realized P&L: +$12.81 (from balance change)")
    print("   - Inconsistency Warning: Should be displayed")
    print("   - This reveals missing trades or incorrect balance updates")
    
    print("\n💡 Possible Explanations:")
    print("   1. Previous profitable trades not recorded in history")
    print("   2. Balance updated incorrectly")
    print("   3. System reset without clearing balance")
    print("   4. Multiple trading sessions with incomplete records")

if __name__ == "__main__":
    test_inconsistent_data_fix()
