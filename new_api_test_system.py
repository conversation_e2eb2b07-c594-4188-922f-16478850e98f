#!/usr/bin/env python3
"""
使用新API密钥的测试系统
先测试连接，再决定使用测试网还是主网
"""

import numpy as np
import logging
import time
import requests
import hmac
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Tuple
import threading
import queue
from urllib.parse import urlencode

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NewAPITester:
    """新API密钥测试器"""
    
    def __init__(self, api_key: str, api_secret: str):
        self.api_key = api_key
        self.api_secret = api_secret
        
        # 测试网和主网端点
        self.testnet_url = "https://testnet.binance.vision"
        self.mainnet_url = "https://api.binance.com"
        
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        })
        
        # 时间同步
        self.server_time_offset = 0
        
    def _sync_server_time(self, base_url: str):
        """同步服务器时间"""
        try:
            response = self.session.get(f"{base_url}/api/v3/time", timeout=10)
            response.raise_for_status()
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            self.server_time_offset = server_time - local_time
            logger.info(f"✅ 时间同步成功，偏移: {self.server_time_offset}ms")
        except Exception as e:
            logger.warning(f"⚠️ 时间同步失败: {e}")
            self.server_time_offset = 0
    
    def _generate_signature(self, params: dict) -> str:
        """生成API签名"""
        query_string = urlencode(params)
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def test_api_connection(self, base_url: str, network_name: str):
        """测试API连接"""
        logger.info(f"\n🔍 测试{network_name}连接...")
        
        try:
            # 1. 测试基础连接
            response = self.session.get(f"{base_url}/api/v3/ping", timeout=10)
            if response.status_code != 200:
                logger.error(f"❌ {network_name} Ping失败: {response.status_code}")
                return False
            
            logger.info(f"✅ {network_name} 基础连接成功")
            
            # 2. 同步时间
            self._sync_server_time(base_url)
            
            # 3. 测试价格获取
            response = self.session.get(f"{base_url}/api/v3/ticker/price", 
                                      params={"symbol": "ADAUSDT"}, timeout=10)
            if response.status_code == 200:
                price_data = response.json()
                logger.info(f"✅ {network_name} ADAUSDT价格: {price_data['price']}")
            else:
                logger.error(f"❌ {network_name} 价格获取失败")
                return False
            
            # 4. 测试API认证
            params = {
                'timestamp': int(time.time() * 1000) + self.server_time_offset,
                'recvWindow': 60000
            }
            params['signature'] = self._generate_signature(params)
            
            response = self.session.get(f"{base_url}/api/v3/account", 
                                      params=params, timeout=10)
            
            if response.status_code == 200:
                account_data = response.json()
                logger.info(f"✅ {network_name} API认证成功")
                
                # 显示账户信息
                logger.info(f"   账户类型: {account_data.get('accountType', 'N/A')}")
                
                # 显示余额
                balances = account_data.get('balances', [])
                usdt_balance = 0
                ada_balance = 0
                
                for balance in balances:
                    if balance['asset'] == 'USDT' and float(balance['free']) > 0:
                        usdt_balance = float(balance['free'])
                    elif balance['asset'] == 'ADA' and float(balance['free']) > 0:
                        ada_balance = float(balance['free'])
                
                logger.info(f"   USDT余额: {usdt_balance}")
                logger.info(f"   ADA余额: {ada_balance}")
                
                return True, usdt_balance, ada_balance
                
            else:
                error_data = response.json() if response.content else {}
                logger.error(f"❌ {network_name} API认证失败: {error_data}")
                return False
                
        except Exception as e:
            logger.error(f"❌ {network_name} 连接异常: {e}")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🔧 开始新API密钥综合测试...")
        logger.info("="*60)
        
        # 测试测试网
        logger.info("🧪 测试网测试")
        logger.info("="*30)
        testnet_result = self.test_api_connection(self.testnet_url, "测试网")
        
        # 测试主网
        logger.info("\n🌐 主网测试")
        logger.info("="*30)
        mainnet_result = self.test_api_connection(self.mainnet_url, "主网")
        
        # 分析结果
        logger.info("\n📋 测试结果分析")
        logger.info("="*30)
        
        if testnet_result and testnet_result[0]:
            logger.info("✅ 测试网：连接成功，可以进行安全测试")
            return "testnet", testnet_result[1], testnet_result[2]
        elif mainnet_result and mainnet_result[0]:
            logger.info("✅ 主网：连接成功，可以进行真实交易（谨慎！）")
            return "mainnet", mainnet_result[1], mainnet_result[2]
        else:
            logger.error("❌ 所有网络连接失败")
            return None, 0, 0

class SmartTradingSystem:
    """智能交易系统 - 自动选择网络"""
    
    def __init__(self, api_key: str, api_secret: str):
        self.api_key = api_key
        self.api_secret = api_secret
        self.symbol = "ADAUSDT"
        
        # 成功验证的剥头皮参数
        self.position_risk = 0.025  # 2.5%风险
        self.stop_loss_ratio = 0.0004  # 0.04%止损
        self.take_profit_ratio = 0.0005  # 0.05%止盈
        self.min_confidence = 0.65  # 65%置信度
        
        # 网络配置
        self.network_type = None
        self.base_url = None
        self.usdt_balance = 0
        self.ada_balance = 0
        
        # 交易状态
        self.current_position = None
        self.total_trades = 0
        self.winning_trades = 0
        self.trade_history = []
        
        # 时间同步
        self.server_time_offset = 0
        
    def initialize_network(self):
        """初始化网络连接"""
        tester = NewAPITester(self.api_key, self.api_secret)
        network_type, usdt_balance, ada_balance = tester.run_comprehensive_test()
        
        if network_type == "testnet":
            self.network_type = "testnet"
            self.base_url = "https://testnet.binance.vision"
            self.usdt_balance = usdt_balance
            self.ada_balance = ada_balance
            logger.info("🧪 使用测试网进行安全交易")
            return True
        elif network_type == "mainnet":
            self.network_type = "mainnet"
            self.base_url = "https://api.binance.com"
            self.usdt_balance = usdt_balance
            self.ada_balance = ada_balance
            logger.warning("🌐 使用主网进行真实交易")
            return True
        else:
            logger.error("❌ 无法连接到任何网络")
            return False
    
    def _sync_server_time(self):
        """同步服务器时间"""
        try:
            session = requests.Session()
            session.headers.update({'X-MBX-APIKEY': self.api_key})
            
            response = session.get(f"{self.base_url}/api/v3/time", timeout=10)
            response.raise_for_status()
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            self.server_time_offset = server_time - local_time
        except Exception as e:
            logger.warning(f"⚠️ 时间同步失败: {e}")
            self.server_time_offset = 0
    
    def get_price(self) -> Optional[float]:
        """获取当前价格"""
        try:
            session = requests.Session()
            response = session.get(f"{self.base_url}/api/v3/ticker/price", 
                                 params={"symbol": self.symbol}, timeout=10)
            if response.status_code == 200:
                return float(response.json()['price'])
            return None
        except Exception as e:
            logger.error(f"获取价格失败: {e}")
            return None
    
    def get_klines(self) -> Optional[list]:
        """获取K线数据"""
        try:
            session = requests.Session()
            params = {
                "symbol": self.symbol,
                "interval": "1m",
                "limit": 20
            }
            response = session.get(f"{self.base_url}/api/v3/klines", 
                                 params=params, timeout=10)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            logger.error(f"获取K线失败: {e}")
            return None
    
    def calculate_signal(self) -> Tuple[str, float]:
        """计算交易信号"""
        try:
            klines = self.get_klines()
            if not klines or len(klines) < 10:
                return "HOLD", 0.0
            
            # 转换价格数据
            closes = [float(k[4]) for k in klines]
            
            current_price = closes[-1]
            prev_price_1 = closes[-2]
            prev_price_3 = closes[-4] if len(closes) >= 4 else closes[-2]
            
            # 计算变化率
            change_1 = (current_price - prev_price_1) / prev_price_1
            change_3 = (current_price - prev_price_3) / prev_price_3
            
            # 简单移动平均
            ma_5 = np.mean(closes[-5:])
            
            # 评分系统
            score = 0
            confidence_factors = []
            
            # 1分钟动量
            if change_1 > 0.0002:
                score += 3
                confidence_factors.append(0.25)
            elif change_1 < -0.0002:
                score -= 3
                confidence_factors.append(0.25)
            
            # 3分钟趋势
            if change_3 > 0.0003:
                score += 2
                confidence_factors.append(0.15)
            elif change_3 < -0.0003:
                score -= 2
                confidence_factors.append(0.15)
            
            # 移动平均
            if current_price > ma_5 * 1.0001:
                score += 1
                confidence_factors.append(0.10)
            elif current_price < ma_5 * 0.9999:
                score -= 1
                confidence_factors.append(0.10)
            
            # 决策
            if score >= 3:
                direction = "LONG"
                base_confidence = 0.70
            elif score <= -3:
                direction = "SHORT"
                base_confidence = 0.70
            else:
                direction = "HOLD"
                base_confidence = 0.5
            
            # 计算最终置信度
            if direction != "HOLD":
                confidence_boost = sum(confidence_factors)
                final_confidence = min(0.90, base_confidence + confidence_boost)
            else:
                final_confidence = base_confidence
            
            return direction, final_confidence
            
        except Exception as e:
            logger.error(f"信号计算失败: {e}")
            return "HOLD", 0.0
    
    def run_signal_demo(self, duration_minutes: int = 10):
        """运行信号演示"""
        logger.info(f"🎯 启动{self.network_type}信号演示")
        logger.info(f"💰 USDT余额: {self.usdt_balance}")
        logger.info(f"📊 参数: 止损{self.stop_loss_ratio:.2%}, 止盈{self.take_profit_ratio:.2%}")
        logger.info(f"⏰ 运行时间: {duration_minutes} 分钟")
        
        if not self.initialize_network():
            return
        
        self._sync_server_time()
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        signal_count = 0
        
        try:
            while datetime.now() < end_time:
                current_price = self.get_price()
                if current_price is None:
                    time.sleep(5)
                    continue
                
                direction, confidence = self.calculate_signal()
                
                if direction in ["LONG", "SHORT"] and confidence >= self.min_confidence:
                    signal_count += 1
                    
                    # 计算止损止盈
                    if direction == "LONG":
                        stop_loss = current_price * (1 - self.stop_loss_ratio)
                        take_profit = current_price * (1 + self.take_profit_ratio)
                    else:
                        stop_loss = current_price * (1 + self.stop_loss_ratio)
                        take_profit = current_price * (1 - self.take_profit_ratio)
                    
                    logger.info(f"🎯 信号#{signal_count}: {direction} @ {current_price:.4f}")
                    logger.info(f"   置信度: {confidence:.1%}")
                    logger.info(f"   止损: {stop_loss:.4f}")
                    logger.info(f"   止盈: {take_profit:.4f}")
                    logger.info(f"   网络: {self.network_type}")
                    
                    # 等待一段时间避免重复信号
                    time.sleep(30)
                else:
                    time.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断演示")
        except Exception as e:
            logger.error(f"❌ 演示异常: {e}")
        
        logger.info(f"🎉 演示完成，共生成 {signal_count} 个交易信号")

if __name__ == "__main__":
    print("🔧 新API密钥智能交易系统")
    print("📊 自动检测网络类型并生成交易信号")
    print("🛡️ 安全演示模式")
    
    # 使用新的API密钥
    NEW_API_KEY = "r6ANpzmeaBOo07VMbQLwxM7NCGCRIPCobxqRFqIpBU4h7eDrBKEx0PODYMTecgTH"
    NEW_API_SECRET = "8ZVMvzSPc1obE9qBIPwdsu7h1IrpQ2RmowVtDnfv8BmZUpJWSRTh06ewWt48kM3J"
    
    trader = SmartTradingSystem(NEW_API_KEY, NEW_API_SECRET)
    
    print("\n🚀 开始10分钟智能信号演示...")
    trader.run_signal_demo(duration_minutes=10)
