#!/usr/bin/env python3
"""
🚀 智能交易系统快速启动器
自动加载配置，无需手动输入API密钥
"""

import subprocess
import sys
import os
from datetime import datetime

def main():
    print("🚀 智能交易系统快速启动器")
    print("=" * 40)
    
    # 检查配置文件
    if not os.path.exists('config.json'):
        print("❌ 配置文件 config.json 不存在")
        print("请先运行系统生成配置文件，或手动创建")
        return
    
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 生成日志文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f'logs/auto_trading_{timestamp}.log'
    
    print(f"📝 日志将保存到: {log_filename}")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 40)
    
    try:
        # 运行交易系统并保存日志
        with open(log_filename, 'w', encoding='utf-8') as log_file:
            # 写入日志头部
            log_file.write(f"智能交易系统自动运行日志\n")
            log_file.write(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            log_file.write("=" * 50 + "\n\n")
            log_file.flush()
            
            # 启动交易系统
            process = subprocess.Popen(
                [sys.executable, 'smart_auto_trading.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时显示和保存输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
                    log_file.write(output)
                    log_file.flush()
            
            # 记录结束信息
            end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            end_info = f"\n\n结束时间: {end_time}\n返回代码: {process.poll()}\n"
            print(end_info)
            log_file.write(end_info)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
    finally:
        print(f"\n📝 完整日志已保存到: {log_filename}")

if __name__ == "__main__":
    main()
