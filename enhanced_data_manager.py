#!/usr/bin/env python3
"""
增强版数据管理器
解决数据质量问题，获取更长历史数据
"""

import pandas as pd
import numpy as np
import requests
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sqlite3
import os
from binance.client import Client
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedDataManager:
    """增强版数据管理器"""
    
    def __init__(self, api_key: str = None, api_secret: str = None):
        """初始化数据管理器"""
        self.client = None
        if api_key and api_secret:
            self.client = Client(api_key, api_secret)
        
        # 数据库设置
        self.db_path = "trading_data.db"
        self.init_database()
        
        # 数据质量参数
        self.min_data_points = 1000  # 最少数据点
        self.max_missing_ratio = 0.05  # 最大缺失比例5%
        self.max_outlier_ratio = 0.02  # 最大异常值比例2%
        
        # 支持的时间框架
        self.timeframes = {
            '1m': Client.KLINE_INTERVAL_1MINUTE,
            '5m': Client.KLINE_INTERVAL_5MINUTE,
            '15m': Client.KLINE_INTERVAL_15MINUTE,
            '1h': Client.KLINE_INTERVAL_1HOUR,
            '4h': Client.KLINE_INTERVAL_4HOUR,
            '1d': Client.KLINE_INTERVAL_1DAY
        }
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建K线数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS klines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timeframe TEXT NOT NULL,
                timestamp INTEGER NOT NULL,
                open_price REAL NOT NULL,
                high_price REAL NOT NULL,
                low_price REAL NOT NULL,
                close_price REAL NOT NULL,
                volume REAL NOT NULL,
                quote_volume REAL,
                trades_count INTEGER,
                taker_buy_base_volume REAL,
                taker_buy_quote_volume REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timeframe, timestamp)
            )
        ''')
        
        # 创建数据质量记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_quality (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timeframe TEXT NOT NULL,
                start_date TEXT NOT NULL,
                end_date TEXT NOT NULL,
                total_points INTEGER NOT NULL,
                missing_points INTEGER NOT NULL,
                outlier_points INTEGER NOT NULL,
                quality_score REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("数据库初始化完成")
    
    def get_historical_data_comprehensive(self, symbol: str, timeframes: List[str], 
                                        days_back: int = 365) -> Dict[str, pd.DataFrame]:
        """获取多时间框架的综合历史数据"""
        logger.info(f"获取 {symbol} 过去 {days_back} 天的多时间框架数据")
        
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days_back)
        
        all_data = {}
        
        for tf in timeframes:
            logger.info(f"获取 {tf} 时间框架数据...")
            
            # 先尝试从数据库加载
            cached_data = self.load_from_database(symbol, tf, start_time, end_time)
            
            if cached_data is not None and len(cached_data) > 0:
                logger.info(f"从缓存加载 {tf} 数据: {len(cached_data)} 条记录")
                all_data[tf] = cached_data
            else:
                # 从API获取新数据
                fresh_data = self.fetch_from_binance(symbol, tf, start_time, end_time)
                if fresh_data is not None:
                    # 保存到数据库
                    self.save_to_database(fresh_data, symbol, tf)
                    all_data[tf] = fresh_data
                    logger.info(f"从API获取 {tf} 数据: {len(fresh_data)} 条记录")
                else:
                    logger.error(f"无法获取 {tf} 数据")
        
        return all_data
    
    def fetch_from_binance(self, symbol: str, timeframe: str, 
                          start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """从币安API获取数据"""
        try:
            if not self.client:
                # 使用公共API
                return self.fetch_public_data(symbol, timeframe, start_time, end_time)
            
            # 使用认证API
            klines = self.client.get_historical_klines(
                symbol, 
                self.timeframes[timeframe],
                start_time.strftime("%d %b %Y %H:%M:%S"),
                end_time.strftime("%d %b %Y %H:%M:%S")
            )
            
            if not klines:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades_count',
                'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
            ])
            
            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 
                             'quote_volume', 'taker_buy_base_volume', 'taker_buy_quote_volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # 删除不需要的列
            df = df[['open', 'high', 'low', 'close', 'volume', 'quote_volume', 
                    'trades_count', 'taker_buy_base_volume', 'taker_buy_quote_volume']]
            
            return df
            
        except Exception as e:
            logger.error(f"从币安API获取数据失败: {e}")
            return None
    
    def fetch_public_data(self, symbol: str, timeframe: str, 
                         start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """使用公共API获取数据"""
        try:
            # 币安公共API端点
            url = "https://api.binance.com/api/v3/klines"
            
            # 时间框架映射
            interval_map = {
                '1m': '1m', '5m': '5m', '15m': '15m', 
                '1h': '1h', '4h': '4h', '1d': '1d'
            }
            
            params = {
                'symbol': symbol,
                'interval': interval_map[timeframe],
                'startTime': int(start_time.timestamp() * 1000),
                'endTime': int(end_time.timestamp() * 1000),
                'limit': 1000
            }
            
            all_data = []
            current_start = start_time
            
            while current_start < end_time:
                params['startTime'] = int(current_start.timestamp() * 1000)
                
                response = requests.get(url, params=params)
                response.raise_for_status()
                
                data = response.json()
                if not data:
                    break
                
                all_data.extend(data)
                
                # 更新起始时间
                last_timestamp = data[-1][0]
                current_start = datetime.fromtimestamp(last_timestamp / 1000) + timedelta(milliseconds=1)
                
                # 避免API限制
                time.sleep(0.1)
            
            if not all_data:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(all_data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades_count',
                'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
            ])
            
            # 数据处理
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            # 去重和排序
            df = df[~df.index.duplicated(keep='first')]
            df = df.sort_index()
            
            return df
            
        except Exception as e:
            logger.error(f"公共API获取数据失败: {e}")
            return None
    
    def save_to_database(self, data: pd.DataFrame, symbol: str, timeframe: str):
        """保存数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            for timestamp, row in data.iterrows():
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO klines 
                    (symbol, timeframe, timestamp, open_price, high_price, 
                     low_price, close_price, volume, quote_volume, trades_count,
                     taker_buy_base_volume, taker_buy_quote_volume)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    symbol, timeframe, int(timestamp.timestamp()),
                    row['open'], row['high'], row['low'], row['close'], row['volume'],
                    row.get('quote_volume', 0), row.get('trades_count', 0),
                    row.get('taker_buy_base_volume', 0), row.get('taker_buy_quote_volume', 0)
                ))
            
            conn.commit()
            conn.close()
            logger.info(f"保存 {len(data)} 条 {symbol} {timeframe} 数据到数据库")
            
        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")
    
    def load_from_database(self, symbol: str, timeframe: str, 
                          start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """从数据库加载数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = '''
                SELECT timestamp, open_price, high_price, low_price, close_price, volume,
                       quote_volume, trades_count, taker_buy_base_volume, taker_buy_quote_volume
                FROM klines 
                WHERE symbol = ? AND timeframe = ? 
                AND timestamp >= ? AND timestamp <= ?
                ORDER BY timestamp
            '''
            
            df = pd.read_sql_query(query, conn, params=(
                symbol, timeframe, 
                int(start_time.timestamp()), 
                int(end_time.timestamp())
            ))
            
            conn.close()
            
            if df.empty:
                return None
            
            # 数据处理
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            df.set_index('timestamp', inplace=True)
            
            # 重命名列
            df.columns = ['open', 'high', 'low', 'close', 'volume',
                         'quote_volume', 'trades_count', 'taker_buy_base_volume', 'taker_buy_quote_volume']
            
            return df
            
        except Exception as e:
            logger.error(f"从数据库加载数据失败: {e}")
            return None
    
    def validate_data_quality(self, data: pd.DataFrame, symbol: str, timeframe: str) -> Dict:
        """验证数据质量"""
        logger.info(f"验证 {symbol} {timeframe} 数据质量...")
        
        total_points = len(data)
        
        # 检查缺失值
        missing_points = data.isnull().sum().sum()
        missing_ratio = missing_points / (total_points * len(data.columns))
        
        # 检查异常值
        outlier_points = 0
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            Q1 = data[col].quantile(0.25)
            Q3 = data[col].quantile(0.75)
            IQR = Q3 - Q1
            outliers = ((data[col] < (Q1 - 3 * IQR)) | 
                       (data[col] > (Q3 + 3 * IQR))).sum()
            outlier_points += outliers
        
        outlier_ratio = outlier_points / total_points
        
        # 检查数据连续性
        time_gaps = 0
        if timeframe == '1m':
            expected_interval = timedelta(minutes=1)
        elif timeframe == '5m':
            expected_interval = timedelta(minutes=5)
        elif timeframe == '15m':
            expected_interval = timedelta(minutes=15)
        elif timeframe == '1h':
            expected_interval = timedelta(hours=1)
        else:
            expected_interval = timedelta(hours=4)
        
        for i in range(1, len(data)):
            actual_interval = data.index[i] - data.index[i-1]
            if actual_interval > expected_interval * 1.5:  # 允许50%误差
                time_gaps += 1
        
        gap_ratio = time_gaps / total_points
        
        # 计算质量评分
        quality_score = 1.0 - (missing_ratio * 0.4 + outlier_ratio * 0.3 + gap_ratio * 0.3)
        quality_score = max(0, min(1, quality_score))
        
        quality_report = {
            'total_points': total_points,
            'missing_points': missing_points,
            'missing_ratio': missing_ratio,
            'outlier_points': outlier_points,
            'outlier_ratio': outlier_ratio,
            'time_gaps': time_gaps,
            'gap_ratio': gap_ratio,
            'quality_score': quality_score,
            'is_acceptable': (
                total_points >= self.min_data_points and
                missing_ratio <= self.max_missing_ratio and
                outlier_ratio <= self.max_outlier_ratio
            )
        }
        
        logger.info(f"数据质量报告: 评分={quality_score:.3f}, 可接受={quality_report['is_acceptable']}")
        
        return quality_report
    
    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """高级数据清洗"""
        logger.info("开始高级数据清洗...")
        
        original_length = len(data)
        
        # 1. 处理缺失值
        data = data.fillna(method='ffill').fillna(method='bfill')
        
        # 2. 处理异常值(使用更严格的标准)
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            Q1 = data[col].quantile(0.25)
            Q3 = data[col].quantile(0.75)
            IQR = Q3 - Q1
            
            # 使用3倍IQR规则
            lower_bound = Q1 - 3 * IQR
            upper_bound = Q3 + 3 * IQR
            
            # 记录异常值数量
            outliers = ((data[col] < lower_bound) | (data[col] > upper_bound)).sum()
            if outliers > 0:
                logger.info(f"列 {col} 发现 {outliers} 个异常值")
            
            # 截断异常值
            data[col] = data[col].clip(lower_bound, upper_bound)
        
        # 3. 处理无穷值
        data = data.replace([np.inf, -np.inf], np.nan)
        data = data.fillna(method='ffill').fillna(method='bfill')
        
        # 4. 价格数据合理性检查
        if 'open' in data.columns and 'high' in data.columns and 'low' in data.columns and 'close' in data.columns:
            # 确保 high >= max(open, close) 和 low <= min(open, close)
            data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
            data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))
        
        # 5. 成交量合理性检查
        if 'volume' in data.columns:
            # 成交量不能为负
            data['volume'] = np.maximum(data['volume'], 0)
        
        cleaned_length = len(data)
        logger.info(f"数据清洗完成: {original_length} → {cleaned_length} 条记录")
        
        return data
    
    def get_quality_enhanced_data(self, symbol: str = "BTCUSDT", 
                                 timeframes: List[str] = ['5m', '15m', '1h'],
                                 days_back: int = 365) -> Dict[str, pd.DataFrame]:
        """获取质量增强的数据"""
        logger.info("开始获取质量增强的历史数据...")
        
        # 获取原始数据
        raw_data = self.get_historical_data_comprehensive(symbol, timeframes, days_back)
        
        enhanced_data = {}
        
        for tf, data in raw_data.items():
            if data is None or len(data) == 0:
                logger.warning(f"{tf} 时间框架数据为空")
                continue
            
            # 数据质量验证
            quality_report = self.validate_data_quality(data, symbol, tf)
            
            if not quality_report['is_acceptable']:
                logger.warning(f"{tf} 数据质量不达标: {quality_report}")
                continue
            
            # 数据清洗
            cleaned_data = self.clean_data(data.copy())
            
            # 最终质量检查
            final_quality = self.validate_data_quality(cleaned_data, symbol, tf)
            logger.info(f"{tf} 最终数据质量: {final_quality['quality_score']:.3f}")
            
            enhanced_data[tf] = cleaned_data
        
        logger.info(f"质量增强完成，获得 {len(enhanced_data)} 个时间框架的数据")
        return enhanced_data

if __name__ == "__main__":
    # 测试数据管理器
    data_manager = EnhancedDataManager()
    
    # 获取质量增强的数据
    enhanced_data = data_manager.get_quality_enhanced_data(
        symbol="BTCUSDT",
        timeframes=['5m', '15m', '1h'],
        days_back=90  # 获取3个月数据进行测试
    )
    
    print("🎉 增强版数据管理器测试完成！")
    for tf, data in enhanced_data.items():
        print(f"  ✅ {tf}: {len(data)} 条高质量数据")
    
    print("\n📊 主要改进:")
    print("  ✅ 多时间框架数据获取")
    print("  ✅ 数据库缓存机制")
    print("  ✅ 严格的数据质量验证")
    print("  ✅ 高级数据清洗算法")
    print("  ✅ 数据连续性检查")
    print("  ✅ 异常值处理优化")
