#!/usr/bin/env python3
"""
平衡训练脚本 - 专门解决严重类别不平衡问题
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import TimeSeriesSplit, StratifiedKFold
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, balanced_accuracy_score
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.under_sampling import RandomUnderSampler
from imblearn.combine import SMOTEENN, SMOTETomek
import joblib

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def balanced_train(symbol='BTCUSDT', model_type='xgb', months_back=24, balance_method='smote'):
    """
    平衡训练 - 专门处理类别不平衡问题
    
    balance_method: 'smote', 'undersample', 'combine', 'cost_sensitive'
    """
    print(f"🚀 开始平衡训练 {symbol} {model_type.upper()} 模型...")
    print(f"📊 数据: {months_back}个月, 平衡方法: {balance_method}")
    
    try:
        # 1. 获取数据
        print("📊 获取历史数据...")
        start_date = (datetime.now() - timedelta(days=months_back*30)).strftime('%Y-%m-%d')
        
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, '1h', start_date, force_refresh=False)
        
        print(f"✅ 获取到 {len(df)} 条数据")
        
        # 2. 特征工程
        print("🔧 特征工程...")
        engineer = FeatureEngineer()
        df_features = engineer.create_features(df)
        
        # 3. 数据准备
        print("📋 数据准备...")
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始列
        original_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
        
        # 数据清理
        before_clean = len(X)
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]
        
        X.replace([np.inf, -np.inf], np.nan, inplace=True)
        X.fillna(X.median(), inplace=True)
        
        print(f"数据清理: {before_clean} -> {len(X)} 样本")
        
        # 编码标签
        label_encoder = LabelEncoder()
        y_encoded = label_encoder.fit_transform(y)
        
        # 分析原始类别分布
        unique, counts = np.unique(y_encoded, return_counts=True)
        total_samples = len(y_encoded)
        
        print(f"\n📊 原始类别分布:")
        for class_id, count in zip(unique, counts):
            percentage = count / total_samples * 100
            print(f"   类别 {class_id}: {count:,} 样本 ({percentage:.1f}%)")
        
        # 检查不平衡程度
        max_count = max(counts)
        min_count = min(counts)
        imbalance_ratio = max_count / min_count
        
        print(f"\n⚖️ 不平衡分析:")
        print(f"   最大类别样本数: {max_count:,}")
        print(f"   最小类别样本数: {min_count:,}")
        print(f"   不平衡比例: {imbalance_ratio:.1f}:1")
        
        if imbalance_ratio > 100:
            print("   🚨 极度不平衡！")
        elif imbalance_ratio > 10:
            print("   ⚠️  严重不平衡")
        else:
            print("   ✅ 相对平衡")
        
        # 4. 处理类别不平衡
        print(f"\n🔄 应用 {balance_method} 平衡策略...")
        
        if balance_method == 'undersample':
            # 下采样主要类别
            target_size = min(1000, min_count * 10)  # 目标大小
            undersampler = RandomUnderSampler(
                sampling_strategy={
                    class_id: min(count, target_size) 
                    for class_id, count in zip(unique, counts)
                },
                random_state=42
            )
            X_balanced, y_balanced = undersampler.fit_resample(X, y_encoded)
            
        elif balance_method == 'smote':
            # SMOTE过采样
            # 先适度下采样主要类别
            max_samples_per_class = min(5000, max_count // 2)
            undersampler = RandomUnderSampler(
                sampling_strategy={
                    class_id: min(count, max_samples_per_class)
                    for class_id, count in zip(unique, counts)
                },
                random_state=42
            )
            X_temp, y_temp = undersampler.fit_resample(X, y_encoded)
            
            # 然后SMOTE过采样少数类别
            min_samples_for_smote = max(50, min(np.bincount(y_temp)))
            smote = SMOTE(
                sampling_strategy={
                    class_id: max(count, min_samples_for_smote)
                    for class_id, count in zip(*np.unique(y_temp, return_counts=True))
                },
                random_state=42,
                k_neighbors=min(5, min_samples_for_smote-1)
            )
            X_balanced, y_balanced = smote.fit_resample(X_temp, y_temp)
            
        elif balance_method == 'combine':
            # 组合方法：SMOTE + Edited Nearest Neighbours
            smote_enn = SMOTEENN(random_state=42)
            X_balanced, y_balanced = smote_enn.fit_resample(X, y_encoded)
            
        else:  # cost_sensitive
            # 不改变数据，使用成本敏感学习
            X_balanced, y_balanced = X, y_encoded
        
        # 显示平衡后的分布
        unique_balanced, counts_balanced = np.unique(y_balanced, return_counts=True)
        print(f"\n📊 平衡后类别分布:")
        for class_id, count in zip(unique_balanced, counts_balanced):
            percentage = count / len(y_balanced) * 100
            print(f"   类别 {class_id}: {count:,} 样本 ({percentage:.1f}%)")
        
        new_imbalance_ratio = max(counts_balanced) / min(counts_balanced)
        print(f"   新的不平衡比例: {new_imbalance_ratio:.1f}:1")
        
        # 5. 时间序列分割
        print("\n✂️  时间序列分割...")
        split_point = int(len(X_balanced) * 0.8)
        
        X_train = X_balanced[:split_point]
        X_test = X_balanced[split_point:]
        y_train = y_balanced[:split_point]
        y_test = y_balanced[split_point:]
        
        print(f"   训练集: {len(X_train):,} 样本")
        print(f"   测试集: {len(X_test):,} 样本")
        
        # 6. 特征缩放
        print("📏 特征缩放...")
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 7. 创建模型
        print(f"🎯 创建 {model_type.upper()} 模型...")
        
        if balance_method == 'cost_sensitive':
            # 计算类别权重
            class_weights = {}
            for class_id, count in zip(unique, counts):
                class_weights[class_id] = total_samples / (len(unique) * count)
            
            print(f"   类别权重: {class_weights}")
            
            if model_type == 'xgb' and HAS_XGB:
                # XGBoost不直接支持class_weight，使用scale_pos_weight
                model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=5,
                    learning_rate=0.1,
                    reg_alpha=0.5,
                    reg_lambda=0.5,
                    random_state=42,
                    n_jobs=-1,
                    eval_metric='mlogloss'
                )
            else:
                model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=8,
                    class_weight='balanced',  # 自动平衡权重
                    random_state=42,
                    n_jobs=-1
                )
        else:
            # 平衡数据，使用标准模型
            if model_type == 'xgb' and HAS_XGB:
                model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=5,
                    learning_rate=0.1,
                    reg_alpha=0.3,
                    reg_lambda=0.3,
                    random_state=42,
                    n_jobs=-1,
                    eval_metric='mlogloss'
                )
            else:
                model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=8,
                    random_state=42,
                    n_jobs=-1
                )
        
        # 8. 训练和评估
        print("🏃 训练模型...")
        model.fit(X_train_scaled, y_train)
        
        print("📊 评估模型...")
        y_train_pred = model.predict(X_train_scaled)
        y_test_pred = model.predict(X_test_scaled)
        
        train_accuracy = accuracy_score(y_train, y_train_pred)
        test_accuracy = accuracy_score(y_test, y_test_pred)
        train_balanced = balanced_accuracy_score(y_train, y_train_pred)
        test_balanced = balanced_accuracy_score(y_test, y_test_pred)
        
        # 9. 显示结果
        print("\n" + "="*60)
        print("🎉 平衡训练完成!")
        print("="*60)
        print(f"平衡方法: {balance_method}")
        print(f"原始样本: {total_samples:,} -> 平衡后: {len(y_balanced):,}")
        print(f"不平衡比例: {imbalance_ratio:.1f}:1 -> {new_imbalance_ratio:.1f}:1")
        print(f"")
        print(f"训练集准确率:     {train_accuracy:.4f}")
        print(f"测试集准确率:     {test_accuracy:.4f}")
        print(f"训练集平衡准确率: {train_balanced:.4f}")
        print(f"测试集平衡准确率: {test_balanced:.4f}")
        
        overfitting = train_accuracy - test_accuracy
        print(f"\n📊 模型健康度:")
        print(f"过拟合程度: {overfitting:.4f}")
        if overfitting > 0.1:
            print("⚠️  存在过拟合")
        else:
            print("✅ 泛化能力良好")
        
        # 详细分类报告
        print("\n📋 详细分类报告:")
        print(classification_report(y_test, y_test_pred))
        
        # 10. 保存模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path("models")
        model_dir.mkdir(exist_ok=True)
        
        model_path = model_dir / f"balanced_{balance_method}_{model_type}_{symbol}_{timestamp}.joblib"
        scaler_path = model_dir / f"balanced_scaler_{balance_method}_{model_type}_{symbol}_{timestamp}.joblib"
        encoder_path = model_dir / f"balanced_encoder_{balance_method}_{model_type}_{symbol}_{timestamp}.joblib"
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        joblib.dump(label_encoder, encoder_path)
        
        print(f"\n💾 模型已保存: {model_path}")
        
        return {
            'model': model,
            'scaler': scaler,
            'label_encoder': label_encoder,
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'balanced_accuracy': test_balanced,
            'balance_method': balance_method,
            'original_imbalance': imbalance_ratio,
            'new_imbalance': new_imbalance_ratio,
            'model_path': model_path
        }
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    model_type = sys.argv[2] if len(sys.argv) > 2 else 'xgb'
    months = int(sys.argv[3]) if len(sys.argv) > 3 else 24
    balance_method = sys.argv[4] if len(sys.argv) > 4 else 'smote'
    
    if model_type == 'xgb' and not HAS_XGB:
        print("XGBoost不可用，使用随机森林")
        model_type = 'rf'
    
    print(f"可用的平衡方法: smote, undersample, combine, cost_sensitive")
    balanced_train(symbol, model_type, months, balance_method)
