#!/usr/bin/env python3
"""
集成版AI增强交易系统 - 第一阶段完整版
整合增强风险管理和多时间框架分析
"""

import pandas as pd
import numpy as np
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer
from enhanced_display import print_enhanced_status, print_trade_execution
from enhanced_risk_management import EnhancedRiskManager
from multi_timeframe_analysis import MultiTimeFrameAnalyzer

class IntegratedTradingSystem:
    """
    集成版AI增强交易系统 - 第一阶段完整版
    """
    
    def __init__(self, initial_capital: float = 50.0, leverage: int = 2):
        """
        初始化集成交易系统
        """
        # 基础设置
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = leverage
        
        # 币安永续合约规则
        self.contract_rules = {
            'min_order_size': 0.001,
            'min_notional': 5.0,
            'maker_fee': 0.0002,
            'taker_fee': 0.0004,
            'funding_rate': 0.0001,
            'maintenance_margin': 0.004,
            'max_leverage': 125,
            'funding_interval': 8
        }
        
        # 持仓状态
        self.position = {
            'size': 0.0,
            'side': None,
            'entry_price': 0.0,
            'entry_time': None,
            'margin_used': 0.0,
            'unrealized_pnl': 0.0,
            'last_funding_time': None
        }
        
        # 交易记录和统计
        self.trades = []
        self.equity_history = []
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'max_equity': initial_capital,
            'win_rate': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_factor': 0.0
        }
        
        # 核心模块初始化
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        self.risk_manager = EnhancedRiskManager(initial_capital)
        self.mtf_analyzer = MultiTimeFrameAnalyzer()
        
        # AI模型
        self.ai_model = None
        self.scaler = None
        self._load_ai_model()
        
        # 状态管理
        self.state_file = "integrated_trading_state.json"
        self._load_state()
        
        # 系统配置
        self.config = {
            'ai_confidence_base': 0.65,
            'mtf_weight_multiplier': 1.2,
            'risk_adjustment_sensitivity': 1.0,
            'signal_confirmation_threshold': 2,
            'max_position_hold_hours': 24
        }
        
        print(f"🚀 集成版AI增强交易系统启动")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   当前资金: ${self.capital:.2f}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   AI模型: {'已加载' if self.ai_model else '使用模拟'}")
        print(f"   增强模块: 风险管理 + 多时间框架分析")
        print(f"   合约规则: 币安永续合约标准")
    
    def _load_ai_model(self):
        """加载AI模型"""
        try:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if model_files:
                model_path = max(model_files, key=lambda x: x.split('_')[-1])
                model_data = joblib.load(model_path)
                self.ai_model = model_data['model']
                self.scaler = model_data['scaler']
                print(f"✅ AI模型加载成功")
            else:
                print(f"⚠️ 未找到AI模型，使用模拟预测")
        except Exception as e:
            print(f"❌ AI模型加载失败: {str(e)}")
    
    def _load_state(self):
        """加载交易状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                
                self.capital = state.get('capital', self.initial_capital)
                self.position = state.get('position', self.position)
                self.trades = state.get('trades', [])
                self.equity_history = state.get('equity_history', [])
                self.performance_stats = state.get('performance_stats', self.performance_stats)
                
                # 转换时间字符串
                if self.position.get('entry_time'):
                    self.position['entry_time'] = datetime.fromisoformat(self.position['entry_time'])
                if self.position.get('last_funding_time'):
                    self.position['last_funding_time'] = datetime.fromisoformat(self.position['last_funding_time'])
                
                print(f"📂 加载历史状态: 资金${self.capital:.2f}, 交易{len(self.trades)}笔")
            except Exception as e:
                print(f"❌ 状态加载失败: {str(e)}")
    
    def _save_state(self):
        """保存交易状态"""
        def convert_numpy_types(obj):
            if isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(v) for v in obj]
            else:
                return obj
        
        state = {
            'capital': float(self.capital),
            'position': {
                'size': float(self.position['size']),
                'side': self.position['side'],
                'entry_price': float(self.position['entry_price']),
                'entry_time': self.position['entry_time'].isoformat() if self.position['entry_time'] else None,
                'margin_used': float(self.position['margin_used']),
                'unrealized_pnl': float(self.position['unrealized_pnl']),
                'last_funding_time': self.position['last_funding_time'].isoformat() if self.position['last_funding_time'] else None
            },
            'trades': convert_numpy_types(self.trades),
            'equity_history': convert_numpy_types(self.equity_history),
            'performance_stats': convert_numpy_types(self.performance_stats),
            'config': self.config,
            'last_saved': datetime.now().isoformat()
        }
        
        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
    
    def get_ai_prediction(self, symbol: str = 'BTCUSDT') -> Tuple[float, float]:
        """获取AI预测"""
        try:
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            
            if self.ai_model is None:
                # 使用模拟预测
                base_prob = 0.372
                noise = np.random.normal(0, 0.08)
                probability = np.clip(base_prob + noise, 0.15, 0.85)
                return probability, current_price
            
            # 使用真实AI模型
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                return 0.372, current_price
            
            # 特征工程
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            # 预测
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.ai_model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            return up_probability, current_price
            
        except Exception as e:
            print(f"❌ AI预测失败: {str(e)}")
            return 0.372, 104000.0
    
    def calculate_technical_indicators(self, symbol: str = 'BTCUSDT') -> Dict:
        """计算技术指标"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 50:
                return self._get_default_indicators()
            
            # 计算RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1] if not rsi.empty else 50
            
            # 计算MACD
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            
            # 计算布林带
            sma_20 = df['close'].rolling(20).mean()
            std_20 = df['close'].rolling(20).std()
            upper_band = sma_20 + (std_20 * 2)
            lower_band = sma_20 - (std_20 * 2)
            
            current_price = df['close'].iloc[-1]
            bb_position = (current_price - lower_band.iloc[-1]) / (upper_band.iloc[-1] - lower_band.iloc[-1])
            
            # 计算成交量比率
            volume_sma = df['volume'].rolling(20).mean()
            volume_ratio = df['volume'].iloc[-1] / volume_sma.iloc[-1] if volume_sma.iloc[-1] > 0 else 1
            
            # 计算ATR波动率
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            atr = true_range.rolling(window=14).mean()
            atr_percentage = atr.iloc[-1] / current_price
            
            return {
                'rsi': current_rsi,
                'macd_trend': 'bullish' if macd_line.iloc[-1] > signal_line.iloc[-1] else 'bearish',
                'bb_position': bb_position,
                'volume_ratio': volume_ratio,
                'atr_percentage': atr_percentage,
                'price': current_price
            }
            
        except Exception as e:
            print(f"❌ 技术指标计算失败: {str(e)}")
            return self._get_default_indicators()
    
    def _get_default_indicators(self) -> Dict:
        """获取默认技术指标"""
        return {
            'rsi': 50.0,
            'macd_trend': 'neutral',
            'bb_position': 0.5,
            'volume_ratio': 1.0,
            'atr_percentage': 0.02,
            'price': 104000.0
        }

    def generate_integrated_signal(self, ai_probability: float, indicators: Dict) -> Dict:
        """
        生成集成信号 - 结合AI、技术指标和多时间框架分析
        """
        current_price = indicators['price']

        # 1. 获取多时间框架分析
        try:
            mtf_analysis = self.mtf_analyzer.get_multi_timeframe_analysis('BTCUSDT')
            mtf_signal = mtf_analysis['combined_signal']
            alignment_score = mtf_analysis['alignment_score']
        except Exception as e:
            print(f"⚠️ 多时间框架分析失败: {str(e)}")
            mtf_signal = {'direction': 'neutral', 'strength': 0.5, 'confidence': 0.3}
            alignment_score = 0.5

        # 2. 获取动态风险参数
        recent_performance = {
            'recent_trades': self.trades[-10:] if len(self.trades) >= 10 else self.trades
        }

        risk_params = self.risk_manager.calculate_dynamic_parameters(
            current_equity=self.capital + self.calculate_unrealized_pnl(current_price),
            market_volatility=indicators['atr_percentage'],
            recent_performance=recent_performance
        )

        # 3. 如果风险管理建议停止交易
        if risk_params['action'] == 'STOP_TRADING':
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0,
                'reason': risk_params['reason'],
                'risk_params': risk_params,
                'mtf_analysis': mtf_analysis if 'mtf_analysis' in locals() else None
            }

        # 4. AI信号分析
        ai_strength = abs(ai_probability - 0.5) * 2
        ai_direction = "bullish" if ai_probability > 0.5 else "bearish"

        # 5. 技术指标确认
        confirmations = []
        conflicts = []

        if ai_probability < 0.45:  # AI看跌
            if indicators['rsi'] > 50:
                confirmations.append("RSI支持看跌")
            else:
                conflicts.append("RSI与AI冲突")

            if indicators['macd_trend'] == 'bearish':
                confirmations.append("MACD支持看跌")
            else:
                conflicts.append("MACD与AI冲突")

            if indicators['bb_position'] > 0.6:
                confirmations.append("布林带支持看跌")
            else:
                conflicts.append("布林带位置偏低")

        elif ai_probability > 0.55:  # AI看涨
            if indicators['rsi'] < 50:
                confirmations.append("RSI支持看涨")
            else:
                conflicts.append("RSI与AI冲突")

            if indicators['macd_trend'] == 'bullish':
                confirmations.append("MACD支持看涨")
            else:
                conflicts.append("MACD与AI冲突")

            if indicators['bb_position'] < 0.4:
                confirmations.append("布林带支持看涨")
            else:
                conflicts.append("布林带位置偏高")

        # 成交量确认
        if indicators['volume_ratio'] > 1.2:
            confirmations.append("成交量确认")
        else:
            conflicts.append("成交量不足")

        # 6. 多时间框架确认
        mtf_weight = self.config['mtf_weight_multiplier']
        if mtf_signal['direction'] == ai_direction and alignment_score > 0.6:
            confirmations.append(f"多时间框架强确认({alignment_score:.1%})")
            mtf_boost = mtf_signal['strength'] * mtf_weight
        elif mtf_signal['direction'] == ai_direction:
            confirmations.append(f"多时间框架弱确认({alignment_score:.1%})")
            mtf_boost = mtf_signal['strength'] * mtf_weight * 0.7
        else:
            conflicts.append(f"多时间框架冲突({mtf_signal['direction']})")
            mtf_boost = 0

        # 7. 计算最终信号强度
        confirmation_count = len(confirmations)
        conflict_count = len(conflicts)

        if confirmation_count >= self.config['signal_confirmation_threshold'] and confirmation_count > conflict_count:
            # 强信号
            base_strength = ai_strength
            confirmation_boost = (confirmation_count - conflict_count) * 0.15
            mtf_contribution = mtf_boost * 0.3

            final_strength = min(base_strength + confirmation_boost + mtf_contribution, 1.0)
            final_confidence = min(0.6 + confirmation_boost + mtf_signal['confidence'] * 0.2, 0.9)

            # 应用风险调整
            adjusted_confidence = final_confidence * risk_params.get('confidence_threshold', 0.65) / 0.65

            if adjusted_confidence >= risk_params['confidence_threshold']:
                final_direction = 'LONG' if ai_probability > 0.5 else 'SHORT'
                reason = f"AI{ai_direction}({ai_probability:.1%})+{confirmation_count}个确认"
            else:
                final_direction = 'WAIT'
                final_strength = 0
                reason = f"信号强度不足，需要{risk_params['confidence_threshold']:.1%}置信度"
        else:
            # 弱信号或冲突
            final_direction = 'WAIT'
            final_strength = 0
            final_confidence = 0.5
            reason = f"确认不足({confirmation_count}个)或冲突过多({conflict_count}个)"

        return {
            'direction': final_direction,
            'strength': final_strength,
            'confidence': final_confidence,
            'reason': reason,
            'confirmations': confirmations,
            'conflicts': conflicts,
            'ai_analysis': {
                'probability': ai_probability,
                'direction': ai_direction,
                'strength': ai_strength
            },
            'mtf_analysis': {
                'signal': mtf_signal,
                'alignment_score': alignment_score,
                'boost': mtf_boost
            },
            'risk_params': risk_params,
            'technical_confirmations': confirmation_count,
            'technical_conflicts': conflict_count
        }

    def calculate_unrealized_pnl(self, current_price: float) -> float:
        """计算未实现盈亏"""
        if self.position['size'] == 0:
            return 0.0

        entry_price = self.position['entry_price']

        if self.position['side'] == 'LONG':
            price_diff = current_price - entry_price
        else:  # SHORT
            price_diff = entry_price - current_price

        # 计算盈亏 (考虑杠杆)
        pnl = (price_diff / entry_price) * self.position['margin_used'] * self.leverage

        return pnl

    def calculate_position_size(self, entry_price: float, signal: Dict) -> Optional[Dict]:
        """
        计算仓位大小 - 集成风险管理
        """
        risk_params = signal.get('risk_params', {})
        confidence = signal['confidence']

        # 使用动态风险参数
        position_size_risk = risk_params.get('position_size_risk', 0.02)

        # 可用资金
        available_capital = self.capital * 0.8

        # 风险金额
        risk_amount = self.capital * position_size_risk

        # 计算仓位价值 (使用动态止损)
        stop_loss_pct = risk_params.get('stop_loss', 0.03)
        position_value = risk_amount / stop_loss_pct

        # 应用杠杆
        margin_required = position_value / self.leverage

        # 检查限制
        if margin_required > available_capital:
            margin_required = available_capital
            position_value = margin_required * self.leverage

        # 计算BTC数量
        btc_size = position_value / entry_price

        # 检查最小订单限制
        if position_value < self.contract_rules['min_notional']:
            return None

        if btc_size < self.contract_rules['min_order_size']:
            return None

        return {
            'btc_size': btc_size,
            'position_value': position_value,
            'margin_required': margin_required,
            'risk_amount': risk_amount,
            'stop_loss_pct': stop_loss_pct,
            'take_profit_pct': risk_params.get('take_profit', 0.06),
            'confidence_multiplier': confidence
        }

    def open_position(self, side: str, entry_price: float, size_info: Dict, signal: Dict) -> bool:
        """开仓 - 集成版"""
        if self.position['size'] != 0:
            print(f"❌ 已有持仓，无法开新仓")
            return False

        btc_size = size_info['btc_size']
        margin_required = size_info['margin_required']

        # 计算手续费
        position_value = btc_size * entry_price
        fee = position_value * self.contract_rules['taker_fee']

        # 检查资金充足
        if margin_required + fee > self.capital:
            print(f"❌ 资金不足: 需要${margin_required + fee:.2f}, 可用${self.capital:.2f}")
            return False

        # 扣除手续费
        self.capital -= fee

        # 设置持仓
        self.position = {
            'size': btc_size if side == 'LONG' else -btc_size,
            'side': side,
            'entry_price': entry_price,
            'entry_time': datetime.now(),
            'margin_used': margin_required,
            'unrealized_pnl': 0.0,
            'last_funding_time': datetime.now()
        }

        # 记录交易
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'OPEN',
            'side': side,
            'size': btc_size,
            'price': entry_price,
            'margin': margin_required,
            'fee': fee,
            'reason': signal['reason'],
            'confidence': signal['confidence'],
            'confirmations': len(signal.get('confirmations', [])),
            'conflicts': len(signal.get('conflicts', [])),
            'ai_probability': signal.get('ai_analysis', {}).get('probability', 0.5),
            'mtf_alignment': signal.get('mtf_analysis', {}).get('alignment_score', 0.5),
            'risk_level': signal.get('risk_params', {}).get('risk_level', 'MEDIUM'),
            'capital_after': self.capital
        }

        self.trades.append(trade_record)
        self.performance_stats['total_trades'] += 1

        # 使用增强显示
        print_trade_execution("OPEN", {
            'side': side,
            'size': btc_size,
            'price': entry_price,
            'margin': margin_required,
            'fee': fee,
            'reason': signal['reason'],
            'confidence': signal['confidence']
        })
        print(f"   剩余资金: ${self.capital:.2f}")
        print(f"   确认信号: {len(signal.get('confirmations', []))}个")
        print(f"   冲突信号: {len(signal.get('conflicts', []))}个")
        print(f"   多时间框架一致性: {signal.get('mtf_analysis', {}).get('alignment_score', 0.5):.1%}")

        self._save_state()
        return True

    def close_position(self, current_price: float, reason: str) -> bool:
        """平仓 - 集成版"""
        if self.position['size'] == 0:
            print(f"❌ 无持仓可平")
            return False

        # 计算盈亏
        unrealized_pnl = self.calculate_unrealized_pnl(current_price)

        # 计算手续费
        position_value = abs(self.position['size']) * current_price
        closing_fee = position_value * self.contract_rules['taker_fee']

        # 最终盈亏
        final_pnl = unrealized_pnl - closing_fee

        # 释放保证金并结算盈亏
        self.capital += self.position['margin_used'] + final_pnl

        # 更新统计
        if final_pnl > 0:
            self.performance_stats['winning_trades'] += 1
        else:
            self.performance_stats['losing_trades'] += 1

        self.performance_stats['total_pnl'] += final_pnl

        # 记录交易
        hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600

        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'CLOSE',
            'side': self.position['side'],
            'size': abs(self.position['size']),
            'entry_price': self.position['entry_price'],
            'exit_price': current_price,
            'unrealized_pnl': unrealized_pnl,
            'closing_fee': closing_fee,
            'final_pnl': final_pnl,
            'hold_hours': hold_hours,
            'reason': reason,
            'capital_after': self.capital
        }

        self.trades.append(trade_record)

        # 使用增强显示
        print_trade_execution("CLOSE", {
            'side': self.position['side'],
            'entry_price': self.position['entry_price'],
            'exit_price': current_price,
            'hold_hours': hold_hours,
            'final_pnl': final_pnl,
            'reason': reason
        })
        print(f"   当前资金: ${self.capital:.2f}")

        # 重置持仓
        self.position = {
            'size': 0.0,
            'side': None,
            'entry_price': 0.0,
            'entry_time': None,
            'margin_used': 0.0,
            'unrealized_pnl': 0.0,
            'last_funding_time': None
        }

        self._save_state()
        return True

    def check_stop_loss_take_profit(self, current_price: float, signal: Dict) -> bool:
        """检查止损止盈 - 使用动态参数"""
        if self.position['size'] == 0:
            return False

        entry_price = self.position['entry_price']
        side = self.position['side']

        # 使用动态止损止盈参数
        risk_params = signal.get('risk_params', {})
        stop_loss_pct = risk_params.get('stop_loss', 0.025)
        take_profit_pct = risk_params.get('take_profit', 0.05)

        if side == 'LONG':
            stop_loss_price = entry_price * (1 - stop_loss_pct)
            take_profit_price = entry_price * (1 + take_profit_pct)

            if current_price <= stop_loss_price:
                self.close_position(current_price, f"动态止损触发 ({stop_loss_pct:.1%})")
                return True
            elif current_price >= take_profit_price:
                self.close_position(current_price, f"动态止盈触发 ({take_profit_pct:.1%})")
                return True

        else:  # SHORT
            stop_loss_price = entry_price * (1 + stop_loss_pct)
            take_profit_price = entry_price * (1 - take_profit_pct)

            if current_price >= stop_loss_price:
                self.close_position(current_price, f"动态止损触发 ({stop_loss_pct:.1%})")
                return True
            elif current_price <= take_profit_price:
                self.close_position(current_price, f"动态止盈触发 ({take_profit_pct:.1%})")
                return True

        # 检查最大持仓时间
        if self.position['entry_time']:
            hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            if hold_hours > self.config['max_position_hold_hours']:
                self.close_position(current_price, f"超过最大持仓时间 ({hold_hours:.1f}小时)")
                return True

        return False

    def update_performance_stats(self):
        """更新绩效统计"""
        if self.performance_stats['total_trades'] > 0:
            self.performance_stats['win_rate'] = self.performance_stats['winning_trades'] / self.performance_stats['total_trades']

        # 计算平均盈亏
        winning_trades = [t for t in self.trades if t.get('action') == 'CLOSE' and t.get('final_pnl', 0) > 0]
        losing_trades = [t for t in self.trades if t.get('action') == 'CLOSE' and t.get('final_pnl', 0) < 0]

        if winning_trades:
            self.performance_stats['avg_win'] = sum(t['final_pnl'] for t in winning_trades) / len(winning_trades)

        if losing_trades:
            self.performance_stats['avg_loss'] = sum(t['final_pnl'] for t in losing_trades) / len(losing_trades)

        # 计算盈亏比
        if self.performance_stats['avg_loss'] != 0:
            self.performance_stats['profit_factor'] = abs(self.performance_stats['avg_win'] / self.performance_stats['avg_loss'])

        # 更新最大权益和回撤
        current_equity = self.capital + self.position['unrealized_pnl']
        if current_equity > self.performance_stats['max_equity']:
            self.performance_stats['max_equity'] = current_equity

        drawdown = (self.performance_stats['max_equity'] - current_equity) / self.performance_stats['max_equity']
        if drawdown > self.performance_stats['max_drawdown']:
            self.performance_stats['max_drawdown'] = drawdown

    def run_single_cycle(self) -> bool:
        """运行单次交易循环 - 集成版"""
        try:
            # 1. 获取AI预测
            ai_probability, current_price = self.get_ai_prediction()

            # 2. 计算技术指标
            indicators = self.calculate_technical_indicators()
            indicators['price'] = current_price  # 确保价格一致

            # 3. 生成集成信号 (包含多时间框架和风险管理)
            signal = self.generate_integrated_signal(ai_probability, indicators)

            # 4. 检查止损止盈
            if self.position['size'] != 0:
                if self.check_stop_loss_take_profit(current_price, signal):
                    # 如果触发止损止盈，重新生成信号
                    signal = self.generate_integrated_signal(ai_probability, indicators)

            # 5. 执行交易决策
            if signal['direction'] in ['LONG', 'SHORT'] and self.position['size'] == 0:
                # 检查是否应该交易
                if self.risk_manager.should_trade(signal['confidence'], signal['risk_params']):
                    # 计算仓位大小
                    size_info = self.calculate_position_size(current_price, signal)
                    if size_info:
                        self.open_position(
                            signal['direction'],
                            current_price,
                            size_info,
                            signal
                        )
                else:
                    print(f"🛡️ 风险管理阻止交易: 置信度{signal['confidence']:.1%} < 要求{signal['risk_params']['confidence_threshold']:.1%}")

            # 6. 打印状态 - 使用增强显示
            self.print_integrated_status(ai_probability, indicators, signal)

            # 7. 记录权益历史
            unrealized_pnl = self.calculate_unrealized_pnl(current_price)
            total_equity = self.capital + unrealized_pnl

            self.equity_history.append({
                'timestamp': datetime.now().isoformat(),
                'capital': self.capital,
                'unrealized_pnl': unrealized_pnl,
                'total_equity': total_equity,
                'btc_price': current_price,
                'ai_probability': ai_probability,
                'mtf_alignment': signal['mtf_analysis']['alignment_score'],
                'risk_level': signal['risk_params']['risk_level'],
                'confirmations': len(signal['confirmations']),
                'conflicts': len(signal['conflicts'])
            })

            # 8. 保存状态
            self._save_state()

            return True

        except Exception as e:
            print(f"❌ 交易循环执行失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def print_integrated_status(self, ai_probability: float, indicators: Dict, signal: Dict):
        """打印集成状态 - 增强版显示"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_price = indicators['price']

        print(f"\n⏰ {current_time}")
        print("=" * 120)

        # 账户状态
        unrealized_pnl = self.calculate_unrealized_pnl(current_price)
        total_equity = self.capital + unrealized_pnl
        total_return = (total_equity - self.initial_capital) / self.initial_capital * 100

        print(f"💰 账户: ${self.capital:.2f} + ${unrealized_pnl:+.2f} = ${total_equity:.2f} ({total_return:+.2f}%)")

        # 持仓状态
        if self.position['size'] != 0:
            hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            pnl_pct = (unrealized_pnl / self.position['margin_used']) * 100 if self.position['margin_used'] > 0 else 0
            print(f"📊 持仓: 🔥 {self.position['side']} {abs(self.position['size']):.6f} BTC @ ${self.position['entry_price']:,.0f}")
            print(f"   📈 当前价: ${current_price:,.0f} | 盈亏: ${unrealized_pnl:+.2f} ({pnl_pct:+.1f}%) | 持仓: {hold_hours:.1f}h")
        else:
            print(f"📊 持仓: 💤 空仓 | BTC: ${current_price:,.0f}")

        # AI分析
        ai_strength = abs(ai_probability - 0.5) * 2
        ai_direction = "看涨" if ai_probability > 0.5 else "看跌"
        ai_level = "强" if ai_strength > 0.3 else "弱" if ai_strength > 0.1 else "中性"

        print(f"\n🤖 AI智能分析:")
        print(f"   📊 概率: {ai_probability:.1%}↑ {1-ai_probability:.1%}↓ | {ai_level}{ai_direction} (强度: {ai_strength:.1%})")

        # 多时间框架分析
        mtf_analysis = signal['mtf_analysis']
        mtf_signal = mtf_analysis['signal']
        alignment_score = mtf_analysis['alignment_score']

        print(f"\n🔄 多时间框架分析:")
        print(f"   📈 综合信号: {mtf_signal['direction']} (强度: {mtf_signal['strength']:.1%})")
        print(f"   🎯 一致性得分: {alignment_score:.1%}")
        print(f"   💪 信号增强: +{mtf_analysis['boost']:.2f}")

        # 技术指标详细分析
        print(f"\n📈 技术指标分析:")
        rsi_val = indicators['rsi']
        rsi_status = "🔴 超买" if rsi_val > 70 else "🟢 超卖" if rsi_val < 30 else "🟡 中性"
        print(f"   RSI: {rsi_val:.1f} {rsi_status}")

        macd_trend = indicators['macd_trend']
        macd_emoji = "🟢" if macd_trend == 'bullish' else "🔴"
        print(f"   MACD: {macd_emoji} {macd_trend}")

        bb_pos = indicators['bb_position']
        bb_status = "🔴 接近上轨" if bb_pos > 0.8 else "🟢 接近下轨" if bb_pos < 0.2 else "🟡 中轨区域"
        print(f"   布林带: {bb_pos:.1%} {bb_status}")

        vol_ratio = indicators['volume_ratio']
        vol_status = "🔥 放量" if vol_ratio > 1.5 else "❄️ 缩量" if vol_ratio < 0.7 else "📊 正常"
        print(f"   成交量: {vol_ratio:.1f}倍 {vol_status}")

        print(f"   波动率: {indicators['atr_percentage']:.2%}")

        # 信号确认分析
        print(f"\n🎯 信号确认分析:")
        print(f"   ✅ 确认信号: {len(signal['confirmations'])}个")
        for conf in signal['confirmations']:
            print(f"      • {conf}")

        print(f"   ❌ 冲突信号: {len(signal['conflicts'])}个")
        for conf in signal['conflicts']:
            print(f"      • {conf}")

        # 风险管理状态
        risk_params = signal['risk_params']
        print(f"\n🛡️ 风险管理状态:")
        print(f"   📊 风险等级: {risk_params['risk_level']}")
        print(f"   🎯 置信度要求: {risk_params['confidence_threshold']:.1%}")
        print(f"   📏 动态止损: {risk_params['stop_loss']:.1%}")
        print(f"   🎯 动态止盈: {risk_params['take_profit']:.1%}")
        print(f"   💰 仓位风险: {risk_params['position_size_risk']:.1%}")

        # 最终决策
        print(f"\n🚀 最终交易决策:")
        signal_emoji = {"LONG": "🟢", "SHORT": "🔴", "WAIT": "⏸️"}
        decision_color = signal_emoji.get(signal['direction'], '❓')

        print(f"   {decision_color} 决策: {signal['direction']}")
        print(f"   📊 置信度: {signal['confidence']:.1%} | 信号强度: {signal['strength']:.1%}")
        print(f"   💭 决策理由: {signal['reason']}")

        # 绩效统计
        self.update_performance_stats()
        stats = self.performance_stats
        print(f"\n📊 绩效统计:")
        if stats['total_trades'] > 0:
            print(f"   📈 总交易: {stats['total_trades']}笔 | 胜率: {stats['win_rate']:.1%} | 总盈亏: ${stats['total_pnl']:+.2f}")
            print(f"   🏆 盈利: {stats['winning_trades']}笔 | 💸 亏损: {stats['losing_trades']}笔 | 📉 最大回撤: {stats['max_drawdown']:.1%}")

            if stats['avg_win'] > 0 and stats['avg_loss'] < 0:
                print(f"   💰 平均盈利: ${stats['avg_win']:+.2f} | 平均亏损: ${stats['avg_loss']:+.2f} | 盈亏比: {stats['profit_factor']:.2f}")
        else:
            print(f"   🆕 尚未开始交易，系统正在寻找最佳机会...")

        print("=" * 120)

def run_integrated_trading_system(check_interval: int = 300):
    """
    运行集成版AI增强交易系统
    """
    print("🚀 集成版AI增强交易系统 - 第一阶段")
    print("=" * 100)
    print("系统升级特点:")
    print("• 🧠 AI预测 + 多时间框架分析")
    print("• 🛡️ 动态风险管理 + 回撤控制")
    print("• 📊 多层信号确认 + 冲突检测")
    print("• ⚡ 自适应参数调整")
    print("• 💎 完整的绩效跟踪")
    print("")
    print("⚠️ 这是模拟交易，不涉及真实资金")
    print("🎯 第一阶段：核心集成与优化测试")
    print("")

    # 初始化集成交易系统
    trader = IntegratedTradingSystem(initial_capital=50.0, leverage=2)

    print(f"⏰ 交易循环间隔: {check_interval/60:.1f}分钟")
    print(f"🔄 按 Ctrl+C 停止系统")
    print("")

    cycle_count = 0
    start_time = datetime.now()

    try:
        while True:
            cycle_count += 1
            print(f"\n🔄 第 {cycle_count} 次交易循环")

            # 执行交易循环
            success = trader.run_single_cycle()

            if not success:
                print(f"❌ 交易循环失败，等待下次重试...")

            # 检查是否爆仓
            if trader.capital < 5:  # 资金低于5美元
                print(f"\n💥 资金不足，停止交易")
                print(f"最终资金: ${trader.capital:.2f}")
                break

            # 生成阶段性报告
            if cycle_count % 12 == 0:  # 每12个循环(1小时)生成一次报告
                generate_phase_report(trader, start_time, cycle_count)

            # 等待下次循环
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟后继续...")
            time.sleep(check_interval)

    except KeyboardInterrupt:
        print(f"\n🛑 用户停止交易系统")

    except Exception as e:
        print(f"\n❌ 系统异常: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # 生成最终报告
        generate_final_report(trader, start_time, cycle_count)

def generate_phase_report(trader: IntegratedTradingSystem, start_time: datetime, cycle_count: int):
    """生成阶段性报告"""
    current_time = datetime.now()
    runtime_hours = (current_time - start_time).total_seconds() / 3600

    print(f"\n📊 阶段性报告 (运行{runtime_hours:.1f}小时, {cycle_count}个循环)")
    print("=" * 80)

    # 账户状态
    current_price = trader.fetcher.get_current_price('BTCUSDT', is_futures=True)
    unrealized_pnl = trader.calculate_unrealized_pnl(current_price)
    total_equity = trader.capital + unrealized_pnl
    total_return = (total_equity - trader.initial_capital) / trader.initial_capital * 100

    print(f"💰 账户表现:")
    print(f"   初始资金: ${trader.initial_capital}")
    print(f"   当前权益: ${total_equity:.2f}")
    print(f"   总收益率: {total_return:+.2f}%")
    print(f"   年化收益率: {(total_return / runtime_hours * 24 * 365):+.1f}%")

    # 交易统计
    stats = trader.performance_stats
    print(f"\n📈 交易统计:")
    print(f"   总交易数: {stats['total_trades']}")
    print(f"   胜率: {stats['win_rate']:.1%}")
    print(f"   最大回撤: {stats['max_drawdown']:.1%}")

    if stats['total_trades'] > 0:
        print(f"   平均持仓时间: {np.mean([t.get('hold_hours', 0) for t in trader.trades if t.get('action') == 'CLOSE']):.1f}小时")

    # 风险管理效果
    risk_report = trader.risk_manager.get_risk_report(total_equity, {'recent_trades': trader.trades[-10:]})
    print(f"\n🛡️ 风险管理:")
    print(f"   当前回撤: {risk_report['current_drawdown']:.1%}")
    print(f"   剩余风险容量: {risk_report['risk_capacity_remaining']:.1%}")
    print(f"   交易状态: {risk_report['trading_status']}")

    print("=" * 80)

def generate_final_report(trader: IntegratedTradingSystem, start_time: datetime, cycle_count: int):
    """生成最终报告"""
    end_time = datetime.now()
    runtime_hours = (end_time - start_time).total_seconds() / 3600

    print(f"\n📊 第一阶段最终报告")
    print("=" * 100)
    print(f"运行时间: {runtime_hours:.1f}小时 ({cycle_count}个循环)")

    # 最终账户状态
    try:
        current_price = trader.fetcher.get_current_price('BTCUSDT', is_futures=True)
        unrealized_pnl = trader.calculate_unrealized_pnl(current_price)
        final_equity = trader.capital + unrealized_pnl
        total_return = (final_equity - trader.initial_capital) / trader.initial_capital * 100

        print(f"\n💰 最终账户状态:")
        print(f"   初始资金: ${trader.initial_capital}")
        print(f"   最终权益: ${final_equity:.2f}")
        print(f"   总收益率: {total_return:+.2f}%")
        print(f"   年化收益率: {(total_return / runtime_hours * 24 * 365):+.1f}%")

        # 基准比较
        if len(trader.equity_history) > 1:
            initial_btc_price = trader.equity_history[0]['btc_price']
            final_btc_price = current_price
            btc_return = (final_btc_price - initial_btc_price) / initial_btc_price * 100

            print(f"   BTC涨跌: {btc_return:+.2f}%")
            print(f"   超额收益: {total_return - btc_return:+.2f}%")

    except Exception as e:
        print(f"❌ 最终状态计算失败: {str(e)}")

    # 详细交易统计
    stats = trader.performance_stats
    print(f"\n📈 详细交易统计:")
    print(f"   总交易数: {stats['total_trades']}")
    print(f"   胜率: {stats['win_rate']:.1%}")
    print(f"   盈利交易: {stats['winning_trades']}")
    print(f"   亏损交易: {stats['losing_trades']}")
    print(f"   总盈亏: ${stats['total_pnl']:+.2f}")
    print(f"   最大回撤: {stats['max_drawdown']:.1%}")

    if stats['avg_win'] > 0 and stats['avg_loss'] < 0:
        print(f"   平均盈利: ${stats['avg_win']:+.2f}")
        print(f"   平均亏损: ${stats['avg_loss']:+.2f}")
        print(f"   盈亏比: {stats['profit_factor']:.2f}")

    # 系统性能分析
    print(f"\n🔧 系统性能分析:")

    # 信号质量分析
    open_trades = [t for t in trader.trades if t.get('action') == 'OPEN']
    if open_trades:
        avg_confidence = np.mean([t.get('confidence', 0) for t in open_trades])
        avg_confirmations = np.mean([t.get('confirmations', 0) for t in open_trades])
        avg_mtf_alignment = np.mean([t.get('mtf_alignment', 0) for t in open_trades])

        print(f"   平均信号置信度: {avg_confidence:.1%}")
        print(f"   平均确认信号数: {avg_confirmations:.1f}")
        print(f"   平均多时间框架一致性: {avg_mtf_alignment:.1%}")

    # 风险管理效果
    try:
        final_equity_calc = trader.capital + trader.calculate_unrealized_pnl(current_price)
        risk_report = trader.risk_manager.get_risk_report(final_equity_calc, {'recent_trades': trader.trades[-10:]})

        print(f"\n🛡️ 风险管理效果:")
        print(f"   最终回撤: {risk_report['current_drawdown']:.1%}")
        print(f"   风险控制状态: {risk_report['trading_status']}")

        for recommendation in risk_report['recommendations']:
            print(f"   • {recommendation}")

    except Exception as e:
        print(f"❌ 风险报告生成失败: {str(e)}")

    # 第一阶段总结
    print(f"\n🎯 第一阶段总结:")
    print(f"✅ 核心模块集成: 增强风险管理 + 多时间框架分析")
    print(f"✅ 动态参数调整: 根据市场状态自适应")
    print(f"✅ 多层信号确认: AI + 技术指标 + 多时间框架")
    print(f"✅ 完整状态保存: 支持中断恢复")

    if total_return > 0:
        print(f"🎉 第一阶段测试成功！系统实现盈利")
    elif total_return > -5:
        print(f"📈 第一阶段测试良好，小幅亏损在可接受范围")
    else:
        print(f"🔧 第一阶段需要进一步优化")

    print(f"\n💾 交易记录已保存到: {trader.state_file}")
    print(f"🔄 可重新运行继续测试或进入第二阶段开发")
    print("=" * 100)

if __name__ == "__main__":
    import sys

    # 解析命令行参数
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300  # 默认5分钟

    print("🎯 集成版AI增强交易系统 - 第一阶段测试")
    print("=" * 80)
    print("第一阶段目标:")
    print("1. ✅ 增强模块集成")
    print("2. ✅ 动态参数优化")
    print("3. ✅ 实时监控显示")
    print("4. 📊 性能数据收集")
    print("")

    # 启动集成系统
    run_integrated_trading_system(interval)
