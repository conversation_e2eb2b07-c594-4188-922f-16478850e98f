#!/usr/bin/env python3
"""
币种自适应AI模型
根据不同币种的特性自动调整模型参数和特征处理
"""

import pandas as pd
import numpy as np
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CoinProfile:
    """币种特性配置"""
    
    def __init__(self, symbol: str):
        self.symbol = symbol
        self.price_range = None
        self.volatility_profile = None
        self.volume_profile = None
        self.scalping_params = None
        self.feature_weights = None
        
    def analyze_coin_characteristics(self) -> Dict:
        """分析币种特性"""
        try:
            # 获取24小时数据
            url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
            params = {'symbol': self.symbol}
            
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            
            data = response.json()
            
            # 获取历史K线数据
            klines_url = "https://fapi.binance.com/fapi/v1/klines"
            klines_params = {
                'symbol': self.symbol,
                'interval': '1m',
                'limit': 1000
            }
            
            klines_response = requests.get(klines_url, params=klines_params, timeout=10)
            klines_response.raise_for_status()
            
            klines_data = klines_response.json()
            
            # 分析价格特性
            prices = [float(k[4]) for k in klines_data]  # 收盘价
            volumes = [float(k[5]) for k in klines_data]  # 成交量
            
            current_price = float(data['lastPrice'])
            price_change_24h = float(data['priceChangePercent'])
            volume_24h = float(data['volume'])
            
            # 计算波动率特性
            returns = pd.Series(prices).pct_change().dropna()
            volatility_1m = returns.std()
            volatility_5m = returns.rolling(5).std().mean()
            volatility_hourly = returns.rolling(60).std().mean()
            
            # 计算价格范围特性
            price_levels = {
                'current': current_price,
                'min_24h': float(data['lowPrice']),
                'max_24h': float(data['highPrice']),
                'avg_price': np.mean(prices),
                'price_decimals': len(str(current_price).split('.')[-1]) if '.' in str(current_price) else 0
            }
            
            # 计算成交量特性
            volume_profile = {
                'avg_volume': np.mean(volumes),
                'volume_volatility': np.std(volumes) / np.mean(volumes),
                'volume_24h': volume_24h
            }
            
            # 计算波动频率
            significant_moves = abs(returns) > (volatility_1m * 1.5)
            move_frequency = significant_moves.sum() / len(returns)
            
            # 币种分类
            if current_price > 1000:
                coin_category = "large_cap"  # BTC, ETH等
            elif current_price > 10:
                coin_category = "mid_cap"    # BNB, SOL等
            elif current_price > 0.1:
                coin_category = "small_cap"  # ADA, XRP等
            else:
                coin_category = "micro_cap"  # DOGE, SHIB等
            
            characteristics = {
                'symbol': self.symbol,
                'category': coin_category,
                'price_levels': price_levels,
                'volatility': {
                    '1m': volatility_1m,
                    '5m': volatility_5m,
                    'hourly': volatility_hourly,
                    'move_frequency': move_frequency
                },
                'volume_profile': volume_profile,
                'scalping_score': self._calculate_scalping_score(returns, volumes),
                'analysis_time': datetime.now().isoformat()
            }
            
            return characteristics
            
        except Exception as e:
            logger.error(f"分析{self.symbol}特性失败: {e}")
            return {}
    
    def _calculate_scalping_score(self, returns: pd.Series, volumes: List[float]) -> float:
        """计算剥头皮适合度评分"""
        # 1分钟平均波动
        avg_move = abs(returns).mean()
        
        # 有效波动频率
        significant_moves = abs(returns) > 0.001
        move_freq = significant_moves.sum() / len(returns)
        
        # 成交量稳定性
        volume_cv = np.std(volumes) / np.mean(volumes) if np.mean(volumes) > 0 else 1
        volume_stability = 1 / (1 + volume_cv)
        
        # 综合评分
        score = (
            avg_move * 1000 +      # 波动幅度
            move_freq * 50 +       # 波动频率
            volume_stability * 20  # 流动性稳定性
        )
        
        return score

class AdaptiveAIModel:
    """自适应AI模型"""
    
    def __init__(self, symbol: str):
        self.symbol = symbol
        self.coin_profile = CoinProfile(symbol)
        self.characteristics = {}
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_importance = {}
        
        # 基础AI参数
        self.base_accuracy = 0.836
        self.min_confidence = 0.70
        
    def initialize_for_symbol(self):
        """为特定币种初始化模型"""
        logger.info(f"🔧 为 {self.symbol} 初始化自适应AI模型...")
        
        # 分析币种特性
        self.characteristics = self.coin_profile.analyze_coin_characteristics()
        
        if not self.characteristics:
            logger.error(f"无法获取{self.symbol}特性")
            return False
        
        # 根据币种特性调整参数
        self._adapt_parameters()
        
        # 获取训练数据
        training_data = self._get_training_data()
        
        if training_data is not None and len(training_data) > 100:
            # 训练模型
            self._train_adaptive_model(training_data)
        else:
            logger.warning(f"训练数据不足，使用规则基础模型")
            self._setup_rule_based_model()
        
        logger.info(f"✅ {self.symbol} 自适应模型初始化完成")
        return True
    
    def _adapt_parameters(self):
        """根据币种特性调整参数"""
        char = self.characteristics
        category = char.get('category', 'small_cap')
        volatility_1m = char.get('volatility', {}).get('1m', 0.002)
        scalping_score = char.get('scalping_score', 20)
        
        # 根据币种类别调整参数
        if category == "large_cap":  # BTC, ETH
            self.min_confidence = 0.75
            self.volatility_threshold = volatility_1m * 2
            self.position_risk = 0.01  # 1%风险
            self.stop_loss_ratio = 0.008  # 0.8%
            self.take_profit_ratio = 0.015  # 1.5%
            
        elif category == "mid_cap":  # BNB, SOL
            self.min_confidence = 0.72
            self.volatility_threshold = volatility_1m * 1.5
            self.position_risk = 0.015  # 1.5%风险
            self.stop_loss_ratio = 0.006  # 0.6%
            self.take_profit_ratio = 0.012  # 1.2%
            
        elif category == "small_cap":  # ADA, XRP
            self.min_confidence = 0.55  # 降低到55%，更容易交易
            self.volatility_threshold = volatility_1m * 1.2
            self.position_risk = 0.02  # 2%风险
            self.stop_loss_ratio = 0.004  # 0.4%
            self.take_profit_ratio = 0.008  # 0.8%
            
        else:  # micro_cap - DOGE, SHIB
            self.min_confidence = 0.65
            self.volatility_threshold = volatility_1m * 1.0
            self.position_risk = 0.025  # 2.5%风险
            self.stop_loss_ratio = 0.003  # 0.3%
            self.take_profit_ratio = 0.006  # 0.6%
        
        # 根据剥头皮评分调整
        if scalping_score > 30:  # 极佳剥头皮标的
            self.min_confidence -= 0.03
            self.position_risk *= 1.2
        elif scalping_score < 15:  # 不适合剥头皮
            self.min_confidence += 0.05
            self.position_risk *= 0.8
        
        logger.info(f"📊 {self.symbol} 参数调整:")
        logger.info(f"   类别: {category}")
        logger.info(f"   置信度门槛: {self.min_confidence:.1%}")
        logger.info(f"   仓位风险: {self.position_risk:.1%}")
        logger.info(f"   止损/止盈: {self.stop_loss_ratio:.1%}/{self.take_profit_ratio:.1%}")
    
    def _get_training_data(self) -> Optional[pd.DataFrame]:
        """获取训练数据"""
        try:
            logger.info(f"📥 获取 {self.symbol} 训练数据...")
            
            url = "https://fapi.binance.com/fapi/v1/klines"
            params = {
                'symbol': self.symbol,
                'interval': '1m',
                'limit': 1000  # 获取1000条1分钟数据
            }
            
            response = requests.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades_count',
                'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
            ])
            
            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            logger.info(f"✅ 获取 {len(df)} 条 {self.symbol} 训练数据")
            return df[['open', 'high', 'low', 'close', 'volume']]
            
        except Exception as e:
            logger.error(f"获取{self.symbol}训练数据失败: {e}")
            return None
    
    def _create_adaptive_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建自适应特征"""
        features = pd.DataFrame(index=df.index)
        
        try:
            # 根据币种特性调整特征计算
            volatility_1m = self.characteristics.get('volatility', {}).get('1m', 0.002)
            price_decimals = self.characteristics.get('price_levels', {}).get('price_decimals', 4)
            
            # 1. 价格变化特征 (根据币种波动率调整)
            for period in [1, 2, 3, 5, 10]:
                features[f'price_change_{period}'] = df['close'].pct_change(period)
                # 标准化到币种特定的波动率
                features[f'price_change_norm_{period}'] = features[f'price_change_{period}'] / volatility_1m
            
            # 2. 移动平均特征 (适应币种价格范围)
            for period in [3, 5, 8, 13, 21]:
                ma = df['close'].rolling(period).mean()
                features[f'ma_{period}'] = ma
                features[f'price_ma_ratio_{period}'] = df['close'] / ma
                features[f'ma_slope_{period}'] = ma.pct_change()
            
            # 3. 波动率特征 (币种特定)
            returns = df['close'].pct_change()
            for window in [5, 10, 20]:
                vol = returns.rolling(window).std()
                features[f'volatility_{window}'] = vol
                features[f'vol_ratio_{window}'] = vol / volatility_1m
            
            # 4. 成交量特征
            for window in [5, 10, 20]:
                vol_ma = df['volume'].rolling(window).mean()
                features[f'volume_ma_{window}'] = vol_ma
                features[f'volume_ratio_{window}'] = df['volume'] / vol_ma
            
            # 5. 技术指标 (适应币种)
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            features['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=9).mean()
            features['macd'] = macd
            features['macd_signal'] = signal
            features['macd_histogram'] = macd - signal
            
            # 6. 价格位置特征
            for window in [10, 20]:
                high_max = df['high'].rolling(window).max()
                low_min = df['low'].rolling(window).min()
                features[f'price_position_{window}'] = (df['close'] - low_min) / (high_max - low_min)
            
            # 7. 币种特定特征
            features['buy_pressure'] = (df['close'] - df['low']) / (df['high'] - df['low'])
            features['hl_ratio'] = (df['high'] - df['low']) / df['close']
            features['oc_ratio'] = (df['close'] - df['open']) / df['open']
            
        except Exception as e:
            logger.error(f"创建{self.symbol}特征失败: {e}")
        
        return features.fillna(0)
    
    def _create_adaptive_labels(self, df: pd.DataFrame) -> pd.Series:
        """创建自适应标签"""
        # 根据币种特性调整预测窗口和阈值
        volatility_1m = self.characteristics.get('volatility', {}).get('1m', 0.002)
        category = self.characteristics.get('category', 'small_cap')
        
        # 根据币种类别调整参数
        if category == "large_cap":
            future_periods = 3
            threshold = volatility_1m * 1.5
        elif category == "mid_cap":
            future_periods = 2
            threshold = volatility_1m * 1.2
        else:  # small_cap, micro_cap
            future_periods = 2
            threshold = volatility_1m * 1.0
        
        # 计算未来收益率
        future_returns = df['close'].pct_change(future_periods).shift(-future_periods)
        
        # 创建标签
        labels = pd.Series(index=df.index, dtype=int)
        labels[future_returns < -threshold] = 0  # 下跌
        labels[(future_returns >= -threshold) & (future_returns <= threshold)] = 1  # 横盘
        labels[future_returns > threshold] = 2  # 上涨
        
        logger.info(f"📊 {self.symbol} 标签分布:")
        label_counts = labels.value_counts().sort_index()
        label_names = {0: '下跌', 1: '横盘', 2: '上涨'}
        for label, count in label_counts.items():
            if not pd.isna(label):
                label_name = label_names.get(int(label), '未知')
                logger.info(f"   {label_name}: {count} ({count/len(labels)*100:.1f}%)")
        
        return labels
    
    def _train_adaptive_model(self, df: pd.DataFrame):
        """训练自适应模型"""
        logger.info(f"🤖 训练 {self.symbol} 自适应AI模型...")
        
        # 创建特征
        features = self._create_adaptive_features(df)
        
        # 创建标签
        labels = self._create_adaptive_labels(df)
        
        # 对齐数据
        common_index = features.index.intersection(labels.index)
        X = features.loc[common_index]
        y = labels.loc[common_index]
        
        # 移除缺失值
        valid_mask = ~(X.isnull().any(axis=1) | y.isnull())
        X = X[valid_mask]
        y = y[valid_mask]
        
        if len(X) < 50:
            logger.warning(f"{self.symbol} 有效训练数据不足")
            return
        
        # 特征缩放
        X_scaled = self.scaler.fit_transform(X)
        
        # 时间序列分割训练
        tscv = TimeSeriesSplit(n_splits=3)
        scores = []
        
        for train_idx, val_idx in tscv.split(X_scaled):
            X_train, X_val = X_scaled[train_idx], X_scaled[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # 训练随机森林
            model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                random_state=42
            )
            
            model.fit(X_train, y_train)
            
            # 验证
            y_pred = model.predict(X_val)
            score = accuracy_score(y_val, y_pred)
            scores.append(score)
        
        # 使用全部数据训练最终模型
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            random_state=42
        )
        
        self.model.fit(X_scaled, y)
        
        # 计算特征重要性
        self.feature_importance = dict(zip(X.columns, self.model.feature_importances_))
        
        avg_score = np.mean(scores)
        self.is_trained = True
        
        logger.info(f"✅ {self.symbol} 模型训练完成")
        logger.info(f"   交叉验证准确率: {avg_score:.1%}")
        logger.info(f"   训练样本数: {len(X)}")
        
        # 显示重要特征
        top_features = sorted(self.feature_importance.items(), key=lambda x: x[1], reverse=True)[:5]
        logger.info(f"   重要特征: {', '.join([f'{name}({imp:.3f})' for name, imp in top_features])}")
    
    def _setup_rule_based_model(self):
        """设置基于规则的模型"""
        logger.info(f"⚙️ 为 {self.symbol} 设置规则基础模型")
        self.is_trained = True  # 标记为已训练
    
    def predict(self, current_data: pd.DataFrame) -> Tuple[str, float]:
        """预测价格方向"""
        if not self.is_trained:
            return "HOLD", 0.0
        
        try:
            # 创建特征
            features = self._create_adaptive_features(current_data)
            
            if len(features) == 0:
                return "HOLD", 0.0
            
            # 获取最新特征
            latest_features = features.iloc[[-1]].fillna(0)
            
            if self.model is not None:
                # 使用训练好的模型
                X_scaled = self.scaler.transform(latest_features)
                prediction = self.model.predict(X_scaled)[0]
                probabilities = self.model.predict_proba(X_scaled)[0]
                confidence = np.max(probabilities)
            else:
                # 使用规则基础模型
                prediction, confidence = self._rule_based_prediction(latest_features.iloc[0])
            
            # 转换预测结果
            if prediction == 2:
                direction = "LONG"
            elif prediction == 0:
                direction = "SHORT"
            else:
                direction = "HOLD"
            
            # 应用币种特定的置信度调整
            if confidence < self.min_confidence:
                direction = "HOLD"
            
            return direction, confidence
            
        except Exception as e:
            logger.error(f"{self.symbol} 预测失败: {e}")
            return "HOLD", 0.0
    
    def _rule_based_prediction(self, features: pd.Series) -> Tuple[int, float]:
        """基于规则的预测"""
        score = 0
        confidence_factors = []
        
        # 价格动量
        if features.get('price_change_norm_2', 0) > 1.5:
            score += 2
            confidence_factors.append(0.15)
        elif features.get('price_change_norm_2', 0) < -1.5:
            score -= 2
            confidence_factors.append(0.15)
        
        # 移动平均
        if features.get('price_ma_ratio_5', 1) > 1.002:
            score += 1
            confidence_factors.append(0.10)
        elif features.get('price_ma_ratio_5', 1) < 0.998:
            score -= 1
            confidence_factors.append(0.10)
        
        # RSI
        rsi = features.get('rsi', 50)
        if rsi < 35:
            score += 1
            confidence_factors.append(0.08)
        elif rsi > 65:
            score -= 1
            confidence_factors.append(0.08)
        
        # 成交量
        if features.get('volume_ratio_10', 1) > 1.5:
            confidence_factors.append(0.05)
        
        # 转换为预测
        if score >= 2:
            prediction = 2  # 上涨
        elif score <= -2:
            prediction = 0  # 下跌
        else:
            prediction = 1  # 横盘
        
        # 计算置信度
        base_confidence = 0.60
        confidence_boost = sum(confidence_factors)
        final_confidence = min(0.85, base_confidence + confidence_boost)
        
        return prediction, final_confidence
    
    def get_trading_params(self) -> Dict:
        """获取交易参数"""
        return {
            'symbol': self.symbol,
            'min_confidence': self.min_confidence,
            'position_risk': self.position_risk,
            'stop_loss_ratio': self.stop_loss_ratio,
            'take_profit_ratio': self.take_profit_ratio,
            'volatility_threshold': self.volatility_threshold,
            'characteristics': self.characteristics
        }

if __name__ == "__main__":
    print("🤖 币种自适应AI模型测试")
    
    # 测试不同币种
    test_symbols = ['ADAUSDT', 'BTCUSDT', 'DOGEUSDT']
    
    for symbol in test_symbols:
        print(f"\n{'='*50}")
        print(f"🔧 测试 {symbol}")
        
        # 创建自适应模型
        adaptive_model = AdaptiveAIModel(symbol)
        
        # 初始化
        if adaptive_model.initialize_for_symbol():
            # 获取交易参数
            params = adaptive_model.get_trading_params()
            
            print(f"✅ {symbol} 自适应参数:")
            print(f"   置信度门槛: {params['min_confidence']:.1%}")
            print(f"   仓位风险: {params['position_risk']:.1%}")
            print(f"   止损/止盈: {params['stop_loss_ratio']:.1%}/{params['take_profit_ratio']:.1%}")
            
            # 测试预测
            try:
                current_data = adaptive_model._get_training_data()
                if current_data is not None:
                    direction, confidence = adaptive_model.predict(current_data.tail(60))
                    print(f"   当前预测: {direction} (置信度: {confidence:.1%})")
            except Exception as e:
                print(f"   预测测试失败: {e}")
        else:
            print(f"❌ {symbol} 初始化失败")
    
    print(f"\n🎉 自适应AI模型测试完成！")
