#!/usr/bin/env python3
"""
Test script to demonstrate the new account reset functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader
from datetime import datetime

def test_reset_functionality():
    """Test the account reset functionality"""
    print("🧪 Testing Account Reset Functionality")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n1️⃣ Initial State:")
    trader._print_enhanced_account_status()
    
    print("\n2️⃣ Simulating Inconsistent Data (Like Your Scenario):")
    # Simulate the problematic state
    trader.account['balance'] = 62.81
    trader.account['equity'] = 62.81
    trader.trade_history.append({
        'action': 'CLOSE',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'side': 'LONG',
        'price': 102000,
        'net_pnl': -2.01,
        'roi_percent': -12.9,
        'hold_time': 0.007,
        'reason': '止损'
    })
    
    print("📊 Problematic State (Before Reset):")
    trader._print_enhanced_account_status()
    trader._print_enhanced_trading_statistics()
    
    print("\n3️⃣ Applying Reset Function:")
    trader.reset_account_data()
    
    print("\n4️⃣ After Reset (Clean State):")
    trader._print_enhanced_account_status()
    trader._print_enhanced_trading_statistics()
    
    print("\n" + "="*60)
    print("🎯 Reset Function Benefits:")
    print("✅ Clears all inconsistent data")
    print("✅ Resets balance to initial amount")
    print("✅ Clears trade history")
    print("✅ Resets position to empty")
    print("✅ Provides clean starting point")
    print("\n💡 Use this when you encounter data inconsistencies!")

if __name__ == "__main__":
    test_reset_functionality()
