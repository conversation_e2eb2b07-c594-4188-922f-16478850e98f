#!/usr/bin/env python3
"""
实时剥头皮交易系统
开仓后每秒监控价格变化，真正的高频剥头皮
"""

import pandas as pd
import numpy as np
import logging
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import threading
import queue

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealTimeAPI:
    """实时API客户端"""
    
    def __init__(self):
        self.session = requests.Session()
        
        # 快速重试策略
        retry_strategy = Retry(
            total=2,  # 减少重试次数
            backoff_factor=0.1,  # 更快重试
            status_forcelist=[429, 500, 502, 503, 504]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=5, pool_maxsize=10)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 价格缓存
        self.last_price = None
        self.last_price_time = None
        
    def get_price_fast(self, symbol: str = "ADAUSDT") -> Optional[float]:
        """快速获取价格"""
        try:
            url = "https://fapi.binance.com/fapi/v1/ticker/price"
            params = {'symbol': symbol}
            
            response = self.session.get(url, params=params, timeout=1.5)  # 1.5秒超时
            response.raise_for_status()
            
            data = response.json()
            price = float(data['price'])
            
            self.last_price = price
            self.last_price_time = datetime.now()
            
            return price
            
        except Exception as e:
            # 如果获取失败，返回缓存价格
            if self.last_price and self.last_price_time:
                age = (datetime.now() - self.last_price_time).total_seconds()
                if age < 10:  # 10秒内的缓存可用
                    return self.last_price
            return None

class RealTimeScalping:
    """实时剥头皮交易系统"""
    
    def __init__(self, initial_balance: float = 50.0):
        self.symbol = "ADAUSDT"
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.leverage = 125.0
        
        # 方案四：极速微利剥头皮参数 (真正的剥头皮)
        self.position_risk = 0.025  # 2.5%风险
        self.stop_loss_ratio = 0.0004  # 0.04%止损 (5%杠杆亏损)
        self.take_profit_ratio = 0.0005  # 0.05%止盈 (6.25%杠杆盈利)
        self.min_confidence = 0.65  # 65%置信度
        
        # API客户端
        self.api = RealTimeAPI()
        
        # 交易状态
        self.current_position = None
        self.last_trade_time = None
        self.total_trades = 0
        self.winning_trades = 0
        self.trade_history = []
        
        # 实时监控
        self.is_running = False
        self.price_monitor_thread = None
        self.price_queue = queue.Queue()
        
        # 性能统计
        self.quick_profits = 0
        self.avg_holding_time = 0
        
    def start_price_monitor(self):
        """启动价格实时监控线程"""
        def price_monitor():
            while self.is_running:
                try:
                    price = self.api.get_price_fast(self.symbol)
                    if price:
                        self.price_queue.put({
                            'timestamp': datetime.now(),
                            'price': price
                        })
                    
                    # 如果有持仓，每秒检查
                    if self.current_position:
                        time.sleep(1)  # 1秒间隔
                    else:
                        time.sleep(3)  # 无持仓时3秒间隔
                        
                except Exception as e:
                    logger.error(f"价格监控错误: {e}")
                    time.sleep(2)
        
        self.price_monitor_thread = threading.Thread(target=price_monitor, daemon=True)
        self.price_monitor_thread.start()
        logger.info("✅ 实时价格监控已启动")
    
    def get_latest_price(self) -> Optional[float]:
        """获取最新价格"""
        try:
            # 从队列获取最新价格
            latest_price = None
            while not self.price_queue.empty():
                price_data = self.price_queue.get_nowait()
                latest_price = price_data['price']
            
            return latest_price if latest_price else self.api.get_price_fast(self.symbol)
            
        except Exception:
            return self.api.get_price_fast(self.symbol)
    
    def calculate_simple_signal(self) -> Tuple[str, float]:
        """简化的交易信号计算"""
        try:
            # 获取简单K线数据
            url = "https://fapi.binance.com/fapi/v1/klines"
            params = {
                'symbol': self.symbol,
                'interval': '1m',
                'limit': 20
            }
            
            response = self.api.session.get(url, params=params, timeout=3)
            response.raise_for_status()
            
            data = response.json()
            
            if len(data) < 10:
                return "HOLD", 0.0
            
            # 转换价格数据
            closes = [float(k[4]) for k in data]
            
            # 简单信号计算
            current_price = closes[-1]
            prev_price_1 = closes[-2]
            prev_price_3 = closes[-4] if len(closes) >= 4 else closes[-2]
            prev_price_5 = closes[-6] if len(closes) >= 6 else closes[-3]
            
            # 计算变化率
            change_1 = (current_price - prev_price_1) / prev_price_1
            change_3 = (current_price - prev_price_3) / prev_price_3
            change_5 = (current_price - prev_price_5) / prev_price_5
            
            # 简单移动平均
            ma_5 = np.mean(closes[-5:])
            ma_10 = np.mean(closes[-10:])
            
            # 评分系统
            score = 0
            confidence_factors = []
            
            # 1分钟动量 (最重要) - 调整为适合0.05%目标
            if change_1 > 0.0002:  # 0.02%上涨 (极敏感)
                score += 3
                confidence_factors.append(0.25)
            elif change_1 < -0.0002:  # 0.02%下跌
                score -= 3
                confidence_factors.append(0.25)

            # 3分钟趋势 - 进一步降低阈值
            if change_3 > 0.0003:  # 0.03%上涨
                score += 2
                confidence_factors.append(0.15)
            elif change_3 < -0.0003:  # 0.03%下跌
                score -= 2
                confidence_factors.append(0.15)
            
            # 移动平均 - 超极敏感阈值
            if current_price > ma_5 * 1.0001:  # 0.01%
                score += 1
                confidence_factors.append(0.10)
            elif current_price < ma_5 * 0.9999:  # 0.01%
                score -= 1
                confidence_factors.append(0.10)
            
            # 决策
            if score >= 3:
                direction = "LONG"
                base_confidence = 0.70
            elif score <= -3:
                direction = "SHORT"
                base_confidence = 0.70
            else:
                direction = "HOLD"
                base_confidence = 0.5
            
            # 计算最终置信度
            if direction != "HOLD":
                confidence_boost = sum(confidence_factors)
                final_confidence = min(0.90, base_confidence + confidence_boost)
            else:
                final_confidence = base_confidence
            
            return direction, final_confidence
            
        except Exception as e:
            logger.error(f"信号计算失败: {e}")
            return "HOLD", 0.0
    
    def can_trade(self) -> bool:
        """检查是否可以交易"""
        # 检查是否有持仓
        if self.current_position:
            return False
        
        # 检查交易间隔 (30秒)
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < 30:
                return False
        
        # 检查余额
        if self.current_balance < self.initial_balance * 0.3:
            return False
        
        return True
    
    def execute_trade(self, direction: str, confidence: float, entry_price: float):
        """执行交易"""
        # 计算仓位
        risk_amount = self.current_balance * self.position_risk
        position_value = risk_amount * self.leverage
        position_size = position_value / entry_price
        
        # 计算止损止盈
        if direction == "LONG":
            stop_loss = entry_price * (1 - self.stop_loss_ratio)
            take_profit = entry_price * (1 + self.take_profit_ratio)
        else:
            stop_loss = entry_price * (1 + self.stop_loss_ratio)
            take_profit = entry_price * (1 - self.take_profit_ratio)
        
        # 创建持仓
        self.current_position = {
            'direction': direction,
            'entry_price': entry_price,
            'entry_time': datetime.now(),
            'position_size': position_size,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'confidence': confidence
        }
        
        self.total_trades += 1
        self.last_trade_time = datetime.now()
        
        logger.info(f"🚀 开仓: {direction} @ {entry_price:.4f}")
        logger.info(f"   置信度: {confidence:.1%}")
        logger.info(f"   止损: {stop_loss:.4f} (-{self.stop_loss_ratio:.2%})")
        logger.info(f"   止盈: {take_profit:.4f} (+{self.take_profit_ratio:.2%})")
        logger.info(f"   开始实时监控...")
    
    def check_exit_realtime(self, current_price: float) -> bool:
        """实时检查退出条件"""
        if not self.current_position:
            return False
        
        pos = self.current_position
        should_exit = False
        exit_reason = ""
        
        # 检查止损止盈
        if pos['direction'] == "LONG":
            if current_price <= pos['stop_loss']:
                should_exit = True
                exit_reason = "止损"
            elif current_price >= pos['take_profit']:
                should_exit = True
                exit_reason = "止盈"
        else:
            if current_price >= pos['stop_loss']:
                should_exit = True
                exit_reason = "止损"
            elif current_price <= pos['take_profit']:
                should_exit = True
                exit_reason = "止盈"
        
        # 检查时间退出 (2分钟)
        duration = (datetime.now() - pos['entry_time']).total_seconds()
        if duration > 120:
            should_exit = True
            exit_reason = "时间退出"
        
        if should_exit:
            self.close_position(current_price, exit_reason)
            return True
        
        return False
    
    def close_position(self, exit_price: float, exit_reason: str):
        """平仓"""
        if not self.current_position:
            return
        
        pos = self.current_position
        
        # 计算盈亏
        if pos['direction'] == "LONG":
            pnl_pct = (exit_price - pos['entry_price']) / pos['entry_price']
        else:
            pnl_pct = (pos['entry_price'] - exit_price) / pos['entry_price']
        
        leveraged_pnl = pnl_pct * self.leverage
        position_value = pos['position_size'] * pos['entry_price']
        margin_used = position_value / self.leverage
        pnl_amount = margin_used * leveraged_pnl
        
        # 更新余额
        self.current_balance += pnl_amount
        
        # 计算持仓时间
        holding_time = (datetime.now() - pos['entry_time']).total_seconds()
        
        # 更新统计
        is_winner = pnl_amount > 0
        if is_winner:
            self.winning_trades += 1
            if holding_time < 60:  # 1分钟内盈利
                self.quick_profits += 1
        
        # 更新平均持仓时间
        self.avg_holding_time = (
            (self.avg_holding_time * (self.total_trades - 1) + holding_time) / self.total_trades
        )
        
        # 记录交易
        trade_record = {
            'trade_id': self.total_trades,
            'entry_time': pos['entry_time'],
            'exit_time': datetime.now(),
            'direction': pos['direction'],
            'entry_price': pos['entry_price'],
            'exit_price': exit_price,
            'pnl_amount': pnl_amount,
            'holding_time': holding_time,
            'is_winner': is_winner,
            'exit_reason': exit_reason,
            'confidence': pos['confidence']
        }
        
        self.trade_history.append(trade_record)
        self.current_position = None
        
        # 显示结果
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        
        status = "✅ 盈利" if is_winner else "❌ 亏损"
        logger.info(f"📈 平仓: {pos['direction']} @ {exit_price:.4f} ({exit_reason})")
        logger.info(f"   {status} 盈亏: ${pnl_amount:+.2f} ({leveraged_pnl:+.2%})")
        logger.info(f"   持仓时间: {holding_time:.0f}秒")
        logger.info(f"   当前余额: ${self.current_balance:.2f}")
        logger.info(f"   胜率: {win_rate:.1%}, 总收益: {total_return:+.1%}")
    
    def run_realtime_scalping(self, duration_minutes: int = 30):
        """运行实时剥头皮交易"""
        logger.info("🚀 启动实时剥头皮交易系统")
        logger.info(f"💰 初始资金: ${self.initial_balance}")
        logger.info(f"📊 方案四参数 (极速微利剥头皮):")
        logger.info(f"   止损: {self.stop_loss_ratio:.2%} (5%杠杆亏损)")
        logger.info(f"   止盈: {self.take_profit_ratio:.2%} (6.25%杠杆盈利)")
        logger.info(f"   策略: 真正的微利快进快出")
        logger.info(f"   特点: 极小目标，高成功率")
        logger.info(f"⚡ 实时监控: 有持仓时每秒检查价格")
        logger.info(f"⏰ 运行时间: {duration_minutes} 分钟")
        
        self.is_running = True
        
        # 启动价格监控
        self.start_price_monitor()
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        try:
            while datetime.now() < end_time and self.is_running:
                # 获取当前价格
                current_price = self.get_latest_price()
                if current_price is None:
                    time.sleep(2)
                    continue
                
                # 如果有持仓，实时检查退出
                if self.current_position:
                    # 显示实时状态
                    pos = self.current_position
                    duration = (datetime.now() - pos['entry_time']).total_seconds()
                    
                    if pos['direction'] == "LONG":
                        unrealized_pnl = (current_price - pos['entry_price']) / pos['entry_price'] * self.leverage
                    else:
                        unrealized_pnl = (pos['entry_price'] - current_price) / pos['entry_price'] * self.leverage
                    
                    logger.info(f"📊 持仓监控: {pos['direction']} @ {current_price:.4f} "
                               f"({duration:.0f}秒) 浮盈: {unrealized_pnl:+.2%}")
                    
                    # 检查退出条件
                    if self.check_exit_realtime(current_price):
                        continue  # 已平仓，继续下一轮
                    
                    time.sleep(1)  # 有持仓时每秒检查
                
                else:
                    # 无持仓时寻找交易机会
                    if self.can_trade():
                        direction, confidence = self.calculate_simple_signal()
                        
                        if direction in ["LONG", "SHORT"] and confidence >= self.min_confidence:
                            self.execute_trade(direction, confidence, current_price)
                    
                    time.sleep(5)  # 无持仓时5秒检查一次
                
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断交易")
        except Exception as e:
            logger.error(f"❌ 系统异常: {e}")
        finally:
            self.is_running = False
            
            # 强制平仓
            if self.current_position:
                final_price = self.get_latest_price()
                if final_price:
                    self.close_position(final_price, "系统停止")
            
            self.show_results()
    
    def show_results(self):
        """显示结果"""
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        quick_profit_rate = self.quick_profits / self.total_trades if self.total_trades > 0 else 0
        
        print("\n" + "="*60)
        print("🎉 实时剥头皮交易完成")
        print("="*60)
        print(f"📊 交易统计:")
        print(f"  总交易数: {self.total_trades}")
        print(f"  胜率: {win_rate:.1%}")
        print(f"  快速盈利率: {quick_profit_rate:.1%}")
        print(f"  平均持仓时间: {self.avg_holding_time:.0f}秒")
        print(f"💰 财务表现:")
        print(f"  最终余额: ${self.current_balance:.2f}")
        print(f"  收益率: {total_return:+.1%}")

if __name__ == "__main__":
    print("⚡ 实时剥头皮交易系统")
    print("🎯 方案四: 止盈0.05%, 止损0.04% (极速微利剥头皮)")
    print("📊 开仓后每秒监控价格变化")
    print("💡 真正的微利快进快出策略")
    print("🚀 目标: 小而稳的盈利积累")
    
    trader = RealTimeScalping(initial_balance=50.0)
    
    try:
        duration = int(input("\n请输入运行时间(分钟，默认15): ") or "15")
        trader.run_realtime_scalping(duration_minutes=duration)
    except:
        print("使用默认15分钟运行...")
        trader.run_realtime_scalping(duration_minutes=15)
