#!/usr/bin/env python3
"""
API问题排查和解决方案
专门解决币安API认证问题
"""

import requests
import hmac
import hashlib
import time
import json
from urllib.parse import urlencode
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class APITroubleshooter:
    """API问题排查器"""
    
    def __init__(self, api_key: str, api_secret: str):
        self.api_key = api_key
        self.api_secret = api_secret
        
        # 测试网和主网端点
        self.testnet_url = "https://testnet.binance.vision"
        self.mainnet_url = "https://api.binance.com"
        
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def get_current_ip(self):
        """获取当前IP地址"""
        try:
            # 尝试多个IP检测服务
            ip_services = [
                "https://httpbin.org/ip",
                "https://api.ipify.org?format=json",
                "https://ipinfo.io/json"
            ]
            
            for service in ip_services:
                try:
                    response = requests.get(service, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if 'origin' in data:
                            return data['origin']
                        elif 'ip' in data:
                            return data['ip']
                except:
                    continue
            
            return "无法获取"
        except Exception as e:
            logger.error(f"获取IP失败: {e}")
            return "无法获取"
    
    def _generate_signature(self, params: dict) -> str:
        """生成API签名"""
        query_string = urlencode(params)
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def test_different_endpoints(self):
        """测试不同的端点"""
        logger.info("🔍 测试不同的API端点...")
        
        endpoints = {
            "测试网": "https://testnet.binance.vision",
            "主网": "https://api.binance.com",
            "主网备用1": "https://api1.binance.com",
            "主网备用2": "https://api2.binance.com",
            "主网备用3": "https://api3.binance.com"
        }
        
        working_endpoints = []
        
        for name, url in endpoints.items():
            try:
                response = self.session.get(f"{url}/api/v3/ping", timeout=5)
                if response.status_code == 200:
                    logger.info(f"✅ {name} ({url}) - 连接正常")
                    working_endpoints.append((name, url))
                else:
                    logger.error(f"❌ {name} ({url}) - 连接失败: {response.status_code}")
            except Exception as e:
                logger.error(f"❌ {name} ({url}) - 连接异常: {e}")
        
        return working_endpoints
    
    def test_api_key_format(self):
        """检查API密钥格式"""
        logger.info("🔍 检查API密钥格式...")
        
        # 检查API Key
        if len(self.api_key) != 64:
            logger.error(f"❌ API Key长度错误: {len(self.api_key)} (应该是64)")
            return False
        
        if not all(c in '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz' for c in self.api_key):
            logger.error("❌ API Key包含无效字符")
            return False
        
        # 检查API Secret
        if len(self.api_secret) != 64:
            logger.error(f"❌ API Secret长度错误: {len(self.api_secret)} (应该是64)")
            return False
        
        if not all(c in '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz' for c in self.api_secret):
            logger.error("❌ API Secret包含无效字符")
            return False
        
        logger.info("✅ API密钥格式正确")
        return True
    
    def test_signature_generation(self):
        """测试签名生成"""
        logger.info("🔍 测试签名生成...")
        
        # 测试标准参数
        test_params = {
            'symbol': 'ADAUSDT',
            'timestamp': 1640995200000,  # 固定时间戳用于测试
            'recvWindow': 5000
        }
        
        signature = self._generate_signature(test_params)
        logger.info(f"✅ 签名生成成功: {signature[:16]}...")
        
        # 验证签名长度
        if len(signature) != 64:
            logger.error(f"❌ 签名长度错误: {len(signature)} (应该是64)")
            return False
        
        return True
    
    def test_time_sync(self, base_url: str):
        """测试时间同步"""
        logger.info(f"🔍 测试时间同步 ({base_url})...")
        
        try:
            response = self.session.get(f"{base_url}/api/v3/time", timeout=10)
            if response.status_code == 200:
                server_time = response.json()['serverTime']
                local_time = int(time.time() * 1000)
                offset = server_time - local_time
                
                logger.info(f"✅ 服务器时间: {server_time}")
                logger.info(f"✅ 本地时间: {local_time}")
                logger.info(f"✅ 时间偏移: {offset}ms")
                
                if abs(offset) > 1000:
                    logger.warning(f"⚠️ 时间偏移较大: {offset}ms")
                
                return offset
            else:
                logger.error(f"❌ 获取服务器时间失败: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"❌ 时间同步异常: {e}")
            return None
    
    def test_auth_with_different_params(self, base_url: str):
        """使用不同参数测试认证"""
        logger.info(f"🔍 测试API认证 ({base_url})...")
        
        # 获取服务器时间偏移
        time_offset = self.test_time_sync(base_url)
        if time_offset is None:
            time_offset = 0
        
        # 测试不同的参数组合
        test_cases = [
            {"recvWindow": 5000},
            {"recvWindow": 10000},
            {"recvWindow": 60000},
        ]
        
        for i, extra_params in enumerate(test_cases):
            logger.info(f"  测试案例 {i+1}: {extra_params}")
            
            try:
                # 准备参数
                params = {
                    'timestamp': int(time.time() * 1000) + time_offset,
                    **extra_params
                }
                params['signature'] = self._generate_signature(params)
                
                # 发送请求
                response = self.session.get(f"{base_url}/api/v3/account", 
                                          params=params, timeout=10)
                
                if response.status_code == 200:
                    logger.info(f"  ✅ 案例 {i+1} 认证成功")
                    account_data = response.json()
                    logger.info(f"     账户类型: {account_data.get('accountType', 'N/A')}")
                    return True
                else:
                    error_data = response.json() if response.content else {}
                    logger.error(f"  ❌ 案例 {i+1} 认证失败: {error_data}")
                    
            except Exception as e:
                logger.error(f"  ❌ 案例 {i+1} 异常: {e}")
        
        return False
    
    def check_api_restrictions(self):
        """检查API限制"""
        logger.info("🔍 检查API限制...")
        
        restrictions = {
            "IP白名单": "已添加但仍失败",
            "API权限": "需要检查是否启用现货交易",
            "密钥类型": "主网密钥无法用于测试网",
            "账户状态": "账户可能被限制",
            "网络环境": "可能需要VPN或代理"
        }
        
        for restriction, description in restrictions.items():
            logger.info(f"  📋 {restriction}: {description}")
    
    def provide_solutions(self):
        """提供解决方案"""
        logger.info("\n💡 解决方案建议:")
        logger.info("="*50)
        
        solutions = [
            "1. 确认API密钥类型:",
            "   - 测试网交易需要测试网API密钥",
            "   - 主网API密钥无法用于测试网",
            "   - 请访问 https://testnet.binance.vision 创建测试网密钥",
            "",
            "2. 检查API权限设置:",
            "   - 确保启用了现货交易权限",
            "   - 检查是否启用了读取权限",
            "   - 避免启用期货或杠杆权限（如不需要）",
            "",
            "3. IP白名单配置:",
            "   - 确认当前IP已正确添加",
            "   - 可以尝试添加 0.0.0.0/0 (所有IP，仅测试用)",
            "   - 检查是否有动态IP变化",
            "",
            "4. 网络环境检查:",
            "   - 尝试使用VPN切换到其他地区",
            "   - 检查防火墙设置",
            "   - 尝试使用手机热点测试",
            "",
            "5. 账户状态确认:",
            "   - 登录币安网页版确认账户正常",
            "   - 检查是否有安全限制",
            "   - 确认邮箱和手机验证已完成"
        ]
        
        for solution in solutions:
            logger.info(solution)
    
    def run_comprehensive_check(self):
        """运行综合检查"""
        logger.info("🔧 开始API综合问题排查...")
        logger.info("="*60)
        
        # 1. 获取当前IP
        current_ip = self.get_current_ip()
        logger.info(f"🌐 当前IP地址: {current_ip}")
        
        # 2. 检查API密钥格式
        key_format_ok = self.test_api_key_format()
        
        # 3. 测试签名生成
        signature_ok = self.test_signature_generation()
        
        # 4. 测试不同端点
        working_endpoints = self.test_different_endpoints()
        
        # 5. 测试认证
        auth_success = False
        for name, url in working_endpoints:
            logger.info(f"\n🔐 测试 {name} 认证...")
            if self.test_auth_with_different_params(url):
                auth_success = True
                logger.info(f"✅ {name} 认证成功！")
                break
        
        # 6. 检查限制
        self.check_api_restrictions()
        
        # 7. 提供解决方案
        if not auth_success:
            self.provide_solutions()
        
        # 总结
        logger.info("\n📋 检查总结:")
        logger.info("="*30)
        logger.info(f"IP地址: {current_ip}")
        logger.info(f"密钥格式: {'✅' if key_format_ok else '❌'}")
        logger.info(f"签名生成: {'✅' if signature_ok else '❌'}")
        logger.info(f"网络连接: {'✅' if working_endpoints else '❌'}")
        logger.info(f"API认证: {'✅' if auth_success else '❌'}")
        
        return auth_success

if __name__ == "__main__":
    print("🔧 币安API问题排查工具")
    print("📊 专门解决API认证问题")
    print("🛡️ 提供详细的解决方案")
    
    # 使用提供的API密钥
    API_KEY = "WO0FoiEOvpN996J38gHKZa6314Cdq0N8JuEy3hr0awGA9ISB0yY5fVpwtW5NEunS"
    API_SECRET = "fTHGkdJJZX5oooKJ89MUlED0WKtTn8gWEqxBQ4Fc5ykiUlrfirJonRlv58mCoBL4"
    
    troubleshooter = APITroubleshooter(API_KEY, API_SECRET)
    success = troubleshooter.run_comprehensive_check()
    
    if success:
        print("\n🎉 API认证成功！可以开始交易了。")
    else:
        print("\n⚠️ API认证仍有问题，请按照上述建议进行排查。")
        print("\n🔑 最可能的原因：")
        print("1. 这是主网API密钥，但您想用测试网")
        print("2. 需要到 https://testnet.binance.vision 创建测试网专用密钥")
        print("3. 或者直接使用主网进行真实交易（谨慎！）")
