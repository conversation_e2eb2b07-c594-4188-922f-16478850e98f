#!/usr/bin/env python3
"""
优化的交易策略
基于新AI模型和风险管理的智能交易策略
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import joblib
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """市场状态"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

class SignalStrength(Enum):
    """信号强度"""
    VERY_STRONG = "very_strong"  # 90%+ 置信度
    STRONG = "strong"           # 80-90% 置信度
    MEDIUM = "medium"           # 70-80% 置信度
    WEAK = "weak"              # 60-70% 置信度
    VERY_WEAK = "very_weak"    # <60% 置信度

@dataclass
class TradingSignal:
    """交易信号"""
    direction: str  # LONG/SHORT/HOLD
    confidence: float  # 0-1
    strength: SignalStrength
    market_regime: MarketRegime
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    expected_holding_time: int  # 分钟
    risk_reward_ratio: float
    timestamp: datetime
    reasons: List[str]

class OptimizedTradingStrategy:
    """优化的交易策略"""
    
    def __init__(self, initial_balance: float = 50.0, leverage: float = 125.0):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.leverage = leverage
        
        # 策略参数
        self.min_confidence = 0.65  # 最低置信度要求(提高门槛)
        self.max_daily_trades = 3   # 每日最大交易次数(降低频率)
        self.min_risk_reward = 1.5  # 最小风险收益比
        self.max_position_risk = 0.03  # 单笔最大风险3%
        
        # 动态参数
        self.daily_trade_count = 0
        self.consecutive_losses = 0
        self.last_trade_time = None
        self.current_drawdown = 0.0
        
        # 市场状态检测
        self.market_regime = MarketRegime.SIDEWAYS
        self.volatility_level = "normal"
        
        # 加载AI模型
        self.ai_model = None
        self.feature_engineer = None
        
    def load_ai_model(self, model_path: str):
        """加载AI模型"""
        try:
            model_data = joblib.load(model_path)
            self.ai_model = model_data['best_model']
            logger.info(f"AI模型已加载: {model_path}")
        except Exception as e:
            logger.error(f"加载AI模型失败: {e}")
    
    def detect_market_regime(self, data: pd.DataFrame) -> MarketRegime:
        """检测市场状态"""
        if len(data) < 50:
            return MarketRegime.SIDEWAYS
        
        # 计算趋势指标
        returns_20 = data['close'].pct_change(20).iloc[-1]
        returns_50 = data['close'].pct_change(50).iloc[-1] if len(data) >= 50 else 0
        
        # 计算波动率
        volatility = data['close'].pct_change().rolling(20).std().iloc[-1]
        avg_volatility = data['close'].pct_change().rolling(50).std().mean() if len(data) >= 50 else volatility
        
        # 趋势判断
        strong_trend_threshold = 0.03  # 3%
        weak_trend_threshold = 0.01    # 1%
        high_vol_threshold = avg_volatility * 1.5 if avg_volatility > 0 else 0.02
        
        if abs(returns_20) > strong_trend_threshold:
            if returns_20 > 0:
                regime = MarketRegime.TRENDING_UP
            else:
                regime = MarketRegime.TRENDING_DOWN
        elif volatility > high_vol_threshold:
            regime = MarketRegime.HIGH_VOLATILITY
        elif volatility < avg_volatility * 0.5:
            regime = MarketRegime.LOW_VOLATILITY
        else:
            regime = MarketRegime.SIDEWAYS
        
        self.market_regime = regime
        self.volatility_level = "high" if volatility > high_vol_threshold else "normal"
        
        return regime
    
    def calculate_signal_strength(self, confidence: float) -> SignalStrength:
        """计算信号强度"""
        if confidence >= 0.9:
            return SignalStrength.VERY_STRONG
        elif confidence >= 0.8:
            return SignalStrength.STRONG
        elif confidence >= 0.7:
            return SignalStrength.MEDIUM
        elif confidence >= 0.6:
            return SignalStrength.WEAK
        else:
            return SignalStrength.VERY_WEAK
    
    def calculate_dynamic_position_size(self, signal: TradingSignal) -> float:
        """动态计算仓位大小"""
        
        # 基础风险敞口
        base_risk = 0.02  # 2%
        
        # 根据信号强度调整
        strength_multiplier = {
            SignalStrength.VERY_STRONG: 1.5,
            SignalStrength.STRONG: 1.2,
            SignalStrength.MEDIUM: 1.0,
            SignalStrength.WEAK: 0.7,
            SignalStrength.VERY_WEAK: 0.5
        }
        
        # 根据市场状态调整
        regime_multiplier = {
            MarketRegime.TRENDING_UP: 1.2,
            MarketRegime.TRENDING_DOWN: 1.2,
            MarketRegime.SIDEWAYS: 0.8,
            MarketRegime.HIGH_VOLATILITY: 0.6,
            MarketRegime.LOW_VOLATILITY: 1.0
        }
        
        # 根据连续亏损调整
        loss_adjustment = max(0.3, 1.0 - (self.consecutive_losses * 0.2))
        
        # 根据回撤调整
        drawdown_adjustment = max(0.5, 1.0 - (self.current_drawdown * 2))
        
        # 计算最终风险敞口
        risk_exposure = (base_risk * 
                        strength_multiplier[signal.strength] * 
                        regime_multiplier[signal.market_regime] * 
                        loss_adjustment * 
                        drawdown_adjustment)
        
        # 限制最大风险
        risk_exposure = min(risk_exposure, self.max_position_risk)
        
        # 计算仓位大小
        margin = self.current_balance * risk_exposure
        nominal_value = margin * self.leverage
        position_size = nominal_value / signal.entry_price
        
        return position_size
    
    def calculate_stop_loss_take_profit(self, direction: str, entry_price: float, 
                                      confidence: float, volatility: float) -> Tuple[float, float]:
        """计算止损止盈"""
        
        # 基于波动率的动态止损止盈
        base_stop_pct = max(0.008, volatility * 2)  # 最小0.8%
        base_profit_pct = base_stop_pct * self.min_risk_reward
        
        # 根据置信度调整
        confidence_factor = 0.5 + confidence  # 0.5-1.5倍
        stop_pct = base_stop_pct / confidence_factor
        profit_pct = base_profit_pct * confidence_factor
        
        # 根据市场状态调整
        if self.market_regime in [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN]:
            # 趋势市场：放宽止损，扩大止盈
            stop_pct *= 0.8
            profit_pct *= 1.3
        elif self.market_regime == MarketRegime.HIGH_VOLATILITY:
            # 高波动：收紧止损止盈
            stop_pct *= 1.2
            profit_pct *= 0.9
        
        # 计算具体价格
        if direction == "LONG":
            stop_loss = entry_price * (1 - stop_pct)
            take_profit = entry_price * (1 + profit_pct)
        else:  # SHORT
            stop_loss = entry_price * (1 + stop_pct)
            take_profit = entry_price * (1 - profit_pct)
        
        return stop_loss, take_profit
    
    def check_trading_conditions(self) -> bool:
        """检查交易条件"""
        
        # 检查每日交易次数
        if self.daily_trade_count >= self.max_daily_trades:
            logger.info(f"达到每日最大交易次数: {self.max_daily_trades}")
            return False
        
        # 检查连续亏损
        if self.consecutive_losses >= 5:
            logger.info(f"连续亏损过多，暂停交易: {self.consecutive_losses}")
            return False
        
        # 检查回撤
        if self.current_drawdown > 0.15:  # 15%回撤限制
            logger.info(f"回撤过大，暂停交易: {self.current_drawdown:.2%}")
            return False
        
        # 检查交易间隔
        if self.last_trade_time:
            time_since_last = datetime.now() - self.last_trade_time
            min_interval = timedelta(minutes=30)  # 最小30分钟间隔
            if time_since_last < min_interval:
                logger.info("交易间隔太短，等待中...")
                return False
        
        return True
    
    def generate_trading_signal(self, data: pd.DataFrame, features: pd.DataFrame) -> Optional[TradingSignal]:
        """生成交易信号"""
        
        # 检查交易条件
        if not self.check_trading_conditions():
            return None
        
        # 检测市场状态
        market_regime = self.detect_market_regime(data)
        
        # AI预测
        if self.ai_model is None:
            logger.warning("AI模型未加载")
            return None
        
        try:
            # 获取最新特征
            latest_features = features.iloc[[-1]].fillna(0)
            
            # AI预测
            predictions, probabilities = self.ai_model.predict(latest_features)
            prediction = predictions[0]
            max_prob = np.max(probabilities[0])
            
            # 转换预测结果
            if prediction == 2:  # 上涨
                direction = "LONG"
            elif prediction == 0:  # 下跌
                direction = "SHORT"
            else:  # 横盘
                return None  # 不交易
            
            confidence = max_prob
            
        except Exception as e:
            logger.error(f"AI预测失败: {e}")
            return None
        
        # 检查置信度
        if confidence < self.min_confidence:
            logger.info(f"置信度不足: {confidence:.3f} < {self.min_confidence}")
            return None
        
        # 计算信号强度
        strength = self.calculate_signal_strength(confidence)
        
        # 获取当前价格和波动率
        current_price = data['close'].iloc[-1]
        volatility = data['close'].pct_change().rolling(20).std().iloc[-1]
        
        # 计算止损止盈
        stop_loss, take_profit = self.calculate_stop_loss_take_profit(
            direction, current_price, confidence, volatility
        )
        
        # 计算风险收益比
        if direction == "LONG":
            risk = current_price - stop_loss
            reward = take_profit - current_price
        else:
            risk = stop_loss - current_price
            reward = current_price - take_profit
        
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        # 检查风险收益比
        if risk_reward_ratio < self.min_risk_reward:
            logger.info(f"风险收益比不足: {risk_reward_ratio:.2f} < {self.min_risk_reward}")
            return None
        
        # 创建信号
        signal = TradingSignal(
            direction=direction,
            confidence=confidence,
            strength=strength,
            market_regime=market_regime,
            entry_price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            position_size=0,  # 稍后计算
            expected_holding_time=self._estimate_holding_time(strength, market_regime),
            risk_reward_ratio=risk_reward_ratio,
            timestamp=datetime.now(),
            reasons=self._generate_signal_reasons(prediction, confidence, market_regime)
        )
        
        # 计算仓位大小
        signal.position_size = self.calculate_dynamic_position_size(signal)
        
        # 更新交易计数
        self.daily_trade_count += 1
        self.last_trade_time = datetime.now()
        
        logger.info(f"生成交易信号: {direction} @ {current_price:.2f}, 置信度: {confidence:.3f}")
        
        return signal
    
    def _estimate_holding_time(self, strength: SignalStrength, regime: MarketRegime) -> int:
        """估计持仓时间"""
        base_time = 60  # 基础60分钟
        
        # 根据信号强度调整
        if strength == SignalStrength.VERY_STRONG:
            time_multiplier = 2.0
        elif strength == SignalStrength.STRONG:
            time_multiplier = 1.5
        elif strength == SignalStrength.MEDIUM:
            time_multiplier = 1.0
        else:
            time_multiplier = 0.5
        
        # 根据市场状态调整
        if regime in [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN]:
            time_multiplier *= 1.5  # 趋势市场持仓更久
        elif regime == MarketRegime.HIGH_VOLATILITY:
            time_multiplier *= 0.5  # 高波动快进快出
        
        return int(base_time * time_multiplier)
    
    def _generate_signal_reasons(self, prediction: int, confidence: float, 
                               regime: MarketRegime) -> List[str]:
        """生成信号原因"""
        reasons = []
        
        if prediction == 2:
            reasons.append(f"AI预测上涨(置信度: {confidence:.1%})")
        elif prediction == 0:
            reasons.append(f"AI预测下跌(置信度: {confidence:.1%})")
        
        reasons.append(f"市场状态: {regime.value}")
        
        if confidence > 0.8:
            reasons.append("高置信度信号")
        
        return reasons
    
    def update_performance(self, pnl: float, is_win: bool):
        """更新交易表现"""
        self.current_balance += pnl
        
        if is_win:
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1
        
        # 更新回撤
        peak_balance = max(self.initial_balance, self.current_balance)
        self.current_drawdown = (peak_balance - self.current_balance) / peak_balance
    
    def get_strategy_status(self) -> Dict:
        """获取策略状态"""
        return {
            'current_balance': self.current_balance,
            'daily_trades': self.daily_trade_count,
            'consecutive_losses': self.consecutive_losses,
            'current_drawdown': self.current_drawdown,
            'market_regime': self.market_regime.value,
            'volatility_level': self.volatility_level,
            'can_trade': self.check_trading_conditions()
        }

def test_strategy():
    """测试策略"""
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))

    from advanced_feature_engineering import AdvancedFeatureEngineer

    # 创建测试数据
    np.random.seed(42)
    n_points = 1000
    dates = pd.date_range('2024-01-01', periods=n_points, freq='5T')

    price = 100.0
    prices = []
    volumes = []

    for i in range(n_points):
        change = np.random.normal(0, 0.002)
        price *= (1 + change)
        prices.append(price)
        volumes.append(np.random.lognormal(10, 1))

    data = pd.DataFrame({
        'close': prices,
        'open': [prices[max(0, i-1)] for i in range(n_points)],
        'high': [p * (1 + abs(np.random.normal(0, 0.003))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.003))) for p in prices],
        'volume': volumes
    }, index=dates)

    # 确保OHLC合理性
    data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
    data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))

    # 创建特征
    feature_engineer = AdvancedFeatureEngineer()
    features = feature_engineer.create_all_features(data)

    # 创建策略
    strategy = OptimizedTradingStrategy()

    # 测试市场状态检测
    regime = strategy.detect_market_regime(data)
    print(f"✅ 市场状态检测: {regime.value}")

    # 测试信号生成（模拟AI模型）
    class MockAIModel:
        def predict(self, X):
            # 模拟预测结果
            pred = np.random.choice([0, 1, 2])  # 随机预测
            prob = np.random.uniform(0.6, 0.9)  # 随机置信度
            return [pred], [[0.1, 0.1, prob] if pred == 2 else [prob, 0.1, 0.1] if pred == 0 else [0.1, prob, 0.1]]

    strategy.ai_model = MockAIModel()

    # 生成测试信号
    signal = strategy.generate_trading_signal(data.tail(100), features.tail(100))

    if signal:
        print(f"✅ 信号生成成功:")
        print(f"   方向: {signal.direction}")
        print(f"   置信度: {signal.confidence:.3f}")
        print(f"   强度: {signal.strength.value}")
        print(f"   风险收益比: {signal.risk_reward_ratio:.2f}")
        print(f"   仓位大小: {signal.position_size:.6f} BTC")
    else:
        print("⚠️ 未生成信号（可能是条件不满足）")

    # 获取策略状态
    status = strategy.get_strategy_status()
    print(f"\n📊 策略状态: {status}")

if __name__ == "__main__":
    print("🎉 优化交易策略已创建！")
    print("\n📊 主要改进:")
    print("  ✅ 提高置信度门槛: 65%")
    print("  ✅ 降低交易频率: 最多3笔/天")
    print("  ✅ 动态仓位管理: 基于信号强度和市场状态")
    print("  ✅ 智能止损止盈: 基于波动率和置信度")
    print("  ✅ 风险收益比控制: 最小1.5:1")
    print("  ✅ 多重安全检查: 连续亏损、回撤、交易间隔")
    print("  ✅ 市场状态适应: 根据趋势和波动率调整")

    print("\n🎯 预期效果:")
    print("  📈 胜率提升: 通过提高信号质量")
    print("  🛡️ 风险控制: 多层次保护机制")
    print("  💰 收益优化: 动态风险收益比")
    print("  ⚡ 频率优化: 质量优于数量")

    print("\n🧪 运行策略测试...")
    test_strategy()
