#!/usr/bin/env python3
"""
市场分析工具 - 为当前头寸提供技术分析
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

def analyze_current_market_condition():
    """
    分析当前市场状况
    """
    print("📊 当前市场技术分析")
    print("=" * 50)
    
    try:
        # 获取数据
        fetcher = BinanceDataFetcher()
        engineer = FeatureEngineer()
        
        # 获取多个时间框架的数据
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        
        # 1小时数据
        df_1h = fetcher.get_historical_data('BTCUSDT', '1h', start_time.strftime('%Y-%m-%d'), is_futures=True)
        
        # 4小时数据
        start_time_4h = end_time - timedelta(days=30)
        df_4h = fetcher.get_historical_data('BTCUSDT', '4h', start_time_4h.strftime('%Y-%m-%d'), is_futures=True)
        
        # 当前价格
        current_price = fetcher.get_current_price('BTCUSDT', is_futures=True)
        
        print(f"当前BTC价格: ${current_price:,.2f}")
        
        # 技术指标分析
        analyze_technical_indicators(df_1h, df_4h, current_price)
        
        # 趋势分析
        analyze_trend_direction(df_1h, df_4h)
        
        # 支撑阻力分析
        analyze_support_resistance(df_1h, current_price)
        
        # 模型预测
        analyze_model_prediction(df_1h)
        
        # 风险评估
        analyze_risk_factors(df_1h, current_price)
        
        return current_price
        
    except Exception as e:
        print(f"❌ 分析错误: {str(e)}")
        return None

def analyze_technical_indicators(df_1h, df_4h, current_price):
    """
    技术指标分析
    """
    print(f"\n📈 技术指标分析:")
    
    # 最近价格变化
    recent_prices = df_1h['close'].tail(24)  # 最近24小时
    price_change_24h = (current_price - recent_prices.iloc[0]) / recent_prices.iloc[0]
    
    # 波动率
    returns = recent_prices.pct_change().dropna()
    volatility = returns.std() * np.sqrt(24)  # 24小时波动率
    
    # 简单移动平均
    ma_20 = df_1h['close'].rolling(20).mean().iloc[-1]
    ma_50 = df_1h['close'].rolling(50).mean().iloc[-1]
    
    print(f"24小时变化: {price_change_24h:+.2%}")
    print(f"24小时波动率: {volatility:.2%}")
    print(f"MA20: ${ma_20:,.2f} ({'上方' if current_price > ma_20 else '下方'})")
    print(f"MA50: ${ma_50:,.2f} ({'上方' if current_price > ma_50 else '下方'})")
    
    # RSI计算
    delta = df_1h['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    current_rsi = rsi.iloc[-1]
    
    print(f"RSI(14): {current_rsi:.1f} ", end="")
    if current_rsi > 70:
        print("(超买)")
    elif current_rsi < 30:
        print("(超卖)")
    else:
        print("(中性)")

def analyze_trend_direction(df_1h, df_4h):
    """
    趋势方向分析
    """
    print(f"\n📊 趋势分析:")
    
    # 1小时趋势
    recent_1h = df_1h['close'].tail(12)  # 最近12小时
    trend_1h = "上升" if recent_1h.iloc[-1] > recent_1h.iloc[0] else "下降"
    trend_strength_1h = abs((recent_1h.iloc[-1] - recent_1h.iloc[0]) / recent_1h.iloc[0])
    
    # 4小时趋势
    recent_4h = df_4h['close'].tail(6)   # 最近24小时
    trend_4h = "上升" if recent_4h.iloc[-1] > recent_4h.iloc[0] else "下降"
    trend_strength_4h = abs((recent_4h.iloc[-1] - recent_4h.iloc[0]) / recent_4h.iloc[0])
    
    print(f"12小时趋势: {trend_1h} (强度: {trend_strength_1h:.2%})")
    print(f"24小时趋势: {trend_4h} (强度: {trend_strength_4h:.2%})")
    
    # 趋势一致性
    if trend_1h == trend_4h:
        print(f"✅ 多时间框架趋势一致: {trend_1h}")
    else:
        print(f"⚠️ 趋势分歧: 短期{trend_1h} vs 中期{trend_4h}")

def analyze_support_resistance(df_1h, current_price):
    """
    支撑阻力分析
    """
    print(f"\n🎯 支撑阻力位:")
    
    # 最近高低点
    recent_data = df_1h.tail(48)  # 最近48小时
    recent_high = recent_data['high'].max()
    recent_low = recent_data['low'].min()
    
    # 关键价位
    resistance_1 = recent_high
    resistance_2 = current_price + (recent_high - current_price) * 0.5
    support_1 = recent_low
    support_2 = current_price - (current_price - recent_low) * 0.5
    
    print(f"阻力位1: ${resistance_1:,.2f} (距离: {(resistance_1/current_price-1)*100:+.2f}%)")
    print(f"阻力位2: ${resistance_2:,.2f} (距离: {(resistance_2/current_price-1)*100:+.2f}%)")
    print(f"支撑位1: ${support_2:,.2f} (距离: {(support_2/current_price-1)*100:+.2f}%)")
    print(f"支撑位2: ${support_1:,.2f} (距离: {(support_1/current_price-1)*100:+.2f}%)")

def analyze_model_prediction(df_1h):
    """
    模型预测分析
    """
    print(f"\n🤖 AI模型预测:")
    
    try:
        # 加载模型
        import glob
        model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
        if not model_files:
            print("❌ 未找到模型文件")
            return
        
        model_path = max(model_files, key=lambda x: x.split('_')[-1])
        model_data = joblib.load(model_path)
        model = model_data['model']
        scaler = model_data['scaler']
        
        # 特征工程
        engineer = FeatureEngineer()
        df_features = engineer.create_features(df_1h)
        
        # 准备数据
        X = df_features.drop(columns=['target'], errors='ignore')
        cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
        
        X_latest = X.iloc[-1:].copy()
        X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
        X_latest.fillna(X.median(), inplace=True)
        
        # 预测
        X_scaled = scaler.transform(X_latest)
        prediction_proba = model.predict_proba(X_scaled)
        up_probability = prediction_proba[0, 1]
        
        print(f"上涨概率: {up_probability:.1%}")
        print(f"下跌概率: {1-up_probability:.1%}")
        
        if up_probability > 0.6:
            print("🟢 模型倾向: 看涨")
        elif up_probability < 0.4:
            print("🔴 模型倾向: 看跌")
        else:
            print("🟡 模型倾向: 中性")
            
        return up_probability
        
    except Exception as e:
        print(f"❌ 模型预测失败: {str(e)}")
        return None

def analyze_risk_factors(df_1h, current_price):
    """
    风险因素分析
    """
    print(f"\n⚠️ 风险因素评估:")
    
    # 波动率风险
    returns = df_1h['close'].pct_change().tail(24)
    volatility = returns.std() * np.sqrt(24)
    
    if volatility > 0.05:
        print(f"🔴 高波动率警告: {volatility:.2%}")
    elif volatility > 0.03:
        print(f"🟡 中等波动率: {volatility:.2%}")
    else:
        print(f"🟢 低波动率: {volatility:.2%}")
    
    # 价格极值风险
    recent_range = df_1h['high'].tail(24).max() - df_1h['low'].tail(24).min()
    range_ratio = recent_range / current_price
    
    if range_ratio > 0.05:
        print(f"🔴 大幅波动区间: {range_ratio:.2%}")
    else:
        print(f"🟢 正常波动区间: {range_ratio:.2%}")
    
    # 成交量分析
    recent_volume = df_1h['volume'].tail(24).mean()
    avg_volume = df_1h['volume'].tail(168).mean()  # 一周平均
    volume_ratio = recent_volume / avg_volume
    
    if volume_ratio > 1.5:
        print(f"🟡 成交量异常放大: {volume_ratio:.1f}x")
    elif volume_ratio < 0.7:
        print(f"🟡 成交量萎缩: {volume_ratio:.1f}x")
    else:
        print(f"🟢 成交量正常: {volume_ratio:.1f}x")

def provide_trading_recommendation(entry_price=104730.90, current_equity=70.08, position_size=0.000382):
    """
    提供交易建议
    """
    print(f"\n💡 交易建议:")
    print("=" * 50)
    
    current_price = analyze_current_market_condition()
    
    if current_price is None:
        print("❌ 无法获取当前价格，建议谨慎操作")
        return
    
    # 计算当前盈亏
    pnl_ratio = (entry_price - current_price) / entry_price  # 做空盈亏
    unrealized_pnl = pnl_ratio * position_size * entry_price * 2  # 2倍杠杆
    
    print(f"\n📊 当前头寸状态:")
    print(f"入场价格: ${entry_price:,.2f}")
    print(f"当前价格: ${current_price:,.2f}")
    print(f"价格变动: {(current_price-entry_price)/entry_price:+.2%}")
    print(f"未实现盈亏: ${unrealized_pnl:+.2f} ({pnl_ratio*200:+.2%})")
    
    # 风险评估
    stop_loss_price = entry_price * 1.025  # 2.5%止损
    distance_to_stop = (stop_loss_price - current_price) / current_price
    
    print(f"\n🛡️ 风险评估:")
    print(f"止损价格: ${stop_loss_price:,.2f}")
    print(f"距离止损: {distance_to_stop:+.2%}")
    
    if distance_to_stop < 0.01:  # 距离止损小于1%
        print("🚨 警告: 接近止损位，建议考虑平仓")
    elif distance_to_stop < 0.02:  # 距离止损小于2%
        print("⚠️ 注意: 距离止损较近，密切关注")
    else:
        print("✅ 安全: 距离止损充足")
    
    # 具体建议
    print(f"\n🎯 具体操作建议:")
    
    if pnl_ratio > 0.02:  # 盈利超过2%
        print("1. 🔒 建议平仓50%锁定利润")
        print("2. 📈 剩余50%设置移动止损")
        print("3. 🎯 目标价位: $102,000-103,000")
    elif pnl_ratio > 0.01:  # 盈利1-2%
        print("1. 📊 可考虑平仓30%")
        print("2. 🛡️ 设置保本止损")
        print("3. ⏰ 密切关注市场变化")
    elif pnl_ratio > 0:  # 小幅盈利
        print("1. 🛡️ 设置保本止损")
        print("2. 📊 观察关键支撑位")
        print("3. ⚠️ 准备随时平仓")
    else:  # 亏损
        print("1. 🚨 考虑立即平仓")
        print("2. 🛡️ 严格执行止损")
        print("3. 📊 重新评估市场方向")

if __name__ == "__main__":
    provide_trading_recommendation()
