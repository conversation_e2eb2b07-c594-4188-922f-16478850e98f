#!/usr/bin/env python3
"""
综合交易系统演示版 - 展示完整功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveTradingDemo:
    """
    综合交易系统演示
    """
    
    def __init__(self, ai_probability: float = 0.372):
        self.ai_probability = ai_probability
        self.current_price = 104226.80
        
        print(f"🚀 综合交易系统演示")
        print(f"   AI概率输入: {self.ai_probability:.1%}")
        print(f"   当前BTC价格: ${self.current_price:,.2f}")
    
    def calculate_technical_indicators(self) -> Dict:
        """计算技术指标"""
        # 模拟技术指标计算结果
        return {
            'macd': {
                'trend': 'bullish',
                'strength': 0.20,
                'golden_cross': False,
                'death_cross': False
            },
            'rsi': {
                'rsi': 53.5,
                'condition': 'neutral',
                'bullish_divergence': False,
                'bearish_divergence': False
            },
            'bollinger': {
                'bb_position': 0.312,
                'upper_touch': False,
                'lower_touch': False,
                'squeeze': False
            },
            'stochastic': {
                'k_percent': 37.2,
                'd_percent': 59.1,
                'golden_cross': False,
                'death_cross': True,
                'overbought': False,
                'oversold': False
            },
            'volume': {
                'volume_ratio': 1.0,
                'condition': 'normal',
                'volume_price_confirm': False
            },
            'atr': {
                'atr_percentage': 0.0039,
                'volatility': 'low'
            }
        }
    
    def analyze_market_regime(self) -> Dict:
        """分析市场状态"""
        return {
            'regime': 'low_volatility',
            'trend_direction': 'down',
            'trend_strength': 0.0014,
            'volatility': 0.0099,
            'price_position': 0.32,
            'volume_trend': 1.0
        }
    
    def calculate_strategy_weights(self, market_regime: Dict) -> Dict:
        """计算策略权重"""
        base_weights = {
            'momentum': 0.25,
            'mean_reversion': 0.25,
            'trend_following': 0.25,
            'ai_enhanced': 0.25
        }
        
        # 低波动率环境调整
        if market_regime['regime'] == 'low_volatility':
            base_weights['mean_reversion'] *= 1.3
            base_weights['ai_enhanced'] *= 1.2
            base_weights['momentum'] *= 0.7
        
        # AI概率强度调整
        ai_strength = abs(self.ai_probability - 0.5) * 2
        base_weights['ai_enhanced'] *= (1 + ai_strength)
        
        # 归一化
        total = sum(base_weights.values())
        return {k: v/total for k, v in base_weights.items()}
    
    def generate_strategy_signals(self, indicators: Dict, weights: Dict) -> Dict:
        """生成策略信号"""
        all_signals = []
        
        # AI增强策略信号
        if self.ai_probability < 0.45:  # 弱看跌
            confirmations = []
            if indicators['rsi']['rsi'] < 55:
                confirmations.append('RSI')
            if indicators['macd']['trend'] == 'bearish':
                confirmations.append('MACD')
            if indicators['bollinger']['bb_position'] > 0.5:
                confirmations.append('BB')
            
            if len(confirmations) >= 2:
                all_signals.append({
                    'strategy': 'ai_enhanced',
                    'direction': 'SHORT',
                    'entry_price': self.current_price,
                    'confidence': 0.65,
                    'reason': f'AI弱看跌+{len(confirmations)}个指标确认',
                    'confirmations': confirmations,
                    'strength': 0.65,
                    'strategy_weight': weights['ai_enhanced'],
                    'weighted_confidence': 0.65 * weights['ai_enhanced']
                })
        
        # 均值回归策略
        if indicators['bollinger']['bb_position'] < 0.4 and indicators['rsi']['condition'] != 'overbought':
            all_signals.append({
                'strategy': 'mean_reversion',
                'direction': 'LONG',
                'entry_price': self.current_price,
                'confidence': 0.6,
                'reason': '价格偏低+RSI非超买',
                'strength': 0.6,
                'strategy_weight': weights['mean_reversion'],
                'weighted_confidence': 0.6 * weights['mean_reversion']
            })
        
        # 随机指标信号
        if indicators['stochastic']['death_cross']:
            all_signals.append({
                'strategy': 'momentum',
                'direction': 'SHORT',
                'entry_price': self.current_price,
                'confidence': 0.55,
                'reason': '随机指标死叉',
                'strength': 0.55,
                'strategy_weight': weights['momentum'],
                'weighted_confidence': 0.55 * weights['momentum']
            })
        
        return {
            'all_signals': {
                'ai_enhanced': [s for s in all_signals if s['strategy'] == 'ai_enhanced'],
                'mean_reversion': [s for s in all_signals if s['strategy'] == 'mean_reversion'],
                'momentum': [s for s in all_signals if s['strategy'] == 'momentum'],
                'trend_following': []
            },
            'weighted_signals': all_signals
        }
    
    def integrate_signals(self, weighted_signals: List[Dict], indicators: Dict) -> Dict:
        """集成信号"""
        if not weighted_signals:
            return {
                'long_strength': 0,
                'short_strength': 0,
                'confirmation_score': 0.5,
                'final_recommendation': {
                    'action': 'WAIT',
                    'confidence': 0.5,
                    'reason': '无明确信号'
                },
                'signal_summary': {
                    'total_signals': 0,
                    'long_signals': 0,
                    'short_signals': 0
                }
            }
        
        # 按方向分组
        long_signals = [s for s in weighted_signals if s['direction'] == 'LONG']
        short_signals = [s for s in weighted_signals if s['direction'] == 'SHORT']
        
        # 计算信号强度
        long_strength = sum(s['weighted_confidence'] * s['strength'] for s in long_signals)
        short_strength = sum(s['weighted_confidence'] * s['strength'] for s in short_signals)
        
        # 确认分数
        confirmation_score = self._calculate_confirmation_score(indicators)
        
        # 最终决策
        if short_strength > long_strength and short_strength > 0.3:
            primary_signal = max(short_signals, key=lambda x: x['weighted_confidence'])
            
            # 计算止损止盈
            atr_pct = indicators['atr']['atr_percentage']
            volatility_adj = min(atr_pct * 2, 0.01)
            
            recommendation = {
                'action': 'SHORT',
                'entry_price': self.current_price,
                'stop_loss': self.current_price * (1 + 0.025 + volatility_adj),
                'take_profit': self.current_price * (1 - 0.05 - volatility_adj),
                'confidence': min(short_strength * confirmation_score, 1.0),
                'primary_strategy': primary_signal['strategy'],
                'reason': primary_signal['reason'],
                'supporting_signals': len(short_signals)
            }
        elif long_strength > short_strength and long_strength > 0.3:
            primary_signal = max(long_signals, key=lambda x: x['weighted_confidence'])
            
            atr_pct = indicators['atr']['atr_percentage']
            volatility_adj = min(atr_pct * 2, 0.01)
            
            recommendation = {
                'action': 'LONG',
                'entry_price': self.current_price,
                'stop_loss': self.current_price * (1 - 0.025 - volatility_adj),
                'take_profit': self.current_price * (1 + 0.05 + volatility_adj),
                'confidence': min(long_strength * confirmation_score, 1.0),
                'primary_strategy': primary_signal['strategy'],
                'reason': primary_signal['reason'],
                'supporting_signals': len(long_signals)
            }
        else:
            recommendation = {
                'action': 'NEUTRAL',
                'confidence': 0.5,
                'reason': '信号强度不足或冲突'
            }
        
        return {
            'long_strength': long_strength,
            'short_strength': short_strength,
            'confirmation_score': confirmation_score,
            'final_recommendation': recommendation,
            'signal_summary': {
                'total_signals': len(weighted_signals),
                'long_signals': len(long_signals),
                'short_signals': len(short_signals)
            }
        }
    
    def _calculate_confirmation_score(self, indicators: Dict) -> float:
        """计算确认分数"""
        confirmations = 0
        total_checks = 4
        
        # MACD + RSI确认
        if ((indicators['macd']['trend'] == 'bullish' and indicators['rsi']['rsi'] < 70) or
            (indicators['macd']['trend'] == 'bearish' and indicators['rsi']['rsi'] > 30)):
            confirmations += 1
        
        # 布林带位置合理
        if 0.2 < indicators['bollinger']['bb_position'] < 0.8:
            confirmations += 1
        
        # 随机指标与AI一致
        stoch_bearish = indicators['stochastic']['death_cross'] or indicators['stochastic']['k_percent'] > 60
        ai_bearish = self.ai_probability < 0.5
        if (stoch_bearish and ai_bearish):
            confirmations += 1
        
        # 波动率适中
        if indicators['atr']['volatility'] in ['normal', 'low']:
            confirmations += 1
        
        return confirmations / total_checks
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print(f"\n🔍 执行综合分析...")
        
        # 1. 计算技术指标
        indicators = self.calculate_technical_indicators()
        
        # 2. 分析市场状态
        market_regime = self.analyze_market_regime()
        
        # 3. 计算策略权重
        strategy_weights = self.calculate_strategy_weights(market_regime)
        
        # 4. 生成策略信号
        strategy_signals = self.generate_strategy_signals(indicators, strategy_weights)
        
        # 5. 集成最终信号
        final_analysis = self.integrate_signals(strategy_signals['weighted_signals'], indicators)
        
        # 6. 打印结果
        self.print_analysis_results(indicators, market_regime, strategy_weights, strategy_signals, final_analysis)
    
    def print_analysis_results(self, indicators, market_regime, strategy_weights, strategy_signals, final_analysis):
        """打印分析结果"""
        print(f"\n🎯 【综合交易系统分析报告】")
        print("=" * 80)
        
        # AI预测
        print(f"🤖 AI模型预测:")
        print(f"   上涨概率: {self.ai_probability:.1%}")
        print(f"   下跌概率: {1-self.ai_probability:.1%}")
        print(f"   当前价格: ${self.current_price:,.2f}")
        
        # 市场状态
        print(f"\n📊 市场状态分析:")
        print(f"   市场状态: {market_regime['regime']}")
        print(f"   趋势方向: {market_regime['trend_direction']}")
        print(f"   趋势强度: {market_regime['trend_strength']:.2%}")
        print(f"   波动率: {market_regime['volatility']:.2%}")
        print(f"   价格位置: {market_regime['price_position']:.1%}")
        
        # 技术指标
        print(f"\n📈 技术指标状态:")
        print(f"   MACD: {indicators['macd']['trend']} (强度: {indicators['macd']['strength']:.2f})")
        print(f"   RSI: {indicators['rsi']['condition']} ({indicators['rsi']['rsi']:.1f})")
        print(f"   布林带: 位置{indicators['bollinger']['bb_position']:.1%}")
        print(f"   随机指标: K={indicators['stochastic']['k_percent']:.1f}, D={indicators['stochastic']['d_percent']:.1f}")
        print(f"   成交量: {indicators['volume']['condition']} (比率: {indicators['volume']['volume_ratio']:.1f})")
        print(f"   波动率: {indicators['atr']['volatility']} (ATR: {indicators['atr']['atr_percentage']:.2%})")
        
        # 策略权重
        print(f"\n⚖️ 策略权重分配:")
        for strategy, weight in strategy_weights.items():
            print(f"   {strategy}: {weight:.1%}")
        
        # 策略信号
        print(f"\n🚀 策略信号汇总:")
        for strategy, signals in strategy_signals['all_signals'].items():
            if signals:
                print(f"   📊 {strategy.upper()}:")
                for signal in signals:
                    direction_emoji = "🟢" if signal['direction'] == 'LONG' else "🔴"
                    print(f"      {direction_emoji} {signal['direction']} - {signal['reason']}")
                    print(f"         置信度: {signal['confidence']:.1%}, 权重后: {signal['weighted_confidence']:.2f}")
        
        # 最终建议
        final = final_analysis['final_recommendation']
        print(f"\n🎯 最终交易建议:")
        action_emoji = {"LONG": "🟢", "SHORT": "🔴", "NEUTRAL": "🟡", "WAIT": "⏸️"}
        print(f"   {action_emoji.get(final['action'], '❓')} 动作: {final['action']}")
        
        if final['action'] in ['LONG', 'SHORT']:
            print(f"   入场价: ${final['entry_price']:,.2f}")
            print(f"   止损价: ${final['stop_loss']:,.2f}")
            print(f"   止盈价: ${final['take_profit']:,.2f}")
            print(f"   主导策略: {final['primary_strategy']}")
            print(f"   支持信号数: {final['supporting_signals']}")
        
        print(f"   置信度: {final['confidence']:.1%}")
        print(f"   确认分数: {final_analysis['confirmation_score']:.1%}")
        print(f"   理由: {final['reason']}")
        
        # 信号统计
        summary = final_analysis['signal_summary']
        print(f"\n📊 信号统计:")
        print(f"   总信号数: {summary['total_signals']}")
        print(f"   做多信号: {summary['long_signals']}")
        print(f"   做空信号: {summary['short_signals']}")
        print(f"   做多强度: {final_analysis['long_strength']:.2f}")
        print(f"   做空强度: {final_analysis['short_strength']:.2f}")
        
        print(f"\n💡 系统升级总结:")
        print(f"✅ 从单一AI概率 ({self.ai_probability:.1%}) 升级为多维度分析")
        print(f"✅ 集成6大技术指标确认")
        print(f"✅ 4种策略动态权重分配")
        print(f"✅ 多层信号确认机制")
        print(f"✅ 完整的风险管理建议")

def main():
    """主函数"""
    print("🚀 综合交易系统演示")
    print("=" * 60)
    print("演示如何将您的AI模型从37.2%概率升级为完整交易决策系统")
    print("")
    
    # 创建演示实例
    demo = ComprehensiveTradingDemo(ai_probability=0.372)
    
    # 运行分析
    demo.run_comprehensive_analysis()
    
    print(f"\n🎯 实现完成!")
    print(f"您的AI模型现在具备了:")
    print(f"• 技术指标集成分析")
    print(f"• 多策略信号生成")
    print(f"• 智能权重分配")
    print(f"• 风险管理建议")
    print(f"• 多层确认机制")

if __name__ == "__main__":
    main()
