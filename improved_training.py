"""
改进的模型训练脚本
包含多种提升模型准确性的技术
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import TimeSeriesSplit, StratifiedKFold
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.under_sampling import RandomUnderSampler
from imblearn.pipeline import Pipeline as ImbPipeline

from model_trainer import ModelTrainer
from feature_engineering import FeatureEngineer, FeatureConfig
from data_fetcher import BinanceDataFetcher
from model_optimization_config import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedModelTrainer:
    """改进的模型训练器"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.feature_engineer = None
        self.models = {}
        self.scalers = {}
        self.results = {}
        
    def prepare_enhanced_data(self, symbol: str, interval: str, start_date: str) -> Tuple[pd.DataFrame, pd.Series]:
        """准备增强的训练数据"""
        logger.info(f"准备增强数据: {symbol} {interval} from {start_date}")
        
        # 1. 获取原始数据
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, interval, start_date)
        
        # 2. 增强特征工程配置
        feature_config = FeatureConfig(
            ma_periods=FEATURE_ENGINEERING_CONFIG['technical_indicators']['ma_periods'],
            rsi_periods=FEATURE_ENGINEERING_CONFIG['technical_indicators']['rsi_periods'],
            enable_variance_threshold_selection=True,
            enable_correlation_selection=True,
            correlation_threshold=FEATURE_ENGINEERING_CONFIG['feature_selection']['correlation_threshold'],
            max_features_after_importance=FEATURE_ENGINEERING_CONFIG['feature_selection']['max_features']
        )
        
        # 3. 创建特征
        self.feature_engineer = FeatureEngineer(config=feature_config)
        df_features = self.feature_engineer.create_features(df, force_refresh=True)
        
        # 4. 分离特征和目标
        if 'target' not in df_features.columns:
            raise ValueError("目标列 'target' 未找到")
            
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 5. 数据质量检查
        X, y = self._quality_check_and_clean(X, y)
        
        # 6. 特征工程增强
        X = self._create_advanced_features(X)
        
        # 7. 特征选择
        X = self._advanced_feature_selection(X, y)
        
        logger.info(f"数据准备完成: {X.shape[0]} 样本, {X.shape[1]} 特征, {len(y.unique())} 类别")
        return X, y
    
    def _quality_check_and_clean(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """数据质量检查和清理"""
        logger.info("执行数据质量检查...")
        
        # 移除目标变量为NaN的样本
        valid_mask = ~y.isna()
        X = X[valid_mask]
        y = y[valid_mask]
        
        # 检查类别分布
        class_counts = y.value_counts()
        logger.info(f"类别分布:\n{class_counts}")
        
        # 移除样本数过少的类别
        min_samples = max(10, len(y) * 0.01)  # 至少10个样本或1%
        valid_classes = class_counts[class_counts >= min_samples].index
        valid_mask = y.isin(valid_classes)
        X = X[valid_mask]
        y = y[valid_mask]
        
        # 重新编码目标变量
        le = LabelEncoder()
        y = pd.Series(le.fit_transform(y), index=y.index)
        
        logger.info(f"清理后数据: {X.shape[0]} 样本, {len(y.unique())} 类别")
        return X, y
    
    def _create_advanced_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """创建高级特征"""
        logger.info("创建高级特征...")
        X_advanced = X.copy()
        
        # 1. 市场微观结构特征
        if all(col in X_advanced.columns for col in ['high', 'low', 'close', 'volume']):
            # 价格效率指标
            X_advanced['price_efficiency'] = (X_advanced['close'] - X_advanced['low']) / (X_advanced['high'] - X_advanced['low'])
            
            # 成交量加权平均价格偏差
            typical_price = (X_advanced['high'] + X_advanced['low'] + X_advanced['close']) / 3
            X_advanced['vwap_deviation'] = (X_advanced['close'] - typical_price) / typical_price
        
        # 2. 波动率聚类特征
        if 'ATR_14' in X_advanced.columns:
            # GARCH效应代理
            X_advanced['volatility_persistence'] = X_advanced['ATR_14'].rolling(10).std()
            X_advanced['volatility_mean_reversion'] = X_advanced['ATR_14'] / X_advanced['ATR_14'].rolling(20).mean()
        
        # 3. 趋势强度特征
        ma_cols = [col for col in X_advanced.columns if 'MA_' in col]
        if len(ma_cols) >= 3:
            ma_data = X_advanced[ma_cols[:3]]
            # MA排列强度
            X_advanced['ma_alignment_strength'] = (ma_data.rank(axis=1) == range(1, len(ma_cols[:3])+1)).sum(axis=1)
        
        # 4. 动量分歧特征
        if all(col in X_advanced.columns for col in ['RSI_14', 'close']):
            # RSI与价格分歧
            price_change = X_advanced['close'].pct_change(5)
            rsi_change = X_advanced['RSI_14'].diff(5)
            X_advanced['rsi_price_divergence'] = np.sign(price_change) != np.sign(rsi_change)
        
        logger.info(f"高级特征创建完成，新增特征数: {X_advanced.shape[1] - X.shape[1]}")
        return X_advanced
    
    def _advanced_feature_selection(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """高级特征选择"""
        logger.info("执行高级特征选择...")
        
        from sklearn.feature_selection import (
            VarianceThreshold, SelectKBest, f_classif, mutual_info_classif,
            RFE, RFECV
        )
        from sklearn.ensemble import RandomForestClassifier
        
        X_selected = X.copy()
        
        # 1. 方差过滤
        variance_selector = VarianceThreshold(threshold=0.01)
        X_selected = pd.DataFrame(
            variance_selector.fit_transform(X_selected),
            columns=X_selected.columns[variance_selector.get_support()],
            index=X_selected.index
        )
        logger.info(f"方差过滤后: {X_selected.shape[1]} 特征")
        
        # 2. 相关性过滤
        corr_matrix = X_selected.corr().abs()
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        high_corr_features = [
            column for column in upper_triangle.columns 
            if any(upper_triangle[column] > 0.95)
        ]
        X_selected = X_selected.drop(columns=high_corr_features)
        logger.info(f"相关性过滤后: {X_selected.shape[1]} 特征")
        
        # 3. 基于重要性的选择
        if X_selected.shape[1] > 50:
            rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
            rf.fit(X_selected, y)
            
            feature_importance = pd.Series(rf.feature_importances_, index=X_selected.columns)
            top_features = feature_importance.nlargest(50).index
            X_selected = X_selected[top_features]
            logger.info(f"重要性过滤后: {X_selected.shape[1]} 特征")
        
        return X_selected
    
    def train_with_optimization(self, X: pd.DataFrame, y: pd.Series, model_type: str = 'xgb') -> Dict:
        """使用优化配置训练模型"""
        logger.info(f"开始优化训练 {model_type} 模型...")
        
        # 1. 处理类别不平衡
        X_balanced, y_balanced = self._handle_class_imbalance(X, y)
        
        # 2. 创建优化的训练器
        trainer_config = self._get_optimized_trainer_config(model_type)
        trainer = ModelTrainer(**trainer_config)
        
        # 3. 训练模型
        results = trainer.train(X_balanced, y_balanced)
        
        # 4. 保存模型和结果
        model_path, scaler_path = trainer.save_model()
        
        # 5. 详细评估
        detailed_results = self._detailed_evaluation(trainer, X, y, results)
        
        self.models[model_type] = trainer
        self.results[model_type] = detailed_results
        
        return detailed_results
    
    def _handle_class_imbalance(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """处理类别不平衡"""
        class_counts = y.value_counts()
        imbalance_ratio = class_counts.max() / class_counts.min()
        
        if imbalance_ratio > 3:  # 如果不平衡比例大于3:1
            logger.info(f"检测到类别不平衡 (比例: {imbalance_ratio:.2f}), 应用SMOTE...")
            
            smote = SMOTE(random_state=42, k_neighbors=min(5, class_counts.min()-1))
            X_balanced, y_balanced = smote.fit_resample(X, y)
            
            logger.info(f"平衡后样本数: {len(y_balanced)}")
            return pd.DataFrame(X_balanced, columns=X.columns), pd.Series(y_balanced)
        
        return X, y
    
    def _get_optimized_trainer_config(self, model_type: str) -> Dict:
        """获取优化的训练器配置"""
        base_config = {
            'model_type': model_type,
            'use_optuna': True,
            'optuna_n_trials': 100,  # 增加试验次数
            'n_splits': 5,
            'scaler_type': 'robust',  # 使用更稳健的缩放器
            'save_plots': True
        }
        
        if model_type == 'lstm':
            base_config.update({
                'lstm_timesteps': 15,  # 增加时间步长
                'lstm_epochs': 100,
                'num_classes': len(np.unique(self.current_y))
            })
        
        return base_config
    
    def _detailed_evaluation(self, trainer, X: pd.DataFrame, y: pd.Series, results: Dict) -> Dict:
        """详细模型评估"""
        logger.info("执行详细模型评估...")
        
        # 预测
        y_pred = trainer.predict(X)
        y_pred_proba = trainer.predict_proba(X)
        
        # 分类报告
        class_report = classification_report(y, y_pred, output_dict=True)
        
        # 混淆矩阵
        conf_matrix = confusion_matrix(y, y_pred)
        
        # 计算方向准确率（金融特定指标）
        directional_accuracy = self._calculate_directional_accuracy(y, y_pred)
        
        detailed_results = {
            **results,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'directional_accuracy': directional_accuracy,
            'prediction_confidence': np.mean(np.max(y_pred_proba, axis=1)) if y_pred_proba.ndim > 1 else None
        }
        
        return detailed_results
    
    def _calculate_directional_accuracy(self, y_true: pd.Series, y_pred: pd.Series) -> float:
        """计算方向准确率"""
        # 假设类别0-3为下跌，4-7为上涨
        true_direction = (y_true >= 4).astype(int)
        pred_direction = (y_pred >= 4).astype(int)
        return (true_direction == pred_direction).mean()

if __name__ == "__main__":
    # 使用示例
    trainer = ImprovedModelTrainer()
    
    # 准备数据
    X, y = trainer.prepare_enhanced_data('BTCUSDT', '1h', '2022-01-01')
    trainer.current_y = y  # 保存用于配置
    
    # 训练多个模型
    models_to_train = ['xgb', 'lgb', 'lstm']
    
    for model_type in models_to_train:
        logger.info(f"\n{'='*50}")
        logger.info(f"训练 {model_type.upper()} 模型")
        logger.info(f"{'='*50}")
        
        try:
            results = trainer.train_with_optimization(X, y, model_type)
            
            # 打印结果
            print(f"\n{model_type.upper()} 模型结果:")
            print(f"准确率: {results['mean_scores']['accuracy']:.4f}")
            print(f"F1分数: {results['mean_scores']['f1']:.4f}")
            print(f"方向准确率: {results['directional_accuracy']:.4f}")
            
        except Exception as e:
            logger.error(f"训练 {model_type} 模型时出错: {e}")
    
    logger.info("所有模型训练完成!")
