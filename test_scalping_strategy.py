#!/usr/bin/env python3
"""
Test script to verify the optimized scalping strategy with fee consideration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader
from datetime import datetime

def test_scalping_strategy():
    """Test the optimized scalping strategy"""
    print("🧪 Testing Optimized Scalping Strategy")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 Scenario: 9.6% ROI Position (Your Current Situation)")
    print("Old Strategy: Wait for 15% ROI (too conservative)")
    print("New Strategy: Take profit at 8% ROI (scalping)")
    
    # Simulate current position with 9.6% ROI
    trader.position = {
        'side': 'LONG',
        'size': 0.000141,
        'entry_price': 102000.0,
        'entry_time': datetime.now(),
        'stop_loss_price': 101800.0,
        'take_profit_price': 102500.0,
        'unrealized_pnl': 1.38,
        'roi_percent': 9.6
    }
    
    trader.account['unrealized_pnl'] = 1.38
    trader.account['margin_used'] = 15.0
    trader.account['available_margin'] = 35.0
    
    print("\n🔍 Testing New Scalping Parameters:")
    current_price = 102200.0
    
    # Test the exit conditions with new parameters
    should_exit = trader.check_exit_conditions(current_price)
    
    print(f"Current Price: ${current_price:,.2f}")
    print(f"Current ROI: {trader.position['roi_percent']:+.1f}%")
    print(f"New ROI Take-Profit: 8.0% (vs old 15.0%)")
    print(f"New ROI Stop-Loss: -2.0% (vs old -3.0%)")
    print(f"Should Exit: {should_exit}")
    print(f"Expected: True (9.6% > 8.0% threshold)")
    
    print("\n💰 Fee Impact Analysis:")
    position_value = abs(trader.position['size']) * current_price
    estimated_fee_rate = 0.0004  # 0.04%
    estimated_fee = position_value * estimated_fee_rate * 2  # Open + Close
    fee_roi_impact = (estimated_fee / trader.account['balance']) * 100
    net_roi_after_fee = trader.position['roi_percent'] - fee_roi_impact
    
    print(f"Position Value: ${position_value:.2f}")
    print(f"Estimated Fee: ${estimated_fee:.4f} (0.04% x 2)")
    print(f"Fee ROI Impact: {fee_roi_impact:.2f}%")
    print(f"Net ROI After Fee: {net_roi_after_fee:+.1f}%")
    
    if net_roi_after_fee > 5.0:
        print(f"✅ Profitable after fees! Net gain: {net_roi_after_fee:+.1f}%")
    else:
        print(f"⚠️ Low profit margin after fees")
    
    print("\n" + "="*60)
    print("🎯 Scalping Strategy Comparison:")
    
    print(f"\n📊 Old Strategy (Conservative):")
    print(f"   🎯 ROI Take-Profit: 15.0%")
    print(f"   🛑 ROI Stop-Loss: -3.0%")
    print(f"   ⏱️ Result: Still holding (9.6% < 15%)")
    print(f"   💭 Risk: Market reversal, missed opportunity")
    
    print(f"\n🚀 New Strategy (Scalping):")
    print(f"   🎯 ROI Take-Profit: 8.0% - 吃一口就走")
    print(f"   🛑 ROI Stop-Loss: -2.0% - 严格风控")
    print(f"   ⏱️ Result: Should exit now (9.6% > 8%)")
    print(f"   💰 Net Profit: ~{net_roi_after_fee:+.1f}% after fees")
    print(f"   ✅ Advantage: Lock in profits, reduce risk")
    
    print("\n🔄 High-Frequency Benefits:")
    print(f"   ⚡ Faster turnover: More trading opportunities")
    print(f"   🛡️ Lower risk: Shorter exposure time")
    print(f"   💎 Compound effect: Small profits accumulate")
    print(f"   🎯 Consistency: Regular profit taking")
    
    print("\n" + "="*60)
    print("💡 Recommendations:")
    print("✅ Use 8% ROI take-profit for true scalping")
    print("✅ Consider fees in profit calculations")
    print("✅ Maintain strict -2% stop-loss")
    print("✅ Focus on trade frequency over individual trade size")
    print("🚀 This strategy better suits 125x leverage high-frequency trading!")

if __name__ == "__main__":
    test_scalping_strategy()
