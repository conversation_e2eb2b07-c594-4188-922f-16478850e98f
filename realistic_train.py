#!/usr/bin/env python3
"""
现实训练脚本 - 解决过拟合和数据泄露问题
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, balanced_accuracy_score
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
import joblib

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def realistic_train(symbol='BTCUSDT', model_type='xgb', days_back=180):
    """
    现实训练 - 解决过拟合问题
    """
    print(f"🚀 开始现实训练 {symbol} {model_type.upper()} 模型...")
    print("⚠️  注意：这次训练会更严格，准确率可能较低但更真实")
    
    try:
        # 1. 获取更多数据
        print("📊 获取数据...")
        start_date = (datetime.now() - pd.Timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, '1h', start_date)
        print(f"✅ 获取到 {len(df)} 条数据")
        
        # 2. 特征工程
        print("🔧 特征工程...")
        engineer = FeatureEngineer()
        df_features = engineer.create_features(df)
        
        # 3. 准备数据
        print("📋 准备数据...")
        
        if 'target' not in df_features.columns:
            raise ValueError("目标列 'target' 未找到")
        
        # 分离特征和目标
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始列和可能泄露未来信息的特征
        cols_to_remove = ['open', 'high', 'low', 'close', 'volume']
        
        # 移除可能包含未来信息的特征
        future_leak_patterns = ['_future', '_next', '_ahead', '_forward']
        for col in X.columns:
            if any(pattern in col.lower() for pattern in future_leak_patterns):
                cols_to_remove.append(col)
        
        X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
        
        # 清理数据
        print("🧹 清理数据...")
        before_clean = len(X)
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]
        
        # 处理无穷大值
        X.replace([np.inf, -np.inf], np.nan, inplace=True)
        X.fillna(X.median(), inplace=True)
        
        print(f"数据清理: {before_clean} -> {len(X)} 样本")
        
        # 编码标签
        label_encoder = LabelEncoder()
        y_encoded = label_encoder.fit_transform(y)
        
        # 显示原始类别分布
        unique, counts = np.unique(y_encoded, return_counts=True)
        print(f"原始类别分布: {dict(zip(unique, counts))}")
        
        # 处理极度不平衡的数据
        print("⚖️ 处理类别不平衡...")
        
        # 只保留有足够样本的类别（至少5%的数据）
        min_samples = len(y_encoded) * 0.05  # 至少5%
        valid_classes = unique[counts >= min_samples]
        
        if len(valid_classes) < 2:
            # 如果太严格，降低到2%
            min_samples = len(y_encoded) * 0.02
            valid_classes = unique[counts >= min_samples]
        
        print(f"保留类别: {valid_classes} (每个至少 {min_samples:.0f} 样本)")
        
        # 过滤数据
        mask = np.isin(y_encoded, valid_classes)
        X = X[mask]
        y_encoded = y_encoded[mask]
        
        # 重新映射标签
        label_mapping = {old: new for new, old in enumerate(valid_classes)}
        y_final = np.array([label_mapping[label] for label in y_encoded])
        
        unique_final, counts_final = np.unique(y_final, return_counts=True)
        print(f"过滤后类别分布: {dict(zip(unique_final, counts_final))}")
        
        # 使用SMOTE平衡数据
        print("🔄 使用SMOTE平衡数据...")
        smote = SMOTE(random_state=42, k_neighbors=min(5, min(counts_final)-1))
        X_balanced, y_balanced = smote.fit_resample(X, y_final)
        
        unique_balanced, counts_balanced = np.unique(y_balanced, return_counts=True)
        print(f"平衡后类别分布: {dict(zip(unique_balanced, counts_balanced))}")
        
        # 4. 时间序列分割 (更严格)
        print("✂️ 时间序列分割...")
        
        # 使用时间序列分割，确保没有数据泄露
        split_point = int(len(X_balanced) * 0.8)
        X_train = X_balanced[:split_point]
        X_test = X_balanced[split_point:]
        y_train = y_balanced[:split_point]
        y_test = y_balanced[split_point:]
        
        print(f"训练集: {len(X_train)} 样本")
        print(f"测试集: {len(X_test)} 样本")
        
        # 5. 特征缩放
        print("📏 特征缩放...")
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 6. 创建更保守的模型
        print(f"🎯 创建保守的 {model_type.upper()} 模型...")
        
        if model_type == 'xgb' and HAS_XGB:
            model = xgb.XGBClassifier(
                n_estimators=50,      # 减少树的数量
                max_depth=4,          # 减少深度
                learning_rate=0.05,   # 降低学习率
                subsample=0.8,        # 子采样
                colsample_bytree=0.8, # 特征子采样
                reg_alpha=1.0,        # L1正则化
                reg_lambda=1.0,       # L2正则化
                random_state=42,
                n_jobs=-1,
                eval_metric='mlogloss'
            )
        else:
            # 保守的随机森林
            model = RandomForestClassifier(
                n_estimators=50,      # 减少树的数量
                max_depth=8,          # 限制深度
                min_samples_split=10, # 增加分割要求
                min_samples_leaf=5,   # 增加叶子节点要求
                max_features='sqrt',  # 限制特征数量
                random_state=42,
                n_jobs=-1
            )
        
        # 7. 交叉验证评估
        print("🔍 交叉验证评估...")
        
        # 使用时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=3)
        cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=tscv, scoring='balanced_accuracy')
        
        print(f"交叉验证平衡准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        # 8. 训练最终模型
        print("🏃 训练最终模型...")
        model.fit(X_train_scaled, y_train)
        
        # 9. 评估模型
        print("📊 评估模型...")
        
        # 预测
        y_train_pred = model.predict(X_train_scaled)
        y_test_pred = model.predict(X_test_scaled)
        
        # 计算多种准确率指标
        train_accuracy = accuracy_score(y_train, y_train_pred)
        test_accuracy = accuracy_score(y_test, y_test_pred)
        train_balanced = balanced_accuracy_score(y_train, y_train_pred)
        test_balanced = balanced_accuracy_score(y_test, y_test_pred)
        
        # 10. 显示结果
        print("\n" + "="*60)
        print("🎉 现实训练完成!")
        print("="*60)
        print(f"训练集准确率:     {train_accuracy:.4f}")
        print(f"测试集准确率:     {test_accuracy:.4f}")
        print(f"训练集平衡准确率: {train_balanced:.4f}")
        print(f"测试集平衡准确率: {test_balanced:.4f}")
        print(f"交叉验证准确率:   {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        # 过拟合检查
        overfitting = train_accuracy - test_accuracy
        print(f"\n📊 过拟合检查:")
        print(f"训练-测试差距: {overfitting:.4f}")
        if overfitting > 0.1:
            print("⚠️  可能存在过拟合")
        elif overfitting > 0.05:
            print("⚠️  轻微过拟合")
        else:
            print("✅ 过拟合控制良好")
        
        # 分类报告
        print("\n📋 详细分类报告:")
        print(classification_report(y_test, y_test_pred))
        
        # 11. 保存模型
        print("💾 保存模型...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path("models")
        model_dir.mkdir(exist_ok=True)
        
        model_path = model_dir / f"realistic_{model_type}_{symbol}_{timestamp}.joblib"
        scaler_path = model_dir / f"realistic_scaler_{model_type}_{symbol}_{timestamp}.joblib"
        encoder_path = model_dir / f"realistic_encoder_{model_type}_{symbol}_{timestamp}.joblib"
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        joblib.dump(label_encoder, encoder_path)
        
        print(f"✅ 模型已保存:")
        print(f"   模型: {model_path}")
        
        # 12. 特征重要性
        if hasattr(model, 'feature_importances_'):
            print("\n🔍 Top 10 重要特征:")
            feature_importance = pd.Series(
                model.feature_importances_, 
                index=X.columns
            ).sort_values(ascending=False)
            
            for i, (feature, importance) in enumerate(feature_importance.head(10).items(), 1):
                print(f"{i:2d}. {feature:25}: {importance:.6f}")
        
        print(f"\n📊 模型性能总结:")
        print(f"   特征数量: {X.shape[1]}")
        print(f"   训练样本: {len(X_train)}")
        print(f"   测试样本: {len(X_test)}")
        print(f"   类别数量: {len(unique_final)}")
        print(f"   测试准确率: {test_accuracy:.4f}")
        print(f"   平衡准确率: {test_balanced:.4f}")
        
        return {
            'model': model,
            'scaler': scaler,
            'label_encoder': label_encoder,
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'balanced_accuracy': test_balanced,
            'cv_scores': cv_scores,
            'overfitting': overfitting,
            'model_path': model_path
        }
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    model_type = sys.argv[2] if len(sys.argv) > 2 else 'xgb'
    
    if model_type == 'xgb' and not HAS_XGB:
        print("XGBoost不可用，使用随机森林")
        model_type = 'rf'
    
    realistic_train(symbol, model_type)
