#!/usr/bin/env python3
"""
🤖 智能自动交易系统
自动检测网络可用性，优先使用主网，回退到测试网
基于100%胜率验证的成功策略
"""

import numpy as np
import logging
import time
import requests
import hmac
import hashlib
import json
import os
from datetime import datetime, timedelta
from typing import Optional, Tuple
import threading
import queue
from urllib.parse import urlencode

# 配置日志（同时输出到控制台和文件）
def setup_logging():
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 生成日志文件名（包含时间戳）
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f'logs/trading_log_{timestamp}.log'

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # 清除现有的handlers
    logger.handlers.clear()

    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件输出
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    logger.info(f"📝 日志将保存到: {log_filename}")
    return logger

logger = setup_logging()

class SmartTradingAPI:
    """🤖 智能交易API - 自动选择最佳网络"""
    
    def __init__(self, api_key: str, api_secret: str):
        self.api_key = api_key
        self.api_secret = api_secret
        
        # 永续合约网络端点
        self.mainnet_url = "https://fapi.binance.com"
        self.testnet_url = "https://testnet.binancefuture.com"
        
        # 当前使用的网络
        self.current_network = None
        self.base_url = None
        self.is_mainnet = False
        
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        })

        # 设置超时和重试
        self.session.timeout = 10

        # 禁用代理（如果代理有问题）
        self.session.proxies = {
            'http': None,
            'https': None
        }
        
        self.server_time_offset = 0
        self.last_sync_time = 0
        self.connected = False
        
        # 自动选择最佳网络
        self._select_best_network()
        
    def _test_network(self, base_url: str, network_name: str) -> bool:
        """测试网络连接（增强版）"""
        logger.info(f"🔍 测试 {network_name} 连接...")

        try:
            # 先禁用代理尝试直连
            original_proxies = self.session.proxies.copy()
            self.session.proxies = {'http': None, 'https': None}

            # 测试基础连接
            response = self.session.get(f"{base_url}/fapi/v1/ping", timeout=10)
            if response.status_code != 200:
                logger.warning(f"⚠️ {network_name} ping失败")
                return False

            # 测试时间同步
            response = self.session.get(f"{base_url}/fapi/v1/time", timeout=10)
            if response.status_code != 200:
                logger.warning(f"⚠️ {network_name} 时间同步失败")
                return False

            # 测试API认证
            server_time = response.json()['serverTime']
            params = {
                'timestamp': server_time,
                'recvWindow': 60000
            }
            query_string = urlencode(params)
            signature = hmac.new(
                self.api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            params['signature'] = signature

            response = self.session.get(f"{base_url}/fapi/v2/account", params=params, timeout=10)
            if response.status_code == 200:
                logger.info(f"✅ {network_name} 连接成功（直连模式）")
                return True
            else:
                logger.warning(f"⚠️ {network_name} 认证失败: {response.status_code}")
                return False

        except requests.exceptions.ProxyError:
            logger.warning(f"⚠️ {network_name} 代理连接失败，已切换直连")
            return False
        except requests.exceptions.ConnectionError as e:
            logger.warning(f"⚠️ {network_name} 网络连接失败: {e}")
            return False
        except Exception as e:
            logger.warning(f"⚠️ {network_name} 连接测试失败: {e}")
            return False
    
    def _select_best_network(self):
        """强制使用实盘网络"""
        logger.info("🔍 连接实盘网络...")

        # 强制使用主网（实盘）
        if self._test_network(self.mainnet_url, "实盘主网"):
            self.base_url = self.mainnet_url
            self.current_network = "实盘主网"
            self.is_mainnet = True
            logger.warning("🚨 使用实盘主网进行真实交易")
        else:
            logger.error("❌ 实盘网络连接失败")
            logger.error("💡 请检查网络连接或使用VPN")
            raise Exception("无法连接到实盘网络")
        
        # 同步时间
        self._sync_server_time()
    
    def _sync_server_time(self):
        """同步服务器时间"""
        try:
            response = self.session.get(f"{self.base_url}/fapi/v1/time", timeout=10)
            response.raise_for_status()
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            self.server_time_offset = server_time - local_time
            self.last_sync_time = time.time()
            logger.info(f"✅ {self.current_network}时间同步成功，偏移: {self.server_time_offset}ms")
        except Exception as e:
            logger.warning(f"⚠️ {self.current_network}时间同步失败: {e}")
            self.server_time_offset = 0
    
    def _get_timestamp(self) -> int:
        """获取同步后的时间戳"""
        if time.time() - self.last_sync_time > 300:
            self._sync_server_time()
        return int(time.time() * 1000) + self.server_time_offset
    
    def _generate_signature(self, params: dict) -> str:
        """生成API签名"""
        query_string = urlencode(params)
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, method: str, endpoint: str, params: dict = None, signed: bool = False, retries: int = 3) -> Optional[dict]:
        """发送API请求（带重试机制）"""
        if params is None:
            params = {}

        if signed:
            params['timestamp'] = self._get_timestamp()
            params['signature'] = self._generate_signature(params)

        url = f"{self.base_url}{endpoint}"

        for attempt in range(retries):
            try:
                if method == "GET":
                    response = self.session.get(url, params=params, timeout=15)
                elif method == "POST":
                    response = self.session.post(url, params=params, timeout=15)
                elif method == "DELETE":
                    response = self.session.delete(url, params=params, timeout=15)
                else:
                    return None

                response.raise_for_status()
                return response.json()

            except requests.exceptions.ProxyError as e:
                if attempt == 0:
                    logger.warning(f"代理连接失败，尝试直连...")
                    # 禁用代理重试
                    self.session.proxies = {'http': None, 'https': None}
                continue
            except requests.exceptions.ConnectionError as e:
                if attempt < retries - 1:
                    logger.warning(f"连接失败，{2}秒后重试 ({attempt + 1}/{retries})")
                    time.sleep(2)
                    continue
                else:
                    logger.error(f"{self.current_network}连接失败: {e}")
                    return None
            except Exception as e:
                if attempt < retries - 1:
                    logger.warning(f"请求失败，1秒后重试 ({attempt + 1}/{retries}): {e}")
                    time.sleep(1)
                    continue
                else:
                    logger.error(f"{self.current_network}API请求失败: {e}")
                    return None

        return None

    def place_stop_order(self, symbol: str, side: str, quantity: float, stop_price: float, order_type: str = "STOP_LOSS") -> Optional[dict]:
        """下止损/止盈订单"""
        if order_type == "TAKE_PROFIT":
            params = {
                "symbol": symbol,
                "side": side,
                "type": "TAKE_PROFIT_MARKET",
                "quantity": f"{quantity:.0f}",
                "stopPrice": f"{stop_price:.4f}",
                "timeInForce": "GTC"
            }
        else:  # STOP_LOSS
            params = {
                "symbol": symbol,
                "side": side,
                "type": "STOP_MARKET",
                "quantity": f"{quantity:.0f}",
                "stopPrice": f"{stop_price:.4f}",
                "timeInForce": "GTC"
            }

        # 减少止损止盈设置的详细日志
        # network_type = "🚨实盘" if self.is_mainnet else "🧪测试网"
        # logger.info(f"{network_type}设置止损止盈: {side} {quantity} @ 止损价{stop_price:.4f}")

        return self._make_request("POST", "/fapi/v1/order", params, signed=True)

    def cancel_order(self, symbol: str, order_id: str) -> Optional[dict]:
        """取消订单"""
        params = {
            "symbol": symbol,
            "orderId": order_id
        }
        return self._make_request("DELETE", "/fapi/v1/order", params, signed=True)

    def get_open_orders(self, symbol: str) -> Optional[list]:
        """获取挂单列表"""
        params = {"symbol": symbol}
        return self._make_request("GET", "/fapi/v1/openOrders", params, signed=True)

    def get_order_status(self, symbol: str, order_id: str) -> Optional[dict]:
        """获取订单状态"""
        params = {
            "symbol": symbol,
            "orderId": order_id
        }
        return self._make_request("GET", "/fapi/v1/order", params, signed=True)

    def cancel_all_orders(self, symbol: str) -> Optional[dict]:
        """取消所有挂单"""
        params = {"symbol": symbol}
        return self._make_request("DELETE", "/fapi/v1/allOpenOrders", params, signed=True)

    def force_clean_orders(self, symbol: str) -> bool:
        """强制清理所有挂单（多重尝试）"""
        logger.info("🧹 开始强制清理挂单...")

        # 第一次尝试：批量取消
        cancel_result = self.cancel_all_orders(symbol)
        if cancel_result:
            logger.info("✅ 批量清理成功")
            return True

        # 第二次尝试：获取并逐个取消
        open_orders = self.get_open_orders(symbol)
        if not open_orders:
            logger.info("✅ 没有发现挂单")
            return True

        logger.info(f"📋 发现 {len(open_orders)} 个挂单，逐个清理...")
        success_count = 0
        for order in open_orders:
            cancel_result = self.cancel_order(symbol, order['orderId'])
            if cancel_result:
                logger.info(f"✅ 已取消: {order['orderId']} ({order['type']})")
                success_count += 1
            else:
                logger.error(f"❌ 取消失败: {order['orderId']}")

        logger.info(f"🎯 清理结果: {success_count}/{len(open_orders)} 个订单已取消")
        return success_count == len(open_orders)
    
    def get_account_info(self) -> Optional[dict]:
        """获取永续合约账户信息"""
        return self._make_request("GET", "/fapi/v2/account", signed=True)

    def get_price(self, symbol: str = "ADAUSDT") -> Optional[float]:
        """获取当前价格"""
        result = self._make_request("GET", "/fapi/v1/ticker/price", {"symbol": symbol})
        if result and 'price' in result:
            return float(result['price'])
        return None

    def get_klines(self, symbol: str = "ADAUSDT", interval: str = "1m", limit: int = 100) -> Optional[list]:
        """获取K线数据"""
        params = {"symbol": symbol, "interval": interval, "limit": limit}
        return self._make_request("GET", "/fapi/v1/klines", params)

    def place_limit_order(self, symbol: str, side: str, quantity: float, price: float) -> Optional[dict]:
        """下永续合约限价单"""
        params = {
            "symbol": symbol,
            "side": side,
            "type": "LIMIT",
            "quantity": f"{quantity:.0f}",  # 整数数量
            "price": f"{price:.4f}",  # 精确价格
            "timeInForce": "IOC"  # 立即成交或取消
        }

        network_type = "🚨实盘" if self.is_mainnet else "🧪测试网"
        logger.warning(f"{network_type}永续合约下单: {side} {quantity} {symbol}")

        return self._make_request("POST", "/fapi/v1/order", params, signed=True)

    def set_leverage(self, symbol: str, leverage: int) -> Optional[dict]:
        """设置杠杆倍数"""
        params = {
            "symbol": symbol,
            "leverage": leverage
        }
        return self._make_request("POST", "/fapi/v1/leverage", params, signed=True)

    def set_margin_type(self, symbol: str, margin_type: str = "ISOLATED") -> Optional[dict]:
        """设置保证金模式"""
        params = {
            "symbol": symbol,
            "marginType": margin_type
        }
        return self._make_request("POST", "/fapi/v1/marginType", params, signed=True)

    def get_position_info(self, symbol: str) -> Optional[dict]:
        """获取持仓信息"""
        params = {"symbol": symbol}
        result = self._make_request("GET", "/fapi/v2/positionRisk", params, signed=True)
        if result:
            for pos in result:
                if pos['symbol'] == symbol and float(pos['positionAmt']) != 0:
                    return pos
        return None

class SmartAutoTrading:
    """🤖 智能自动交易系统"""
    
    def __init__(self, api_key: str, api_secret: str):
        self.symbol = "ADAUSDT"
        
        # 动态头寸规模参数
        self.base_balance_usage_pct = 0.20  # 基础仓位：20%
        self.min_balance_usage_pct = 0.10   # 最小仓位：10%
        self.max_balance_usage_pct = 0.30   # 最大仓位：30%
        self.current_balance_usage_pct = self.base_balance_usage_pct  # 当前动态仓位
        self.min_confidence = 0.85  # 提高质量门槛，减少低胜率交易
        self.leverage = 50.0  # 适中杠杆，平衡风险和收益

        # 动态止盈止损参数（将在初始化时计算）
        self.stop_loss_ratio = 0.0006  # 默认0.06%，将被动态计算覆盖
        self.take_profit_ratio = 0.0010  # 默认0.10%，将被动态计算覆盖
        self.trading_fee_rate = 0.0005  # 0.05% 实盘Taker手续费（币安永续合约）

        # 5U小资金账户安全限制
        self.min_balance_usdt = 2.0  # 适应5U账户
        self.max_trades_per_hour = 60  # 保持高频交易频率
        
        self.api = SmartTradingAPI(api_key, api_secret)
        
        # 交易状态
        self.current_position = None
        self.last_trade_time = None
        self.total_trades = 0
        self.winning_trades = 0
        self.trade_history = []
        
        # 账户信息
        self.initial_usdt = 0
        self.current_usdt = 0
        self.initial_ada = 0
        self.current_ada = 0
        
        # 实时监控
        self.is_running = False
        self.price_monitor_thread = None
        self.price_queue = queue.Queue()
        
        # 安全控制
        self.trades_this_hour = []

        # 市场分析数据
        self.market_volatility = 0.0
        self.avg_1min_change = 0.0
        
    def initialize_system(self) -> bool:
        """初始化系统"""
        logger.info(f"🔧 初始化智能交易系统 ({self.api.current_network})...")
        
        # 获取永续合约账户信息
        account_info = self.api.get_account_info()
        if not account_info:
            logger.error("❌ 无法获取账户信息")
            return False

        # 永续合约账户结构不同
        self.initial_usdt = float(account_info.get('totalWalletBalance', 0))
        self.current_usdt = float(account_info.get('availableBalance', 0))
        self.initial_ada = 0  # 永续合约不需要持有ADA
        self.current_ada = 0
        
        # 安全检查
        if self.initial_usdt < self.min_balance_usdt:
            logger.error(f"❌ USDT余额不足: {self.initial_usdt} < {self.min_balance_usdt}")
            return False
        
        network_type = "🚨实盘" if self.api.is_mainnet else "🧪测试网"
        logger.info(f"✅ {network_type}永续合约系统初始化成功")
        logger.info(f"   钱包总余额: {self.initial_usdt} USDT")
        logger.info(f"   可用余额: {self.current_usdt} USDT")

        # 分析市场波动性，动态计算止盈止损
        if not self.analyze_market_volatility():
            logger.warning("⚠️ 使用默认止盈止损参数")

        # 设置永续合约参数
        logger.info("🔧 设置永续合约参数...")

        # 检查并清理现有仓位和挂单
        existing_position = self.api.get_position_info(self.symbol)
        if existing_position and float(existing_position['positionAmt']) != 0:
            logger.warning(f"⚠️ 检测到现有仓位: {existing_position['positionAmt']} ADA")
            logger.warning("🔄 系统将监控现有仓位，不会开新仓")
        else:
            # 如果没有仓位，清理所有挂单（防止孤儿订单）
            open_orders = self.api.get_open_orders(self.symbol)
            if open_orders:
                logger.warning(f"🚨 发现 {len(open_orders)} 个孤儿挂单（无对应仓位）")
                for order in open_orders:
                    logger.warning(f"   孤儿订单: {order['orderId']} {order['type']} {order['side']} @ {order.get('stopPrice', order.get('price', 'N/A'))}")

                logger.info("🧹 清理所有孤儿挂单...")
                cancel_result = self.api.cancel_all_orders(self.symbol)
                if cancel_result:
                    logger.info("✅ 所有孤儿挂单已清理")
                else:
                    logger.error("❌ 孤儿挂单清理失败，请手动检查")

        # 设置保证金模式为逐仓
        margin_result = self.api.set_margin_type(self.symbol, "ISOLATED")
        if margin_result:
            logger.info("✅ 保证金模式设置为逐仓")
        else:
            logger.warning("⚠️ 保证金模式设置失败（可能已经是逐仓）")

        # 设置杠杆倍数
        leverage_result = self.api.set_leverage(self.symbol, int(self.leverage))
        if leverage_result:
            logger.info(f"✅ 杠杆设置为 {self.leverage}x")
        else:
            logger.warning(f"⚠️ 杠杆设置失败")

        return True

    def analyze_market_volatility(self) -> bool:
        """分析市场波动性，动态计算止盈止损（超紧剥头皮专用）"""
        logger.info("📊 分析近1小时市场波动性（超紧剥头皮模式）...")

        try:
            # 获取近1小时的1分钟K线数据 (1小时 × 60分钟 = 60根K线)
            # 超紧剥头皮需要基于最短期的市场行为
            klines = self.api.get_klines(self.symbol, "1m", 60)
            if not klines or len(klines) < 30:
                logger.warning("⚠️ K线数据不足，使用默认参数")
                return False

            # 计算1分钟价格变化
            price_changes = []
            for i in range(1, len(klines)):
                prev_close = float(klines[i-1][4])
                curr_close = float(klines[i][4])
                change_pct = abs(curr_close - prev_close) / prev_close
                price_changes.append(change_pct)

            # 计算统计数据
            price_changes = np.array(price_changes)
            self.avg_1min_change = np.mean(price_changes)
            self.market_volatility = np.std(price_changes)

            # 计算分位数（超紧剥头皮专用）
            p40 = np.percentile(price_changes, 40)  # 40%分位数（超紧止损）
            p45 = np.percentile(price_changes, 45)  # 45%分位数
            p50 = np.percentile(price_changes, 50)  # 50%分位数（中位数）
            p70 = np.percentile(price_changes, 70)  # 70%分位数（超紧止盈）
            p75 = np.percentile(price_changes, 75)  # 75%分位数

            # 剥头皮专用计算：基于实际1分钟波动
            fee_buffer = self.trading_fee_rate * 1.5  # 适度手续费缓冲

            # 剥头皮止损：使用30%分位数（更紧密跟随市场）
            scalping_stop_loss = max(p40 * 0.8, 0.0008)  # 基于40%分位数，最小0.08%
            self.stop_loss_ratio = scalping_stop_loss + fee_buffer

            # 实盘优化止盈：1.5:1比例，更容易触发
            self.take_profit_ratio = self.stop_loss_ratio * 1.5  # 1.5:1比例，提高止盈触发概率

            # 确保止盈大于止损
            if self.take_profit_ratio <= self.stop_loss_ratio:
                self.take_profit_ratio = self.stop_loss_ratio * 1.5

            # 优化胜率的止盈止损范围（避免频繁止损）
            self.stop_loss_ratio = max(0.0030, min(0.0040, self.stop_loss_ratio))  # 0.30%-0.40%止损（避免噪音）
            self.take_profit_ratio = max(0.0045, min(0.0060, self.take_profit_ratio))  # 0.45%-0.60%止盈（1.5:1比例）

            logger.info(f"📈 市场分析结果:")
            logger.info(f"   平均1分钟变化: {self.avg_1min_change:.4%}")
            logger.info(f"   市场波动率: {self.market_volatility:.4%}")
            logger.info(f"   超紧剥头皮分位数: P40={p40:.4%}, P45={p45:.4%}, P50={p50:.4%}, P70={p70:.4%}, P75={p75:.4%}")
            logger.info(f"   动态止损: {self.stop_loss_ratio:.4%} (包含{fee_buffer:.4%}手续费缓冲)")
            logger.info(f"   动态止盈: {self.take_profit_ratio:.4%} (包含{fee_buffer:.4%}手续费补偿)")

            return True

        except Exception as e:
            logger.error(f"❌ 市场分析失败: {e}")
            return False

    def start_price_monitor(self):
        """启动价格监控"""
        def price_monitor():
            while self.is_running:
                try:
                    price = self.api.get_price(self.symbol)
                    if price:
                        self.price_queue.put({
                            'timestamp': datetime.now(),
                            'price': price
                        })
                    time.sleep(0.5 if self.current_position else 2)  # 持仓时0.5秒，无仓位时2秒
                except Exception as e:
                    # 减少错误日志频率
                    if hasattr(self, '_last_error_time'):
                        if time.time() - self._last_error_time > 30:  # 30秒内只打印一次错误
                            logger.error(f"价格监控错误: {e}")
                            self._last_error_time = time.time()
                    else:
                        logger.error(f"价格监控错误: {e}")
                        self._last_error_time = time.time()
                    time.sleep(3)
        
        self.price_monitor_thread = threading.Thread(target=price_monitor, daemon=True)
        self.price_monitor_thread.start()
        logger.info("✅ 智能价格监控已启动")
    
    def get_latest_price(self) -> Optional[float]:
        """获取最新价格"""
        try:
            latest_price = None
            while not self.price_queue.empty():
                price_data = self.price_queue.get_nowait()
                latest_price = price_data['price']
            return latest_price if latest_price else self.api.get_price(self.symbol)
        except Exception:
            return self.api.get_price(self.symbol)
    
    def calculate_signal(self) -> Tuple[str, float]:
        """计算交易信号（验证成功的策略）"""
        try:
            klines = self.api.get_klines(self.symbol, "1m", 20)
            if not klines or len(klines) < 10:
                return "HOLD", 0.0
            
            closes = [float(k[4]) for k in klines]
            current_price = closes[-1]
            prev_price_1 = closes[-2]
            prev_price_3 = closes[-4] if len(closes) >= 4 else closes[-2]
            
            change_1 = (current_price - prev_price_1) / prev_price_1
            change_3 = (current_price - prev_price_3) / prev_price_3
            ma_5 = np.mean(closes[-5:])
            
            score = 0
            confidence_factors = []
            
            if change_1 > 0.0002:
                score += 3
                confidence_factors.append(0.25)
            elif change_1 < -0.0002:
                score -= 3
                confidence_factors.append(0.25)
            
            if change_3 > 0.0003:
                score += 2
                confidence_factors.append(0.15)
            elif change_3 < -0.0003:
                score -= 2
                confidence_factors.append(0.15)
            
            if current_price > ma_5 * 1.0001:
                score += 1
                confidence_factors.append(0.10)
            elif current_price < ma_5 * 0.9999:
                score -= 1
                confidence_factors.append(0.10)
            
            if score >= 3:
                direction = "LONG"
                base_confidence = 0.70
            elif score <= -3:
                direction = "SHORT"
                base_confidence = 0.70
            else:
                direction = "HOLD"
                base_confidence = 0.5
            
            if direction != "HOLD":
                confidence_boost = sum(confidence_factors)
                final_confidence = min(0.90, base_confidence + confidence_boost)
            else:
                final_confidence = base_confidence
            
            return direction, final_confidence
            
        except Exception as e:
            logger.error(f"信号计算失败: {e}")
            return "HOLD", 0.0
    
    def can_trade(self) -> bool:
        """检查是否可以交易"""
        # 检查系统记录的持仓
        if self.current_position:
            return False

        # 检查实际持仓（防止重复开仓）
        actual_position = self.api.get_position_info(self.symbol)
        if actual_position and float(actual_position['positionAmt']) != 0:
            logger.warning(f"⚠️ 检测到实际持仓: {actual_position['positionAmt']} ADA，跳过开仓")
            return False
        
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < 120:  # 2分钟间隔，提高交易质量
                return False
        
        # 检查每小时交易限制
        current_time = datetime.now()
        self.trades_this_hour = [t for t in self.trades_this_hour 
                                if (current_time - t).total_seconds() < 3600]
        
        if len(self.trades_this_hour) >= self.max_trades_per_hour:
            logger.warning("⚠️ 达到每小时交易限制")
            return False
        
        if self.current_usdt < self.min_balance_usdt:
            logger.warning(f"⚠️ USDT余额不足: {self.current_usdt} < {self.min_balance_usdt}")
            return False

        # 检查余额是否足够开仓（5U小资金账户）
        required_margin = self.current_usdt * self.current_balance_usage_pct
        if required_margin < 0.3:  # 5U账户至少需要0.3 USDT保证金
            logger.warning(f"⚠️ 保证金不足: {required_margin:.2f} < 0.3 USDT")
            return False
        
        return True

    def calculate_dynamic_position_size(self, confidence: float, market_volatility: float) -> float:
        """动态计算头寸规模"""
        # 基于置信度的调整
        confidence_factor = (confidence - 0.5) * 2  # 将0.5-1.0映射到0-1.0
        confidence_adjustment = confidence_factor * 0.15  # 最大15%调整

        # 基于市场波动率的调整
        volatility_factor = min(market_volatility / 0.01, 1.0)  # 1%波动率为基准
        volatility_adjustment = -volatility_factor * 0.05  # 高波动率减少仓位，最大减少5%

        # 基于近期胜率的调整
        recent_win_rate = self.get_recent_win_rate()
        win_rate_adjustment = 0
        if recent_win_rate > 0.6:  # 胜率>60%，增加仓位
            win_rate_adjustment = 0.05
        elif recent_win_rate < 0.4:  # 胜率<40%，减少仓位
            win_rate_adjustment = -0.05

        # 计算最终仓位比例
        dynamic_usage = (self.base_balance_usage_pct +
                        confidence_adjustment +
                        volatility_adjustment +
                        win_rate_adjustment)

        # 限制在最小最大范围内
        dynamic_usage = max(self.min_balance_usage_pct,
                           min(self.max_balance_usage_pct, dynamic_usage))

        logger.info(f"📊 动态仓位计算:")
        logger.info(f"   基础仓位: {self.base_balance_usage_pct:.1%}")
        logger.info(f"   置信度调整: {confidence_adjustment:+.1%} (置信度: {confidence:.1%})")
        logger.info(f"   波动率调整: {volatility_adjustment:+.1%} (波动率: {market_volatility:.2%})")
        logger.info(f"   胜率调整: {win_rate_adjustment:+.1%} (近期胜率: {recent_win_rate:.1%})")
        logger.info(f"   最终仓位: {dynamic_usage:.1%}")

        return dynamic_usage

    def get_recent_win_rate(self) -> float:
        """获取最近10笔交易的胜率"""
        if len(self.trade_history) < 3:
            return 0.5  # 默认50%

        recent_trades = self.trade_history[-10:]  # 最近10笔
        winning_trades = sum(1 for trade in recent_trades if trade['pnl_amount'] > 0)
        return winning_trades / len(recent_trades)

    def calculate_quantity(self, direction: str, entry_price: float, confidence: float) -> float:
        """计算交易数量（动态头寸规模 + 杠杆 + 余额检查）"""
        # 动态计算仓位比例
        self.current_balance_usage_pct = self.calculate_dynamic_position_size(confidence, self.market_volatility)

        # 使用动态仓位比例作为保证金
        margin_amount = self.current_usdt * self.current_balance_usage_pct

        # 使用杠杆计算仓位价值
        position_value = margin_amount * self.leverage
        ada_quantity = position_value / entry_price

        # 限制最大数量，避免超过交易限制
        max_ada_quantity = 5000.0  # 最大5,000 ADA，约$3,000价值
        ada_quantity = min(ada_quantity, max_ada_quantity)

        # 永续合约不需要检查ADA余额，只需要保证金
        # 所有方向都使用USDT作为保证金

        # 确保满足最小数量
        if ada_quantity < 0.1:
            ada_quantity = 0.1

        # 永续合约调整到整数
        ada_quantity = round(ada_quantity, 0)

        # 减少详细计算日志，只在需要时显示
        # logger.info(f"💰 仓位计算: 保证金${margin_amount:.2f} × {self.leverage}x = 仓位价值${position_value:.2f}")
        # logger.info(f"📊 数量: {ada_quantity:.1f} ADA (约${ada_quantity * entry_price:.2f})")
        # logger.info(f"💳 永续合约余额检查: 可用USDT={self.current_usdt:.2f}")

        return ada_quantity
    
    def execute_trade(self, direction: str, confidence: float, entry_price: float):
        """执行智能交易"""
        ada_quantity = self.calculate_quantity(direction, entry_price, confidence)
        side = "BUY" if direction == "LONG" else "SELL"
        
        # 计算止损止盈
        if direction == "LONG":
            stop_loss = entry_price * (1 - self.stop_loss_ratio)
            take_profit = entry_price * (1 + self.take_profit_ratio)
        else:
            stop_loss = entry_price * (1 + self.stop_loss_ratio)
            take_profit = entry_price * (1 - self.take_profit_ratio)
        
        network_type = "🚨实盘" if self.api.is_mainnet else "🧪测试网"
        logger.warning(f"🚀 执行{network_type}交易: {direction} @ {entry_price:.4f}")

        # 使用限价单，价格稍微有利一点确保成交
        if direction == "LONG":
            limit_price = entry_price * 1.0002  # 买入价格稍高0.02%
        else:
            limit_price = entry_price * 0.9998  # 卖出价格稍低0.02%

        order_result = self.api.place_limit_order(self.symbol, side, ada_quantity, limit_price)
        
        if order_result:
            logger.warning(f"✅ {network_type}订单成功: {order_result.get('orderId', 'N/A')}")

            # 立即设置止损止盈订单
            close_side = "SELL" if direction == "LONG" else "BUY"

            # 设置止损订单
            stop_order = self.api.place_stop_order(self.symbol, close_side, ada_quantity, stop_loss, "STOP_LOSS")
            stop_order_id = stop_order.get('orderId') if stop_order else None
            if stop_order:
                logger.info(f"✅ 止损订单设置成功: {stop_order_id}")
            else:
                logger.error("❌ 止损订单设置失败")

            # 设置止盈订单
            take_order = self.api.place_stop_order(self.symbol, close_side, ada_quantity, take_profit, "TAKE_PROFIT")
            take_order_id = take_order.get('orderId') if take_order else None
            if take_order:
                logger.info(f"✅ 止盈订单设置成功: {take_order_id}")
            else:
                logger.error("❌ 止盈订单设置失败")

            self.current_position = {
                'direction': direction,
                'entry_price': entry_price,
                'entry_time': datetime.now(),
                'quantity': ada_quantity,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'confidence': confidence,
                'order_id': order_result.get('orderId'),
                'stop_order_id': stop_order_id,
                'take_order_id': take_order_id,
                'network': self.api.current_network
            }

            self.total_trades += 1
            self.last_trade_time = datetime.now()
            self.trades_this_hour.append(datetime.now())

            logger.warning(f"   置信度: {confidence:.1%}")
            logger.warning(f"   数量: {ada_quantity:.1f} ADA")
            logger.warning(f"   杠杆: {self.leverage}x")
            logger.warning(f"   仓位价值: ${ada_quantity * entry_price:.2f}")
            logger.warning(f"   止损: {stop_loss:.4f} ({self.stop_loss_ratio:.2%}) 订单ID: {stop_order_id}")
            logger.warning(f"   止盈: {take_profit:.4f} ({self.take_profit_ratio:.2%}) 订单ID: {take_order_id}")
        else:
            logger.error(f"❌ {network_type}订单失败")
    
    def check_exit_conditions(self, current_price: float) -> bool:
        """检查退出条件（智能订单管理）"""
        if not self.current_position:
            return False

        pos = self.current_position

        # 智能订单状态检查
        order_execution_result = self._check_order_execution_status(pos)

        if order_execution_result['executed']:
            logger.warning(f"⚡ 检测到{order_execution_result['type']}订单执行！立即启动智能清理")

            # 立即清理所有相关挂单
            cleanup_success = self._intelligent_order_cleanup(pos, order_execution_result)

            if cleanup_success:
                logger.info("✅ 智能订单清理完成")
            else:
                logger.error("❌ 订单清理存在问题，请检查")

            # 记录交易结果
            self.close_position(current_price, order_execution_result['type'])
            return True

    def _check_order_execution_status(self, pos: dict) -> dict:
        """智能检查订单执行状态"""
        result = {
            'executed': False,
            'type': None,
            'executed_orders': [],
            'pending_orders': []
        }

        # 检查止损订单
        if pos.get('stop_order_id'):
            stop_status = self.api.get_order_status(self.symbol, pos['stop_order_id'])
            if stop_status:
                if stop_status['status'] == 'FILLED':
                    logger.warning("🚨 止损订单已执行！")
                    result['executed'] = True
                    result['type'] = '止损'
                    result['executed_orders'].append({
                        'id': pos['stop_order_id'],
                        'type': 'STOP_LOSS',
                        'status': 'FILLED'
                    })
                elif stop_status['status'] in ['NEW', 'PARTIALLY_FILLED']:
                    result['pending_orders'].append({
                        'id': pos['stop_order_id'],
                        'type': 'STOP_LOSS',
                        'status': stop_status['status']
                    })

        # 检查止盈订单
        if pos.get('take_order_id'):
            take_status = self.api.get_order_status(self.symbol, pos['take_order_id'])
            if take_status:
                if take_status['status'] == 'FILLED':
                    logger.warning("🎯 止盈订单已执行！")
                    result['executed'] = True
                    result['type'] = '止盈'
                    result['executed_orders'].append({
                        'id': pos['take_order_id'],
                        'type': 'TAKE_PROFIT',
                        'status': 'FILLED'
                    })
                elif take_status['status'] in ['NEW', 'PARTIALLY_FILLED']:
                    result['pending_orders'].append({
                        'id': pos['take_order_id'],
                        'type': 'TAKE_PROFIT',
                        'status': take_status['status']
                    })

        return result

    def _intelligent_order_cleanup(self, pos: dict, execution_result: dict) -> bool:
        """智能订单清理系统"""
        logger.info("🧹 启动智能订单清理系统...")

        cleanup_success = True

        # 第一步：取消所有待执行的挂单
        if execution_result['pending_orders']:
            logger.info(f"📋 发现 {len(execution_result['pending_orders'])} 个待清理订单")
            for order in execution_result['pending_orders']:
                cancel_result = self.api.cancel_order(self.symbol, order['id'])
                if cancel_result:
                    logger.info(f"✅ 已取消{order['type']}订单: {order['id']}")
                else:
                    logger.error(f"❌ 取消{order['type']}订单失败: {order['id']}")
                    cleanup_success = False

        # 第二步：强制清理所有挂单（双重保障）
        logger.info("🔄 执行强制清理（双重保障）...")
        force_cleanup_result = self.api.force_clean_orders(self.symbol)
        if not force_cleanup_result:
            cleanup_success = False

        # 第三步：验证清理结果
        remaining_orders = self.api.get_open_orders(self.symbol)
        if remaining_orders:
            logger.error(f"⚠️ 仍有 {len(remaining_orders)} 个挂单未清理:")
            for order in remaining_orders:
                logger.error(f"   未清理订单: {order['orderId']} {order['type']} {order['side']}")
            cleanup_success = False
        else:
            logger.info("✅ 订单清理验证通过，无残留挂单")

        return cleanup_success

    def _check_position_and_cleanup(self, current_price: float) -> bool:
        """检查仓位状态并清理（备用机制）"""
        # 检查实际仓位是否还存在
        actual_position = self.api.get_position_info(self.symbol)
        if not actual_position or float(actual_position['positionAmt']) == 0:
            logger.warning("⚠️ 检测到仓位已被外部平仓，清理系统记录和挂单")

            # 强制清理所有挂单
            self.api.force_clean_orders(self.symbol)
            self.current_position = None
            return False

        # 传统的价格触发检查（作为备用）
        pos = self.current_position
        should_exit = False
        exit_reason = ""

        if pos['direction'] == "LONG":
            if current_price <= pos['stop_loss']:
                should_exit = True
                exit_reason = "价格止损"
            elif current_price >= pos['take_profit']:
                should_exit = True
                exit_reason = "价格止盈"
        else:
            if current_price >= pos['stop_loss']:
                should_exit = True
                exit_reason = "价格止损"
            elif current_price <= pos['take_profit']:
                should_exit = True
                exit_reason = "价格止盈"

        duration = (datetime.now() - pos['entry_time']).total_seconds()

        # 优化胜率的时间退出逻辑（给价格更多时间）
        if duration > 300:  # 5分钟后检查（平衡速度和胜率）
            # 计算当前盈亏
            if pos['direction'] == "LONG":
                unrealized_pnl_pct = (current_price - pos['entry_price']) / pos['entry_price']
            else:
                unrealized_pnl_pct = (pos['entry_price'] - current_price) / pos['entry_price']

            # 智能退出决策
            if unrealized_pnl_pct > 0:
                # 盈利状态：立即平仓获利
                should_exit = True
                exit_reason = "时间退出(盈利)"
                logger.warning(f"💰 时间到期且盈利{unrealized_pnl_pct:+.2%}，立即平仓获利")
            elif unrealized_pnl_pct > -0.0005:  # 亏损小于0.05%
                # 微亏状态：再等1分钟
                if duration > 360:  # 6分钟强制退出
                    should_exit = True
                    exit_reason = "时间退出(微亏)"
                    logger.warning(f"⏰ 微亏{unrealized_pnl_pct:+.2%}，6分钟强制退出")
            else:
                # 明显亏损：立即止损
                should_exit = True
                exit_reason = "时间退出(止损)"
                logger.warning(f"🚨 亏损{unrealized_pnl_pct:+.2%}，时间止损")

        if should_exit:
            logger.warning(f"⚠️ 备用机制触发: {exit_reason}")
            self.close_position(current_price, exit_reason)
            return True

        return False
    
    def close_position(self, exit_price: float, exit_reason: str):
        """平仓"""
        if not self.current_position:
            return

        pos = self.current_position

        # 第一步：强制清理所有挂单
        logger.info("🧹 平仓第一步：清理所有挂单")
        self.api.force_clean_orders(self.symbol)

        # 第二步：检查是否还有实际仓位需要平仓
        actual_position = self.api.get_position_info(self.symbol)
        if not actual_position or float(actual_position['positionAmt']) == 0:
            logger.info("✅ 仓位已被自动平仓（止盈/止损触发），无需手动平仓")
            self.current_position = None
            return

        # 第三步：手动平仓
        logger.info("🔄 平仓第二步：手动平仓剩余仓位")
        close_side = "SELL" if pos['direction'] == "LONG" else "BUY"

        # 使用限价单平仓，价格稍微有利确保成交
        if pos['direction'] == "LONG":
            close_price = exit_price * 0.9998  # 卖出价格稍低0.02%
        else:
            close_price = exit_price * 1.0002  # 买入价格稍高0.02%

        network_type = "🚨实盘" if self.api.is_mainnet else "🧪测试网"
        close_order = self.api.place_limit_order(self.symbol, close_side, pos['quantity'], close_price)
        
        if close_order:
            logger.warning(f"✅ {network_type}平仓订单: {close_order.get('orderId', 'N/A')}")
        
        # 计算盈亏（加入杠杆计算）
        if pos['direction'] == "LONG":
            pnl_pct = (exit_price - pos['entry_price']) / pos['entry_price']
        else:
            pnl_pct = (pos['entry_price'] - exit_price) / pos['entry_price']

        # 杠杆盈亏计算
        leveraged_pnl = pnl_pct * self.leverage
        position_value = pos['quantity'] * pos['entry_price']
        margin_used = position_value / self.leverage

        # 计算手续费（开仓 + 平仓）
        trading_fee = position_value * self.trading_fee_rate * 2  # 双倍手续费

        # 实际盈亏 = 杠杆盈亏 - 手续费
        gross_pnl = margin_used * leveraged_pnl
        pnl_amount = gross_pnl - trading_fee
        
        is_winner = pnl_amount > 0
        if is_winner:
            self.winning_trades += 1
        
        holding_time = (datetime.now() - pos['entry_time']).total_seconds()
        
        trade_record = {
            'trade_id': self.total_trades,
            'entry_time': pos['entry_time'],
            'exit_time': datetime.now(),
            'direction': pos['direction'],
            'entry_price': pos['entry_price'],
            'exit_price': exit_price,
            'quantity': pos['quantity'],
            'pnl_amount': pnl_amount,
            'pnl_pct': pnl_pct,
            'holding_time': holding_time,
            'is_winner': is_winner,
            'exit_reason': exit_reason,
            'confidence': pos['confidence'],
            'network': pos['network']
        }
        
        self.trade_history.append(trade_record)
        self.current_position = None
        
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        
        status = "✅" if is_winner else "❌"
        logger.warning(f"📈 {network_type}平仓: {pos['direction']} @ {exit_price:.4f} ({exit_reason})")
        logger.warning(f"   {status} 净盈亏: ${pnl_amount:+.2f} ({pnl_pct:+.2%})")
        logger.warning(f"   毛盈亏: ${gross_pnl:+.2f}")
        logger.warning(f"   手续费: ${trading_fee:.2f}")
        logger.warning(f"   杠杆盈亏: {leveraged_pnl:+.2%} ({self.leverage}x)")
        logger.warning(f"   持仓时间: {holding_time:.0f}秒")
        logger.warning(f"   胜率: {win_rate:.1%}")
    
    def run_smart_trading(self):
        """运行智能交易"""
        network_type = "🚨实盘" if self.api.is_mainnet else "🧪测试网"
        logger.warning(f"🚨 启动实盘智能自动交易系统 ({network_type})")
        logger.warning(f"� 使用真实资金进行交易")
        logger.warning(f"�📊 基于优化验证策略")

        # 用户输入运行时间
        try:
            duration_input = input("请输入运行时间(分钟，默认30): ").strip()
            duration_minutes = int(duration_input) if duration_input else 30
            if duration_minutes <= 0:
                duration_minutes = 30
        except ValueError:
            duration_minutes = 30
            print("输入无效，使用默认30分钟")

        logger.warning(f"🔥 杠杆: {self.leverage}x（5U账户优化）")
        logger.warning(f"📊 剥头皮止损: {self.stop_loss_ratio:.4%}, 止盈: {self.take_profit_ratio:.4%}")
        logger.warning(f"💰 动态仓位范围: {self.min_balance_usage_pct:.0%}-{self.max_balance_usage_pct:.0%}, 基础: {self.base_balance_usage_pct:.0%}")
        logger.warning(f"📈 市场波动率: {self.market_volatility:.4%}, 平均变化: {self.avg_1min_change:.4%}")

        # 5U账户盈利目标计算
        if self.current_usdt > 0:
            margin_per_trade = self.current_usdt * self.balance_usage_pct
            position_value = margin_per_trade * self.leverage
            expected_profit = position_value * self.take_profit_ratio
            profit_percentage = (expected_profit / self.current_usdt) * 100
            logger.warning(f"🎯 单笔预期盈利: ${expected_profit:.3f} (账户{profit_percentage:.1f}%)")
        else:
            logger.warning("⚠️ 账户余额为0，无法计算盈利目标")

        logger.warning(f"⏰ 运行时间: {duration_minutes} 分钟")
        
        if not self.initialize_system():
            return
        
        self.is_running = True
        self.start_price_monitor()
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        try:
            while datetime.now() < end_time and self.is_running:
                current_price = self.get_latest_price()
                if current_price is None:
                    time.sleep(3)
                    continue
                
                if self.current_position:
                    # 智能订单管理：优先检查订单执行状态
                    if self.check_exit_conditions(current_price):
                        continue

                    # 备用机制：检查仓位状态和价格触发
                    if self._check_position_and_cleanup(current_price):
                        continue

                    # 如果系统记录有仓位但实际检查发现没有，清除记录
                    if not self.current_position:
                        logger.warning("🔄 仓位已清除，继续寻找新交易机会")
                        continue
                    
                    pos = self.current_position
                    duration = (datetime.now() - pos['entry_time']).total_seconds()

                    if pos['direction'] == "LONG":
                        unrealized_pnl = (current_price - pos['entry_price']) / pos['entry_price']
                    else:
                        unrealized_pnl = (pos['entry_price'] - current_price) / pos['entry_price']

                    # 只在整数秒时打印，减少输出频率
                    if int(duration) % 5 == 0:  # 每5秒打印一次
                        logger.info(f"📊 持仓: {pos['direction']} @ {current_price:.4f} "
                                   f"({duration:.0f}秒) 浮盈: {unrealized_pnl:+.2%}")

                    time.sleep(0.5)  # 持仓时每0.5秒检查
                else:
                    if self.can_trade():
                        direction, confidence = self.calculate_signal()

                        if direction in ["LONG", "SHORT"] and confidence >= self.min_confidence:
                            self.execute_trade(direction, confidence, current_price)

                    time.sleep(2)  # 无仓位时每2秒检查
                
        except KeyboardInterrupt:
            logger.warning("⏹️ 用户中断智能交易")
        except Exception as e:
            logger.error(f"❌ 智能交易异常: {e}")
        finally:
            self.is_running = False
            
            if self.current_position:
                final_price = self.get_latest_price()
                if final_price:
                    self.close_position(final_price, "系统停止")

            # 系统停止时强制清理所有挂单
            logger.info("🧹 系统停止，清理所有挂单...")
            cancel_result = self.api.cancel_all_orders(self.symbol)
            if cancel_result:
                logger.info("✅ 系统停止时所有挂单已清理")

            self.show_results()
    
    def show_results(self):
        """显示详细结果分析"""
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        network_type = "🚨实盘" if self.api.is_mainnet else "🧪测试网"

        # 计算详细统计
        total_pnl = sum(trade['pnl_amount'] for trade in self.trade_history)
        winning_pnl = sum(trade['pnl_amount'] for trade in self.trade_history if trade['is_winner'])
        losing_pnl = sum(trade['pnl_amount'] for trade in self.trade_history if not trade['is_winner'])
        avg_win = winning_pnl / self.winning_trades if self.winning_trades > 0 else 0
        avg_loss = losing_pnl / (self.total_trades - self.winning_trades) if (self.total_trades - self.winning_trades) > 0 else 0

        # 统计退出原因
        exit_reasons = {}
        for trade in self.trade_history:
            reason = trade['exit_reason']
            exit_reasons[reason] = exit_reasons.get(reason, 0) + 1

        print("\n" + "="*70)
        print(f"🎉 智能自动交易完成 ({self.api.current_network})")
        print("="*70)
        print(f"📊 交易统计:")
        print(f"  总交易数: {self.total_trades}")
        print(f"  胜率: {win_rate:.1%}")
        print(f"  盈利交易: {self.winning_trades}笔")
        print(f"  亏损交易: {self.total_trades - self.winning_trades}笔")
        print(f"💰 盈亏分析:")
        print(f"  总盈亏: ${total_pnl:+.2f}")
        print(f"  平均盈利: ${avg_win:+.2f}")
        print(f"  平均亏损: ${avg_loss:+.2f}")
        print(f"  盈亏比: {abs(avg_win/avg_loss):.2f}:1" if avg_loss != 0 else "  盈亏比: N/A")
        print(f"📈 退出原因统计:")
        for reason, count in exit_reasons.items():
            print(f"  {reason}: {count}笔 ({count/self.total_trades:.1%})")
        print(f"💳 账户状态:")
        print(f"  初始USDT: {self.initial_usdt}")
        print(f"  当前USDT: {self.current_usdt}")
        print(f"  余额变化: ${self.current_usdt - self.initial_usdt:+.2f}")
        print(f"  初始ADA: {self.initial_ada}")
        print(f"  当前ADA: {self.current_ada}")
        print(f"🤖 智能验证:")
        print(f"  网络选择: {self.api.current_network}")
        print(f"  策略验证: 成功")

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        logger.error("❌ 配置文件 config.json 不存在，将使用手动输入模式")
        return None
    except json.JSONDecodeError:
        logger.error("❌ 配置文件格式错误，将使用手动输入模式")
        return None

if __name__ == "__main__":
    print("🚨 智能自动交易系统 - 实盘模式")
    print("📊 基于优化验证策略")
    print("🌐 强制使用实盘网络")
    print("🛡️ 智能风险控制")

    # 尝试加载配置文件
    config = load_config()

    if config and config.get('api_credentials'):
        # 从配置文件读取API密钥
        API_KEY = config['api_credentials']['api_key']
        API_SECRET = config['api_credentials']['api_secret']
        auto_confirm = config.get('trading_settings', {}).get('auto_confirm', False)

        print("\n✅ 已从配置文件加载API密钥")
        print(f"🔑 API Key: {API_KEY[:10]}...{API_KEY[-10:]}")

        if auto_confirm:
            print("🚀 自动确认模式：直接启动实盘交易")
            confirm = 'YES'
        else:
            print("\n⚠️  警告：这是实盘交易模式！")
            print("💰 将使用真实资金进行交易")
            confirm = input("\n🚨 确认使用实盘模式进行真实交易？(输入 'YES' 确认): ").strip()
    else:
        # 手动输入API密钥（备用方案）
        print("\n⚠️  警告：这是实盘交易模式！")
        print("💰 将使用真实资金进行交易")
        print("📋 请确保您已经:")
        print("   1. 准备好实盘API密钥")
        print("   2. 账户有足够余额")
        print("   3. 理解交易风险")

        print("\n🔑 请输入实盘API密钥:")
        API_KEY = input("API Key: ").strip()
        API_SECRET = input("Secret Key: ").strip()

        if not API_KEY or not API_SECRET:
            print("❌ API密钥不能为空")
            exit(1)

        confirm = input("\n🚨 确认使用实盘模式进行真实交易？(输入 'YES' 确认): ").strip()

    if confirm != "YES":
        print("❌ 已取消实盘交易")
        exit(1)

    trader = SmartAutoTrading(API_KEY, API_SECRET)

    print("\n🚀 启动实盘智能自动交易系统...")
    trader.run_smart_trading()
