"""
配置示例文件
请复制此文件为 config_local.py 并填入您的实际配置
"""

# Binance API配置
BINANCE_CONFIG = {
    'api_key': 'your_binance_api_key',
    'api_secret': 'your_binance_api_secret',
    'testnet': True  # 设置为True使用测试网络
}

# Twitter API配置
TWITTER_CONFIG = {
    'api_key': 'your_twitter_api_key',
    'api_secret': 'your_twitter_api_secret',
    'access_token': 'your_twitter_access_token',
    'access_secret': 'your_twitter_access_secret'
}

# NewsAPI配置
NEWSAPI_CONFIG = {
    'api_key': 'your_newsapi_key'
}

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'crypto_prediction',
    'user': 'postgres',
    'password': 'postgres'
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'file': 'logs/app.log',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 监控配置
MONITORING_CONFIG = {
    'sentry_dsn': '',  # 留空表示不使用Sentry
    'alert_email': '<EMAIL>',
    'performance_threshold': 0.7,
    'error_threshold': 0.1
}

# 性能配置
PERFORMANCE_CONFIG = {
    'batch_size': 1000,
    'num_workers': 4,
    'cache_ttl': 3600
}

# 模型配置
MODEL_CONFIG = {
    'version': '1.0.0',
    'path': 'models/',
    'window_size': 1000,
    'step_size': 100,
    'performance_threshold': 0.7
}

# 回测配置
BACKTEST_CONFIG = {
    'initial_capital': 10000,
    'commission': 0.001,
    'slippage': 0.001
}

# 情绪分析配置
SENTIMENT_CONFIG = {
    'twitter_weight': 0.4,
    'news_weight': 0.6,
    'update_interval': 900  # 15分钟
} 