#!/usr/bin/env python3
"""
实时监控面板
为剥头皮交易系统提供实时监控和可视化界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.animation import FuncAnimation
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("matplotlib未安装，图表功能不可用")
    MATPLOTLIB_AVAILABLE = False

import numpy as np
import threading
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import queue
from collections import deque

class TradingDashboard:
    """交易监控面板"""
    
    def __init__(self, trading_system=None):
        self.trading_system = trading_system
        
        # 数据存储
        self.price_data = deque(maxlen=100)
        self.pnl_data = deque(maxlen=100)
        self.trade_history = []
        self.performance_metrics = {}
        
        # 实时数据队列
        self.data_queue = queue.Queue()
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("剥头皮交易系统 - 实时监控面板")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1e1e1e')
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 启动数据更新
        self.start_data_updates()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', 
                       background='#1e1e1e', 
                       foreground='#ffffff', 
                       font=('Arial', 16, 'bold'))
        
        style.configure('Info.TLabel', 
                       background='#1e1e1e', 
                       foreground='#00ff00', 
                       font=('Arial', 12))
        
        style.configure('Warning.TLabel', 
                       background='#1e1e1e', 
                       foreground='#ffaa00', 
                       font=('Arial', 12))
        
        style.configure('Error.TLabel', 
                       background='#1e1e1e', 
                       foreground='#ff0000', 
                       font=('Arial', 12))
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🚀 剥头皮交易系统监控面板", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # 创建上半部分：状态和控制
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.create_status_panel(top_frame)
        self.create_control_panel(top_frame)
        
        # 创建中间部分：图表
        if MATPLOTLIB_AVAILABLE:
            middle_frame = ttk.Frame(main_frame)
            middle_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            self.create_charts(middle_frame)
        else:
            # 如果没有matplotlib，显示文本信息
            text_frame = ttk.Frame(main_frame)
            text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            ttk.Label(text_frame, text="图表功能需要安装matplotlib", style='Warning.TLabel').pack()
        
        # 创建下半部分：交易历史和风险指标
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.BOTH, expand=True)
        
        self.create_trade_history(bottom_frame)
        self.create_risk_indicators(bottom_frame)
    
    def create_status_panel(self, parent):
        """创建状态面板"""
        status_frame = ttk.LabelFrame(parent, text="系统状态", padding=10)
        status_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 系统状态
        self.system_status_label = ttk.Label(status_frame, text="系统状态: 未连接", style='Warning.TLabel')
        self.system_status_label.pack(anchor=tk.W)
        
        # 当前持仓
        self.position_label = ttk.Label(status_frame, text="当前持仓: 无", style='Info.TLabel')
        self.position_label.pack(anchor=tk.W)
        
        # 当前价格
        self.price_label = ttk.Label(status_frame, text="ADAUSDT: --", style='Info.TLabel')
        self.price_label.pack(anchor=tk.W)
        
        # 浮动盈亏
        self.unrealized_pnl_label = ttk.Label(status_frame, text="浮动盈亏: $0.00", style='Info.TLabel')
        self.unrealized_pnl_label.pack(anchor=tk.W)
        
        # 网络状态
        self.network_status_label = ttk.Label(status_frame, text="网络状态: 正常", style='Info.TLabel')
        self.network_status_label.pack(anchor=tk.W)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="交易控制", padding=10)
        control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 控制按钮
        self.start_button = ttk.Button(control_frame, text="启动交易", command=self.start_trading)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止交易", command=self.stop_trading)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.emergency_button = ttk.Button(control_frame, text="紧急停止", command=self.emergency_stop)
        self.emergency_button.pack(side=tk.LEFT, padx=5)
        
        # 参数显示
        params_frame = ttk.Frame(control_frame)
        params_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(params_frame, text="止损: 0.04%", style='Info.TLabel').pack(anchor=tk.W)
        ttk.Label(params_frame, text="止盈: 0.05%", style='Info.TLabel').pack(anchor=tk.W)
        ttk.Label(params_frame, text="杠杆: 125x", style='Info.TLabel').pack(anchor=tk.W)
    
    def create_charts(self, parent):
        """创建图表"""
        if not MATPLOTLIB_AVAILABLE:
            return
            
        chart_frame = ttk.Frame(parent)
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建matplotlib图表
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(12, 6), facecolor='#1e1e1e')
        self.fig.patch.set_facecolor('#1e1e1e')
        
        # 价格图表
        self.ax1.set_facecolor('#2e2e2e')
        self.ax1.set_title('ADAUSDT 实时价格', color='white')
        self.ax1.tick_params(colors='white')
        self.ax1.grid(True, alpha=0.3)
        
        # 盈亏图表
        self.ax2.set_facecolor('#2e2e2e')
        self.ax2.set_title('累计盈亏', color='white')
        self.ax2.tick_params(colors='white')
        self.ax2.grid(True, alpha=0.3)
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 启动动画
        self.animation = FuncAnimation(self.fig, self.update_charts, interval=1000, blit=False)
    
    def create_trade_history(self, parent):
        """创建交易历史"""
        history_frame = ttk.LabelFrame(parent, text="交易历史", padding=10)
        history_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 创建表格
        columns = ('时间', '方向', '入场价', '出场价', '盈亏', '持仓时间')
        self.trade_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        for col in columns:
            self.trade_tree.heading(col, text=col)
            self.trade_tree.column(col, width=100)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.trade_tree.yview)
        self.trade_tree.configure(yscrollcommand=scrollbar.set)
        
        self.trade_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_risk_indicators(self, parent):
        """创建风险指标"""
        risk_frame = ttk.LabelFrame(parent, text="风险指标", padding=10)
        risk_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 性能指标
        self.balance_label = ttk.Label(risk_frame, text="账户余额: $1000.00", style='Info.TLabel')
        self.balance_label.pack(anchor=tk.W)
        
        self.total_trades_label = ttk.Label(risk_frame, text="总交易数: 0", style='Info.TLabel')
        self.total_trades_label.pack(anchor=tk.W)
        
        self.win_rate_label = ttk.Label(risk_frame, text="胜率: 0.0%", style='Info.TLabel')
        self.win_rate_label.pack(anchor=tk.W)
        
        self.total_return_label = ttk.Label(risk_frame, text="总收益: 0.0%", style='Info.TLabel')
        self.total_return_label.pack(anchor=tk.W)
        
        # 风险指标
        ttk.Label(risk_frame, text="", style='Info.TLabel').pack()  # 分隔符
        
        self.consecutive_losses_label = ttk.Label(risk_frame, text="连续亏损: 0", style='Info.TLabel')
        self.consecutive_losses_label.pack(anchor=tk.W)
        
        self.daily_loss_label = ttk.Label(risk_frame, text="日亏损: $0.00", style='Info.TLabel')
        self.daily_loss_label.pack(anchor=tk.W)
        
        self.network_errors_label = ttk.Label(risk_frame, text="网络错误: 0", style='Info.TLabel')
        self.network_errors_label.pack(anchor=tk.W)
        
        # 风险警告
        self.risk_warning_label = ttk.Label(risk_frame, text="", style='Warning.TLabel')
        self.risk_warning_label.pack(anchor=tk.W, pady=(10, 0))
    
    def start_data_updates(self):
        """启动数据更新"""
        def update_worker():
            while True:
                try:
                    # 模拟数据更新
                    self.update_mock_data()
                    time.sleep(1)
                except Exception as e:
                    print(f"数据更新错误: {e}")
                    time.sleep(5)
        
        self.update_thread = threading.Thread(target=update_worker, daemon=True)
        self.update_thread.start()
        
        # 启动GUI更新
        self.update_gui()
    
    def update_mock_data(self):
        """更新模拟数据"""
        # 模拟价格数据
        if len(self.price_data) == 0:
            price = 0.5900
        else:
            price = self.price_data[-1] + np.random.normal(0, 0.0005)
        
        self.price_data.append(price)
        
        # 模拟盈亏数据
        if len(self.pnl_data) == 0:
            pnl = 0
        else:
            pnl = self.pnl_data[-1] + np.random.normal(0, 0.5)
        
        self.pnl_data.append(pnl)
        
        # 模拟交易记录
        if np.random.random() < 0.01:  # 1%概率产生新交易
            trade = {
                'time': datetime.now().strftime('%H:%M:%S'),
                'direction': np.random.choice(['LONG', 'SHORT']),
                'entry_price': price,
                'exit_price': price + np.random.normal(0, 0.0002),
                'pnl': np.random.normal(0, 2),
                'holding_time': np.random.randint(10, 120)
            }
            self.trade_history.append(trade)
            
            # 限制历史记录数量
            if len(self.trade_history) > 50:
                self.trade_history.pop(0)
    
    def update_charts(self, frame):
        """更新图表"""
        if not MATPLOTLIB_AVAILABLE or len(self.price_data) < 2:
            return
        
        # 清除图表
        self.ax1.clear()
        self.ax2.clear()
        
        # 价格图表
        times = list(range(len(self.price_data)))
        self.ax1.plot(times, list(self.price_data), color='#00ff00', linewidth=2)
        self.ax1.set_facecolor('#2e2e2e')
        self.ax1.set_title('ADAUSDT 实时价格', color='white')
        self.ax1.tick_params(colors='white')
        self.ax1.grid(True, alpha=0.3)
        
        # 盈亏图表
        self.ax2.plot(times, list(self.pnl_data), color='#ffaa00', linewidth=2)
        self.ax2.axhline(y=0, color='white', linestyle='--', alpha=0.5)
        self.ax2.set_facecolor('#2e2e2e')
        self.ax2.set_title('累计盈亏', color='white')
        self.ax2.tick_params(colors='white')
        self.ax2.grid(True, alpha=0.3)
        
        # 设置标签颜色
        for ax in [self.ax1, self.ax2]:
            ax.spines['bottom'].set_color('white')
            ax.spines['top'].set_color('white')
            ax.spines['right'].set_color('white')
            ax.spines['left'].set_color('white')
    
    def update_gui(self):
        """更新GUI显示"""
        try:
            # 更新状态标签
            if len(self.price_data) > 0:
                current_price = self.price_data[-1]
                self.price_label.config(text=f"ADAUSDT: {current_price:.4f}")
            
            if len(self.pnl_data) > 0:
                current_pnl = self.pnl_data[-1]
                color = 'Info.TLabel' if current_pnl >= 0 else 'Error.TLabel'
                self.unrealized_pnl_label.config(text=f"浮动盈亏: ${current_pnl:+.2f}", style=color)
            
            # 更新交易历史
            self.update_trade_history_display()
            
            # 更新风险指标
            self.update_risk_indicators()
            
        except Exception as e:
            print(f"GUI更新错误: {e}")
        
        # 每秒更新一次
        self.root.after(1000, self.update_gui)
    
    def update_trade_history_display(self):
        """更新交易历史显示"""
        # 清除现有项目
        for item in self.trade_tree.get_children():
            self.trade_tree.delete(item)
        
        # 添加最新的交易记录
        for trade in self.trade_history[-10:]:  # 只显示最近10笔
            pnl_color = 'green' if trade['pnl'] > 0 else 'red'
            self.trade_tree.insert('', 'end', values=(
                trade['time'],
                trade['direction'],
                f"{trade['entry_price']:.4f}",
                f"{trade['exit_price']:.4f}",
                f"${trade['pnl']:+.2f}",
                f"{trade['holding_time']}s"
            ), tags=(pnl_color,))
        
        # 设置颜色
        self.trade_tree.tag_configure('green', foreground='green')
        self.trade_tree.tag_configure('red', foreground='red')
    
    def update_risk_indicators(self):
        """更新风险指标"""
        # 计算统计数据
        total_trades = len(self.trade_history)
        winning_trades = sum(1 for trade in self.trade_history if trade['pnl'] > 0)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = sum(trade['pnl'] for trade in self.trade_history)
        current_balance = 1000 + total_pnl
        total_return = total_pnl / 1000
        
        # 更新标签
        self.balance_label.config(text=f"账户余额: ${current_balance:.2f}")
        self.total_trades_label.config(text=f"总交易数: {total_trades}")
        self.win_rate_label.config(text=f"胜率: {win_rate:.1%}")
        
        # 设置收益颜色
        return_color = 'Info.TLabel' if total_return >= 0 else 'Error.TLabel'
        self.total_return_label.config(text=f"总收益: {total_return:+.1%}", style=return_color)
        
        # 模拟风险指标
        consecutive_losses = 0
        for trade in reversed(self.trade_history):
            if trade['pnl'] < 0:
                consecutive_losses += 1
            else:
                break
        
        self.consecutive_losses_label.config(text=f"连续亏损: {consecutive_losses}")
        
        # 风险警告
        if consecutive_losses >= 3:
            self.risk_warning_label.config(text="⚠️ 连续亏损警告", style='Warning.TLabel')
        elif total_return < -0.05:
            self.risk_warning_label.config(text="⚠️ 亏损过大警告", style='Error.TLabel')
        else:
            self.risk_warning_label.config(text="✅ 风险正常", style='Info.TLabel')
    
    def start_trading(self):
        """启动交易"""
        self.system_status_label.config(text="系统状态: 运行中", style='Info.TLabel')
        messagebox.showinfo("交易控制", "交易已启动")
    
    def stop_trading(self):
        """停止交易"""
        self.system_status_label.config(text="系统状态: 已停止", style='Warning.TLabel')
        messagebox.showinfo("交易控制", "交易已停止")
    
    def emergency_stop(self):
        """紧急停止"""
        result = messagebox.askyesno("紧急停止", "确认紧急停止所有交易？")
        if result:
            self.system_status_label.config(text="系统状态: 紧急停止", style='Error.TLabel')
            messagebox.showwarning("紧急停止", "所有交易已紧急停止")
    
    def run(self):
        """运行面板"""
        self.root.mainloop()

def main():
    """主函数"""
    print("🖥️ 启动实时监控面板...")
    
    # 创建并运行面板
    dashboard = TradingDashboard()
    dashboard.run()

if __name__ == "__main__":
    main()
