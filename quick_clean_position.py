#!/usr/bin/env python3
"""
快速清理微小持仓脚本
"""

import json
import os
from datetime import datetime

def quick_clean_micro_position():
    """
    快速清理微小持仓
    """
    print("🧹 快速清理微小持仓")
    print("=" * 40)
    
    # 检查状态文件
    state_file = "trading_state.json"
    
    if os.path.exists(state_file):
        try:
            # 读取当前状态
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            current_position = state.get('position', 0)
            current_price = 104572.10  # 当前价格
            
            print(f"📊 当前状态:")
            print(f"   持仓: {current_position:.6f} BTC")
            
            if abs(current_position) > 0:
                position_value = abs(current_position) * current_price
                print(f"   价值: ${position_value:.4f}")
                
                if position_value < 1.0:
                    print(f"\n🧹 执行清理...")
                    
                    # 清理持仓
                    state['position'] = 0
                    state['entry_price'] = 0
                    state['entry_time'] = None
                    state['margin_used'] = 0
                    
                    # 添加清理记录
                    clean_record = {
                        'timestamp': datetime.now().isoformat(),
                        'action': 'QUICK_MICRO_CLEAN',
                        'original_position': current_position,
                        'position_value': position_value,
                        'reason': 'Quick cleanup of micro position'
                    }
                    
                    if 'trades' not in state:
                        state['trades'] = []
                    state['trades'].append(clean_record)
                    
                    # 保存更新的状态
                    with open(state_file, 'w', encoding='utf-8') as f:
                        json.dump(state, f, indent=2, ensure_ascii=False)
                    
                    print(f"✅ 清理完成!")
                    print(f"   新持仓: 0.000000 BTC")
                    print(f"   状态已保存")
                    
                    return True
                else:
                    print(f"⚠️ 持仓价值足够大，无需清理")
                    return False
            else:
                print(f"✅ 当前无持仓，无需清理")
                return False
                
        except Exception as e:
            print(f"❌ 读取状态文件失败: {str(e)}")
            return False
    else:
        print(f"❌ 状态文件不存在: {state_file}")
        return False

def manual_force_clean():
    """
    手动强制清理（直接修改）
    """
    print(f"\n🔧 手动强制清理")
    print("=" * 40)
    
    # 创建清理后的状态
    cleaned_state = {
        'position': 0,
        'entry_price': 0,
        'entry_time': None,
        'margin_used': 0,
        'capital': 90.10,  # 保持当前权益
        'initial_capital': 50.00,
        'last_cleaned': datetime.now().isoformat()
    }
    
    print(f"强制清理设置:")
    for key, value in cleaned_state.items():
        if key != 'last_cleaned':
            print(f"   {key}: {value}")
    
    return cleaned_state

if __name__ == "__main__":
    print("🧹 微小持仓快速清理工具")
    print("=" * 50)
    
    # 尝试自动清理
    success = quick_clean_micro_position()
    
    if not success:
        print(f"\n💡 如果自动清理失败，可以手动执行:")
        manual_state = manual_force_clean()
        
        print(f"\n📝 手动操作步骤:")
        print(f"1. 停止当前交易系统")
        print(f"2. 编辑 trading_state.json 文件")
        print(f"3. 设置 position: 0")
        print(f"4. 设置 margin_used: 0")
        print(f"5. 重启交易系统")
    
    print(f"\n🎯 清理的好处:")
    print(f"✅ 完全平仓，无残余持仓")
    print(f"✅ 释放所有保证金")
    print(f"✅ 清晰的账户状态")
    print(f"✅ 为下次交易做准备")
    
    print(f"\n💰 您的80.21%收益已经非常成功！")
    print(f"🎉 现在是时候完全平仓，享受胜利果实了！")
