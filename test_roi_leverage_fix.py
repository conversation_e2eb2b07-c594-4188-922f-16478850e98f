#!/usr/bin/env python3
"""
Test script to verify ROI and leverage calculation fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_roi_leverage_fix():
    """Test the ROI and leverage calculation fixes"""
    print("🧪 Testing ROI and Leverage Calculation Fixes")
    print("=" * 60)
    
    print("\n📊 User's Problem Analysis:")
    print("❌ Problem 1: 杠杆重复计算 - PnL被放大125倍")
    print("❌ Problem 2: ROI计算基数错误 - 分母使用错误值")
    print("✅ Expected: 小幅价格变动 → 合理ROI")
    
    # Simulate the trading scenario from log
    print(f"\n🔧 Simulating Fixed Calculation:")
    
    # Trade parameters from log (交易#1)
    entry_price = 102535.60
    # Assume small price movement for $7.34 profit
    # Let's reverse calculate what the exit price should be
    
    # User requirements
    account_balance = 50.0
    margin_percentage = 0.05  # 5%
    leverage = 125.0
    expected_margin = account_balance * margin_percentage  # $2.5
    
    print(f"   💰 Account Balance: ${account_balance:.2f}")
    print(f"   📊 Margin Percentage: {margin_percentage:.1%}")
    print(f"   ⚡ Leverage: {leverage}x")
    print(f"   💎 Expected Margin: ${expected_margin:.2f}")
    
    # Calculate position size
    nominal_value = expected_margin * leverage  # $2.5 × 125 = $312.5
    position_size = nominal_value / entry_price  # BTC amount
    
    print(f"   📈 Nominal Value: ${nominal_value:.2f}")
    print(f"   📊 Position Size: {position_size:.6f} BTC")
    
    # Test different price movements
    test_scenarios = [
        {"name": "Small Up", "exit_price": 102555.60, "direction": "LONG"},
        {"name": "Small Down", "exit_price": 102515.60, "direction": "SHORT"},
        {"name": "Medium Up", "exit_price": 102635.60, "direction": "LONG"},
        {"name": "Medium Down", "exit_price": 102435.60, "direction": "SHORT"},
    ]
    
    print(f"\n🧪 Testing Different Price Movements:")
    
    for scenario in test_scenarios:
        print(f"\n   📊 Scenario: {scenario['name']} ({scenario['direction']})")
        print(f"      📍 Entry Price: ${entry_price:,.2f}")
        print(f"      📍 Exit Price: ${scenario['exit_price']:,.2f}")
        
        # Calculate price difference
        if scenario['direction'] == 'LONG':
            price_diff = scenario['exit_price'] - entry_price
        else:
            price_diff = entry_price - scenario['exit_price']
        
        price_change_pct = (abs(scenario['exit_price'] - entry_price) / entry_price) * 100
        
        print(f"      📈 Price Change: ${price_diff:+.2f} ({price_change_pct:.3f}%)")
        
        # OLD (Wrong) Calculation
        print(f"\n      ❌ OLD (Wrong) Calculation:")
        pnl_old = position_size * price_diff * leverage  # Wrong: double leverage
        position_value_old = position_size * scenario['exit_price'] * leverage  # Wrong: double leverage
        trading_fee_old = position_value_old * 0.0004
        net_pnl_old = pnl_old - trading_fee_old
        roi_old = (net_pnl_old / expected_margin) * 100  # Even if using correct margin
        
        print(f"         💰 PnL: {position_size:.6f} × {price_diff:+.2f} × {leverage} = ${pnl_old:+.2f}")
        print(f"         💸 Fee: ${position_value_old:,.2f} × 0.0004 = ${trading_fee_old:+.2f}")
        print(f"         💵 Net PnL: ${net_pnl_old:+.2f}")
        print(f"         📈 ROI: {roi_old:+.1f}%")
        print(f"         🚨 Problem: Massive PnL and fees due to double leverage!")
        
        # NEW (Fixed) Calculation
        print(f"\n      ✅ NEW (Fixed) Calculation:")
        pnl_new = position_size * price_diff  # Fixed: no double leverage
        position_value_new = position_size * scenario['exit_price']  # Fixed: no double leverage
        trading_fee_new = position_value_new * 0.0004
        net_pnl_new = pnl_new - trading_fee_new
        roi_new = (net_pnl_new / expected_margin) * 100  # Fixed: correct margin base
        
        print(f"         💰 PnL: {position_size:.6f} × {price_diff:+.2f} = ${pnl_new:+.2f}")
        print(f"         💸 Fee: ${position_value_new:.2f} × 0.0004 = ${trading_fee_new:+.2f}")
        print(f"         💵 Net PnL: ${net_pnl_new:+.2f}")
        print(f"         📈 ROI: {roi_new:+.1f}%")
        print(f"         ✅ Result: Reasonable PnL and fees!")
        
        # Compare results
        pnl_reduction = abs(pnl_old) / abs(pnl_new) if pnl_new != 0 else float('inf')
        fee_reduction = trading_fee_old / trading_fee_new if trading_fee_new != 0 else float('inf')
        
        print(f"\n      📊 Improvement:")
        print(f"         📉 PnL reduced by: {pnl_reduction:.1f}x")
        print(f"         📉 Fee reduced by: {fee_reduction:.1f}x")
        print(f"         📈 ROI change: {roi_old:+.1f}% → {roi_new:+.1f}%")
        
        # Verify reasonableness
        expected_roi_from_leverage = price_change_pct * leverage
        if scenario['direction'] == 'SHORT' and price_diff < 0:
            expected_roi_from_leverage = -expected_roi_from_leverage
        
        print(f"         🎯 Expected ROI: {expected_roi_from_leverage:+.1f}% (price change × leverage)")
        
        if abs(roi_new - expected_roi_from_leverage) < 1:
            print(f"         ✅ ROI calculation is now correct!")
        else:
            print(f"         ⚠️ ROI still differs from expected")
    
    # Test the specific case from user's log
    print(f"\n" + "="*60)
    print("🎯 Testing User's Specific Case:")
    
    # From log: 盈亏: $+7.34, 收益率: +276.9%
    reported_pnl = 7.34
    reported_roi = 276.9
    
    print(f"   📊 Reported P&L: ${reported_pnl:+.2f}")
    print(f"   📊 Reported ROI: {reported_roi:+.1f}%")
    
    # What price movement would give $7.34 with fixed calculation?
    target_pnl = reported_pnl + 0.12  # Add back reasonable trading fee
    required_price_diff = target_pnl / position_size
    new_exit_price = entry_price + required_price_diff  # Assuming LONG
    
    print(f"\n   🔍 Reverse Engineering:")
    print(f"      💰 To get ${reported_pnl:.2f} profit with fixed calculation:")
    print(f"      📈 Required price move: ${required_price_diff:+.2f}")
    print(f"      📍 Exit price should be: ${new_exit_price:,.2f}")
    print(f"      📊 Price change: {(required_price_diff/entry_price)*100:.3f}%")
    
    # Calculate what ROI should be with fixed calculation
    correct_roi = (reported_pnl / expected_margin) * 100
    print(f"      📈 Correct ROI should be: {correct_roi:+.1f}%")
    
    # Compare with leverage expectation
    price_change_pct = (required_price_diff / entry_price) * 100
    leverage_expected_roi = price_change_pct * leverage
    
    print(f"      🎯 Leverage-based ROI: {leverage_expected_roi:+.1f}%")
    
    if abs(correct_roi - leverage_expected_roi) < 5:
        print(f"      ✅ Fixed calculation matches leverage expectation!")
    else:
        print(f"      ⚠️ Still some discrepancy in calculation")
    
    print(f"\n" + "="*60)
    print("🎉 Fix Verification Complete!")
    
    print(f"\n✅ Key Fixes Applied:")
    print(f"   1. 🔧 Removed double leverage in PnL calculation")
    print(f"   2. 🔧 Fixed ROI calculation to use $2.5 margin base")
    print(f"   3. 🔧 Reduced trading fees by 125x")
    print(f"   4. 🔧 Made ROI proportional to actual price movement")
    
    print(f"\n💡 Expected Results:")
    print(f"   📊 Small price movements → Small ROI (not 200%+)")
    print(f"   💰 Reasonable trading fees (~$0.12 not $15+)")
    print(f"   📈 ROI = (Price Change % × 125) for correct leverage effect")
    print(f"   🎯 $7.34 profit should show ~294% ROI (reasonable for 125x leverage)")
    
    print(f"\n🚀 System should now show:")
    print(f"   ✅ Realistic ROI values matching actual price movements")
    print(f"   ✅ Proper leverage effect without double counting")
    print(f"   ✅ Reasonable trading fees")
    print(f"   ✅ Consistent P&L and ROI relationship")

if __name__ == "__main__":
    test_roi_leverage_fix()
