#!/usr/bin/env python3
"""
行动导向的永续合约交易系统 - 提供具体的交易动作建议
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class ActionOrientedTrader:
    """
    行动导向的交易器 - 提供具体的交易动作和等待条件
    """
    
    def __init__(self, initial_capital=50, leverage=2):
        """
        初始化行动导向交易器
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = leverage
        
        # 持仓状态
        self.position = 0
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0
        
        # 交易阈值
        self.thresholds = {
            'strong_long': 0.70,
            'weak_long': 0.55,
            'weak_short': 0.45,
            'strong_short': 0.30
        }
        
        # 价格目标设置
        self.price_targets = {
            'long_entry_levels': [103800, 103500, 103000],  # 做多入场价位
            'short_entry_levels': [105000, 105500, 106000], # 做空入场价位
            'support_levels': [103000, 102500, 102000],     # 支撑位
            'resistance_levels': [105000, 105500, 106000]   # 阻力位
        }
        
        # 交易动作状态
        self.current_action = "观望"
        self.waiting_conditions = []
        self.next_targets = []
        
        self.commission_rate = 0.0004
        self.trades = []
        self.equity_history = []
        
        # 加载模型
        try:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if model_files:
                model_path = max(model_files, key=lambda x: x.split('_')[-1])
                self.model_data = joblib.load(model_path)
                self.model = self.model_data['model']
                self.scaler = self.model_data['scaler']
        except:
            self.model = None
        
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        
        print(f"🎯 行动导向交易器初始化完成")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   交易模式: 行动导向")
    
    def get_current_prediction(self, symbol='BTCUSDT'):
        """获取当前预测"""
        try:
            if self.model is None:
                return 0.372, self.fetcher.get_current_price(symbol, is_futures=True), datetime.now()
            
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                return None, None, None
            
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            current_time = datetime.now()
            
            return up_probability, current_price, current_time
            
        except Exception as e:
            print(f"❌ 获取预测错误: {str(e)}")
            return None, None, None
    
    def analyze_market_action(self, up_probability, current_price):
        """
        分析市场并确定具体行动
        """
        # 重置动作状态
        self.current_action = "观望"
        self.waiting_conditions = []
        self.next_targets = []
        
        # 如果有持仓，分析平仓动作
        if self.position != 0:
            return self.analyze_position_action(up_probability, current_price)
        
        # 无持仓时的开仓分析
        return self.analyze_entry_action(up_probability, current_price)
    
    def analyze_entry_action(self, up_probability, current_price):
        """
        分析开仓动作
        """
        actions = []
        
        # 强烈看跌信号 (做空)
        if up_probability < self.thresholds['strong_short']:
            self.current_action = "立即做空"
            actions.append({
                'action': '立即做空',
                'reason': f'强烈看跌信号 ({up_probability:.1%})',
                'entry_price': current_price,
                'target_profit': current_price * 0.94,  # 6%目标
                'stop_loss': current_price * 1.025,    # 2.5%止损
                'position_size': '70%仓位',
                'urgency': '高'
            })
        
        # 轻微看跌信号 (等待更好价位做空)
        elif up_probability < self.thresholds['weak_short']:
            self.current_action = "等待做空机会"
            
            # 寻找更好的做空价位
            better_short_levels = [level for level in self.price_targets['short_entry_levels'] if level > current_price]
            
            if better_short_levels:
                target_price = min(better_short_levels)
                self.waiting_conditions.append(f"等待价格上涨至${target_price:,.0f}做空")
                actions.append({
                    'action': '等待做空',
                    'reason': f'轻微看跌 ({up_probability:.1%})，等待更好价位',
                    'wait_condition': f'价格上涨至${target_price:,.0f}',
                    'entry_price': target_price,
                    'position_size': '50%仓位',
                    'urgency': '中'
                })
            else:
                actions.append({
                    'action': '谨慎做空',
                    'reason': f'轻微看跌 ({up_probability:.1%})',
                    'entry_price': current_price,
                    'position_size': '30%仓位',
                    'urgency': '低'
                })
        
        # 轻微看涨信号 (等待回调做多)
        elif up_probability > self.thresholds['weak_long']:
            self.current_action = "等待做多机会"
            
            # 寻找回调做多价位
            better_long_levels = [level for level in self.price_targets['long_entry_levels'] if level < current_price]
            
            if better_long_levels:
                target_price = max(better_long_levels)
                self.waiting_conditions.append(f"等待价格回调至${target_price:,.0f}做多")
                actions.append({
                    'action': '等待做多',
                    'reason': f'轻微看涨 ({up_probability:.1%})，等待回调',
                    'wait_condition': f'价格回调至${target_price:,.0f}',
                    'entry_price': target_price,
                    'position_size': '50%仓位',
                    'urgency': '中'
                })
        
        # 强烈看涨信号 (立即做多)
        elif up_probability > self.thresholds['strong_long']:
            self.current_action = "立即做多"
            actions.append({
                'action': '立即做多',
                'reason': f'强烈看涨信号 ({up_probability:.1%})',
                'entry_price': current_price,
                'target_profit': current_price * 1.06,  # 6%目标
                'stop_loss': current_price * 0.975,    # 2.5%止损
                'position_size': '70%仓位',
                'urgency': '高'
            })
        
        # 中性信号 (观望)
        else:
            self.current_action = "观望等待"
            
            # 设置观望条件
            next_support = max([level for level in self.price_targets['support_levels'] if level < current_price], default=current_price * 0.97)
            next_resistance = min([level for level in self.price_targets['resistance_levels'] if level > current_price], default=current_price * 1.03)
            
            self.waiting_conditions.extend([
                f"跌破${next_support:,.0f}考虑做空",
                f"突破${next_resistance:,.0f}考虑做多"
            ])
            
            actions.append({
                'action': '观望等待',
                'reason': f'中性信号 ({up_probability:.1%})',
                'wait_conditions': [
                    f'跌破${next_support:,.0f}→做空',
                    f'突破${next_resistance:,.0f}→做多'
                ],
                'urgency': '无'
            })
        
        return actions
    
    def analyze_position_action(self, up_probability, current_price):
        """
        分析持仓动作
        """
        actions = []
        
        # 计算当前盈亏
        if self.position > 0:  # 多头
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
            position_type = "多头"
        else:  # 空头
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
            position_type = "空头"
        
        # 盈利情况分析
        if pnl_ratio > 0.05:  # 盈利超过5%
            self.current_action = "考虑止盈"
            actions.append({
                'action': '部分止盈',
                'reason': f'盈利{pnl_ratio:.1%}，保护利润',
                'suggestion': '平仓50%，剩余设置移动止损',
                'urgency': '中'
            })
        
        elif pnl_ratio > 0.02:  # 盈利2-5%
            self.current_action = "设置保护"
            actions.append({
                'action': '设置移动止损',
                'reason': f'盈利{pnl_ratio:.1%}，保护收益',
                'stop_loss': self.entry_price if self.position > 0 else self.entry_price,
                'urgency': '中'
            })
        
        elif pnl_ratio < -0.02:  # 亏损超过2%
            self.current_action = "考虑止损"
            actions.append({
                'action': '准备止损',
                'reason': f'亏损{pnl_ratio:.1%}，控制风险',
                'stop_loss': current_price * (0.975 if self.position > 0 else 1.025),
                'urgency': '高'
            })
        
        # 信号反转检查
        if self.position > 0 and up_probability < 0.35:
            self.current_action = "信号反转平仓"
            actions.append({
                'action': '立即平仓',
                'reason': f'多头信号反转 ({up_probability:.1%})',
                'urgency': '高'
            })
        elif self.position < 0 and up_probability > 0.65:
            self.current_action = "信号反转平仓"
            actions.append({
                'action': '立即平仓',
                'reason': f'空头信号反转 ({up_probability:.1%})',
                'urgency': '高'
            })
        
        return actions
    
    def print_action_status(self, current_price=None, up_probability=None):
        """
        打印行动导向的状态
        """
        # 计算当前权益
        current_equity = self.capital
        total_return = (current_equity - self.initial_capital) / self.initial_capital
        
        print(f"\n🎯 【行动导向永续合约交易系统】")
        print("=" * 60)
        
        # 基础状态
        print(f"💰 账户状态:")
        print(f"   当前权益: ${current_equity:.2f}")
        print(f"   总收益率: {total_return:+.2%}")
        print(f"   可用资金: ${self.capital:.2f}")
        
        # 持仓状态
        if self.position != 0:
            position_type = "🟢 多头" if self.position > 0 else "🔴 空头"
            pnl_ratio = ((current_price - self.entry_price) / self.entry_price) if self.position > 0 else ((self.entry_price - current_price) / self.entry_price)
            
            print(f"\n📈 当前持仓:")
            print(f"   持仓类型: {position_type}")
            print(f"   持仓数量: {abs(self.position):.6f} BTC")
            print(f"   入场价格: ${self.entry_price:,.2f}")
            if current_price:
                print(f"   当前价格: ${current_price:,.2f}")
                print(f"   浮动盈亏: {pnl_ratio:+.2%}")
        else:
            print(f"\n📈 当前持仓: 💤 空仓")
        
        # 市场分析
        if current_price and up_probability:
            print(f"\n📊 市场分析:")
            print(f"   BTC价格: ${current_price:,.2f}")
            print(f"   上涨概率: {up_probability:.1%}")
            print(f"   下跌概率: {1-up_probability:.1%}")
            
            # 获取交易动作建议
            actions = self.analyze_market_action(up_probability, current_price)
            
            # 显示当前动作
            print(f"\n🎯 当前动作: {self.current_action}")
            
            # 显示等待条件
            if self.waiting_conditions:
                print(f"\n⏳ 等待条件:")
                for condition in self.waiting_conditions:
                    print(f"   📍 {condition}")
            
            # 显示具体行动建议
            if actions:
                print(f"\n🚀 行动建议:")
                for i, action in enumerate(actions, 1):
                    urgency_emoji = {"高": "🚨", "中": "⚠️", "低": "📊", "无": "💤"}.get(action.get('urgency', '无'), "📊")
                    print(f"   {urgency_emoji} {action['action']}")
                    print(f"      理由: {action['reason']}")
                    
                    if 'wait_condition' in action:
                        print(f"      等待: {action['wait_condition']}")
                    if 'entry_price' in action:
                        print(f"      入场价: ${action['entry_price']:,.2f}")
                    if 'position_size' in action:
                        print(f"      仓位: {action['position_size']}")
                    if 'target_profit' in action:
                        print(f"      目标: ${action['target_profit']:,.2f}")
                    if 'stop_loss' in action:
                        print(f"      止损: ${action['stop_loss']:,.2f}")
                    print()
            
            # 关键价位提醒
            print(f"📍 关键价位:")
            support_levels = [level for level in self.price_targets['support_levels'] if level < current_price]
            resistance_levels = [level for level in self.price_targets['resistance_levels'] if level > current_price]
            
            if support_levels:
                nearest_support = max(support_levels)
                print(f"   📉 下方支撑: ${nearest_support:,.0f} (距离: {(current_price-nearest_support)/current_price:.1%})")
            
            if resistance_levels:
                nearest_resistance = min(resistance_levels)
                print(f"   📈 上方阻力: ${nearest_resistance:,.0f} (距离: {(nearest_resistance-current_price)/current_price:.1%})")
        
        print("=" * 60)

def run_action_oriented_simulation(check_interval=300, leverage=2):
    """运行行动导向模拟"""
    print("🎯 启动行动导向永续合约交易系统")
    print("=" * 60)
    print("特点: 具体交易动作、等待条件、价位目标")
    print("")
    
    trader = ActionOrientedTrader(initial_capital=50, leverage=leverage)
    
    try:
        while True:
            current_time = datetime.now()
            print(f"\n⏰ {current_time.strftime('%Y-%m-%d %H:%M:%S')} - 行动分析...")
            
            up_prob, current_price, _ = trader.get_current_prediction()
            
            if up_prob is None:
                print("❌ 无法获取预测，跳过")
                time.sleep(check_interval)
                continue
            
            trader.print_action_status(current_price, up_prob)
            
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟后重新分析...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        print(f"\n🛑 停止行动导向交易")
        trader.print_action_status(current_price, up_prob)

if __name__ == "__main__":
    import sys
    
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300
    leverage = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    
    print("🎯 行动导向交易系统说明:")
    print("- 提供具体的交易动作建议")
    print("- 设置明确的等待条件和价位目标")
    print("- 实时更新行动策略")
    print("- 包含风险控制建议")
    print("")
    
    run_action_oriented_simulation(interval, leverage)
