#!/usr/bin/env python3
"""
改进的多币种策略 - 针对验证结果的优化
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from final_optimized_strategy import create_binary_trend_model, adaptive_backtest

def create_conservative_model(symbol='ETHUSDT', months_back=24):
    """
    创建更保守的模型以减少过拟合
    """
    print(f"🔧 为 {symbol} 创建保守模型...")
    
    from data_fetcher import BinanceDataFetcher
    from feature_engineering import FeatureEngineer
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import RobustScaler, LabelEncoder
    from sklearn.metrics import classification_report, accuracy_score, balanced_accuracy_score
    
    try:
        import xgboost as xgb
        HAS_XGB = True
    except ImportError:
        HAS_XGB = False
    
    # 1. 获取数据
    start_date = (datetime.now() - timedelta(days=months_back*30)).strftime('%Y-%m-%d')
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date)
    print(f"✅ 获取到 {len(df)} 条数据")
    
    # 2. 创建更严格的趋势标签
    print("🎯 创建保守趋势标签...")
    
    # 使用更长的预测窗口和更高的阈值
    df['future_price'] = df['close'].shift(-6)  # 6小时后的价格
    df['price_change'] = (df['future_price'] - df['close']) / df['close']
    
    # 更高的阈值，减少噪声
    threshold = 0.015  # 1.5%的变化阈值
    df['trend_target'] = (df['price_change'] > threshold).astype(int)
    
    df = df[:-6].copy()
    
    print(f"趋势分布: 上涨={df['trend_target'].sum()}, 下跌={(1-df['trend_target']).sum()}")
    
    # 3. 特征工程
    print("🔧 特征工程...")
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    df_features['target'] = df['trend_target']
    
    # 准备特征
    X = df_features.drop(columns=['target'])
    y = df_features['target']
    
    # 移除原始列
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp', 
                      'future_price', 'price_change', 'trend_target']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    # 清理数据
    mask = ~(X.isna().any(axis=1) | y.isna())
    X = X[mask]
    y = y[mask]
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    print(f"✅ 清理后: {len(X)} 样本")
    
    # 4. 更严格的数据平衡
    print("⚖️ 保守数据平衡...")
    
    # 每个类别最多500样本，减少过拟合
    max_samples = 500
    
    up_indices = np.where(y == 1)[0]
    down_indices = np.where(y == 0)[0]
    
    if len(up_indices) > max_samples:
        up_indices = np.random.choice(up_indices, max_samples, replace=False)
    if len(down_indices) > max_samples:
        down_indices = np.random.choice(down_indices, max_samples, replace=False)
    
    balanced_indices = np.concatenate([up_indices, down_indices])
    
    X_balanced = X.iloc[balanced_indices]
    y_balanced = y.iloc[balanced_indices]
    
    print(f"平衡后: 上涨={y_balanced.sum()}, 下跌={(1-y_balanced).sum()}")
    
    # 5. 时间序列分割
    time_index = X_balanced.index
    sort_indices = np.argsort(time_index)
    
    X_sorted = X_balanced.iloc[sort_indices]
    y_sorted = y_balanced.iloc[sort_indices]
    
    split_point = int(len(X_sorted) * 0.8)
    
    X_train = X_sorted.iloc[:split_point]
    X_test = X_sorted.iloc[split_point:]
    y_train = y_sorted.iloc[:split_point]
    y_test = y_sorted.iloc[split_point:]
    
    print(f"训练集: {len(X_train)}, 测试集: {len(X_test)}")
    
    # 6. 特征缩放
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 7. 训练极保守的模型
    print(f"🎯 训练极保守模型...")
    
    if HAS_XGB:
        model = xgb.XGBClassifier(
            n_estimators=20,        # 大幅减少
            max_depth=3,            # 很浅的树
            learning_rate=0.03,     # 很低的学习率
            subsample=0.6,          # 激进子采样
            colsample_bytree=0.6,   # 特征子采样
            reg_alpha=3.0,          # 很强的正则化
            reg_lambda=3.0,
            min_child_weight=10,    # 增加最小子权重
            random_state=42,
            n_jobs=-1
        )
    else:
        model = RandomForestClassifier(
            n_estimators=20,
            max_depth=4,
            min_samples_split=20,
            min_samples_leaf=10,
            max_features='sqrt',
            random_state=42,
            n_jobs=-1
        )
    
    model.fit(X_train_scaled, y_train)
    
    # 8. 评估
    y_train_pred = model.predict(X_train_scaled)
    y_test_pred = model.predict(X_test_scaled)
    
    train_acc = accuracy_score(y_train, y_train_pred)
    test_acc = accuracy_score(y_test, y_test_pred)
    test_balanced = balanced_accuracy_score(y_test, y_test_pred)
    
    print(f"\n📊 保守模型性能:")
    print(f"训练准确率: {train_acc:.4f}")
    print(f"测试准确率: {test_acc:.4f}")
    print(f"平衡准确率: {test_balanced:.4f}")
    print(f"过拟合程度: {train_acc - test_acc:.4f}")
    
    print("\n📋 分类报告:")
    print(classification_report(y_test, y_test_pred, target_names=['下跌', '上涨']))
    
    # 9. 保存模型
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = f"models/conservative_{symbol}_{timestamp}.joblib"
    
    import os
    os.makedirs("models", exist_ok=True)
    
    joblib.dump({
        'model': model,
        'scaler': scaler,
        'feature_names': X.columns.tolist(),
        'threshold': threshold
    }, model_path)
    
    print(f"💾 保守模型已保存: {model_path}")
    
    return model_path

def test_conservative_models():
    """
    测试保守模型在所有币种上的表现
    """
    print("🔧 测试保守模型策略")
    print("=" * 50)
    
    symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
    results = {}
    
    for symbol in symbols:
        print(f"\n📊 测试保守 {symbol} 模型...")
        
        try:
            # 创建保守模型
            model_path = create_conservative_model(symbol, 24)
            
            # 使用保守回测
            result = conservative_backtest(model_path, symbol, 3)
            
            results[symbol] = result
            
            print(f"✅ {symbol} 保守模型: 收益{result['total_return']:.2%}, 胜率{result['win_rate']:.2%}")
            
        except Exception as e:
            print(f"❌ {symbol} 失败: {str(e)}")
            results[symbol] = {'error': str(e)}
    
    # 对比结果
    print(f"\n📊 保守模型 vs 原始模型对比:")
    print("=" * 60)
    print(f"{'币种':<10} {'原始收益':<12} {'保守收益':<12} {'改进':<10}")
    print("-" * 60)
    
    original_results = {
        'BTCUSDT': 0.1428,
        'ETHUSDT': 0.1156,
        'BNBUSDT': -0.0028
    }
    
    for symbol in symbols:
        if symbol in results and 'total_return' in results[symbol]:
            original = original_results[symbol]
            conservative = results[symbol]['total_return']
            improvement = conservative - original
            
            print(f"{symbol:<10} {original:>10.2%} {conservative:>10.2%} {improvement:>8.2%}")
    
    return results

def conservative_backtest(model_path, symbol='BTCUSDT', test_months=3):
    """
    更保守的回测策略
    """
    from data_fetcher import BinanceDataFetcher
    from feature_engineering import FeatureEngineer
    
    # 加载模型
    model_data = joblib.load(model_path)
    model = model_data['model']
    scaler = model_data['scaler']
    
    # 获取测试数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=test_months*30)
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date.strftime('%Y-%m-%d'))
    
    # 特征工程
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    X = df_features.drop(columns=['target'])
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    mask = ~X.isna().any(axis=1)
    X = X[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    # 预测
    X_scaled = scaler.transform(X)
    prediction_proba = model.predict_proba(X_scaled)
    up_proba = prediction_proba[:, 1]
    
    # 极保守的交易策略
    capital = 10000
    position = 0
    trades = []
    equity_curve = []
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    # 更高的置信度要求
    confidence_threshold = 0.75  # 提高到75%
    
    for i in range(len(up_proba)):
        price = prices[i]
        up_prob = up_proba[i]
        
        # 买入信号：需要很高的置信度
        if up_prob > confidence_threshold and position == 0:
            position = 1
            entry_price = price
            trades.append({
                'type': '买入',
                'price': price,
                'time': timestamps[i],
                'confidence': up_prob
            })
        
        # 卖出信号：更严格的止损
        elif position == 1 and (up_prob < 0.4 or (price / entry_price - 1) < -0.02):  # 2%止损
            position = 0
            pnl_ratio = (price - entry_price) / entry_price
            capital = capital * (1 + pnl_ratio - 0.002)
            
            trades.append({
                'type': '卖出',
                'price': price,
                'entry_price': entry_price,
                'pnl_ratio': pnl_ratio,
                'time': timestamps[i],
                'confidence': up_prob
            })
        
        # 记录权益
        if position == 1:
            current_value = capital * (price / entry_price)
        else:
            current_value = capital
        
        equity_curve.append(current_value)
    
    # 最后平仓
    if position == 1:
        final_pnl = (prices[-1] - entry_price) / entry_price
        capital = capital * (1 + final_pnl - 0.002)
    
    # 计算结果
    total_return = (capital - 10000) / 10000
    buy_hold_return = (prices[-1] - prices[0]) / prices[0]
    
    profitable_trades = len([t for t in trades if t.get('pnl_ratio', 0) > 0])
    total_completed_trades = len([t for t in trades if 'pnl_ratio' in t])
    win_rate = profitable_trades / total_completed_trades if total_completed_trades > 0 else 0
    
    # 最大回撤
    peak = np.maximum.accumulate(equity_curve)
    drawdown = (np.array(equity_curve) - peak) / peak
    max_drawdown = np.min(drawdown)
    
    return {
        'total_return': total_return,
        'benchmark_return': buy_hold_return,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'total_trades': total_completed_trades,
        'final_capital': capital
    }

if __name__ == "__main__":
    test_conservative_models()
