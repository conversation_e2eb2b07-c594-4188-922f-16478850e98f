import os
import logging
from datetime import datetime, timedelta, timezone
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer, FeatureConfig
from model_trainer import ModelTrainer
import pandas as pd
from dotenv import load_dotenv
from pathlib import Path
from typing import Dict, Union, List
import json
import argparse
import time
import csv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crypto_prediction.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
LOGS_DIR = Path("logs")
LOGS_DIR.mkdir(exist_ok=True)

def setup_environment():
    """设置环境变量和创建必要的目录"""
    load_dotenv()
    
    # 创建模型和数据目录
    os.makedirs('models', exist_ok=True)
    os.makedirs('data', exist_ok=True)

def train_model(symbol: str = 'BTCUSDT', 
                interval: str = '1h',
                start_date: str = '2022-01-01',
                prediction_window: int = 24,
                model_type: str = 'rf',
                is_futures: bool = False,
                # LSTM specific parameters
                lstm_units: int = 50,
                lstm_timesteps: int = 1,
                lstm_epochs: int = 50,
                lstm_batch_size: int = 32,
                lstm_learning_rate: float = 0.001,
                use_optuna_main: bool = True, # Renamed to avoid clash with ModelTrainer internal
                optuna_n_trials_main: int = 50 # Renamed
                ):
    """
    训练加密货币预测模型
    
    参数:
        symbol (str): 交易对
        interval (str): K线间隔
        start_date (str): 开始日期
        prediction_window (int): 预测窗口（小时）
        model_type (str): 模型类型 ('rf', 'gb', 'xgb', 'lgb', 'ensemble', 'lstm')
        is_futures (bool): 是否使用永续合约数据
        lstm_units (int): LSTM模型的单元数量 (如果 model_type='lstm')
        lstm_timesteps (int): LSTM模型的时间步长 (如果 model_type='lstm')
        lstm_epochs (int): LSTM模型的训练周期数 (如果 model_type='lstm')
        lstm_batch_size (int): LSTM模型的批量大小 (如果 model_type='lstm')
        lstm_learning_rate (float): LSTM模型的学习率 (如果 model_type='lstm')
        use_optuna_main (bool): 是否在训练时使用Optuna进行超参数优化
        optuna_n_trials_main (int): Optuna的试验次数
    """
    try:
        logger.info(f"开始训练新模型... 交易对: {symbol}{'(永续)' if is_futures else ''}, 时间间隔: {interval}")
        
        # 1. 获取数据
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, interval, start_date, is_futures=is_futures)
        
        # 2. 特征工程
        feature_eng_config = FeatureConfig(prediction_window=prediction_window)
        engineer = FeatureEngineer(config=feature_eng_config)
        df_features_and_target = engineer.create_features(df)
        
        # 检查目标列是否存在并分离特征和目标
        if 'target' not in df_features_and_target.columns:
            logger.error("目标列 'target' 在特征工程处理后的DataFrame中未找到!")
            raise ValueError("目标列 'target' 在特征工程处理后的DataFrame中未找到!")
            
        y = df_features_and_target['target']
        # X 包含所有列，除了 'target' 列，以及可能需要排除的其他原始列或非特征列
        # 确保只选择数值类型的特征列，或在模型训练前进一步处理
        X = df_features_and_target.drop(columns=['target'])
        
        # 考虑从X中移除其他非特征列，例如原始的价格/成交量数据（如果它们不是被设计的特征）
        # 例如: cols_to_remove = ['open', 'high', 'low', 'volume'] # 根据实际特征定义调整
        # X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
        
        # 3. 训练模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir_path_str = f"models/{symbol}/{interval}/{model_type}_{timestamp}"
        model_dir = Path(model_dir_path_str)
        model_dir.mkdir(parents=True, exist_ok=True)
        
        trainer = ModelTrainer(
            model_type=model_type,
            output_dir=model_dir,
            save_plots=True,
            use_optuna=use_optuna_main, # Pass from main args
            optuna_n_trials=optuna_n_trials_main, # Pass from main args
            # LSTM parameters
            lstm_units=lstm_units,
            lstm_timesteps=lstm_timesteps,
            lstm_epochs=lstm_epochs,
            lstm_batch_size=lstm_batch_size,
            lstm_learning_rate=lstm_learning_rate,
            num_classes=8 # <-- 新增：为多分类指定类别数量
        )
        results = trainer.train(X, y)
        
        # 4. 保存模型和结果
        saved_paths = trainer.save_model()
        save_results(results, symbol, interval, model_type, timestamp)
        
        return trainer, results
        
    except Exception as e:
        logger.error(f"训练过程中发生错误: {str(e)}")
        raise

def save_results(results: Dict, symbol: str, interval: str, model_type: str, timestamp: str):
    """保存训练结果到文件"""
    output_dir = Path(f"results/{symbol}/{interval}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    output_file = output_dir / f"training_results_{model_type}_{timestamp}.txt"
    
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(f"模型类型: {model_type}\n")
        f.write(f"交易对: {symbol}\n")
        f.write(f"时间间隔: {interval}\n")
        f.write(f"训练时间: {timestamp}\n\n")
        
        f.write("交叉验证结果:\n")
        f.write(f"准确率: {results['mean_scores']['accuracy']:.4f} (+/- {results['std_scores']['accuracy']:.4f})\n")
        f.write(f"精确率: {results['mean_scores']['precision']:.4f} (+/- {results['std_scores']['precision']:.4f})\n")
        f.write(f"召回率: {results['mean_scores']['recall']:.4f} (+/- {results['std_scores']['recall']:.4f})\n")
        f.write(f"F1分数: {results['mean_scores']['f1']:.4f} (+/- {results['std_scores']['f1']:.4f})\n\n")
        
        if results.get('best_params'):
            f.write("最佳参数:\n")
            for param, value in results['best_params'].items():
                f.write(f"{param}: {value}\n")
        
        if results.get('feature_importance') is not None:
            f.write("\n特征重要性 (Top 10):\n")
            top_features = results['feature_importance'].head(10)
            for feature, importance in top_features.items():
                f.write(f"{feature}: {importance:.4f}\n")
    
    logger.info(f"训练结果已保存到: {output_file}")
    return output_file

def predict_next_movement(model_trainer: ModelTrainer,
                        symbol: str = 'BTCUSDT',
                        interval: str = '1h',
                        lookback_period: str = '7 days ago UTC',
                        is_futures: bool = False):
    """预测下一个时间窗口的价格走势并生成详细分析报告"""
    try:
        # 处理lookback_period
        if isinstance(lookback_period, str) and 'days ago' in lookback_period:
            days = int(lookback_period.split()[0])
            lookback_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        else:
            lookback_date = lookback_period

        # 获取最新数据
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, interval, lookback_date, is_futures, force_refresh=True)
        
        # 特征工程
        engineer = FeatureEngineer()
        df_features = engineer.create_features(df, force_refresh=True)
        
        # 获取特征数据（排除target列和其他非特征列）
        X = df_features.drop(columns=['target'] if 'target' in df_features.columns else [], errors='ignore')
        
        # 获取最新一条数据进行预测
        latest_features = X.iloc[-1:]
        prediction = model_trainer.predict(latest_features)
        
        # 获取预测概率
        prediction_proba = None
        try:
            prediction_proba = model_trainer.predict_proba(latest_features)[0]
        except:
            pass
            
        # 获取市场分析报告
        market_analysis = engineer.analyze_market_trend(df_features)
        
        # 市场状态映射
        market_states = {
            0: "大幅下跌（-3%以上）",
            1: "中度下跌（-2%至-3%）",
            2: "轻微下跌（-1%至-2%）",
            3: "横盘偏下（-1%至0%）",
            4: "横盘偏上（0%至1%）",
            5: "轻微上涨（1%至2%）",
            6: "中度上涨（2%至3%）",
            7: "大幅上涨（3%以上）"
        }

        # 时间间隔映射
        interval_map = {
            '1m': '1分钟',
            '3m': '3分钟',
            '5m': '5分钟',
            '15m': '15分钟',
            '30m': '30分钟',
            '1h': '1小时',
            '2h': '2小时',
            '4h': '4小时',
            '6h': '6小时',
            '8h': '8小时',
            '12h': '12小时',
            '1d': '24小时',
            '3d': '3天',
            '1w': '1周',
            '1M': '1个月'
        }

        # 获取预测结果
        predicted_class_index = prediction[0]
        predicted_state = market_states.get(predicted_class_index, "未知状态")
        time_window = interval_map.get(interval, interval)
        
        result = {
            'symbol': symbol,
            'predicted_market_state_index': predicted_class_index,
            'predicted_market_state': predicted_state,
            'time_window': time_window,
            'timestamp': datetime.now(),
            'current_price': market_analysis['current_price'],
            'price_change_24h': market_analysis['price_change_24h'],
            'trend': market_analysis['trend'],
            'technical_analysis': market_analysis['technical_analysis'],
            'support_resistance': market_analysis.get('support_resistance', {}),
            'volume_analysis': market_analysis.get('volume_analysis', {})
        }
        
        # 如果有概率预测，添加到结果中
        if prediction_proba is not None:
            result['class_probabilities'] = prediction_proba.tolist()
            
        # 生成报告
        report = f"\n{symbol}{'(永续)' if is_futures else ''} {interval}行情分析报告"
        report += f"\n\n基本信息："
        report += f"\n当前价格：{result['current_price']:.2f} USDT"
        report += f"\n24小时涨跌：{result['price_change_24h']:.2f}%"
        report += f"\n当前趋势：{result['trend']}"
        
        # 预测结果
        report += "\n\n预测结果："
        report += f"\n预测的市场状态：未来{time_window}，{result['predicted_market_state']}"
        if 'class_probabilities' in result:
            report += "\n各状态概率："
            probs = result['class_probabilities']
            # 按概率从高到低排序，并显示所有状态的概率
            sorted_probs = sorted([(i, prob) for i, prob in enumerate(probs)], key=lambda x: x[1], reverse=True)
            for i, prob in sorted_probs:
                if prob > 0.001:  # 只显示概率大于0.1%的状态
                    report += f"\n{market_states[i]}：概率为 {prob:.2%}"

        # 技术指标
        report += "\n\n技术指标分析："
        
        # KDJ分析（如果存在）
        if 'technical_analysis' in result and 'KDJ' in result['technical_analysis']:
            kdj = result['technical_analysis']['KDJ']
            if all(key in kdj for key in ['K', 'D', 'J']) and all(kdj[key] is not None for key in ['K', 'D', 'J']):
                report += "\nKDJ指标："
                try:
                    k, d, j = kdj['K'], kdj['D'], kdj['J']
                    report += f"\n  K值：{k:.2f} {'（超买）' if k > 80 else '（超卖）' if k < 20 else ''}"
                    report += f"\n  D值：{d:.2f}"
                    report += f"\n  J值：{j:.2f}"
                    if k > d:
                        report += "\n  当前KDJ金叉形态，可能上涨"
                    else:
                        report += "\n  当前KDJ死叉形态，可能下跌"
                except (TypeError, ValueError):
                    report += "  部分KDJ数据不可用"

        # MACD分析（如果存在）
        if 'technical_analysis' in result and 'MACD' in result['technical_analysis']:
            macd = result['technical_analysis']['MACD']
            if 'HIST' in macd and macd['HIST'] is not None:
                report += "\nMACD指标："
                try:
                    hist = macd['HIST']
                    report += f"\n  MACD柱状值：{hist:.2f}"
                    if hist > 0:
                        report += "（多头趋势）"
                    else:
                        report += "（空头趋势）"
                except (TypeError, ValueError):
                    report += "  MACD数据不完整"

        # 风险提示
        report += "\n\n风险提示："
        if 'support_resistance' in result and 'R2' in result['support_resistance'] and result['support_resistance']['R2'] is not None:
            try:
                report += f"\n1. 若价格突破{result['support_resistance']['R2']:.2f} USDT（R2）并站稳，需及时止损并重新评估趋势。"
            except (TypeError, ValueError):
                report += "\n1. 支撑/压力位数据暂不可用，请关注价格关键点位。"

        if ('technical_analysis' in result and 
            'ATR' in result['technical_analysis'] and 
            result['technical_analysis']['ATR'] is not None):
            try:
                atr = result['technical_analysis']['ATR']
                report += f"\n2. 当前ATR（{atr:.2f}）显示市场波动"
                if atr > 1000:
                    report += "剧烈，建议降低仓位或暂时观望。"
                elif atr > 500:
                    report += "较大，建议谨慎操作。"
                else:
                    report += "在正常范围，可以按计划操作。"
            except (TypeError, ValueError):
                report += "\n2. ATR数据暂不可用，请注意控制风险。"
        else:
            report += "\n2. 波动指标数据暂不可用，请注意控制风险。"

        report += "\n3. 本分析仅供参考，不构成投资建议。请结合其他因素综合判断。"
        
        result['detailed_report'] = report
        print(report)  # 直接打印报告
        return result
        
    except Exception as e:
        logger.error(f"预测过程中发生错误: {str(e)}")
        raise

def load_trained_model(model_dir: Union[str, Path]) -> ModelTrainer:
    """
    加载已训练的模型
    
    参数:
        model_dir (Union[str, Path]): 模型目录路径
        
    返回:
        ModelTrainer: 加载了模型的ModelTrainer实例
    """
    try:
        model_dir = Path(model_dir)
        if not model_dir.exists():
            raise FileNotFoundError(f"模型目录不存在: {model_dir}")
            
        # 查找模型文件
        model_files = list(model_dir.glob("model_*.joblib"))
        scaler_files = list(model_dir.glob("scaler_*.joblib"))
        info_files = list(model_dir.glob("model_info_*.json"))
        
        if not (model_files and scaler_files and info_files):
            raise FileNotFoundError(f"模型文件不完整: {model_dir}")
            
        # 加载模型
        trainer = ModelTrainer.load_model(
            model_path=str(model_files[0]),
            scaler_path=str(scaler_files[0]),
            info_path=str(info_files[0])
        )
        
        logger.info(f"成功加载模型: {model_dir}")
        return trainer
        
    except Exception as e:
        logger.error(f"加载模型时发生错误: {str(e)}")
        raise

def list_available_models() -> List[Path]:
    """列出所有可用的已训练模型"""
    models_dir = Path("models")
    if not models_dir.exists():
        return []
        
    # 查找所有包含完整模型文件的目录
    available_models = []
    for model_dir in models_dir.iterdir():
        if model_dir.is_dir():
            has_model = any(model_dir.glob("model_*.joblib"))
            has_scaler = any(model_dir.glob("scaler_*.joblib"))
            has_info = any(model_dir.glob("model_info_*.json"))
            
            if has_model and has_scaler and has_info:
                available_models.append(model_dir)
                
    return sorted(available_models)

def get_futures_symbol(symbol: str) -> str:
    """
    获取正确的合约交易对符号
    
    参数:
        symbol (str): 原始交易对符号
        
    返回:
        str: 处理后的交易对符号
    """
    # 移除 _PERP 后缀（如果有）
    if symbol.endswith('_PERP'):
        symbol = symbol[:-5]
    # 确保以USDT结尾
    if not symbol.endswith('USDT'):
        symbol = f"{symbol}USDT"
    return symbol

def get_next_kline_close_datetime_utc(interval_str: str, current_dt_utc: datetime) -> datetime:
    """
    Calculates the UTC datetime when the next K-line for the given interval is expected to close.
    Assumes K-lines close at the start of their interval period.
    e.g., for '1h' at 10:15, next close is 11:00:00.
    """
    now = current_dt_utc.replace(tzinfo=timezone.utc) # Ensure timezone awareness
    
    next_dt = now # Initialize next_dt

    if interval_str.endswith('m'):
        minutes = int(interval_str[:-1])
        if minutes == 0: raise ValueError("Minute interval cannot be 0.")
        # Calculate how many full 'minutes' intervals have passed in the current hour
        intervals_passed_in_hour = now.minute // minutes
        # Calculate the start minute of the next interval within the current hour
        next_interval_minute_start = (intervals_passed_in_hour + 1) * minutes
        
        next_dt = now.replace(second=0, microsecond=0)
        if next_interval_minute_start >= 60:
            next_dt = next_dt + timedelta(hours=1)
            next_dt = next_dt.replace(minute=(next_interval_minute_start % 60))
        else:
            next_dt = next_dt.replace(minute=next_interval_minute_start)
            
    elif interval_str.endswith('h'):
        hours = int(interval_str[:-1])
        if hours == 0: raise ValueError("Hour interval cannot be 0.")
        # Calculate the current hour block
        current_hour_block = now.hour // hours
        # Calculate the start hour of the next block
        next_block_start_hour = (current_hour_block + 1) * hours
        
        next_dt = now.replace(minute=0, second=0, microsecond=0)
        if next_block_start_hour >= 24:
            days_to_add = next_block_start_hour // 24
            next_dt = next_dt + timedelta(days=days_to_add)
            next_dt = next_dt.replace(hour=(next_block_start_hour % 24))
        else:
            next_dt = next_dt.replace(hour=next_block_start_hour)
            
    elif interval_str.endswith('d'):
        days = int(interval_str[:-1])
        if days == 0: raise ValueError("Day interval cannot be 0.")
        # Next K-line closes at midnight UTC, 'days' after the current K-line's start.
        # Simplified: find next midnight, then align to 'days' boundary if needed.
        # This part might need more sophisticated logic for multi-day intervals if they don't align with day start.
        # For 1d, it's the next midnight.
        next_dt = (now + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        if days > 1: # For multi-day intervals, adjust to the 'days' boundary from a reference point (e.g. Unix epoch)
            # This is a placeholder for more complex multi-day logic. For now, assumes '1d'.
            logger.warning(f"Multi-day interval ({interval_str}) logic in get_next_kline_close_datetime_utc is simplified for '1d'.")

    else:
        # Placeholder for 'w' (week), 'M' (month) - these are more complex
        raise ValueError(f"Unsupported interval string for K-line close calculation: {interval_str}")

    # If the calculated next_dt is in the past or too close (within a small safety margin),
    # advance it by one interval period. This handles cases where the script starts
    # very near or just after a K-line close.
    safety_margin = timedelta(seconds=10) # Safety margin
    while next_dt <= now + safety_margin:
        logger.debug(f"Calculated next_dt {next_dt} is too close to now {now}. Advancing by one interval.")
        if interval_str.endswith('m'):
            next_dt += timedelta(minutes=int(interval_str[:-1]))
        elif interval_str.endswith('h'):
            next_dt += timedelta(hours=int(interval_str[:-1]))
        elif interval_str.endswith('d'):
             next_dt += timedelta(days=int(interval_str[:-1]))
        # Add other interval types if supported
    
    return next_dt

def save_prediction_to_csv(prediction_data: dict, symbol: str, interval: str):
    filename = LOGS_DIR / f"continuous_predictions_{symbol.replace('/', '_')}_{interval}.csv"
    file_exists = filename.exists()
    
    # Define fieldnames, ensure all keys from prediction_data that you want to save are included.
    fieldnames = [
        'prediction_run_timestamp_utc', 'kline_target_timestamp_utc', 'symbol', 'interval', 
        'model_path_used', 'current_price_at_prediction', 'price_fetched_at', 
        'predicted_market_state_index', 'predicted_market_state', 'class_probabilities',
        'trend', 'price_change_24h', 'detailed_report_preview' 
    ]

    row_to_write = {}
    for key in fieldnames:
        value = prediction_data.get(key)
        if isinstance(value, list) or isinstance(value, dict): # Serialize lists/dicts (like probabilities)
            row_to_write[key] = json.dumps(value)
        elif isinstance(value, datetime): # Format datetime objects
             row_to_write[key] = value.strftime('%Y-%m-%d %H:%M:%S %Z')
        elif value is None:
            row_to_write[key] = 'N/A'
        else:
            row_to_write[key] = value
            
    # Ensure all fieldnames have a corresponding value in row_to_write, even if it's 'N/A'
    for field in fieldnames:
        if field not in row_to_write:
            row_to_write[field] = 'N/A'


    try:
        with open(filename, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            if not file_exists:
                writer.writeheader()
            writer.writerow(row_to_write)
        logger.info(f"Prediction for {symbol} ({interval}) saved to {filename}")
    except Exception as e:
        logger.error(f"Error saving prediction to CSV {filename}: {e}", exc_info=True)

def run_continuous_prediction(
    symbol: str, 
    interval: str, 
    model_path: str, 
    scaler_path: str,
    model_type: str,
    lookback_period: str = '30 days ago UTC', 
    is_futures: bool = False,
    kline_finish_buffer_seconds: int = 0 # Increased buffer
):
    logger.info(f"Starting continuous prediction for {symbol} ({interval}). Model: {model_path}, Type: {model_type}")
    
    active_trainer: Optional[ModelTrainer] = None
    try:
        # Initialize ModelTrainer once
        # Critical: Ensure ModelTrainer's __init__ can correctly load the model and scaler
        # and store necessary attributes like lstm_timesteps, num_classes if loaded from info.json
        active_trainer = ModelTrainer(
            model_type=model_type,
            model_path=model_path,
            scaler_path=scaler_path,
            # output_dir is needed if _find_latest_model_files is used internally, but not if paths are direct
            # Provide other necessary args if your ModelTrainer.__init__ requires them for loading
            # e.g., lstm_timesteps, num_classes might be needed if not automatically read from a model_info.json
        )
        if active_trainer.model is None or active_trainer.scaler is None:
            logger.error("Failed to load model or scaler. Exiting continuous prediction.")
            return
        logger.info(f"Model and scaler loaded successfully for continuous prediction. LSTM Timesteps: {active_trainer.lstm_timesteps if hasattr(active_trainer, 'lstm_timesteps') else 'N/A'}, Num Classes: {active_trainer.num_classes if hasattr(active_trainer, 'num_classes') else 'N/A'}")

    except Exception as e_init:
        logger.error(f"Error initializing ModelTrainer for continuous prediction: {e_init}", exc_info=True)
        return

    while True:
        prediction_cycle_start_utc = datetime.now(timezone.utc)
        kline_target_timestamp_utc_str = "N/A" # Initialize for logging in case of early error
        
        try:
            # 1. Calculate next K-line close time and wait
            # This is the theoretical close time of the K-line we will predict FOR.
            # e.g. if it's 10:05, and interval is 1h, this will be 11:00.
            # Data up to 10:00 will be used to predict the 10:00-11:00 K-line.
            next_kline_closes_at_utc = get_next_kline_close_datetime_utc(interval, prediction_cycle_start_utc)
            kline_target_timestamp_utc_str = next_kline_closes_at_utc.strftime('%Y-%m-%d %H:%M:%S %Z')
            
            # We need to wait until this K-line has actually closed and data is available.
            # So, we wait until `next_kline_closes_at_utc` + buffer.
            wait_until_utc = next_kline_closes_at_utc + timedelta(seconds=kline_finish_buffer_seconds)
            
            sleep_duration_seconds = (wait_until_utc - prediction_cycle_start_utc).total_seconds()

            if sleep_duration_seconds > 0:
                logger.info(f"Target K-line period ends at {kline_target_timestamp_utc_str}. "
                            f"Waiting for {sleep_duration_seconds:.2f} seconds (until {wait_until_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}) for data availability.")
                time.sleep(sleep_duration_seconds)
            else: # If already past the ideal fetch time, proceed with a small delay for safety
                logger.info(f"Calculated wait time is {sleep_duration_seconds:.2f}s. Proceeding after a short safety delay.")
                time.sleep(max(0, 5 + int(sleep_duration_seconds))) # sleep at least 5s, or more if sleep_duration was slightly negative


            current_time_after_wait_utc = datetime.now(timezone.utc)
            logger.info(f"[{current_time_after_wait_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}] Waited. Fetching data and predicting for K-line ending at {kline_target_timestamp_utc_str}.")

            # 2. Perform prediction using the pre-loaded trainer
            # predict_next_movement should use data up to the K-line that just closed (i.e., data up to `next_kline_closes_at_utc`)
            # to predict the K-line that *follows* it.
            # The 'start_date' for get_historical_data inside predict_next_movement needs to be far enough back.
            prediction_result = predict_next_movement(
                model_trainer=active_trainer,
                symbol=symbol,
                interval=interval,
                lookback_period=lookback_period, 
                is_futures=is_futures
            )
            
            prediction_run_timestamp_utc = datetime.now(timezone.utc)

            if prediction_result and 'current_price' in prediction_result:
                # 'current_price' in prediction_result is the close of the latest K-line used for features.
                # 'timestamp' in prediction_result is when predict_next_movement finished.
                
                # The K-line we are predicting FOR is the one that STARTS at `next_kline_closes_at_utc`.
                # (Assuming `next_kline_closes_at_utc` is the END time of the last available data candle)
                
                data_to_save = {
                    'prediction_run_timestamp_utc': prediction_run_timestamp_utc, # When this script block ran
                    'kline_target_timestamp_utc': next_kline_closes_at_utc, # The START time of the K-line this prediction is FOR
                    'symbol': symbol,
                    'interval': interval,
                    'model_path_used': model_path,
                    'current_price_at_prediction': prediction_result.get('current_price'), # Price of the last candle used for features
                    'price_fetched_at': prediction_result.get('timestamp'), # Time of prediction generation / last data point
                    'predicted_market_state_index': prediction_result.get('predicted_market_state_index'),
                    'predicted_market_state': prediction_result.get('predicted_market_state'),
                    'class_probabilities': prediction_result.get('class_probabilities'),
                    'trend': prediction_result.get('trend'), # Corrected key from 'trend_analysis'
                    'price_change_24h': prediction_result.get('price_change_24h'),
                    'detailed_report_preview': (prediction_result.get('detailed_report')[:250].replace('\\n', ' ') + "...") if prediction_result.get('detailed_report') else 'N/A'
                }
                save_prediction_to_csv(data_to_save, symbol, interval)
                logger.info(f"Prediction for K-line starting {next_kline_closes_at_utc.strftime('%Y-%m-%d %H:%M:%S %Z')} successful. State: {data_to_save['predicted_market_state']}")

            else:
                logger.warning(f"Prediction for K-line starting {next_kline_closes_at_utc.strftime('%Y-%m-%d %H:%M:%S %Z')} did not return expected results or failed.")
        
        except KeyboardInterrupt:
            logger.info("Continuous prediction loop interrupted by user. Exiting.")
            break
        except Exception as e:
            logger.error(f"Error in continuous prediction cycle for K-line {kline_target_timestamp_utc_str}: {e}", exc_info=True)
            logger.info("Waiting for 60 seconds before next cycle due to error...")
            time.sleep(60) 
        
        logger.info(f"Prediction cycle for K-line starting {kline_target_timestamp_utc_str} finished. Preparing for next.")
        # A small sleep here can prevent tight looping if an error occurs before the main sleep calculation,
        # or if kline_finish_buffer_seconds is very small.
        time.sleep(min(kline_finish_buffer_seconds, 10))

def parse_args():
    parser = argparse.ArgumentParser(description="加密货币预测模型命令行工具")
    subparsers = parser.add_subparsers(dest="action", help="选择操作: train, predict, continuous_predict", required=True)

    # 训练子命令
    train_parser = subparsers.add_parser("train", help="训练新模型")
    train_parser.add_argument("--symbol", type=str, default="BTCUSDT", help="交易对 (例如 BTCUSDT)")
    train_parser.add_argument("--interval", type=str, default="1h", help="K线间隔 (例如 1m, 5m, 1h, 1d)")
    train_parser.add_argument("--data_start_date", type=str, default="2022-01-01", help="数据开始日期 (YYYY-MM-DD)")
    train_parser.add_argument("--prediction_window", type=int, default=24, help="预测窗口（小时）")
    train_parser.add_argument("--model_type", type=str, default="rf", choices=['rf', 'gb', 'xgb', 'lgb', 'ensemble', 'lstm'], help="模型类型")
    train_parser.add_argument("--contract_type", type=str, default="spot", choices=['spot', 'futures'], help="合约类型 (spot 或 futures)")
    # Optuna 参数
    train_parser.add_argument("--no-use_optuna", action="store_false", dest="use_optuna_main", help="禁用Optuna进行超参数优化")
    train_parser.add_argument("--optuna_n_trials", type=int, default=50, dest="optuna_n_trials_main", help="Optuna的试验次数")
    # LSTM 特定参数
    train_parser.add_argument("--lstm_units", type=int, default=50, help="LSTM模型的单元数量")
    train_parser.add_argument("--lstm_timesteps", type=int, default=10, help="LSTM模型的时间步长")
    train_parser.add_argument("--lstm_epochs", type=int, default=50, help="LSTM模型的训练周期数")
    train_parser.add_argument("--lstm_batch_size", type=int, default=32, help="LSTM模型的批量大小")
    train_parser.add_argument("--lstm_learning_rate", type=float, default=0.001, help="LSTM模型的学习率")

    # 预测子命令
    predict_parser = subparsers.add_parser('predict', help='使用最新数据进行预测')
    predict_parser.add_argument('--symbol', type=str, default='BTCUSDT', help='交易对')
    predict_parser.add_argument('--interval', type=str, default='1h', help='K线间隔')
    predict_parser.add_argument('--lookback_period', type=str, default='7 days ago UTC', help='回看时间')
    predict_parser.add_argument('--is_futures', action='store_true', help='是否为永续合约数据')
    predict_parser.add_argument('--model_type', type=str, required=True, help='模型类型 (lstm, rf, gb, xgb, lgb, ensemble)')
    predict_parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    predict_parser.add_argument('--scaler_path', type=str, required=True, help='scaler文件路径')

    # 连续预测子命令
    continuous_predict_parser = subparsers.add_parser('continuous_predict', help='运行连续预测循环')
    continuous_predict_parser.add_argument('--symbol', type=str, default='BTCUSDT', help='交易对 (例如 BTCUSDT)')
    continuous_predict_parser.add_argument('--interval', type=str, default='1h', help='K线间隔 (例如 1m, 5m, 1h, 1d)')
    continuous_predict_parser.add_argument('--model_path', type=str, required=True, help='训练好的模型文件路径')
    continuous_predict_parser.add_argument('--scaler_path', type=str, required=True, help='对应的scaler文件路径')
    continuous_predict_parser.add_argument('--model_type', type=str, required=True, help='模型类型 (例如 lstm, rf, xgb)')
    continuous_predict_parser.add_argument('--lookback_period', type=str, default='30 days ago UTC', help='获取特征工程历史数据的回看期 (例如 "7 days ago UTC")')
    continuous_predict_parser.add_argument('--is_futures', action='store_true', help='是否为永续合约数据')
    continuous_predict_parser.add_argument('--buffer_seconds', type=int, default=20, help='K线关闭后等待数据可用的缓冲秒数')
    
    args = parser.parse_args()
    
    # 验证时间间隔格式 (对所有子命令的interval参数进行验证)
    valid_intervals = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'] # 扩展支持的间隔
    
    # 检查 args 是否有 interval 属性，因为 train 子命令的参数名是 interval，而 predict 是 interval
    current_interval_to_check = None
    if hasattr(args, 'interval') and args.interval is not None:
        current_interval_to_check = args.interval
    
    if current_interval_to_check and current_interval_to_check not in valid_intervals:
        parser.error(f'无效的时间间隔 "{current_interval_to_check}". 请使用以下之一: {", ".join(valid_intervals)}')
    
    return args

def load_latest_model(symbol: str, interval: str) -> ModelTrainer:
    """
    加载最新的训练模型
    
    参数:
        symbol (str): 交易对符号
        interval (str): 时间间隔
        
    返回:
        ModelTrainer: 加载了模型的ModelTrainer实例，如果没有找到模型则返回None
    """
    try:
        # 构建模型目录路径
        model_base_dir = Path(f"models/{symbol}/{interval}")
        if not model_base_dir.exists():
            logger.warning(f"模型目录不存在: {model_base_dir}")
            return None
            
        # 获取所有模型子目录
        model_dirs = []
        for model_type in ['rf', 'gb']:  # 支持的模型类型
            model_dirs.extend(model_base_dir.glob(f"{model_type}_*"))
            
        if not model_dirs:
            logger.warning(f"未找到任何模型: {model_base_dir}")
            return None
            
        # 选择最新的模型
        latest_model_dir = sorted(model_dirs)[-1]
        logger.info(f"找到最新模型: {latest_model_dir}")
        
        return load_trained_model(latest_model_dir)
        
    except Exception as e:
        logger.error(f"加载最新模型时发生错误: {str(e)}")
        return None

def predict(symbol: str = 'BTCUSDT', interval: str = '1h', model_type: str = 'lstm',
         model_path: str = None, scaler_path: str = None):
    """执行预测"""
    try:
        # 初始化数据获取器
        fetcher = BinanceDataFetcher()
        
        # 获取最新数据（强制刷新）
        df = fetcher.get_historical_data(
            symbol=symbol,
            interval=interval,
            start_date=(datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'),
            is_futures=symbol.endswith('USDT'),
            force_refresh=True
        )
        
        if df.empty:
            print(f"错误：无法获取{symbol}的历史数据")
            return
        
        # 特征工程
        engineer = FeatureEngineer()
        df_features = engineer.create_features(df, force_refresh=True)
        
        # 获取特征数据（排除target列和其他非特征列）
        X = df_features.drop(columns=['target'] if 'target' in df_features.columns else [], errors='ignore')
        
        # 获取最新一条数据进行预测
        latest_features = X.iloc[-1:]
        prediction = model_trainer.predict(latest_features)
        
        # 获取预测概率
        prediction_proba = None
        try:
            prediction_proba = model_trainer.predict_proba(latest_features)[0]
        except:
            pass
            
        # 获取市场分析报告
        market_analysis = engineer.analyze_market_trend(df_features)
        
        # 市场状态映射
        market_states = {
            0: "大幅下跌（-3%以上）",
            1: "中度下跌（-2%至-3%）",
            2: "轻微下跌（-1%至-2%）",
            3: "横盘偏下（-1%至0%）",
            4: "横盘偏上（0%至1%）",
            5: "轻微上涨（1%至2%）",
            6: "中度上涨（2%至3%）",
            7: "大幅上涨（3%以上）"
        }

        # 时间间隔映射
        interval_map = {
            '1m': '1分钟',
            '3m': '3分钟',
            '5m': '5分钟',
            '15m': '15分钟',
            '30m': '30分钟',
            '1h': '1小时',
            '2h': '2小时',
            '4h': '4小时',
            '6h': '6小时',
            '8h': '8小时',
            '12h': '12小时',
            '1d': '24小时',
            '3d': '3天',
            '1w': '1周',
            '1M': '1个月'
        }

        # 获取预测结果
        predicted_class_index = prediction[0]
        predicted_state = market_states.get(predicted_class_index, "未知状态")
        time_window = interval_map.get(interval, interval)
        
        result = {
            'symbol': symbol,
            'predicted_market_state_index': predicted_class_index,
            'predicted_market_state': predicted_state,
            'time_window': time_window,
            'timestamp': datetime.now(),
            'current_price': market_analysis['current_price'],
            'price_change_24h': market_analysis['price_change_24h'],
            'trend': market_analysis['trend'],
            'technical_analysis': market_analysis['technical_analysis'],
            'support_resistance': market_analysis.get('support_resistance', {}),
            'volume_analysis': market_analysis.get('volume_analysis', {})
        }
        
        # 如果有概率预测，添加到结果中
        if prediction_proba is not None:
            result['class_probabilities'] = prediction_proba.tolist()
            
        # 生成报告
        report = f"\n{symbol}{'(永续)' if is_futures else ''} {interval}行情分析报告"
        report += f"\n\n基本信息："
        report += f"\n当前价格：{result['current_price']:.2f} USDT"
        report += f"\n24小时涨跌：{result['price_change_24h']:.2f}%"
        report += f"\n当前趋势：{result['trend']}"
        
        # 预测结果
        report += "\n\n预测结果："
        report += f"\n预测的市场状态：未来{time_window}，{result['predicted_market_state']}"
        if 'class_probabilities' in result:
            report += "\n各状态概率："
            probs = result['class_probabilities']
            # 按概率从高到低排序，并显示所有状态的概率
            sorted_probs = sorted([(i, prob) for i, prob in enumerate(probs)], key=lambda x: x[1], reverse=True)
            for i, prob in sorted_probs:
                if prob > 0.001:  # 只显示概率大于0.1%的状态
                    report += f"\n{market_states[i]}：概率为 {prob:.2%}"

        # 技术指标
        report += "\n\n技术指标分析："
        
        # KDJ分析（如果存在）
        if 'technical_analysis' in result and 'KDJ' in result['technical_analysis']:
            kdj = result['technical_analysis']['KDJ']
            if all(key in kdj for key in ['K', 'D', 'J']) and all(kdj[key] is not None for key in ['K', 'D', 'J']):
                report += "\nKDJ指标："
                try:
                    k, d, j = kdj['K'], kdj['D'], kdj['J']
                    report += f"\n  K值：{k:.2f} {'（超买）' if k > 80 else '（超卖）' if k < 20 else ''}"
                    report += f"\n  D值：{d:.2f}"
                    report += f"\n  J值：{j:.2f}"
                    if k > d:
                        report += "\n  当前KDJ金叉形态，可能上涨"
                    else:
                        report += "\n  当前KDJ死叉形态，可能下跌"
                except (TypeError, ValueError):
                    report += "  部分KDJ数据不可用"

        # MACD分析（如果存在）
        if 'technical_analysis' in result and 'MACD' in result['technical_analysis']:
            macd = result['technical_analysis']['MACD']
            if 'HIST' in macd and macd['HIST'] is not None:
                report += "\nMACD指标："
                try:
                    hist = macd['HIST']
                    report += f"\n  MACD柱状值：{hist:.2f}"
                    if hist > 0:
                        report += "（多头趋势）"
                    else:
                        report += "（空头趋势）"
                except (TypeError, ValueError):
                    report += "  MACD数据不完整"

        # 风险提示
        report += "\n\n风险提示："
        if 'support_resistance' in result and 'R2' in result['support_resistance'] and result['support_resistance']['R2'] is not None:
            try:
                report += f"\n1. 若价格突破{result['support_resistance']['R2']:.2f} USDT（R2）并站稳，需及时止损并重新评估趋势。"
            except (TypeError, ValueError):
                report += "\n1. 支撑/压力位数据暂不可用，请关注价格关键点位。"

        if ('technical_analysis' in result and 
            'ATR' in result['technical_analysis'] and 
            result['technical_analysis']['ATR'] is not None):
            try:
                atr = result['technical_analysis']['ATR']
                report += f"\n2. 当前ATR（{atr:.2f}）显示市场波动"
                if atr > 1000:
                    report += "剧烈，建议降低仓位或暂时观望。"
                elif atr > 500:
                    report += "较大，建议谨慎操作。"
                else:
                    report += "在正常范围，可以按计划操作。"
            except (TypeError, ValueError):
                report += "\n2. ATR数据暂不可用，请注意控制风险。"
        else:
            report += "\n2. 波动指标数据暂不可用，请注意控制风险。"

        report += "\n3. 本分析仅供参考，不构成投资建议。请结合其他因素综合判断。"
        
        result['detailed_report'] = report
        print(report)  # 直接打印报告
        return result
        
    except Exception as e:
        logger.error(f"预测过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        setup_environment() # Call setup at the beginning
        args = parse_args()
        
        # 设置默认参数
        original_symbol = args.symbol
        is_perpetual = original_symbol.endswith('_PERP')
        SYMBOL = get_futures_symbol(original_symbol)
        INTERVAL = args.interval
        
        if args.action == 'train':
            # 根据 contract_type 设置 is_futures
            is_futures_arg = True if args.contract_type == 'futures' else False
            
            trainer, results = train_model(
                SYMBOL, INTERVAL, args.data_start_date, args.prediction_window, args.model_type, is_futures_arg,
                args.lstm_units, args.lstm_timesteps, args.lstm_epochs, args.lstm_batch_size, args.lstm_learning_rate,
                args.use_optuna_main, args.optuna_n_trials_main
            )
            
            # 打印训练结果
            print("\n训练结果:")
            print(f"准确率: {results['mean_scores']['accuracy']:.4f} (+/- {results['std_scores']['accuracy']:.4f})")
            print(f"精确率: {results['mean_scores']['precision']:.4f} (+/- {results['std_scores']['precision']:.4f})")
            print(f"召回率: {results['mean_scores']['recall']:.4f} (+/- {results['std_scores']['recall']:.4f})")
            print(f"F1分数: {results['mean_scores']['f1']:.4f} (+/- {results['std_scores']['f1']:.4f})")
            
            if results.get('best_params'):
                print("\n最佳参数:")
                for param, value in results['best_params'].items():
                    print(f"{param}: {value}")
                    
            if results.get('feature_importance') is not None:
                print("\n特征重要性 (Top 5):")
                top_features = results['feature_importance'].head(5)
                for feature, importance in top_features.items():
                    print(f"{feature}: {importance:.4f}")
        elif args.action == 'predict':
            # 预测模式
            try:
                # 初始化 ModelTrainer
                trainer = ModelTrainer(
                    model_type=args.model_type,
                    model_path=args.model_path,
                    scaler_path=args.scaler_path
                )
                
                # 进行预测
                prediction = predict_next_movement(
                    trainer, 
                    args.symbol, 
                    args.interval, 
                    args.lookback_period, 
                    args.is_futures
                )
                
            # 打印预测结果
                print(f"\n{args.symbol}{'(永续)' if args.is_futures else ''} 预测结果:")
                print(f"当前价格: {prediction['current_price']:.2f} USDT")
                print(f"预测的市场状态: {prediction['predicted_market_state']}")
                print(f"预测时间: {prediction['timestamp']}")
                
                # 如果有概率预测，也打印出来
                if 'class_probabilities' in prediction:
                    print("\n各类别概率:")
                    for i, prob in enumerate(prediction['class_probabilities']):
                        print(f"类别 {i}: {prob:.2%}")
                
                # 打印市场分析
                if 'technical_analysis' in prediction:
                    print("\n技术分析:")
                    print(prediction['technical_analysis'])
                
            except Exception as e:
                logger.error(f"预测过程中发生错误: {str(e)}")
                raise
        elif args.action == 'continuous_predict':
            logger.info(f"启动连续预测模式: 符号={args.symbol}, 间隔={args.interval}, 模型类型={args.model_type}")
            run_continuous_prediction(
                symbol=args.symbol,
                interval=args.interval,
                model_path=args.model_path,
                scaler_path=args.scaler_path,
                model_type=args.model_type,
                lookback_period=args.lookback_period,
                is_futures=args.is_futures,
                kline_finish_buffer_seconds=args.buffer_seconds
            )
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {str(e)}")
        raise