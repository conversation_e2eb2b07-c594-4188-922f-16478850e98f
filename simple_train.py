#!/usr/bin/env python3
"""
最简单的训练脚本 - 避免复杂的交叉验证问题
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer, FeatureConfig

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score
import joblib

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

try:
    import lightgbm as lgb
    HAS_LGB = True
except ImportError:
    HAS_LGB = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_train_model(symbol='BTCUSDT', model_type='rf', days_back=90):
    """
    最简单的模型训练方法
    """
    print(f"🚀 开始简单训练 {symbol} {model_type.upper()} 模型...")
    
    try:
        # 1. 获取数据
        print("📊 获取数据...")
        start_date = (datetime.now() - pd.Timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, '1h', start_date)
        print(f"✅ 获取到 {len(df)} 条数据")
        
        # 2. 特征工程
        print("🔧 特征工程...")
        config = FeatureConfig(
            ma_periods=[5, 10, 20, 50],
            rsi_periods=[14],
            prediction_window=24
        )
        engineer = FeatureEngineer(config=config)
        df_features = engineer.create_features(df)
        
        # 3. 准备训练数据
        print("📋 准备训练数据...")
        
        # 检查目标列
        if 'target' not in df_features.columns:
            raise ValueError("目标列 'target' 未找到")
        
        # 分离特征和目标
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始列
        original_cols = ['open', 'high', 'low', 'close', 'volume']
        X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
        
        # 清理数据
        print("🧹 清理数据...")
        
        # 移除包含NaN的行
        before_clean = len(X)
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]
        after_clean = len(X)
        
        print(f"数据清理: {before_clean} -> {after_clean} 样本")
        
        # 处理无穷大值
        X.replace([np.inf, -np.inf], np.nan, inplace=True)
        X.fillna(X.median(), inplace=True)
        
        # 编码标签
        label_encoder = LabelEncoder()
        y_encoded = label_encoder.fit_transform(y)

        print(f"✅ 数据准备完成: {X.shape[1]} 特征, {len(y_encoded)} 样本")
        print(f"类别分布: {np.bincount(y_encoded)}")

        # 处理类别不平衡问题
        print("⚖️ 处理类别不平衡...")
        class_counts = np.bincount(y_encoded)
        print(f"原始类别分布: {class_counts}")
        min_samples_per_class = 2  # 每个类别至少需要2个样本才能分层抽样

        # 找出样本数过少的类别
        rare_classes = np.where(class_counts < min_samples_per_class)[0]

        if len(rare_classes) > 0:
            print(f"发现 {len(rare_classes)} 个稀有类别: {rare_classes}")
            print("移除稀有类别样本...")

            # 移除稀有类别的样本
            mask = ~np.isin(y_encoded, rare_classes)
            X = X[mask]
            y_encoded = y_encoded[mask]

            # 重新编码标签以确保连续性
            unique_labels = np.unique(y_encoded)
            label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
            y_encoded = np.array([label_mapping[label] for label in y_encoded])

            print(f"处理后样本数: {len(y_encoded)}")
            print(f"处理后类别分布: {np.bincount(y_encoded)}")

        # 检查是否还有足够的样本进行分层抽样
        final_class_counts = np.bincount(y_encoded)
        print(f"最终类别分布: {final_class_counts}")

        if np.any(final_class_counts < 2):
            print("⚠️ 仍有类别样本不足，使用随机分割而非分层分割")
            stratify_param = None
        else:
            print("✅ 所有类别样本充足，使用分层分割")
            stratify_param = y_encoded

        # 4. 分割数据
        print("✂️ 分割训练/测试数据...")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=stratify_param
        )
        
        # 5. 特征缩放
        print("📏 特征缩放...")
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 6. 创建和训练模型
        print(f"🎯 训练 {model_type.upper()} 模型...")
        
        if model_type == 'rf':
            model = RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == 'xgb' and HAS_XGB:
            model = xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1,
                eval_metric='mlogloss'
            )
        elif model_type == 'lgb' and HAS_LGB:
            model = lgb.LGBMClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            )
        else:
            print(f"模型类型 {model_type} 不可用，使用随机森林")
            model = RandomForestClassifier(
                n_estimators=200,
                random_state=42,
                n_jobs=-1
            )
        
        # 训练模型
        model.fit(X_train_scaled, y_train)
        
        # 7. 评估模型
        print("📊 评估模型...")
        
        # 训练集预测
        y_train_pred = model.predict(X_train_scaled)
        train_accuracy = accuracy_score(y_train, y_train_pred)
        
        # 测试集预测
        y_test_pred = model.predict(X_test_scaled)
        test_accuracy = accuracy_score(y_test, y_test_pred)
        
        # 8. 显示结果
        print("\n" + "="*50)
        print("🎉 训练完成!")
        print("="*50)
        print(f"训练集准确率: {train_accuracy:.4f}")
        print(f"测试集准确率: {test_accuracy:.4f}")
        
        # 详细分类报告
        print("\n📋 详细分类报告:")
        print(classification_report(y_test, y_test_pred))
        
        # 9. 保存模型
        print("💾 保存模型...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path("models")
        model_dir.mkdir(exist_ok=True)
        
        model_path = model_dir / f"simple_model_{model_type}_{symbol}_{timestamp}.joblib"
        scaler_path = model_dir / f"simple_scaler_{model_type}_{symbol}_{timestamp}.joblib"
        encoder_path = model_dir / f"simple_encoder_{model_type}_{symbol}_{timestamp}.joblib"
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        joblib.dump(label_encoder, encoder_path)
        
        print(f"✅ 模型已保存:")
        print(f"   模型: {model_path}")
        print(f"   缩放器: {scaler_path}")
        print(f"   编码器: {encoder_path}")
        
        # 10. 特征重要性 (如果支持)
        if hasattr(model, 'feature_importances_'):
            print("\n🔍 Top 10 重要特征:")
            feature_importance = pd.Series(
                model.feature_importances_, 
                index=X.columns
            ).sort_values(ascending=False)
            
            for i, (feature, importance) in enumerate(feature_importance.head(10).items(), 1):
                print(f"{i:2d}. {feature:30}: {importance:.6f}")
        
        return {
            'model': model,
            'scaler': scaler,
            'label_encoder': label_encoder,
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'feature_names': X.columns.tolist(),
            'model_path': model_path,
            'scaler_path': scaler_path,
            'encoder_path': encoder_path
        }
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

def batch_simple_train():
    """批量简单训练"""
    
    symbols = ['BTCUSDT', 'ETHUSDT']
    models = ['rf', 'xgb', 'lgb']
    
    results = {}
    
    for symbol in symbols:
        for model_type in models:
            print(f"\n{'='*60}")
            print(f"训练 {symbol} - {model_type.upper()}")
            print(f"{'='*60}")
            
            try:
                result = simple_train_model(symbol, model_type)
                results[f"{symbol}_{model_type}"] = result
                print(f"✅ {symbol} - {model_type} 训练完成")
                
            except Exception as e:
                print(f"❌ {symbol} - {model_type} 训练失败: {str(e)}")
                results[f"{symbol}_{model_type}"] = {'error': str(e)}
    
    # 显示汇总
    print(f"\n{'='*60}")
    print("📊 批量训练结果汇总")
    print(f"{'='*60}")
    
    for key, result in results.items():
        if 'test_accuracy' in result:
            print(f"{key:20}: 测试准确率 {result['test_accuracy']:.4f}")
        else:
            print(f"{key:20}: 训练失败")
    
    return results

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'batch':
            batch_simple_train()
        else:
            # 单个模型训练
            symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
            model_type = sys.argv[2] if len(sys.argv) > 2 else 'rf'
            simple_train_model(symbol, model_type)
    else:
        # 默认训练
        simple_train_model('BTCUSDT', 'xgb')
