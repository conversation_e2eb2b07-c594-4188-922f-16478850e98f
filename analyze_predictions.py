import pandas as pd
import json
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

PREDICTION_LOG_FILE = Path('prediction_log.jsonl')

def analyze_prediction_log(log_file: Path = PREDICTION_LOG_FILE):
    """
    分析 prediction_log.jsonl 文件，并输出基本统计信息。
    """
    if not log_file.exists():
        logger.error(f"预测日志文件 {log_file} 未找到。请先运行 predict_crypto.py 生成日志。")
        return

    predictions = []
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    predictions.append(json.loads(line.strip()))
                except json.JSONDecodeError as e:
                    logger.warning(f"跳过无法解析的行: {line.strip()} - 错误: {e}")
                    continue
    except Exception as e:
        logger.error(f"读取或解析日志文件 {log_file} 时发生错误: {e}", exc_info=True)
        return

    if not predictions:
        logger.info(f"日志文件 {log_file} 为空或未能加载任何预测记录。")
        return

    df = pd.DataFrame(predictions)
    logger.info(f"成功从 {log_file} 加载了 {len(df)} 条预测记录。")

    # 基本统计
    print("\n--- 预测日志分析 ---")
    print(f"总预测次数: {len(df)}")

    if 'core_prediction' in df.columns:
        print("\n预测结果分布:")
        print(df['core_prediction'].value_counts(normalize=True).apply(lambda x: f"{x:.2%}"))
    else:
        print("\n未能找到 'core_prediction' 列用于统计结果分布。")

    if 'prediction_time' in df.columns:
        df['prediction_time'] = pd.to_datetime(df['prediction_time'])
        print(f"\n预测时间范围: 从 {df['prediction_time'].min()} 到 {df['prediction_time'].max()}")
    
    print("\n最近 5 条预测记录摘要:")
    # 选择一些关键列进行展示，可以根据需要调整
    columns_to_show = ['prediction_time', 'symbol', 'target_interval', 'core_prediction', 'current_price_basis', 'real_time_snapshot_price', 'expected_change_pct_str']
    # 为了处理列可能不存在的情况，并构建 "current_price_basis"
    display_df_data = []
    for _, row in df.tail(5).iterrows():
        record = {}
        record['prediction_time'] = row.get('prediction_time', 'N/A')
        record['symbol'] = row.get('symbol', 'N/A')
        record['target_interval'] = row.get('target_interval', 'N/A')
        record['core_prediction'] = row.get('core_prediction', 'N/A')
        
        kline_close = row.get('indicators', {}).get('current_price', 'N/A')
        record['current_price_basis'] = f"{kline_close:.2f}" if isinstance(kline_close, (float, int)) else kline_close
        
        snapshot_price = row.get('real_time_snapshot_price', 'N/A')
        record['real_time_snapshot_price'] = f"{snapshot_price:.2f}" if isinstance(snapshot_price, (float, int)) else snapshot_price
        
        record['expected_change_pct_str'] = row.get('expected_change_pct_str', 'N/A')
        display_df_data.append(record)

    if display_df_data:
        display_df = pd.DataFrame(display_df_data)[columns_to_show] # 确保列顺序
        print(display_df.to_string())
    else:
        print("没有足够的记录来展示。")
    
    # 示例：分析实时价格快照与K线收盘价的差异
    if 'indicators' in df.columns and 'real_time_snapshot_price' in df.columns:
        prices_data = []
        for _, row in df.iterrows():
            kline_close = row.get('indicators', {}).get('current_price')
            snapshot_price = row.get('real_time_snapshot_price')
            if isinstance(kline_close, (int, float)) and isinstance(snapshot_price, (int, float)):
                prices_data.append({
                    'kline_close': kline_close,
                    'snapshot_price': snapshot_price,
                    'difference': snapshot_price - kline_close,
                    'difference_pct': ((snapshot_price - kline_close) / kline_close) * 100 if kline_close != 0 else 0
                })
        
        if prices_data:
            price_diff_df = pd.DataFrame(prices_data)
            print("\n--- 实时价格快照 vs K线收盘价分析 ---")
            print(f"平均差异: {price_diff_df['difference'].mean():.2f}")
            print(f"平均差异百分比: {price_diff_df['difference_pct'].mean():.2f}%")
            print(f"最大正差异: {price_diff_df['difference'].max():.2f} ({price_diff_df['difference_pct'].max():.2f}%)")
            print(f"最大负差异: {price_diff_df['difference'].min():.2f} ({price_diff_df['difference_pct'].min():.2f}%)")
        else:
            print("\n未能提取足够数据进行价格差异分析。") 

    print("\n--- 分析结束 ---")

if __name__ == "__main__":
    analyze_prediction_log() 