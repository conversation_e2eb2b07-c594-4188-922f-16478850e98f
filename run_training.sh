#!/bin/bash

echo "========================================"
echo "加密货币预测模型训练脚本"
echo "========================================"
echo

echo "选择训练模式:"
echo "1. 快速训练 (推荐新手)"
echo "2. 标准训练"
echo "3. 高级训练 (LSTM)"
echo "4. 批量训练"
echo "5. 模型比较"
echo "6. 改进训练 (最高准确性)"
echo

read -p "请输入选择 (1-6): " choice

case $choice in
    1)
        echo
        echo "🚀 开始快速训练..."
        python quick_train.py
        ;;
    2)
        echo
        echo "🔧 开始标准训练..."
        python main.py train --symbol BTCUSDT --interval 1h --model_type xgb
        ;;
    3)
        echo
        echo "🧠 开始LSTM训练..."
        python train_crypto_model.py --symbol BTCUSDT --interval 1h --model-type lstm --lstm-timesteps 15 --lstm-epochs 100
        ;;
    4)
        echo
        echo "📊 开始批量训练..."
        python quick_train.py batch
        ;;
    5)
        echo
        echo "🔍 开始模型比较..."
        python quick_train.py compare BTCUSDT
        ;;
    6)
        echo
        echo "🚀 开始改进训练..."
        python improved_training.py
        ;;
    *)
        echo "无效选择，退出..."
        exit 1
        ;;
esac

echo
echo "训练完成！"
