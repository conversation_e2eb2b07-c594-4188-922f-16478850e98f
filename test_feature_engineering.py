#!/usr/bin/env python3
"""
测试特征工程模块
"""

import pandas as pd
import numpy as np
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_feature_engineering import AdvancedFeatureEngineer

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    
    # 创建1000个数据点
    n_points = 1000
    dates = pd.date_range('2024-01-01', periods=n_points, freq='5T')
    
    # 生成价格序列
    price = 100.0
    prices = []
    volumes = []
    
    for i in range(n_points):
        # 价格随机游走
        change = np.random.normal(0, 0.001)
        price *= (1 + change)
        prices.append(price)
        
        # 成交量
        volume = np.random.lognormal(10, 1)
        volumes.append(volume)
    
    # 创建OHLC数据
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = [prices[max(0, i-1)] for i in range(n_points)]
    data['high'] = [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices]
    data['low'] = [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices]
    data['volume'] = volumes
    
    # 确保OHLC数据的合理性
    data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
    data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))
    
    return data

def test_feature_engineering():
    """测试特征工程"""
    print("🧪 测试高级特征工程...")
    
    # 创建测试数据
    data = create_test_data()
    print(f"✅ 创建测试数据: {len(data)} 条记录")
    
    # 创建特征工程器
    feature_engineer = AdvancedFeatureEngineer()
    
    # 测试各类特征创建
    print("\n📊 测试特征创建...")
    
    try:
        # 价格特征
        price_features = feature_engineer.create_price_features(data)
        print(f"  ✅ 价格特征: {len(price_features.columns)} 个")
        
        # 成交量特征
        volume_features = feature_engineer.create_volume_features(data)
        print(f"  ✅ 成交量特征: {len(volume_features.columns)} 个")
        
        # 波动率特征
        volatility_features = feature_engineer.create_volatility_features(data)
        print(f"  ✅ 波动率特征: {len(volatility_features.columns)} 个")
        
        # 动量特征
        momentum_features = feature_engineer.create_momentum_features(data)
        print(f"  ✅ 动量特征: {len(momentum_features.columns)} 个")
        
        # 趋势特征
        trend_features = feature_engineer.create_trend_features(data)
        print(f"  ✅ 趋势特征: {len(trend_features.columns)} 个")
        
        # 模式特征
        pattern_features = feature_engineer.create_pattern_features(data)
        print(f"  ✅ 模式特征: {len(pattern_features.columns)} 个")
        
        # 市场结构特征
        market_structure_features = feature_engineer.create_market_structure_features(data)
        print(f"  ✅ 市场结构特征: {len(market_structure_features.columns)} 个")
        
    except Exception as e:
        print(f"  ❌ 特征创建失败: {e}")
        return False
    
    # 测试所有特征创建
    print("\n🔧 测试所有特征创建...")
    try:
        all_features = feature_engineer.create_all_features(data)
        print(f"  ✅ 总特征数: {len(all_features.columns)}")
        print(f"  ✅ 数据形状: {all_features.shape}")
        
        # 检查数据质量
        missing_ratio = all_features.isnull().sum().sum() / (all_features.shape[0] * all_features.shape[1])
        print(f"  📊 缺失值比例: {missing_ratio:.3f}")
        
        inf_count = np.isinf(all_features.select_dtypes(include=[np.number])).sum().sum()
        print(f"  📊 无穷值数量: {inf_count}")
        
    except Exception as e:
        print(f"  ❌ 所有特征创建失败: {e}")
        return False
    
    # 测试特征选择
    print("\n🎯 测试特征选择...")
    try:
        # 创建目标变量(未来5期收益率)
        target = data['close'].pct_change(5).shift(-5)
        
        # 特征选择
        selected_features = feature_engineer.select_best_features(all_features, target, n_features=20)
        print(f"  ✅ 选择特征数: {len(selected_features)}")
        
        # 获取报告
        report = feature_engineer.get_feature_engineering_report()
        print(f"  📊 特征分布: {report['feature_distribution']}")
        
    except Exception as e:
        print(f"  ❌ 特征选择失败: {e}")
        return False
    
    # 测试特征重要性
    print("\n📈 特征重要性分析...")
    try:
        if hasattr(feature_engineer, 'feature_importance') and feature_engineer.feature_importance:
            rf_importance = feature_engineer.feature_importance.get('random_forest', {})
            if rf_importance:
                top_features = sorted(rf_importance.items(), key=lambda x: x[1], reverse=True)[:10]
                print("  🏆 Top 10 重要特征:")
                for i, (feature, importance) in enumerate(top_features, 1):
                    print(f"    {i:2d}. {feature}: {importance:.4f}")
        
    except Exception as e:
        print(f"  ⚠️ 特征重要性分析失败: {e}")
    
    return True

def test_feature_categories():
    """测试特征分类"""
    print("\n📂 测试特征分类...")
    
    feature_engineer = AdvancedFeatureEngineer()
    data = create_test_data()
    
    # 创建所有特征
    all_features = feature_engineer.create_all_features(data)
    
    # 检查特征分类
    total_categorized = 0
    for category, features in feature_engineer.feature_categories.items():
        count = len(features)
        total_categorized += count
        print(f"  📊 {category}: {count} 个特征")
    
    print(f"  📊 总分类特征: {total_categorized}")
    print(f"  📊 实际特征数: {len(all_features.columns)}")
    
    # 检查是否有未分类的特征
    all_categorized_features = set()
    for features in feature_engineer.feature_categories.values():
        all_categorized_features.update(features)
    
    uncategorized = set(all_features.columns) - all_categorized_features
    if uncategorized:
        print(f"  ⚠️ 未分类特征: {len(uncategorized)} 个")
        print(f"    {list(uncategorized)[:5]}...")  # 显示前5个
    else:
        print(f"  ✅ 所有特征都已正确分类")

if __name__ == "__main__":
    print("🎉 开始特征工程测试")
    print("=" * 60)
    
    # 基础功能测试
    success = test_feature_engineering()
    
    if success:
        # 特征分类测试
        test_feature_categories()
        
        print("\n" + "=" * 60)
        print("🎉 特征工程测试完成！")
        print("\n✅ 主要改进:")
        print("  🔧 不依赖talib，使用自定义技术指标")
        print("  📊 创建7大类特征，覆盖全面")
        print("  🎯 智能特征选择，提高信号质量")
        print("  📈 特征重要性分析，可解释性强")
        print("  🛡️ 数据质量检查，确保稳定性")
        print("  📂 特征分类管理，便于维护")
        
        print("\n💡 预期效果:")
        print("  📈 提高预测准确率")
        print("  🎯 减少噪音特征")
        print("  ⚡ 加快模型训练")
        print("  🔍 增强可解释性")
        
    else:
        print("\n❌ 特征工程测试失败")
        print("需要进一步调试和优化")
