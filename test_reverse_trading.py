#!/usr/bin/env python3
"""
Test script to verify reverse trading strategy
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader

def test_reverse_trading():
    """Test the reverse trading strategy"""
    print("🧪 Testing Reverse Trading Strategy")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 Problem Analysis:")
    print("❌ Current win rate: 11.8% (2胜/15负)")
    print("💡 User suggestion: Reverse trading strategy")
    print("🎯 Logic: If AI predicts down → Trade up")
    print("🎯 Logic: If AI predicts up → Trade down")
    
    print("\n🔧 Reverse Trading Implementation:")
    print("✅ AI predicts UP (类别1,2) → Execute SHORT")
    print("✅ AI predicts DOWN (类别0,3) → Execute LONG")
    print("💡 Expected result: Higher win rate")
    
    # Test different AI prediction scenarios
    test_scenarios = [
        {
            'ai_prediction': 0,
            'ai_description': 'AI预测下跌',
            'expected_action': 'LONG',
            'expected_description': '反向做多'
        },
        {
            'ai_prediction': 1,
            'ai_description': 'AI预测上涨',
            'expected_action': 'SHORT',
            'expected_description': '反向做空'
        },
        {
            'ai_prediction': 2,
            'ai_description': 'AI预测上涨',
            'expected_action': 'SHORT',
            'expected_description': '反向做空'
        },
        {
            'ai_prediction': 3,
            'ai_description': 'AI预测下跌',
            'expected_action': 'LONG',
            'expected_description': '反向做多'
        }
    ]
    
    print(f"\n🧪 Testing Reverse Logic:")
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n   📊 Test {i}: AI Prediction = {scenario['ai_prediction']}")
        print(f"      🤖 AI Says: {scenario['ai_description']}")
        print(f"      🎯 Expected Action: {scenario['expected_action']}")
        print(f"      💡 Expected Description: {scenario['expected_description']}")
        
        # Create mock market data for testing
        mock_market_data = {
            'current_price': 102400.0,
            'features': {f'feature_{i}': 0.5 for i in range(24)},
            'price_change_1h': -0.002,
            'volatility': 0.005
        }
        
        # Mock the AI prediction result
        mock_features = [0.5] * 24  # 24 features
        
        try:
            # Test the AI prediction with reverse logic
            ai_result = trader.predict_with_ai_model(mock_market_data)
            
            # Check if the reverse logic is working
            if ai_result['direction'] in ['LONG', 'SHORT']:
                print(f"      ✅ System Action: {ai_result['direction']}")
                print(f"      ✅ System Description: {ai_result['action']}")
                print(f"      ✅ Reason: {ai_result['reason']}")
                
                # Verify reverse logic
                if scenario['ai_prediction'] in [0, 3] and ai_result['direction'] == 'LONG':
                    print(f"      ✅ CORRECT: AI predicts down → System goes LONG")
                elif scenario['ai_prediction'] in [1, 2] and ai_result['direction'] == 'SHORT':
                    print(f"      ✅ CORRECT: AI predicts up → System goes SHORT")
                else:
                    print(f"      ❌ ERROR: Reverse logic not working correctly")
            else:
                print(f"      ⚠️ NEUTRAL: System chose to wait")
                
        except Exception as e:
            print(f"      ❌ ERROR: {e}")
    
    # Calculate expected improvement
    print(f"\n📈 Expected Win Rate Improvement:")
    
    current_win_rate = 11.8
    if current_win_rate < 50:
        expected_reverse_win_rate = 100 - current_win_rate
        improvement = expected_reverse_win_rate - current_win_rate
        
        print(f"   📊 Current Win Rate: {current_win_rate:.1f}%")
        print(f"   🔄 Expected Reverse Win Rate: {expected_reverse_win_rate:.1f}%")
        print(f"   📈 Expected Improvement: +{improvement:.1f}%")
        
        if expected_reverse_win_rate > 80:
            print(f"   🎉 EXCELLENT: Very high expected win rate!")
        elif expected_reverse_win_rate > 60:
            print(f"   ✅ GOOD: Solid expected win rate")
        elif expected_reverse_win_rate > 50:
            print(f"   ✅ POSITIVE: Better than random")
        else:
            print(f"   ⚠️ STILL LOW: May need further optimization")
    
    # Risk analysis
    print(f"\n⚖️ Risk Analysis with Reverse Trading:")
    
    # With 5% margin usage and isolated margin
    margin_per_trade = 50.0 * 0.05  # $2.5
    max_loss_per_trade = margin_per_trade  # Isolated margin protection
    
    print(f"   💰 Margin per trade: ${margin_per_trade:.2f}")
    print(f"   🛡️ Max loss per trade: ${max_loss_per_trade:.2f}")
    print(f"   📊 Account impact per loss: {(max_loss_per_trade/50)*100:.1f}%")
    
    # Calculate potential with improved win rate
    if 'expected_reverse_win_rate' in locals():
        trades_per_day = 20  # Estimate
        avg_profit_per_win = 2.0  # Estimate $2 profit per win
        avg_loss_per_loss = 2.5   # Max loss per trade
        
        daily_wins = trades_per_day * (expected_reverse_win_rate / 100)
        daily_losses = trades_per_day * ((100 - expected_reverse_win_rate) / 100)
        
        daily_profit = (daily_wins * avg_profit_per_win) - (daily_losses * avg_loss_per_loss)
        
        print(f"\n💰 Potential Daily Performance:")
        print(f"   📊 Estimated trades/day: {trades_per_day}")
        print(f"   ✅ Expected wins: {daily_wins:.1f}")
        print(f"   ❌ Expected losses: {daily_losses:.1f}")
        print(f"   💰 Expected daily P&L: ${daily_profit:+.2f}")
        
        if daily_profit > 0:
            print(f"   🎉 PROFITABLE: Positive expected return!")
        else:
            print(f"   ⚠️ UNPROFITABLE: Still negative expected return")
    
    print(f"\n" + "="*60)
    print("🎉 Reverse Trading Strategy Test Complete!")
    
    print(f"\n✅ Implementation Summary:")
    print(f"   🔄 Reverse logic: AI prediction → Opposite action")
    print(f"   📈 Expected win rate: ~88.2% (reverse of 11.8%)")
    print(f"   🛡️ Risk control: $2.5 max loss per trade")
    print(f"   💰 Position size: 50u × 125x × 5%")
    
    print(f"\n💡 Key Benefits:")
    print(f"   🎯 Higher win rate: 11.8% → ~88.2%")
    print(f"   💰 Better profitability: More wins than losses")
    print(f"   🛡️ Same risk control: Isolated margin protection")
    print(f"   ⚡ Same execution: High-frequency scalping")
    
    print(f"\n🚀 Next Steps:")
    print(f"   1. Monitor actual win rate improvement")
    print(f"   2. Fine-tune reverse logic if needed")
    print(f"   3. Adjust position sizing based on results")
    print(f"   4. Consider partial reverse (not 100% opposite)")

if __name__ == "__main__":
    test_reverse_trading()
