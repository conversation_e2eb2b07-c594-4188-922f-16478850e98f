"""
模型诊断和分析工具
用于识别和解决模型准确性问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
from sklearn.model_selection import learning_curve, validation_curve
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class ModelDiagnostics:
    """模型诊断工具"""
    
    def __init__(self, output_dir: str = 'diagnostics'):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def comprehensive_analysis(self, model, X: pd.DataFrame, y: pd.Series, 
                             model_name: str = 'model') -> Dict:
        """综合模型分析"""
        logger.info(f"开始 {model_name} 的综合诊断分析...")
        
        results = {}
        
        # 1. 基础性能分析
        results['basic_performance'] = self._analyze_basic_performance(model, X, y)
        
        # 2. 学习曲线分析
        results['learning_curves'] = self._analyze_learning_curves(model, X, y, model_name)
        
        # 3. 特征重要性分析
        results['feature_importance'] = self._analyze_feature_importance(model, X, model_name)
        
        # 4. 预测分布分析
        results['prediction_distribution'] = self._analyze_prediction_distribution(model, X, y, model_name)
        
        # 5. 错误分析
        results['error_analysis'] = self._analyze_errors(model, X, y, model_name)
        
        # 6. 数据质量分析
        results['data_quality'] = self._analyze_data_quality(X, y)
        
        # 7. 生成诊断报告
        self._generate_diagnostic_report(results, model_name)
        
        return results
    
    def _analyze_basic_performance(self, model, X: pd.DataFrame, y: pd.Series) -> Dict:
        """基础性能分析"""
        y_pred = model.predict(X)
        y_pred_proba = model.predict_proba(X) if hasattr(model, 'predict_proba') else None
        
        # 分类报告
        class_report = classification_report(y, y_pred, output_dict=True)
        
        # 混淆矩阵
        conf_matrix = confusion_matrix(y, y_pred)
        
        # 类别分布
        true_dist = y.value_counts().sort_index()
        pred_dist = pd.Series(y_pred).value_counts().sort_index()
        
        return {
            'classification_report': class_report,
            'confusion_matrix': conf_matrix,
            'true_distribution': true_dist.to_dict(),
            'predicted_distribution': pred_dist.to_dict(),
            'accuracy': (y == y_pred).mean(),
            'prediction_confidence': np.mean(np.max(y_pred_proba, axis=1)) if y_pred_proba is not None else None
        }
    
    def _analyze_learning_curves(self, model, X: pd.DataFrame, y: pd.Series, model_name: str) -> Dict:
        """学习曲线分析"""
        logger.info("分析学习曲线...")
        
        try:
            # 学习曲线
            train_sizes, train_scores, val_scores = learning_curve(
                model, X, y, cv=5, n_jobs=-1, 
                train_sizes=np.linspace(0.1, 1.0, 10),
                scoring='accuracy'
            )
            
            # 绘制学习曲线
            plt.figure(figsize=(10, 6))
            plt.plot(train_sizes, np.mean(train_scores, axis=1), 'o-', label='训练分数')
            plt.plot(train_sizes, np.mean(val_scores, axis=1), 'o-', label='验证分数')
            plt.fill_between(train_sizes, 
                           np.mean(train_scores, axis=1) - np.std(train_scores, axis=1),
                           np.mean(train_scores, axis=1) + np.std(train_scores, axis=1), 
                           alpha=0.1)
            plt.fill_between(train_sizes, 
                           np.mean(val_scores, axis=1) - np.std(val_scores, axis=1),
                           np.mean(val_scores, axis=1) + np.std(val_scores, axis=1), 
                           alpha=0.1)
            plt.xlabel('训练样本数')
            plt.ylabel('准确率')
            plt.title(f'{model_name} 学习曲线')
            plt.legend()
            plt.grid(True)
            plt.savefig(self.output_dir / f'{model_name}_learning_curve.png')
            plt.close()
            
            # 分析过拟合/欠拟合
            final_train_score = np.mean(train_scores[-1])
            final_val_score = np.mean(val_scores[-1])
            gap = final_train_score - final_val_score
            
            if gap > 0.1:
                diagnosis = "过拟合"
            elif final_val_score < 0.6:
                diagnosis = "欠拟合"
            else:
                diagnosis = "正常"
            
            return {
                'train_scores': train_scores.tolist(),
                'val_scores': val_scores.tolist(),
                'train_sizes': train_sizes.tolist(),
                'final_train_score': final_train_score,
                'final_val_score': final_val_score,
                'overfitting_gap': gap,
                'diagnosis': diagnosis
            }
            
        except Exception as e:
            logger.error(f"学习曲线分析失败: {e}")
            return {'error': str(e)}
    
    def _analyze_feature_importance(self, model, X: pd.DataFrame, model_name: str) -> Dict:
        """特征重要性分析"""
        logger.info("分析特征重要性...")
        
        try:
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
            elif hasattr(model, 'coef_'):
                importance = np.abs(model.coef_[0])
            else:
                return {'error': '模型不支持特征重要性分析'}
            
            feature_importance = pd.Series(importance, index=X.columns).sort_values(ascending=False)
            
            # 绘制特征重要性
            plt.figure(figsize=(12, 8))
            top_features = feature_importance.head(20)
            top_features.plot(kind='barh')
            plt.title(f'{model_name} Top 20 特征重要性')
            plt.xlabel('重要性')
            plt.tight_layout()
            plt.savefig(self.output_dir / f'{model_name}_feature_importance.png')
            plt.close()
            
            # 分析特征重要性分布
            importance_stats = {
                'top_10_features': top_features.head(10).to_dict(),
                'importance_concentration': (top_features.head(10).sum() / feature_importance.sum()),
                'zero_importance_features': (feature_importance == 0).sum(),
                'low_importance_features': (feature_importance < 0.001).sum()
            }
            
            return {
                'feature_importance': feature_importance.to_dict(),
                'statistics': importance_stats
            }
            
        except Exception as e:
            logger.error(f"特征重要性分析失败: {e}")
            return {'error': str(e)}
    
    def _analyze_prediction_distribution(self, model, X: pd.DataFrame, y: pd.Series, model_name: str) -> Dict:
        """预测分布分析"""
        logger.info("分析预测分布...")
        
        y_pred = model.predict(X)
        y_pred_proba = model.predict_proba(X) if hasattr(model, 'predict_proba') else None
        
        # 预测置信度分析
        if y_pred_proba is not None:
            confidence = np.max(y_pred_proba, axis=1)
            
            plt.figure(figsize=(12, 4))
            
            # 置信度分布
            plt.subplot(1, 2, 1)
            plt.hist(confidence, bins=50, alpha=0.7)
            plt.xlabel('预测置信度')
            plt.ylabel('频次')
            plt.title('预测置信度分布')
            
            # 置信度vs准确率
            plt.subplot(1, 2, 2)
            confidence_bins = np.linspace(0, 1, 11)
            bin_accuracy = []
            for i in range(len(confidence_bins)-1):
                mask = (confidence >= confidence_bins[i]) & (confidence < confidence_bins[i+1])
                if mask.sum() > 0:
                    acc = (y[mask] == y_pred[mask]).mean()
                    bin_accuracy.append(acc)
                else:
                    bin_accuracy.append(0)
            
            plt.plot(confidence_bins[:-1], bin_accuracy, 'o-')
            plt.xlabel('置信度区间')
            plt.ylabel('准确率')
            plt.title('置信度vs准确率')
            
            plt.tight_layout()
            plt.savefig(self.output_dir / f'{model_name}_prediction_analysis.png')
            plt.close()
            
            return {
                'confidence_stats': {
                    'mean_confidence': np.mean(confidence),
                    'std_confidence': np.std(confidence),
                    'low_confidence_ratio': (confidence < 0.5).mean()
                },
                'calibration': {
                    'confidence_bins': confidence_bins[:-1].tolist(),
                    'bin_accuracy': bin_accuracy
                }
            }
        
        return {'error': '模型不支持概率预测'}
    
    def _analyze_errors(self, model, X: pd.DataFrame, y: pd.Series, model_name: str) -> Dict:
        """错误分析"""
        logger.info("分析预测错误...")
        
        y_pred = model.predict(X)
        errors = y != y_pred
        
        if errors.sum() == 0:
            return {'message': '没有预测错误'}
        
        # 错误样本分析
        error_samples = X[errors]
        correct_samples = X[~errors]
        
        # 错误样本的特征统计
        error_stats = {}
        for col in X.columns:
            if X[col].dtype in ['int64', 'float64']:
                error_mean = error_samples[col].mean()
                correct_mean = correct_samples[col].mean()
                error_stats[col] = {
                    'error_mean': error_mean,
                    'correct_mean': correct_mean,
                    'difference': abs(error_mean - correct_mean)
                }
        
        # 找出错误样本中差异最大的特征
        feature_differences = {k: v['difference'] for k, v in error_stats.items()}
        top_diff_features = sorted(feature_differences.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'error_rate': errors.mean(),
            'error_count': errors.sum(),
            'top_discriminative_features': top_diff_features,
            'error_by_class': pd.crosstab(y[errors], y_pred[errors]).to_dict()
        }
    
    def _analyze_data_quality(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """数据质量分析"""
        logger.info("分析数据质量...")
        
        quality_issues = {}
        
        # 缺失值分析
        missing_data = X.isnull().sum()
        quality_issues['missing_data'] = missing_data[missing_data > 0].to_dict()
        
        # 重复样本
        duplicates = X.duplicated().sum()
        quality_issues['duplicate_samples'] = duplicates
        
        # 常数特征
        constant_features = []
        for col in X.columns:
            if X[col].nunique() <= 1:
                constant_features.append(col)
        quality_issues['constant_features'] = constant_features
        
        # 高相关性特征对
        corr_matrix = X.corr().abs()
        high_corr_pairs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                if corr_matrix.iloc[i, j] > 0.95:
                    high_corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_matrix.iloc[i, j]))
        quality_issues['high_correlation_pairs'] = high_corr_pairs
        
        # 类别不平衡
        class_distribution = y.value_counts()
        imbalance_ratio = class_distribution.max() / class_distribution.min()
        quality_issues['class_imbalance'] = {
            'ratio': imbalance_ratio,
            'distribution': class_distribution.to_dict()
        }
        
        return quality_issues
    
    def _generate_diagnostic_report(self, results: Dict, model_name: str):
        """生成诊断报告"""
        logger.info("生成诊断报告...")
        
        report_path = self.output_dir / f'{model_name}_diagnostic_report.txt'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"模型诊断报告: {model_name}\n")
            f.write("=" * 50 + "\n\n")
            
            # 基础性能
            if 'basic_performance' in results:
                perf = results['basic_performance']
                f.write("1. 基础性能分析\n")
                f.write(f"   准确率: {perf['accuracy']:.4f}\n")
                if perf['prediction_confidence']:
                    f.write(f"   平均置信度: {perf['prediction_confidence']:.4f}\n")
                f.write("\n")
            
            # 学习曲线诊断
            if 'learning_curves' in results and 'diagnosis' in results['learning_curves']:
                lc = results['learning_curves']
                f.write("2. 学习曲线诊断\n")
                f.write(f"   诊断结果: {lc['diagnosis']}\n")
                f.write(f"   训练-验证差距: {lc.get('overfitting_gap', 0):.4f}\n")
                f.write("\n")
            
            # 数据质量问题
            if 'data_quality' in results:
                dq = results['data_quality']
                f.write("3. 数据质量问题\n")
                f.write(f"   重复样本: {dq['duplicate_samples']}\n")
                f.write(f"   常数特征: {len(dq['constant_features'])}\n")
                f.write(f"   高相关特征对: {len(dq['high_correlation_pairs'])}\n")
                f.write(f"   类别不平衡比例: {dq['class_imbalance']['ratio']:.2f}\n")
                f.write("\n")
            
            # 改进建议
            f.write("4. 改进建议\n")
            suggestions = self._generate_suggestions(results)
            for suggestion in suggestions:
                f.write(f"   - {suggestion}\n")
        
        logger.info(f"诊断报告已保存到: {report_path}")
    
    def _generate_suggestions(self, results: Dict) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 基于学习曲线的建议
        if 'learning_curves' in results and 'diagnosis' in results['learning_curves']:
            diagnosis = results['learning_curves']['diagnosis']
            if diagnosis == "过拟合":
                suggestions.extend([
                    "增加正则化强度",
                    "减少模型复杂度",
                    "增加训练数据",
                    "使用Dropout或Early Stopping"
                ])
            elif diagnosis == "欠拟合":
                suggestions.extend([
                    "增加模型复杂度",
                    "添加更多特征",
                    "减少正则化",
                    "增加训练轮数"
                ])
        
        # 基于数据质量的建议
        if 'data_quality' in results:
            dq = results['data_quality']
            if dq['class_imbalance']['ratio'] > 3:
                suggestions.append("使用SMOTE或其他方法处理类别不平衡")
            if len(dq['constant_features']) > 0:
                suggestions.append("移除常数特征")
            if len(dq['high_correlation_pairs']) > 0:
                suggestions.append("移除高相关性特征")
        
        # 基于预测置信度的建议
        if 'basic_performance' in results:
            conf = results['basic_performance'].get('prediction_confidence')
            if conf and conf < 0.6:
                suggestions.append("模型置信度较低，考虑改进特征工程或模型架构")
        
        return suggestions

if __name__ == "__main__":
    # 使用示例
    diagnostics = ModelDiagnostics()
    
    # 假设已有训练好的模型和数据
    # results = diagnostics.comprehensive_analysis(model, X_test, y_test, 'XGBoost')
    print("模型诊断工具已准备就绪")
