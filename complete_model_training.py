#!/usr/bin/env python3
"""
完整的AI模型训练流程
结合高级特征工程和模型训练
"""

import pandas as pd
import numpy as np
import logging
import sys
import os
from datetime import datetime
from typing import Dict, List

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_feature_engineering import AdvancedFeatureEngineer
from enhanced_ai_model import EnhancedAIModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CompleteModelTrainer:
    """完整的模型训练器"""
    
    def __init__(self):
        self.feature_engineer = AdvancedFeatureEngineer()
        self.ai_model = EnhancedAIModel()
        self.training_data = None
        self.features = None
        self.target = None
        
    def create_enhanced_test_data(self, n_points: int = 3000) -> pd.DataFrame:
        """创建增强的测试数据"""
        logger.info(f"创建 {n_points} 条增强测试数据...")
        
        np.random.seed(42)
        dates = pd.date_range('2024-01-01', periods=n_points, freq='5T')
        
        # 创建更真实的价格数据
        price = 100.0
        prices = []
        volumes = []
        
        # 添加多种市场状态
        for i in range(n_points):
            # 长期趋势
            long_trend = 0.0001 * np.sin(i / 200)
            
            # 中期波动
            medium_cycle = 0.0005 * np.sin(i / 50)
            
            # 短期噪音
            short_noise = np.random.normal(0, 0.003)
            
            # 偶尔的大幅波动
            if np.random.random() < 0.01:  # 1%概率
                shock = np.random.normal(0, 0.02)
            else:
                shock = 0
            
            # 综合价格变动
            total_change = long_trend + medium_cycle + short_noise + shock
            price *= (1 + total_change)
            prices.append(price)
            
            # 成交量与波动率相关
            volatility = abs(total_change)
            volume = np.random.lognormal(10 + volatility * 100, 1)
            volumes.append(volume)
        
        # 创建OHLC数据
        data = pd.DataFrame(index=dates)
        data['close'] = prices
        data['open'] = [prices[max(0, i-1)] for i in range(n_points)]
        
        # 高低价基于收盘价和随机波动
        highs = []
        lows = []
        for i, price in enumerate(prices):
            daily_range = abs(np.random.normal(0, price * 0.005))
            high = price + daily_range * np.random.random()
            low = price - daily_range * np.random.random()
            highs.append(high)
            lows.append(low)
        
        data['high'] = highs
        data['low'] = lows
        data['volume'] = volumes
        
        # 确保OHLC数据合理性
        data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
        data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))
        
        logger.info(f"数据创建完成: {len(data)} 条记录")
        return data
    
    def run_complete_training(self, use_test_data: bool = True) -> Dict:
        """运行完整的训练流程"""
        logger.info("🚀 开始完整的AI模型训练流程...")
        
        # 1. 准备数据
        if use_test_data:
            self.training_data = self.create_enhanced_test_data()
        else:
            # 这里可以加载真实数据
            logger.error("真实数据加载功能待实现")
            return {}
        
        # 2. 特征工程
        logger.info("📊 开始特征工程...")
        self.features = self.feature_engineer.create_all_features(self.training_data)
        logger.info(f"创建了 {len(self.features.columns)} 个特征")
        
        # 3. 创建目标标签
        logger.info("🎯 创建目标标签...")
        self.target = self.ai_model.create_target_labels(
            self.training_data, 
            future_periods=5, 
            threshold=0.003  # 0.3%的阈值
        )
        
        # 4. 特征选择
        logger.info("🔍 进行特征选择...")
        selected_features = self.feature_engineer.select_best_features(
            self.features, 
            self.target, 
            n_features=25  # 选择25个最佳特征
        )
        
        # 使用选择的特征
        selected_feature_data = self.features[selected_features]
        
        # 5. 准备训练数据
        logger.info("🛠️ 准备训练数据...")
        X, y = self.ai_model.prepare_training_data(selected_feature_data, self.target)
        
        if len(X) < 500:
            logger.error("训练数据不足")
            return {}
        
        # 6. 模型训练
        logger.info("🤖 开始AI模型训练...")
        
        # 为了测试，只使用快速的模型
        self.ai_model.model_configs = {
            'random_forest': self.ai_model.model_configs['random_forest'],
            'gradient_boosting': self.ai_model.model_configs['gradient_boosting'],
            'logistic_regression': self.ai_model.model_configs['logistic_regression']
        }
        
        # 简化参数网格以加快训练
        for model_name in self.ai_model.model_configs:
            config = self.ai_model.model_configs[model_name]
            if model_name == 'random_forest':
                config['params'] = {
                    'n_estimators': [100, 200],
                    'max_depth': [10, 20],
                    'min_samples_split': [2, 5]
                }
            elif model_name == 'gradient_boosting':
                config['params'] = {
                    'n_estimators': [100, 150],
                    'learning_rate': [0.1, 0.2],
                    'max_depth': [3, 5]
                }
            elif model_name == 'logistic_regression':
                config['params'] = {
                    'C': [0.1, 1.0, 10.0],
                    'penalty': ['l2']
                }
        
        training_results = self.ai_model.train_ensemble_models(X, y)
        
        # 7. 模型评估
        logger.info("📈 评估最佳模型...")
        evaluation = self.ai_model.evaluate_model(self.ai_model.best_model, X, y)
        
        # 8. 特征重要性
        logger.info("🔍 分析特征重要性...")
        feature_importance = self.ai_model.extract_feature_importance(
            self.ai_model.best_model, 
            selected_features
        )
        self.ai_model.feature_importance = feature_importance
        
        # 9. 保存模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"enhanced_trading_model_{timestamp}.joblib"
        self.ai_model.save_model(model_filename)
        
        # 10. 生成报告
        report = self.generate_training_report(
            training_results, 
            evaluation, 
            feature_importance, 
            selected_features,
            model_filename
        )
        
        logger.info("🎉 完整训练流程完成！")
        return report
    
    def generate_training_report(self, training_results: Dict, 
                               evaluation: Dict, 
                               feature_importance: Dict,
                               selected_features: List[str],
                               model_filename: str) -> Dict:
        """生成训练报告"""
        
        # 获取特征工程报告
        feature_report = self.feature_engineer.get_feature_engineering_report()
        
        # 获取AI模型报告
        model_report = self.ai_model.get_training_report()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'model_filename': model_filename,
            'data_info': {
                'total_samples': len(self.training_data),
                'training_samples': len(self.features),
                'features_created': feature_report['total_features_created'],
                'features_selected': len(selected_features)
            },
            'feature_engineering': {
                'feature_distribution': feature_report['feature_distribution'],
                'selected_features': selected_features[:10],  # 前10个
                'top_features': list(feature_importance.keys())[:10] if feature_importance else []
            },
            'model_training': {
                'models_trained': model_report['models_trained'],
                'model_scores': model_report['model_scores'],
                'best_model': model_report['best_model_type']
            },
            'model_evaluation': {
                'accuracy': evaluation['accuracy'],
                'precision': evaluation['precision'],
                'recall': evaluation['recall'],
                'f1_score': evaluation['f1_score']
            },
            'feature_importance': dict(list(feature_importance.items())[:15]) if feature_importance else {}
        }
        
        return report
    
    def print_report(self, report: Dict):
        """打印训练报告"""
        print("\n" + "="*80)
        print("🎉 AI交易模型训练完成报告")
        print("="*80)
        
        print(f"\n📊 数据信息:")
        print(f"  总样本数: {report['data_info']['total_samples']:,}")
        print(f"  训练样本: {report['data_info']['training_samples']:,}")
        print(f"  创建特征: {report['data_info']['features_created']}")
        print(f"  选择特征: {report['data_info']['features_selected']}")
        
        print(f"\n🔧 特征工程:")
        print(f"  特征分布: {report['feature_engineering']['feature_distribution']}")
        print(f"  选择特征: {report['feature_engineering']['selected_features']}")
        
        print(f"\n🤖 模型训练:")
        print(f"  训练模型: {report['model_training']['models_trained']}")
        print(f"  最佳模型: {report['model_training']['best_model']}")
        print(f"  模型得分:")
        for model, score in report['model_training']['model_scores'].items():
            print(f"    {model}: {score:.4f}")
        
        print(f"\n📈 模型评估:")
        print(f"  准确率: {report['model_evaluation']['accuracy']:.4f}")
        print(f"  精确率: {report['model_evaluation']['precision']:.4f}")
        print(f"  召回率: {report['model_evaluation']['recall']:.4f}")
        print(f"  F1得分: {report['model_evaluation']['f1_score']:.4f}")
        
        print(f"\n🏆 重要特征 (Top 10):")
        for i, (feature, importance) in enumerate(list(report['feature_importance'].items())[:10], 1):
            print(f"  {i:2d}. {feature}: {importance:.4f}")
        
        print(f"\n💾 模型已保存: {report['model_filename']}")
        print("="*80)

if __name__ == "__main__":
    trainer = CompleteModelTrainer()
    
    print("🚀 开始完整的AI交易模型训练...")
    print("⏳ 这可能需要几分钟时间...")
    
    report = trainer.run_complete_training(use_test_data=True)
    
    if report:
        trainer.print_report(report)
        
        print("\n💡 下一步建议:")
        print("  1. 🔍 分析特征重要性，优化特征工程")
        print("  2. 📊 使用更多历史数据重新训练")
        print("  3. 🎯 调整目标标签的阈值参数")
        print("  4. 🤖 尝试更多模型算法")
        print("  5. 📈 进行回测验证")
        
    else:
        print("❌ 训练失败，请检查数据和参数")
