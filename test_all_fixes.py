#!/usr/bin/env python3
"""
Test script to verify all fixes are working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader

def test_all_fixes():
    """Test all the fixes applied"""
    print("🧪 Testing All Fixes Applied")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n✅ Fixes Applied:")
    print("1. 🔧 AI model feature mismatch: 122 → 24 features")
    print("2. 💰 Position size: Using 50u × 125x × 5% = $2.5 margin")
    print("3. 🛡️ Isolated margin protection: Max loss = margin only")
    print("4. 📊 Confidence threshold: Lowered for easier trading")
    print("5. 🎯 Stop-loss optimization: Dynamic 1.8-2.2% take-profit")
    
    # Test confidence threshold
    print(f"\n📊 Confidence Threshold Analysis:")
    
    # Calculate final threshold after all adjustments
    base_threshold = 0.55  # aggressive mode
    small_account_reduction = 0.05  # <$100 account
    ultra_small_reduction = 0.05   # <$50 account  
    high_freq_reduction = 0.25     # high frequency mode
    
    final_threshold = base_threshold - small_account_reduction - ultra_small_reduction - high_freq_reduction
    final_threshold = max(0.1, final_threshold)  # Minimum 10%
    
    print(f"   📊 Base (Aggressive): {base_threshold:.0%}")
    print(f"   📉 Small Account: -{small_account_reduction:.0%}")
    print(f"   📉 Ultra Small: -{ultra_small_reduction:.0%}")
    print(f"   📉 High Frequency: -{high_freq_reduction:.0%}")
    print(f"   🎯 Final Threshold: {final_threshold:.0%}")
    print(f"   📊 System Threshold: {trader.trading_params['confidence_threshold']:.0%}")
    
    if trader.trading_params['confidence_threshold'] <= 0.25:
        print(f"   ✅ GOOD: Very low threshold, easy to trigger trades")
    elif trader.trading_params['confidence_threshold'] <= 0.35:
        print(f"   ✅ OK: Reasonable threshold for high frequency")
    else:
        print(f"   ⚠️ HIGH: May be too strict for high frequency trading")
    
    # Test position calculation
    print(f"\n💰 Position Size Test:")
    
    mock_signal = {
        'direction': 'LONG',
        'confidence': 0.25,  # 25% confidence (should be enough now)
        'trading_style': 'right_side',
        'signal_count': 1,
        'reasons': ['Test signal']
    }
    
    mock_market_data = {
        'current_price': 102400.0,
        'volatility': 0.005,
        'volume_ratio': 1.0
    }
    
    position_size = trader.calculate_dynamic_position_size(mock_signal, mock_market_data)
    
    if position_size > 0:
        nominal_value = position_size * mock_market_data['current_price']
        margin_required = nominal_value / trader.leverage
        
        print(f"   📊 Position Size: {position_size:.6f} BTC")
        print(f"   💎 Nominal Value: ${nominal_value:.2f}")
        print(f"   💰 Margin Required: ${margin_required:.2f}")
        
        # Check if it matches user requirements
        expected_margin = 50.0 * 0.05  # 5% of $50
        margin_diff = abs(margin_required - expected_margin)
        
        if margin_diff < 1.0:
            print(f"   ✅ CORRECT: Margin matches user requirement (${expected_margin:.2f})")
        else:
            print(f"   ❌ ERROR: Margin doesn't match (expected ${expected_margin:.2f})")
    else:
        print(f"   ❌ ERROR: No position calculated")
    
    # Test AI model features
    print(f"\n🤖 AI Model Feature Test:")
    
    # Create mock market data with features
    mock_features = {f'feature_{i}': 0.5 for i in range(122)}  # 122 features
    mock_market_data_with_features = {
        'current_price': 102400.0,
        'features': mock_features,
        'price_change_1h': -0.002,
        'volatility': 0.005
    }
    
    try:
        ai_result = trader.predict_with_ai_model(mock_market_data_with_features)
        print(f"   ✅ AI Prediction: {ai_result['action']}")
        print(f"   ✅ Confidence: {ai_result['confidence']:.1%}")
        print(f"   ✅ Reason: {ai_result['reason']}")
        print(f"   ✅ Feature adjustment worked!")
    except Exception as e:
        print(f"   ❌ AI Prediction failed: {e}")
    
    # Test trading decision
    print(f"\n🎯 Trading Decision Test:")
    
    signal_confidence = 0.25  # 25% confidence
    threshold = trader.trading_params['confidence_threshold']
    
    print(f"   📊 Signal Confidence: {signal_confidence:.0%}")
    print(f"   📊 Required Threshold: {threshold:.0%}")
    
    if signal_confidence >= threshold:
        print(f"   ✅ SHOULD TRADE: Signal meets threshold")
    else:
        print(f"   ❌ WON'T TRADE: Signal below threshold")
        print(f"   💡 Need to lower threshold further or improve signals")
    
    # Summary
    print(f"\n" + "="*60)
    print("🎉 Fix Verification Complete!")
    
    fixes_working = []
    
    # Check each fix
    if trader.trading_params['confidence_threshold'] <= 0.30:
        fixes_working.append("✅ Confidence threshold lowered")
    else:
        fixes_working.append("❌ Confidence threshold still too high")
    
    if position_size > 0 and margin_diff < 1.0:
        fixes_working.append("✅ Position size calculation fixed")
    else:
        fixes_working.append("❌ Position size calculation still wrong")
    
    if 'ai_result' in locals() and ai_result['confidence'] > 0:
        fixes_working.append("✅ AI model feature mismatch fixed")
    else:
        fixes_working.append("❌ AI model still has issues")
    
    print(f"\n📊 Fix Status:")
    for fix in fixes_working:
        print(f"   {fix}")
    
    working_fixes = sum(1 for fix in fixes_working if fix.startswith("✅"))
    total_fixes = len(fixes_working)
    
    print(f"\n🎯 Overall Status: {working_fixes}/{total_fixes} fixes working")
    
    if working_fixes == total_fixes:
        print("🎉 ALL FIXES WORKING! System should trade properly now!")
    elif working_fixes >= total_fixes * 0.7:
        print("✅ MOSTLY WORKING! Minor issues remain")
    else:
        print("⚠️ MAJOR ISSUES: Multiple fixes still needed")
    
    print(f"\n💡 Expected Improvements:")
    print(f"   🤖 AI predictions should work (no feature mismatch)")
    print(f"   📊 Confidence threshold: {threshold:.0%} (easier to trade)")
    print(f"   💰 Position size: ~$2.5 margin (user requirement)")
    print(f"   🛡️ Max loss: $2.5 (isolated margin protection)")
    print(f"   📈 Better win rate with proper signals")

if __name__ == "__main__":
    test_all_fixes()
