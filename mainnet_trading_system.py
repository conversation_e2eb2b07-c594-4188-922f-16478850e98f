#!/usr/bin/env python3
"""
⚠️ 主网剥头皮交易系统 - 使用真实资金！⚠️
基于成功验证的策略，但这是真实交易！
"""

import numpy as np
import logging
import time
import requests
import hmac
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Tuple
import threading
import queue
from urllib.parse import urlencode

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MainnetBinanceAPI:
    """⚠️ 主网币安API客户端 - 真实交易！⚠️"""
    
    def __init__(self, api_key: str, api_secret: str):
        # ⚠️ 主网端点 - 真实交易！
        self.base_url = "https://api.binance.com"
        self.api_key = api_key
        self.api_secret = api_secret
        
        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        })
        
        # 时间同步
        self.server_time_offset = 0
        self.last_sync_time = 0
        self.mainnet_connected = False
        
        # 初始化时间同步
        self._sync_server_time()
        
    def _sync_server_time(self):
        """同步服务器时间"""
        try:
            response = self.session.get(f"{self.base_url}/api/v3/time", timeout=10)
            response.raise_for_status()
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            self.server_time_offset = server_time - local_time
            self.last_sync_time = time.time()
            logger.info(f"✅ 主网时间同步成功，偏移: {self.server_time_offset}ms")
        except Exception as e:
            logger.warning(f"⚠️ 主网时间同步失败: {e}")
            self.server_time_offset = 0
    
    def _get_timestamp(self) -> int:
        """获取同步后的时间戳"""
        # 每5分钟重新同步一次
        if time.time() - self.last_sync_time > 300:
            self._sync_server_time()
        
        return int(time.time() * 1000) + self.server_time_offset
    
    def _generate_signature(self, params: dict) -> str:
        """生成API签名"""
        query_string = urlencode(params)
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, method: str, endpoint: str, params: dict = None, signed: bool = False) -> Optional[dict]:
        """发送API请求"""
        if params is None:
            params = {}
        
        if signed:
            # 使用同步后的时间戳
            params['timestamp'] = self._get_timestamp()
            params['recvWindow'] = 60000  # 60秒接收窗口
            params['signature'] = self._generate_signature(params)
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == "GET":
                response = self.session.get(url, params=params, timeout=10)
            elif method == "POST":
                response = self.session.post(url, params=params, timeout=10)
            else:
                return None
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                logger.error("❌ 主网API认证失败，请检查密钥")
                try:
                    error_msg = e.response.json()
                    logger.error(f"错误详情: {error_msg}")
                except:
                    pass
            elif e.response.status_code == 418:
                logger.error("❌ IP被限制，请检查网络环境")
            else:
                logger.error(f"主网API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"主网网络请求失败: {e}")
            return None
    
    def test_connectivity(self) -> bool:
        """测试连接"""
        try:
            result = self._make_request("GET", "/api/v3/ping")
            if result == {}:
                logger.info("✅ 主网连接成功")
                self.mainnet_connected = True
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 主网连接失败: {e}")
            return False
    
    def get_account_info(self) -> Optional[dict]:
        """获取账户信息"""
        return self._make_request("GET", "/api/v3/account", signed=True)
    
    def get_price(self, symbol: str = "ADAUSDT") -> Optional[float]:
        """获取当前价格"""
        result = self._make_request("GET", "/api/v3/ticker/price", {"symbol": symbol})
        if result and 'price' in result:
            return float(result['price'])
        return None
    
    def get_klines(self, symbol: str = "ADAUSDT", interval: str = "1m", limit: int = 100) -> Optional[list]:
        """获取K线数据"""
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        return self._make_request("GET", "/api/v3/klines", params)

class MainnetScalpingSystem:
    """⚠️ 主网剥头皮交易系统 - 使用真实资金！⚠️"""
    
    def __init__(self, api_key: str, api_secret: str, initial_balance: float = 50.0):
        self.symbol = "ADAUSDT"
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        
        # 成功验证的剥头皮参数
        self.position_risk = 0.025  # 2.5%风险
        self.stop_loss_ratio = 0.0004  # 0.04%止损
        self.take_profit_ratio = 0.0005  # 0.05%止盈
        self.min_confidence = 0.65  # 65%置信度
        
        # ⚠️ 主网API - 真实交易！
        self.api = MainnetBinanceAPI(api_key, api_secret)
        
        # 交易状态
        self.current_position = None
        self.last_trade_time = None
        self.total_trades = 0
        self.winning_trades = 0
        self.trade_history = []
        
        # 实时监控
        self.is_running = False
        self.price_monitor_thread = None
        self.price_queue = queue.Queue()
        
        # 安全限制
        self.max_trades_per_hour = 10  # 每小时最大交易数
        self.trades_this_hour = []
        
    def initialize_mainnet(self) -> bool:
        """⚠️ 初始化主网连接 - 真实交易！⚠️"""
        logger.warning("⚠️ 警告：正在初始化主网连接，这将使用真实资金！")
        
        # 测试连接
        if not self.api.test_connectivity():
            logger.error("❌ 主网连接失败")
            return False
        
        # 获取账户信息
        account_info = self.api.get_account_info()
        if account_info:
            logger.info("✅ 主网账户连接成功")
            
            # 显示账户余额
            usdt_balance = 0
            ada_balance = 0
            
            for balance in account_info.get('balances', []):
                if balance['asset'] == 'USDT' and float(balance['free']) > 0:
                    usdt_balance = float(balance['free'])
                    logger.info(f"   USDT余额: {usdt_balance}")
                elif balance['asset'] == 'ADA' and float(balance['free']) > 0:
                    ada_balance = float(balance['free'])
                    logger.info(f"   ADA余额: {ada_balance}")
            
            if usdt_balance < 10:
                logger.error(f"❌ USDT余额不足: {usdt_balance} (建议至少10 USDT)")
                return False
                
        else:
            logger.error("❌ 无法获取主网账户信息")
            return False
        
        return True
    
    def start_price_monitor(self):
        """启动价格监控"""
        def price_monitor():
            while self.is_running:
                try:
                    price = self.api.get_price(self.symbol)
                    if price:
                        self.price_queue.put({
                            'timestamp': datetime.now(),
                            'price': price
                        })
                    
                    time.sleep(2 if self.current_position else 5)
                        
                except Exception as e:
                    logger.error(f"主网价格监控错误: {e}")
                    time.sleep(3)
        
        self.price_monitor_thread = threading.Thread(target=price_monitor, daemon=True)
        self.price_monitor_thread.start()
        logger.info("✅ 主网价格监控已启动")
    
    def get_latest_price(self) -> Optional[float]:
        """获取最新价格"""
        try:
            latest_price = None
            while not self.price_queue.empty():
                price_data = self.price_queue.get_nowait()
                latest_price = price_data['price']
            
            return latest_price if latest_price else self.api.get_price(self.symbol)
            
        except Exception:
            return self.api.get_price(self.symbol)
    
    def calculate_signal(self) -> Tuple[str, float]:
        """计算交易信号（保持成功的策略）"""
        try:
            klines = self.api.get_klines(self.symbol, "1m", 20)
            if not klines or len(klines) < 10:
                return "HOLD", 0.0
            
            # 转换价格数据
            closes = [float(k[4]) for k in klines]
            
            current_price = closes[-1]
            prev_price_1 = closes[-2]
            prev_price_3 = closes[-4] if len(closes) >= 4 else closes[-2]
            
            # 计算变化率
            change_1 = (current_price - prev_price_1) / prev_price_1
            change_3 = (current_price - prev_price_3) / prev_price_3
            
            # 简单移动平均
            ma_5 = np.mean(closes[-5:])
            
            # 评分系统（保持成功的逻辑）
            score = 0
            confidence_factors = []
            
            # 1分钟动量
            if change_1 > 0.0002:  # 0.02%
                score += 3
                confidence_factors.append(0.25)
            elif change_1 < -0.0002:
                score -= 3
                confidence_factors.append(0.25)
            
            # 3分钟趋势
            if change_3 > 0.0003:  # 0.03%
                score += 2
                confidence_factors.append(0.15)
            elif change_3 < -0.0003:
                score -= 2
                confidence_factors.append(0.15)
            
            # 移动平均
            if current_price > ma_5 * 1.0001:  # 0.01%
                score += 1
                confidence_factors.append(0.10)
            elif current_price < ma_5 * 0.9999:
                score -= 1
                confidence_factors.append(0.10)
            
            # 决策
            if score >= 3:
                direction = "LONG"
                base_confidence = 0.70
            elif score <= -3:
                direction = "SHORT"
                base_confidence = 0.70
            else:
                direction = "HOLD"
                base_confidence = 0.5
            
            # 计算最终置信度
            if direction != "HOLD":
                confidence_boost = sum(confidence_factors)
                final_confidence = min(0.90, base_confidence + confidence_boost)
            else:
                final_confidence = base_confidence
            
            return direction, final_confidence
            
        except Exception as e:
            logger.error(f"信号计算失败: {e}")
            return "HOLD", 0.0
    
    def can_trade(self) -> bool:
        """检查是否可以交易（增强安全检查）"""
        # 检查是否有持仓
        if self.current_position:
            return False
        
        # 检查交易间隔
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < 60:  # 主网增加到60秒间隔
                return False
        
        # 检查每小时交易限制
        current_time = datetime.now()
        self.trades_this_hour = [t for t in self.trades_this_hour 
                                if (current_time - t).total_seconds() < 3600]
        
        if len(self.trades_this_hour) >= self.max_trades_per_hour:
            logger.warning("⚠️ 达到每小时交易限制")
            return False
        
        # 检查余额
        if self.current_balance < self.initial_balance * 0.5:
            logger.warning("⚠️ 余额过低，停止交易")
            return False
        
        return True
    
    def run_demo_mode(self, duration_minutes: int = 5):
        """⚠️ 运行演示模式 - 仅显示信号，不执行真实交易 ⚠️"""
        logger.warning("🎭 演示模式：仅显示交易信号，不执行真实交易")
        logger.info(f"💰 模拟资金: ${self.initial_balance}")
        logger.info(f"📊 参数: 止损{self.stop_loss_ratio:.2%}, 止盈{self.take_profit_ratio:.2%}")
        logger.info(f"⏰ 运行时间: {duration_minutes} 分钟")
        
        # 初始化主网连接
        if not self.initialize_mainnet():
            logger.error("❌ 主网初始化失败")
            return
        
        self.is_running = True
        self.start_price_monitor()
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        try:
            while datetime.now() < end_time and self.is_running:
                current_price = self.get_latest_price()
                if current_price is None:
                    time.sleep(3)
                    continue
                
                # 计算交易信号
                if self.can_trade():
                    direction, confidence = self.calculate_signal()
                    
                    if direction in ["LONG", "SHORT"] and confidence >= self.min_confidence:
                        logger.info(f"🎯 交易信号: {direction} @ {current_price:.4f}")
                        logger.info(f"   置信度: {confidence:.1%}")
                        logger.info(f"   ⚠️ 演示模式：未执行真实交易")
                        
                        # 记录信号
                        self.trades_this_hour.append(datetime.now())
                        self.last_trade_time = datetime.now()
                
                time.sleep(10)  # 演示模式较慢的检查频率
                
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断演示")
        except Exception as e:
            logger.error(f"❌ 演示异常: {e}")
        finally:
            self.is_running = False
            logger.info("🎭 演示模式结束")
    
    def show_safety_warning(self):
        """显示安全警告"""
        print("\n" + "="*70)
        print("⚠️ ⚠️ ⚠️  重要安全警告  ⚠️ ⚠️ ⚠️")
        print("="*70)
        print("🚨 这是主网交易系统，将使用您的真实资金！")
        print("🚨 任何交易都可能导致资金损失！")
        print("🚨 建议先使用测试网进行测试！")
        print("🚨 请确保您完全理解风险！")
        print("="*70)

if __name__ == "__main__":
    print("⚠️ 主网剥头皮交易系统")
    print("🚨 使用真实资金进行交易")
    
    # 使用提供的API密钥
    API_KEY = "WO0FoiEOvpN996J38gHKZa6314Cdq0N8JuEy3hr0awGA9ISB0yY5fVpwtW5NEunS"
    API_SECRET = "fTHGkdJJZX5oooKJ89MUlED0WKtTn8gWEqxBQ4Fc5ykiUlrfirJonRlv58mCoBL4"
    
    trader = MainnetScalpingSystem(API_KEY, API_SECRET, initial_balance=50.0)
    trader.show_safety_warning()
    
    print("\n🎭 运行5分钟演示模式（仅显示信号，不执行交易）...")
    trader.run_demo_mode(duration_minutes=5)
