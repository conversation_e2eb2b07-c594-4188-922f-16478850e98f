#!/usr/bin/env python3
"""
增强版显示模块 - 优化交易系统打印输出
"""

from datetime import datetime
from typing import Dict

def print_enhanced_status(trader, ai_probability: float, indicators: Dict, signal: Dict):
    """
    增强版状态打印 - 显示完整的决策过程
    """
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    current_price = indicators['price']
    
    print(f"\n⏰ {current_time}")
    print("=" * 100)
    
    # 账户状态 - 简化显示
    unrealized_pnl = trader.calculate_unrealized_pnl(current_price)
    total_equity = trader.capital + unrealized_pnl
    total_return = (total_equity - trader.initial_capital) / trader.initial_capital * 100
    
    print(f"💰 账户: ${trader.capital:.2f} + ${unrealized_pnl:+.2f} = ${total_equity:.2f} ({total_return:+.2f}%)")
    
    # 持仓状态
    if trader.position['size'] != 0:
        hold_hours = (datetime.now() - trader.position['entry_time']).total_seconds() / 3600
        pnl_pct = (unrealized_pnl / trader.position['margin_used']) * 100 if trader.position['margin_used'] > 0 else 0
        print(f"📊 持仓: 🔥 {trader.position['side']} {abs(trader.position['size']):.6f} BTC @ ${trader.position['entry_price']:,.0f} | 盈亏: ${unrealized_pnl:+.2f} ({pnl_pct:+.1f}%) | {hold_hours:.1f}h")
    else:
        print(f"📊 持仓: 💤 空仓 | BTC: ${current_price:,.0f}")
    
    # AI分析 - 增强显示
    print(f"\n🤖 AI智能分析:")
    ai_strength = abs(ai_probability - 0.5) * 2
    ai_direction = "看涨" if ai_probability > 0.5 else "看跌"
    ai_level = "强" if ai_strength > 0.3 else "弱" if ai_strength > 0.1 else "中性"
    
    print(f"   📊 概率: {ai_probability:.1%}↑ {1-ai_probability:.1%}↓ | {ai_level}{ai_direction} (强度: {ai_strength:.1%})")
    
    # 技术指标 - 详细分析
    print(f"\n📈 技术指标详细分析:")
    
    # RSI分析
    rsi_val = indicators['rsi']
    if rsi_val > 70:
        rsi_status = "🔴 超买"
        rsi_signal = "看跌"
    elif rsi_val < 30:
        rsi_status = "🟢 超卖"
        rsi_signal = "看涨"
    else:
        rsi_status = "🟡 中性"
        rsi_signal = "中性"
    print(f"   RSI: {rsi_val:.1f} {rsi_status} → {rsi_signal}")
    
    # MACD分析
    macd_trend = indicators['macd_trend']
    macd_emoji = "🟢" if macd_trend == 'bullish' else "🔴" if macd_trend == 'bearish' else "🟡"
    print(f"   MACD: {macd_emoji} {macd_trend} → {'看涨' if macd_trend == 'bullish' else '看跌' if macd_trend == 'bearish' else '中性'}")
    
    # 布林带分析
    bb_pos = indicators['bb_position']
    if bb_pos > 0.8:
        bb_status = "🔴 接近上轨"
        bb_signal = "看跌"
    elif bb_pos < 0.2:
        bb_status = "🟢 接近下轨"
        bb_signal = "看涨"
    else:
        bb_status = "🟡 中轨区域"
        bb_signal = "中性"
    print(f"   布林带: {bb_pos:.1%} {bb_status} → {bb_signal}")
    
    # 成交量分析
    vol_ratio = indicators['volume_ratio']
    if vol_ratio > 1.5:
        vol_status = "🔥 放量"
        vol_signal = "确认"
    elif vol_ratio < 0.7:
        vol_status = "❄️ 缩量"
        vol_signal = "观望"
    else:
        vol_status = "📊 正常"
        vol_signal = "中性"
    print(f"   成交量: {vol_ratio:.1f}倍 {vol_status} → {vol_signal}")
    
    # 信号确认分析
    print(f"\n🎯 信号确认分析:")
    
    # 分析各种确认情况
    confirmations = []
    conflicts = []
    
    # AI vs 技术指标确认
    if ai_probability < 0.45:  # AI看跌
        print(f"   🤖 AI信号: 看跌 ({1-ai_probability:.1%})")
        
        if rsi_val > 50:
            confirmations.append("RSI支持")
        else:
            conflicts.append("RSI冲突")
            
        if macd_trend == 'bearish':
            confirmations.append("MACD支持")
        else:
            conflicts.append("MACD冲突")
            
        if bb_pos > 0.6:
            confirmations.append("布林带支持")
        else:
            conflicts.append("布林带冲突")
            
    elif ai_probability > 0.55:  # AI看涨
        print(f"   🤖 AI信号: 看涨 ({ai_probability:.1%})")
        
        if rsi_val < 50:
            confirmations.append("RSI支持")
        else:
            conflicts.append("RSI冲突")
            
        if macd_trend == 'bullish':
            confirmations.append("MACD支持")
        else:
            conflicts.append("MACD冲突")
            
        if bb_pos < 0.4:
            confirmations.append("布林带支持")
        else:
            conflicts.append("布林带冲突")
    else:
        print(f"   🤖 AI信号: 中性 ({ai_probability:.1%})")
    
    # 成交量确认
    if vol_ratio > 1.2:
        confirmations.append("成交量确认")
    else:
        conflicts.append("成交量不足")
    
    print(f"   ✅ 确认信号: {len(confirmations)}个 - {', '.join(confirmations) if confirmations else '无'}")
    print(f"   ❌ 冲突信号: {len(conflicts)}个 - {', '.join(conflicts) if conflicts else '无'}")
    
    # 决策逻辑展示
    print(f"\n🧠 决策逻辑:")
    if len(confirmations) >= 3:
        decision_logic = f"强信号: {len(confirmations)}个确认 → 开仓"
    elif len(confirmations) >= 2:
        decision_logic = f"中等信号: {len(confirmations)}个确认 → 谨慎开仓"
    elif len(confirmations) >= 1:
        decision_logic = f"弱信号: {len(confirmations)}个确认 → 观望"
    else:
        decision_logic = f"无信号: 0个确认 → 等待"
    
    print(f"   💭 {decision_logic}")
    
    # 最终交易决策
    print(f"\n🚀 最终交易决策:")
    signal_emoji = {"LONG": "🟢", "SHORT": "🔴", "WAIT": "⏸️", "NEUTRAL": "🟡"}
    decision_color = signal_emoji.get(signal['direction'], '❓')
    
    print(f"   {decision_color} 决策: {signal['direction']}")
    print(f"   📊 置信度: {signal['confidence']:.1%} | 信号强度: {signal['strength']:.1%}")
    print(f"   💭 决策理由: {signal['reason']}")
    
    # 如果是交易信号，显示具体参数
    if signal['direction'] in ['LONG', 'SHORT']:
        # 计算预期的交易参数
        stop_loss_pct = 0.025
        take_profit_pct = 0.05
        
        if signal['direction'] == 'LONG':
            stop_loss = current_price * (1 - stop_loss_pct)
            take_profit = current_price * (1 + take_profit_pct)
        else:
            stop_loss = current_price * (1 + stop_loss_pct)
            take_profit = current_price * (1 - take_profit_pct)
        
        print(f"   💰 入场价: ${current_price:,.0f}")
        print(f"   🛡️ 止损价: ${stop_loss:,.0f} ({stop_loss_pct:.1%})")
        print(f"   🎯 止盈价: ${take_profit:,.0f} ({take_profit_pct:.1%})")
        
        # 计算仓位大小
        size_info = trader.calculate_position_size(current_price, signal['confidence'])
        if size_info:
            print(f"   📏 仓位: {size_info['btc_size']:.6f} BTC (${size_info['position_value']:.0f})")
            print(f"   💳 保证金: ${size_info['margin_required']:.2f}")
    
    # 绩效统计
    trader.update_performance_stats()
    stats = trader.performance_stats
    print(f"\n📊 绩效统计:")
    if stats['total_trades'] > 0:
        print(f"   📈 总交易: {stats['total_trades']}笔 | 胜率: {stats['win_rate']:.1%} | 总盈亏: ${stats['total_pnl']:+.2f}")
        print(f"   🏆 盈利: {stats['winning_trades']}笔 | 💸 亏损: {stats['losing_trades']}笔 | 📉 最大回撤: {stats['max_drawdown']:.1%}")
        
        if stats['avg_win'] > 0 and stats['avg_loss'] < 0:
            print(f"   💰 平均盈利: ${stats['avg_win']:+.2f} | 平均亏损: ${stats['avg_loss']:+.2f} | 盈亏比: {stats['profit_factor']:.2f}")
    else:
        print(f"   🆕 尚未开始交易，系统正在寻找最佳机会...")
    
    print("=" * 100)

def print_trade_execution(action: str, details: Dict):
    """
    打印交易执行信息
    """
    if action == "OPEN":
        print(f"\n🚀 开仓执行:")
        print(f"   方向: {details['side']}")
        print(f"   数量: {details['size']:.6f} BTC")
        print(f"   价格: ${details['price']:,.2f}")
        print(f"   保证金: ${details['margin']:.2f}")
        print(f"   手续费: ${details['fee']:.4f}")
        print(f"   理由: {details['reason']}")
        print(f"   置信度: {details['confidence']:.1%}")
    
    elif action == "CLOSE":
        print(f"\n🏁 平仓执行:")
        print(f"   方向: {details['side']}")
        print(f"   入场价: ${details['entry_price']:,.2f}")
        print(f"   出场价: ${details['exit_price']:,.2f}")
        print(f"   持仓时间: {details['hold_hours']:.1f}小时")
        print(f"   盈亏: ${details['final_pnl']:+.2f}")
        print(f"   理由: {details['reason']}")

if __name__ == "__main__":
    # 测试函数
    print("增强版显示模块已加载")
