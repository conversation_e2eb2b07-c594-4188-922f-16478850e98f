#!/usr/bin/env python3
"""
完整的高频交易系统
整合82.2%准确率的深度学习模型
"""

import pandas as pd
import numpy as np
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import joblib
import sys
import os

# 导入我们的模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from sklearn_deep_hft_system import AdvancedHFTFeatureEngineer, DeepMLPHFTModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TradingSignal:
    """交易信号"""
    timestamp: datetime
    direction: str  # LONG/SHORT/HOLD
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    expected_duration: int  # 分钟
    risk_reward_ratio: float
    model_prediction: int
    features_snapshot: Dict

@dataclass
class TradeRecord:
    """交易记录"""
    trade_id: str
    signal: TradingSignal
    entry_time: datetime
    entry_price: float
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    exit_reason: str = ""
    pnl: float = 0.0
    pnl_percentage: float = 0.0
    is_winner: bool = False
    duration_minutes: int = 0

class HighFrequencyTradingSystem:
    """高频交易系统"""
    
    def __init__(self, initial_balance: float = 50.0, leverage: float = 125.0):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.leverage = leverage
        
        # 模型和特征工程器
        self.feature_engineer = AdvancedHFTFeatureEngineer()
        self.ai_model = DeepMLPHFTModel()
        
        # 交易参数
        self.min_confidence = 0.75  # 高置信度要求
        self.max_position_risk = 0.02  # 单笔最大风险2%
        self.min_risk_reward = 1.5  # 最小风险收益比
        self.max_daily_trades = 10  # 每日最大交易次数
        self.min_trade_interval = 5  # 最小交易间隔(分钟)
        
        # 交易状态
        self.current_position = None
        self.trade_history = []
        self.daily_trade_count = 0
        self.last_trade_time = None
        self.consecutive_losses = 0
        self.max_drawdown = 0.0
        self.peak_balance = initial_balance
        
        # 性能统计
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        
        # 数据缓存
        self.price_data = pd.DataFrame()
        self.features_cache = pd.DataFrame()
        
    def load_trained_model(self, model_path: str = None):
        """加载训练好的模型"""
        if model_path and os.path.exists(model_path):
            try:
                self.ai_model = joblib.load(model_path)
                logger.info(f"已加载模型: {model_path}")
            except Exception as e:
                logger.error(f"加载模型失败: {e}")
                self._train_default_model()
        else:
            logger.info("未找到预训练模型，使用默认训练")
            self._train_default_model()
    
    def _train_default_model(self):
        """训练默认模型"""
        logger.info("开始训练默认高频模型...")
        
        # 创建训练数据
        from sklearn_deep_hft_system import create_realistic_hft_data
        data = create_realistic_hft_data(5000)
        
        # 特征工程
        features = self.feature_engineer.create_all_features(data)
        
        # 创建标签
        target = self.ai_model.create_target_labels(data, future_periods=2, threshold=0.0008)
        
        # 准备数据
        common_index = features.index.intersection(target.index)
        features_aligned = features.loc[common_index]
        target_aligned = target.loc[common_index]
        
        valid_mask = ~(features_aligned.isnull().any(axis=1) | target_aligned.isnull())
        X = features_aligned[valid_mask]
        y = target_aligned[valid_mask]
        
        # 训练模型
        if len(X) > 1000:
            results = self.ai_model.train_deep_models(X, y)
            logger.info("默认模型训练完成")
        else:
            logger.error("训练数据不足")
    
    def update_market_data(self, new_data: pd.DataFrame):
        """更新市场数据"""
        if self.price_data.empty:
            self.price_data = new_data.copy()
        else:
            # 合并新数据，保持最近1000条记录
            self.price_data = pd.concat([self.price_data, new_data]).tail(1000)
        
        # 更新特征
        if len(self.price_data) >= 100:  # 需要足够的历史数据
            self.features_cache = self.feature_engineer.create_all_features(self.price_data)
    
    def generate_trading_signal(self) -> Optional[TradingSignal]:
        """生成交易信号"""
        if len(self.features_cache) < 60:  # 需要足够的特征数据
            return None
        
        # 检查交易条件
        if not self._can_trade():
            return None
        
        try:
            # 获取最新特征
            latest_features = self.features_cache.iloc[[-1]].fillna(0)
            
            # AI预测
            predictions, confidence = self.ai_model.predict(latest_features)
            prediction = predictions[0]
            conf = confidence[0]
            
            # 检查置信度
            if conf < self.min_confidence:
                logger.debug(f"置信度不足: {conf:.3f} < {self.min_confidence}")
                return None
            
            # 转换预测结果
            if prediction == 2:  # 上涨
                direction = "LONG"
            elif prediction == 0:  # 下跌
                direction = "SHORT"
            else:  # 横盘
                return None
            
            # 获取当前价格
            current_price = self.price_data['close'].iloc[-1]
            
            # 计算止损止盈
            stop_loss, take_profit = self._calculate_stop_loss_take_profit(
                direction, current_price, conf
            )
            
            # 计算风险收益比
            if direction == "LONG":
                risk = current_price - stop_loss
                reward = take_profit - current_price
            else:
                risk = stop_loss - current_price
                reward = current_price - take_profit
            
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # 检查风险收益比
            if risk_reward_ratio < self.min_risk_reward:
                logger.debug(f"风险收益比不足: {risk_reward_ratio:.2f}")
                return None
            
            # 计算仓位大小
            position_size = self._calculate_position_size(current_price, stop_loss, conf)
            
            # 创建交易信号
            signal = TradingSignal(
                timestamp=datetime.now(),
                direction=direction,
                confidence=conf,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                position_size=position_size,
                expected_duration=self._estimate_duration(conf),
                risk_reward_ratio=risk_reward_ratio,
                model_prediction=prediction,
                features_snapshot=latest_features.iloc[0].to_dict()
            )
            
            logger.info(f"生成交易信号: {direction} @ {current_price:.2f}, 置信度: {conf:.3f}")
            return signal
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return None
    
    def _can_trade(self) -> bool:
        """检查是否可以交易"""
        # 检查是否有持仓
        if self.current_position:
            return False
        
        # 检查每日交易次数
        if self.daily_trade_count >= self.max_daily_trades:
            return False
        
        # 检查交易间隔
        if self.last_trade_time:
            time_since_last = datetime.now() - self.last_trade_time
            if time_since_last < timedelta(minutes=self.min_trade_interval):
                return False
        
        # 检查连续亏损
        if self.consecutive_losses >= 5:
            return False
        
        # 检查回撤
        current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
        if current_drawdown > 0.20:  # 20%回撤限制
            return False
        
        return True
    
    def _calculate_stop_loss_take_profit(self, direction: str, entry_price: float, 
                                       confidence: float) -> Tuple[float, float]:
        """计算止损止盈"""
        # 基于波动率的动态止损
        if len(self.price_data) >= 20:
            volatility = self.price_data['close'].pct_change().rolling(20).std().iloc[-1]
        else:
            volatility = 0.002  # 默认波动率
        
        # 基础止损比例
        base_stop_pct = max(0.005, volatility * 2)  # 最小0.5%
        
        # 根据置信度调整
        confidence_factor = 0.5 + confidence  # 0.5-1.5倍
        stop_pct = base_stop_pct / confidence_factor
        profit_pct = stop_pct * self.min_risk_reward
        
        if direction == "LONG":
            stop_loss = entry_price * (1 - stop_pct)
            take_profit = entry_price * (1 + profit_pct)
        else:  # SHORT
            stop_loss = entry_price * (1 + stop_pct)
            take_profit = entry_price * (1 - profit_pct)
        
        return stop_loss, take_profit
    
    def _calculate_position_size(self, entry_price: float, stop_loss: float, 
                               confidence: float) -> float:
        """计算仓位大小"""
        # 基础风险敞口
        risk_amount = self.current_balance * self.max_position_risk
        
        # 根据置信度调整
        confidence_multiplier = 0.5 + confidence  # 0.5-1.5倍
        adjusted_risk = risk_amount * confidence_multiplier
        
        # 计算价格风险
        price_risk = abs(entry_price - stop_loss) / entry_price
        
        # 计算仓位大小
        margin_needed = adjusted_risk / price_risk
        nominal_value = margin_needed * self.leverage
        position_size = nominal_value / entry_price
        
        return position_size
    
    def _estimate_duration(self, confidence: float) -> int:
        """估计持仓时间"""
        base_duration = 10  # 基础10分钟
        confidence_factor = 1 + confidence  # 1-2倍
        return int(base_duration * confidence_factor)
    
    def execute_trade(self, signal: TradingSignal) -> bool:
        """执行交易"""
        try:
            # 创建交易记录
            trade_id = f"HFT_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            trade_record = TradeRecord(
                trade_id=trade_id,
                signal=signal,
                entry_time=signal.timestamp,
                entry_price=signal.entry_price
            )
            
            # 更新交易状态
            self.current_position = trade_record
            self.daily_trade_count += 1
            self.last_trade_time = signal.timestamp
            self.total_trades += 1
            
            logger.info(f"执行交易: {signal.direction} {signal.position_size:.6f} BTC @ {signal.entry_price:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            return False
    
    def check_exit_conditions(self) -> Optional[str]:
        """检查退出条件"""
        if not self.current_position:
            return None
        
        current_price = self.price_data['close'].iloc[-1]
        signal = self.current_position.signal
        
        # 检查止损
        if signal.direction == "LONG":
            if current_price <= signal.stop_loss:
                return "stop_loss"
            elif current_price >= signal.take_profit:
                return "take_profit"
        else:  # SHORT
            if current_price >= signal.stop_loss:
                return "stop_loss"
            elif current_price <= signal.take_profit:
                return "take_profit"
        
        # 检查时间退出
        duration = (datetime.now() - signal.timestamp).total_seconds() / 60
        if duration >= signal.expected_duration * 2:  # 超过预期时间2倍
            return "time_exit"
        
        return None
    
    def close_position(self, exit_reason: str):
        """平仓"""
        if not self.current_position:
            return
        
        current_price = self.price_data['close'].iloc[-1]
        trade = self.current_position
        
        # 更新交易记录
        trade.exit_time = datetime.now()
        trade.exit_price = current_price
        trade.exit_reason = exit_reason
        trade.duration_minutes = int((trade.exit_time - trade.entry_time).total_seconds() / 60)
        
        # 计算盈亏
        if trade.signal.direction == "LONG":
            pnl_pct = (current_price - trade.entry_price) / trade.entry_price
        else:  # SHORT
            pnl_pct = (trade.entry_price - current_price) / trade.entry_price
        
        # 考虑杠杆
        leveraged_pnl_pct = pnl_pct * self.leverage
        
        # 计算实际盈亏金额
        position_value = trade.signal.position_size * trade.entry_price
        margin_used = position_value / self.leverage
        pnl_amount = margin_used * leveraged_pnl_pct
        
        trade.pnl = pnl_amount
        trade.pnl_percentage = leveraged_pnl_pct
        trade.is_winner = pnl_amount > 0
        
        # 更新账户余额
        self.current_balance += pnl_amount
        self.total_pnl += pnl_amount
        
        # 更新统计
        if trade.is_winner:
            self.winning_trades += 1
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1
        
        # 更新最大回撤
        self.peak_balance = max(self.peak_balance, self.current_balance)
        current_drawdown = (self.peak_balance - self.current_balance) / self.peak_balance
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
        
        # 保存交易记录
        self.trade_history.append(trade)
        self.current_position = None
        
        logger.info(f"平仓: {trade.signal.direction} @ {current_price:.2f}, "
                   f"盈亏: {pnl_amount:.2f} ({leveraged_pnl_pct:.2%}), "
                   f"原因: {exit_reason}")
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        
        return {
            'current_balance': self.current_balance,
            'total_pnl': self.total_pnl,
            'total_return': (self.current_balance - self.initial_balance) / self.initial_balance,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'max_drawdown': self.max_drawdown,
            'consecutive_losses': self.consecutive_losses,
            'daily_trades': self.daily_trade_count,
            'has_position': self.current_position is not None
        }
    
    def save_trading_session(self, filename: str = None):
        """保存交易会话"""
        if not filename:
            filename = f"hft_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        session_data = {
            'performance': self.get_performance_stats(),
            'trade_history': [asdict(trade) for trade in self.trade_history],
            'system_params': {
                'initial_balance': self.initial_balance,
                'leverage': self.leverage,
                'min_confidence': self.min_confidence,
                'max_position_risk': self.max_position_risk
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(session_data, f, indent=2, default=str)
        
        logger.info(f"交易会话已保存: {filename}")

def simulate_live_trading():
    """模拟实时交易"""
    logger.info("🚀 启动高频交易系统模拟...")
    
    # 创建交易系统
    hft_system = HighFrequencyTradingSystem(initial_balance=50.0, leverage=125.0)
    
    # 加载模型
    hft_system.load_trained_model()
    
    # 模拟实时数据流
    from sklearn_deep_hft_system import create_realistic_hft_data
    
    # 创建初始数据
    initial_data = create_realistic_hft_data(200)
    hft_system.update_market_data(initial_data)
    
    logger.info("开始模拟交易...")
    
    # 模拟100个交易周期
    for i in range(100):
        # 模拟新的市场数据
        new_data_point = create_realistic_hft_data(1)
        hft_system.update_market_data(new_data_point)
        
        # 检查退出条件
        exit_reason = hft_system.check_exit_conditions()
        if exit_reason:
            hft_system.close_position(exit_reason)
        
        # 生成新信号
        if not hft_system.current_position:
            signal = hft_system.generate_trading_signal()
            if signal:
                hft_system.execute_trade(signal)
        
        # 每10个周期显示状态
        if i % 10 == 0:
            stats = hft_system.get_performance_stats()
            logger.info(f"周期 {i}: 余额={stats['current_balance']:.2f}, "
                       f"胜率={stats['win_rate']:.1%}, "
                       f"交易数={stats['total_trades']}")
        
        # 模拟时间间隔
        time.sleep(0.1)
    
    # 最终统计
    final_stats = hft_system.get_performance_stats()
    
    print("\n🎉 高频交易模拟完成！")
    print("=" * 50)
    print(f"📊 最终统计:")
    print(f"  初始余额: ${hft_system.initial_balance:.2f}")
    print(f"  最终余额: ${final_stats['current_balance']:.2f}")
    print(f"  总收益率: {final_stats['total_return']:.2%}")
    print(f"  总交易数: {final_stats['total_trades']}")
    print(f"  胜率: {final_stats['win_rate']:.1%}")
    print(f"  最大回撤: {final_stats['max_drawdown']:.2%}")
    
    # 保存会话
    hft_system.save_trading_session()
    
    return hft_system

if __name__ == "__main__":
    # 运行模拟交易
    trading_system = simulate_live_trading()
