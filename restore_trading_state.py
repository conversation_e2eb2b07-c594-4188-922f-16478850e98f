#!/usr/bin/env python3
"""
恢复交易状态工具 - 手动恢复之前的收益状态
"""

import json
from datetime import datetime

def create_restored_state():
    """
    创建恢复的交易状态
    """
    # 基于您之前的实际状态
    restored_state = {
        'initial_capital': 50.00,
        'current_capital': 70.06,
        'total_return': 0.4013,  # 40.13%
        'position': {
            'size': -0.000382,  # 空头
            'entry_price': 104730.90,
            'current_price': 104524.60,
            'unrealized_pnl': 0.08,
            'margin_used': 20.00
        },
        'equity_history': [
            {
                'timestamp': datetime.now().isoformat(),
                'equity': 70.06,
                'total_return': 0.4013
            }
        ],
        'trades': [],  # 第一笔交易还未完成
        'session_info': {
            'restored_at': datetime.now().isoformat(),
            'reason': 'Manual restoration after system restart',
            'previous_performance': '40.13% return achieved'
        }
    }
    
    return restored_state

def save_restored_state(filename='restored_trading_state.json'):
    """
    保存恢复的状态
    """
    state = create_restored_state()
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(state, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 交易状态已恢复并保存到: {filename}")
    print(f"📊 恢复的状态:")
    print(f"   初始资金: ${state['initial_capital']}")
    print(f"   当前权益: ${state['current_capital']}")
    print(f"   总收益率: {state['total_return']:.2%}")
    print(f"   当前持仓: 空头 {abs(state['position']['size']):.6f} BTC")
    print(f"   入场价格: ${state['position']['entry_price']:,.2f}")
    print(f"   未实现盈亏: ${state['position']['unrealized_pnl']:+.2f}")

def create_corrected_trader_init():
    """
    创建修正的交易器初始化代码
    """
    corrected_code = '''
# 修正的交易器初始化
class CorrectedFuturesTrader:
    def __init__(self, initial_capital=50, leverage=2):
        # 恢复之前的状态
        self.initial_capital = 50.00
        self.capital = 70.06  # 恢复实际权益
        self.leverage = leverage
        
        # 恢复持仓状态
        self.position = -0.000382  # 空头持仓
        self.entry_price = 104730.90
        self.entry_time = datetime.now()
        self.margin_used = 20.00
        
        # 权益历史
        self.equity_history = [{
            'timestamp': datetime.now(),
            'equity': 70.06,
            'total_return': 0.4013
        }]
        
        print(f"🔄 交易状态已恢复:")
        print(f"   权益: ${self.capital:.2f} (收益率: {((self.capital-self.initial_capital)/self.initial_capital):.2%})")
        print(f"   持仓: 空头 {abs(self.position):.6f} BTC @ ${self.entry_price:,.2f}")
'''
    
    return corrected_code

if __name__ == "__main__":
    print("🔄 恢复交易状态工具")
    print("=" * 50)
    
    # 保存恢复状态
    save_restored_state()
    
    # 显示修正代码
    print(f"\n📝 修正代码:")
    print(create_corrected_trader_init())
    
    print(f"\n💡 建议操作:")
    print(f"1. 使用修正的初始资金: $70.06")
    print(f"2. 确认当前持仓: 空头 0.000382 BTC")
    print(f"3. 立即执行利润保护: 平仓70%头寸")
    print(f"4. 实施数据持久化机制")
