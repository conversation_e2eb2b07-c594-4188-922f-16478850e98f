#!/usr/bin/env python3
"""
数据源对比分析 - 真实数据 vs 模拟数据
"""

from sentiment_analysis import SentimentAnalyzer
from real_sentiment_analysis import RealSentimentAnalyzer
import json
from datetime import datetime

def compare_data_sources():
    """对比真实数据和模拟数据"""
    print("🔍 数据源对比分析")
    print("=" * 80)
    
    # 1. 模拟数据分析器
    print("📊 模拟数据分析器:")
    mock_analyzer = SentimentAnalyzer()
    mock_result = mock_analyzer.get_comprehensive_sentiment()
    
    print(f"   总体情绪: {mock_result['sentiment_classification']}")
    print(f"   情绪分数: {mock_result['overall_sentiment_score']:.2f}")
    print(f"   数据来源: 模拟生成")
    
    # 2. 真实数据分析器
    print(f"\n📡 真实数据分析器:")
    real_analyzer = RealSentimentAnalyzer()
    real_result = real_analyzer.get_comprehensive_real_sentiment()
    
    print(f"   总体情绪: {real_result['sentiment_classification']}")
    print(f"   情绪分数: {real_result['overall_sentiment_score']:.2f}")
    print(f"   数据来源: 真实API")
    
    # 3. 详细对比
    print(f"\n🔬 详细数据源对比:")
    print("=" * 80)
    
    comparison = {
        'timestamp': datetime.now().isoformat(),
        'mock_data': {
            'sentiment_score': mock_result['overall_sentiment_score'],
            'classification': mock_result['sentiment_classification'],
            'sources': {}
        },
        'real_data': {
            'sentiment_score': real_result['overall_sentiment_score'],
            'classification': real_result['sentiment_classification'],
            'sources': {}
        }
    }
    
    # 模拟数据源详情
    print("📊 模拟数据源:")
    for source, data in mock_result['sentiment_breakdown'].items():
        print(f"   {source}: {data['score']:.2f} ({data['classification']}) - 模拟生成")
        comparison['mock_data']['sources'][source] = {
            'score': data['score'],
            'classification': data['classification'],
            'type': 'simulated'
        }
    
    # 真实数据源详情
    print(f"\n📡 真实数据源:")
    for source, data in real_result['sentiment_breakdown'].items():
        is_real = 'Alternative.me' in data['source'] or 'Reddit' in data['source']
        data_type = '真实API' if is_real else '默认值'
        print(f"   {source}: {data['score']:.2f} ({data['classification']}) - {data_type}")
        comparison['real_data']['sources'][source] = {
            'score': data['score'],
            'classification': data['classification'],
            'type': 'real' if is_real else 'default',
            'source': data['source']
        }
    
    # 4. 数据质量评估
    print(f"\n📈 数据质量评估:")
    print("=" * 80)
    
    real_sources = sum(1 for source in comparison['real_data']['sources'].values() 
                      if source['type'] == 'real')
    total_sources = len(comparison['real_data']['sources'])
    
    print(f"真实数据覆盖率: {real_sources}/{total_sources} ({real_sources/total_sources:.1%})")
    
    # 评估各数据源
    data_source_quality = {
        'fear_greed': {
            'api': 'Alternative.me',
            'cost': '免费',
            'reliability': '高',
            'update_frequency': '每小时',
            'status': '✅ 已集成'
        },
        'reddit': {
            'api': 'Reddit JSON',
            'cost': '免费',
            'reliability': '中',
            'update_frequency': '实时',
            'status': '✅ 已集成'
        },
        'news': {
            'api': 'NewsAPI',
            'cost': '免费(有限)/付费',
            'reliability': '高',
            'update_frequency': '实时',
            'status': '⚠️ 需要API密钥'
        },
        'onchain': {
            'api': 'Glassnode/CoinMetrics',
            'cost': '付费',
            'reliability': '极高',
            'update_frequency': '每小时',
            'status': '❌ 未集成'
        }
    }
    
    print(f"\n📋 数据源详细评估:")
    for source, info in data_source_quality.items():
        print(f"   {source}:")
        print(f"      API: {info['api']}")
        print(f"      成本: {info['cost']}")
        print(f"      可靠性: {info['reliability']}")
        print(f"      更新频率: {info['update_frequency']}")
        print(f"      状态: {info['status']}")
    
    # 5. 升级建议
    print(f"\n🚀 数据源升级建议:")
    print("=" * 80)
    
    upgrade_plan = {
        '立即可用': [
            '✅ 恐慌贪婪指数 - 已集成真实API',
            '✅ Reddit情绪 - 已集成真实API'
        ],
        '短期升级 (1周内)': [
            '🔑 申请NewsAPI密钥 (免费版每月1000次请求)',
            '🔗 集成更多免费链上数据API',
            '📊 添加Twitter情绪分析 (需要API v2)'
        ],
        '中期升级 (1个月内)': [
            '💰 订阅专业数据服务 (Glassnode, CoinMetrics)',
            '🤖 集成更高级的NLP情绪分析',
            '📈 添加技术分析情绪指标'
        ],
        '长期升级 (3个月内)': [
            '🧠 训练专用的加密货币情绪分析模型',
            '🌐 多语言情绪分析支持',
            '⚡ 实时流数据处理'
        ]
    }
    
    for phase, tasks in upgrade_plan.items():
        print(f"\n{phase}:")
        for task in tasks:
            print(f"   {task}")
    
    # 6. 成本效益分析
    print(f"\n💰 成本效益分析:")
    print("=" * 80)
    
    cost_analysis = {
        '免费方案': {
            'cost': '$0/月',
            'coverage': '50%',
            'sources': ['恐慌贪婪指数', 'Reddit', '部分新闻'],
            'reliability': '中等'
        },
        '基础方案': {
            'cost': '$20/月',
            'coverage': '80%',
            'sources': ['恐慌贪婪指数', 'Reddit', 'NewsAPI', '基础链上数据'],
            'reliability': '良好'
        },
        '专业方案': {
            'cost': '$100/月',
            'coverage': '95%',
            'sources': ['全部免费源', 'Glassnode', 'CoinMetrics', 'Twitter API'],
            'reliability': '优秀'
        }
    }
    
    for plan, details in cost_analysis.items():
        print(f"{plan}:")
        print(f"   成本: {details['cost']}")
        print(f"   覆盖率: {details['coverage']}")
        print(f"   数据源: {', '.join(details['sources'])}")
        print(f"   可靠性: {details['reliability']}")
    
    # 保存对比结果
    with open('data_source_comparison.json', 'w', encoding='utf-8') as f:
        json.dump(comparison, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 对比结果已保存: data_source_comparison.json")
    
    return comparison

def get_api_setup_guide():
    """获取API设置指南"""
    print(f"\n📖 API设置指南")
    print("=" * 80)
    
    api_guide = {
        'NewsAPI': {
            'url': 'https://newsapi.org/',
            'steps': [
                '1. 访问 https://newsapi.org/',
                '2. 点击 "Get API Key"',
                '3. 注册免费账户',
                '4. 获取API密钥',
                '5. 每月1000次免费请求'
            ],
            'code': '''
# 使用方法:
api_keys = {
    'newsapi': 'your_api_key_here'
}
analyzer = RealSentimentAnalyzer(api_keys)
            '''
        },
        'Twitter API': {
            'url': 'https://developer.twitter.com/',
            'steps': [
                '1. 访问 https://developer.twitter.com/',
                '2. 申请开发者账户',
                '3. 创建应用',
                '4. 获取Bearer Token',
                '5. 基础版免费，有请求限制'
            ]
        },
        'Glassnode': {
            'url': 'https://glassnode.com/',
            'steps': [
                '1. 访问 https://glassnode.com/',
                '2. 注册账户',
                '3. 选择订阅计划',
                '4. 获取API密钥',
                '5. 专业链上数据，付费服务'
            ]
        }
    }
    
    for api, info in api_guide.items():
        print(f"\n{api}:")
        print(f"   网址: {info['url']}")
        print(f"   步骤:")
        for step in info['steps']:
            print(f"      {step}")
        
        if 'code' in info:
            print(f"   使用示例:")
            print(info['code'])
    
    return api_guide

if __name__ == "__main__":
    # 运行对比分析
    comparison = compare_data_sources()
    
    # 显示API设置指南
    api_guide = get_api_setup_guide()
    
    print(f"\n🎯 总结:")
    print("=" * 80)
    print(f"✅ 当前已集成2个真实数据源 (恐慌贪婪指数 + Reddit)")
    print(f"⚠️ 2个数据源使用默认值 (新闻 + 链上数据)")
    print(f"🚀 建议优先申请NewsAPI密钥提升数据质量")
    print(f"💡 系统架构支持真实数据，只需配置API密钥即可升级")
