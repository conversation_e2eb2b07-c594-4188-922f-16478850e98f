#!/usr/bin/env python3
"""
简单移动平均策略 - 作为基准对比
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from data_fetcher import BinanceDataFetcher

def simple_ma_backtest(symbol='BTCUSDT', test_months=3, short_ma=20, long_ma=50):
    """
    简单移动平均交叉策略回测
    """
    print(f"📊 简单移动平均策略回测 {symbol}")
    print(f"参数: 短期MA={short_ma}, 长期MA={long_ma}")
    
    # 获取数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=test_months*30 + 60)  # 多获取一些数据计算MA
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date.strftime('%Y-%m-%d'))
    
    print(f"✅ 获取到 {len(df)} 条数据")
    
    # 计算移动平均
    df[f'MA_{short_ma}'] = df['close'].rolling(window=short_ma).mean()
    df[f'MA_{long_ma}'] = df['close'].rolling(window=long_ma).mean()
    
    # 生成信号
    df['signal'] = 0
    df.loc[df[f'MA_{short_ma}'] > df[f'MA_{long_ma}'], 'signal'] = 1  # 买入
    df.loc[df[f'MA_{short_ma}'] < df[f'MA_{long_ma}'], 'signal'] = -1  # 卖出
    
    # 信号变化点
    df['signal_change'] = df['signal'].diff()
    
    # 移除前期数据（MA计算需要）
    df = df[long_ma:].copy()
    
    # 只使用最近test_months的数据进行回测
    test_start = end_date - timedelta(days=test_months*30)
    df_test = df[df.index >= test_start].copy()
    
    print(f"回测期间: {df_test.index[0]} 到 {df_test.index[-1]}")
    
    # 回测
    capital = 10000
    position = 0  # 0=空仓, 1=多头, -1=空头
    trades = []
    equity_curve = []
    
    for i, (timestamp, row) in enumerate(df_test.iterrows()):
        price = row['close']
        signal = row['signal']
        signal_change = row['signal_change']
        
        # 信号变化时交易
        if signal_change == 2:  # 从-1变为1，买入
            if position == -1:  # 先平空
                pnl_ratio = (entry_price - price) / entry_price
                capital = capital * (1 + pnl_ratio - 0.002)
                trades.append({
                    'type': '平空',
                    'price': price,
                    'entry_price': entry_price,
                    'pnl_ratio': pnl_ratio,
                    'time': timestamp
                })
            
            # 开多
            position = 1
            entry_price = price
            trades.append({
                'type': '开多',
                'price': price,
                'time': timestamp
            })
            
        elif signal_change == -2:  # 从1变为-1，卖出
            if position == 1:  # 先平多
                pnl_ratio = (price - entry_price) / entry_price
                capital = capital * (1 + pnl_ratio - 0.002)
                trades.append({
                    'type': '平多',
                    'price': price,
                    'entry_price': entry_price,
                    'pnl_ratio': pnl_ratio,
                    'time': timestamp
                })
            
            # 开空
            position = -1
            entry_price = price
            trades.append({
                'type': '开空',
                'price': price,
                'time': timestamp
            })
        
        # 计算当前权益
        if position == 1:  # 多头
            current_value = capital * (price / entry_price)
        elif position == -1:  # 空头
            current_value = capital * (entry_price / price)
        else:
            current_value = capital
        
        equity_curve.append({
            'time': timestamp,
            'value': current_value,
            'price': price,
            'position': position,
            'signal': signal
        })
    
    # 最后平仓
    if position != 0:
        if position == 1:
            pnl_ratio = (df_test['close'].iloc[-1] - entry_price) / entry_price
        else:
            pnl_ratio = (entry_price - df_test['close'].iloc[-1]) / entry_price
        
        capital = capital * (1 + pnl_ratio - 0.002)
        trades.append({
            'type': '强制平仓',
            'price': df_test['close'].iloc[-1],
            'entry_price': entry_price,
            'pnl_ratio': pnl_ratio,
            'time': df_test.index[-1]
        })
    
    # 计算结果
    total_return = (capital - 10000) / 10000
    buy_hold_return = (df_test['close'].iloc[-1] - df_test['close'].iloc[0]) / df_test['close'].iloc[0]
    
    profitable_trades = len([t for t in trades if t.get('pnl_ratio', 0) > 0])
    total_completed_trades = len([t for t in trades if 'pnl_ratio' in t])
    win_rate = profitable_trades / total_completed_trades if total_completed_trades > 0 else 0
    
    # 最大回撤
    equity_values = [eq['value'] for eq in equity_curve]
    peak = np.maximum.accumulate(equity_values)
    drawdown = (np.array(equity_values) - peak) / peak
    max_drawdown = np.min(drawdown)
    
    print(f"\n📊 移动平均策略结果:")
    print(f"💰 最终资金: ${capital:,.2f}")
    print(f"📈 总收益率: {total_return:.2%}")
    print(f"📈 基准收益率: {buy_hold_return:.2%}")
    print(f"🎯 超额收益: {total_return - buy_hold_return:.2%}")
    print(f"📊 完成交易: {total_completed_trades}")
    print(f"📊 胜率: {win_rate:.2%}")
    print(f"📊 最大回撤: {max_drawdown:.2%}")
    
    # 绘制图表
    plt.figure(figsize=(15, 10))
    
    # 价格和MA
    plt.subplot(3, 1, 1)
    plt.plot(df_test.index, df_test['close'], label='价格', alpha=0.7)
    plt.plot(df_test.index, df_test[f'MA_{short_ma}'], label=f'MA{short_ma}', linewidth=2)
    plt.plot(df_test.index, df_test[f'MA_{long_ma}'], label=f'MA{long_ma}', linewidth=2)
    
    # 标记买卖点
    buy_trades = [t for t in trades if t['type'] in ['开多', '平空']]
    sell_trades = [t for t in trades if t['type'] in ['开空', '平多']]
    
    if buy_trades:
        buy_times = [t['time'] for t in buy_trades]
        buy_prices = [t['price'] for t in buy_trades]
        plt.scatter(buy_times, buy_prices, color='green', marker='^', s=100, label='买入', zorder=5)
    
    if sell_trades:
        sell_times = [t['time'] for t in sell_trades]
        sell_prices = [t['price'] for t in sell_trades]
        plt.scatter(sell_times, sell_prices, color='red', marker='v', s=100, label='卖出', zorder=5)
    
    plt.title(f'{symbol} 移动平均策略')
    plt.ylabel('价格')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 权益曲线
    plt.subplot(3, 1, 2)
    times = [eq['time'] for eq in equity_curve]
    values = [eq['value'] for eq in equity_curve]
    plt.plot(times, values, label='策略权益', linewidth=2, color='blue')
    
    # 基准线
    benchmark_values = 10000 * (1 + (df_test['close'] - df_test['close'].iloc[0]) / df_test['close'].iloc[0])
    plt.plot(df_test.index, benchmark_values, label='买入持有', alpha=0.7, linestyle='--', color='orange')
    
    plt.title('权益曲线对比')
    plt.ylabel('资金')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 回撤
    plt.subplot(3, 1, 3)
    plt.fill_between(times, drawdown * 100, 0, alpha=0.3, color='red', label='回撤')
    plt.title('回撤曲线')
    plt.ylabel('回撤 (%)')
    plt.xlabel('时间')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = f"ma_strategy_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存: {chart_path}")
    
    plt.show()
    
    return {
        'total_return': total_return,
        'benchmark_return': buy_hold_return,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'total_trades': total_completed_trades,
        'final_capital': capital
    }

def compare_strategies():
    """
    比较不同参数的移动平均策略
    """
    print("🔍 比较不同移动平均策略参数")
    
    strategies = [
        (10, 30),   # 快速
        (20, 50),   # 中等
        (50, 200),  # 慢速
    ]
    
    results = {}
    
    for short_ma, long_ma in strategies:
        print(f"\n测试 MA({short_ma}, {long_ma})...")
        try:
            result = simple_ma_backtest('BTCUSDT', 3, short_ma, long_ma)
            results[f"MA({short_ma},{long_ma})"] = result
        except Exception as e:
            print(f"策略 MA({short_ma},{long_ma}) 失败: {e}")
    
    # 显示对比
    print(f"\n{'='*60}")
    print("📊 策略对比结果")
    print(f"{'='*60}")
    print(f"{'策略':<15} {'收益率':<10} {'胜率':<10} {'回撤':<10} {'超额收益':<10}")
    print("-" * 60)
    
    for name, result in results.items():
        if result:
            print(f"{name:<15} {result['total_return']:>8.2%} {result['win_rate']:>8.2%} "
                  f"{result['max_drawdown']:>8.2%} {result['total_return']-result['benchmark_return']:>8.2%}")
    
    return results

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'compare':
        # 比较不同策略
        compare_strategies()
    else:
        # 单个策略测试
        symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
        months = int(sys.argv[2]) if len(sys.argv) > 2 else 3
        
        result = simple_ma_backtest(symbol, months)
        
        print(f"\n🎯 移动平均策略总结:")
        if result['total_return'] > 0:
            print("✅ 策略盈利")
        else:
            print("❌ 策略亏损")
        
        if result['total_return'] > result['benchmark_return']:
            print("✅ 跑赢基准")
        else:
            print("❌ 跑输基准")
        
        print(f"\n这个简单的移动平均策略可以作为机器学习模型的基准对比")
        print(f"如果ML模型无法跑赢这个简单策略，说明复杂度可能不值得")
