#!/usr/bin/env python3
"""
Test script to verify the logical consistency fix for performance metrics
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader
from datetime import datetime

def test_logical_consistency_fix():
    """Test the logical consistency fix"""
    print("🧪 Testing Logical Consistency Fix")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 Test Scenario: The Impossible Case")
    print("Before Fix: 0% win rate + 56.1% excellent performance")
    print("After Fix: Should show consistent metrics")
    
    # Simulate the problematic scenario
    # Case 1: All losing trades but positive unrealized P&L
    trader.account['balance'] = 48.00  # -$2.00 realized loss
    trader.account['equity'] = 78.05   # +$28.05 total equity (with unrealized gains)
    trader.account['unrealized_pnl'] = 30.05  # Large unrealized gain
    
    # Add losing trades
    trader.trade_history.append({
        'action': 'CLOSE',
        'timestamp': '2025-06-22 11:00:00',
        'side': 'LONG',
        'net_pnl': -1.50,
        'roi_percent': -10.0,
        'hold_time': 0.1
    })
    
    trader.trade_history.append({
        'action': 'CLOSE',
        'timestamp': '2025-06-22 11:05:00',
        'side': 'SHORT',
        'net_pnl': -0.50,
        'roi_percent': -3.0,
        'hold_time': 0.05
    })
    
    print("\n🔧 After Fix - Performance Rating:")
    trader._print_enhanced_trading_statistics()
    
    # Manual verification
    closed_trades = [t for t in trader.trade_history if t['action'] == 'CLOSE']
    winning_trades = [t for t in closed_trades if t.get('net_pnl', 0) > 0]
    win_rate = len(winning_trades) / len(closed_trades) if closed_trades else 0
    actual_realized_pnl = trader.account['balance'] - trader.initial_balance
    
    print(f"\n✅ Mathematical Verification:")
    print(f"   📊 Win Rate: {win_rate:.1%} (from completed trades)")
    print(f"   📊 Realized P&L: ${actual_realized_pnl:+.2f} (from balance change)")
    print(f"   📊 Unrealized P&L: ${trader.account['unrealized_pnl']:+.2f} (current position)")
    print(f"   📊 Total Equity: ${trader.account['equity']:+.2f} (balance + unrealized)")
    
    realized_return = (actual_realized_pnl / trader.initial_balance) * 100
    total_return = ((trader.account['equity'] - trader.initial_balance) / trader.initial_balance) * 100
    
    print(f"\n🎯 Return Calculations:")
    print(f"   📊 Realized Return: {realized_return:+.1f}% (should match performance rating)")
    print(f"   📊 Total Return: {total_return:+.1f}% (includes unrealized gains)")
    
    print(f"\n✅ Logical Consistency Check:")
    if win_rate == 0 and realized_return < 0:
        print(f"   ✅ CONSISTENT: 0% win rate + negative realized return")
    elif win_rate == 0 and realized_return > 0:
        print(f"   ❌ INCONSISTENT: 0% win rate + positive realized return")
    else:
        print(f"   ✅ CONSISTENT: Win rate and return direction match")
    
    print("\n" + "="*60)
    print("📊 Test Scenario 2: All Winning Trades")
    
    # Reset and test winning scenario
    trader.account['balance'] = 55.00  # +$5.00 realized profit
    trader.account['equity'] = 55.00   # No unrealized P&L
    trader.account['unrealized_pnl'] = 0.00
    
    # Clear and add winning trades
    trader.trade_history = []
    trader.trade_history.append({
        'action': 'CLOSE',
        'timestamp': '2025-06-22 11:00:00',
        'side': 'LONG',
        'net_pnl': 3.00,
        'roi_percent': 15.0,
        'hold_time': 0.1
    })
    
    trader.trade_history.append({
        'action': 'CLOSE',
        'timestamp': '2025-06-22 11:05:00',
        'side': 'SHORT',
        'net_pnl': 2.00,
        'roi_percent': 12.0,
        'hold_time': 0.05
    })
    
    print("\n🔧 Winning Trades Scenario:")
    trader._print_enhanced_trading_statistics()
    
    print("\n" + "="*60)
    print("🎉 Logical Consistency Test Complete!")
    print("✅ Fixed: Performance rating now based on realized P&L")
    print("✅ Consistent: Win rate and performance rating use same data")
    print("✅ Logical: No more impossible scenarios")
    print("✅ Warning: System alerts when logic seems inconsistent")

if __name__ == "__main__":
    test_logical_consistency_fix()
