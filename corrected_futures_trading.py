#!/usr/bin/env python3
"""
修正版永续合约交易系统 - 修复计算错误
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
import os
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class CorrectedFuturesTrader:
    """
    修正版永续合约交易器 - 正确的计算逻辑
    """
    
    def __init__(self, initial_capital=50, leverage=2, state_file="corrected_trading_state.json"):
        """
        初始化修正版交易器
        """
        self.state_file = state_file
        self.leverage = min(max(leverage, 1), 3)
        
        # 正确的初始化
        self.initial_capital = initial_capital  # 固定为50
        self.capital = initial_capital  # 从50开始，不是70.06！
        
        # 持仓状态
        self.position = 0  # BTC数量
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0
        
        # 交易参数
        self.adaptive_params = {
            'base_thresholds': {
                'strong_long': 0.70, 'weak_long': 0.55,
                'weak_short': 0.45, 'strong_short': 0.30
            },
            'position_sizes': {
                'strong': 0.7, 'weak': 0.5
            },
            'risk_params': {
                'strong_signal': {'stop_loss': 0.025, 'take_profit': 0.06, 'max_hold_hours': 24},
                'weak_signal': {'stop_loss': 0.025, 'take_profit': 0.06, 'max_hold_hours': 16}
            }
        }
        
        self.commission_rate = 0.0004
        self.funding_rate = 0.0001
        
        # 交易记录
        self.trades = []
        self.equity_history = []
        
        # 加载模型
        try:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if model_files:
                model_path = max(model_files, key=lambda x: x.split('_')[-1])
                self.model_data = joblib.load(model_path)
                self.model = self.model_data['model']
                self.scaler = self.model_data['scaler']
        except:
            print("⚠️ 模型加载失败，使用模拟预测")
            self.model = None
        
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        
        print(f"🔧 修正版永续合约交易器初始化完成")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   当前资金: ${self.capital}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   计算逻辑: 已修正")
    
    def calculate_position_size(self, price, position_ratio):
        """
        正确计算仓位大小
        """
        # 可用保证金
        available_margin = self.capital * 0.8  # 80%资金用作保证金
        
        # 实际使用的保证金
        used_margin = available_margin * position_ratio
        
        # 杠杆后的仓位价值
        position_value = used_margin * self.leverage
        
        # BTC数量
        btc_amount = position_value / price
        
        return btc_amount, used_margin
    
    def calculate_pnl(self, current_price):
        """
        正确计算盈亏
        """
        if self.position == 0:
            return 0, 0
        
        # 价格变动比例
        if self.position > 0:  # 多头
            price_change_ratio = (current_price - self.entry_price) / self.entry_price
        else:  # 空头
            price_change_ratio = (self.entry_price - current_price) / self.entry_price
        
        # 杠杆放大的收益率
        leveraged_return = price_change_ratio * self.leverage
        
        # 实际盈亏金额 = 保证金 × 杠杆收益率
        pnl_amount = self.margin_used * leveraged_return
        
        return pnl_amount, price_change_ratio
    
    def get_current_prediction(self, symbol='BTCUSDT'):
        """
        获取当前预测
        """
        try:
            if self.model is None:
                # 模拟预测
                return 0.372, self.fetcher.get_current_price(symbol, is_futures=True), datetime.now()
            
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                return None, None, None
            
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            current_time = datetime.now()
            
            return up_probability, current_price, current_time
            
        except Exception as e:
            print(f"❌ 获取预测错误: {str(e)}")
            return None, None, None
    
    def should_open_position(self, up_probability):
        """
        判断是否开仓
        """
        if self.position != 0:
            return False, 0, 0, None, "已有持仓"
        
        thresholds = self.adaptive_params['base_thresholds']
        position_sizes = self.adaptive_params['position_sizes']
        
        # 确定信号类型
        if up_probability > thresholds['strong_long']:
            signal_type = 'strong_long'
            position_size = position_sizes['strong']
            risk_params = self.adaptive_params['risk_params']['strong_signal'].copy()
        elif up_probability > thresholds['weak_long']:
            signal_type = 'weak_long'
            position_size = position_sizes['weak']
            risk_params = self.adaptive_params['risk_params']['weak_signal'].copy()
        elif up_probability < thresholds['strong_short']:
            signal_type = 'strong_short'
            position_size = position_sizes['strong']
            risk_params = self.adaptive_params['risk_params']['strong_signal'].copy()
        elif up_probability < thresholds['weak_short']:
            signal_type = 'weak_short'
            position_size = position_sizes['weak']
            risk_params = self.adaptive_params['risk_params']['weak_signal'].copy()
        else:
            return False, 0, 0, None, f"中性信号 ({up_probability:.1%})"
        
        # 确定方向
        direction = 1 if 'long' in signal_type else -1
        
        reason = f"{signal_type} (概率: {up_probability:.1%})"
        
        return True, direction, position_size, risk_params, reason
    
    def should_close_position(self, up_probability, current_price):
        """
        判断是否平仓
        """
        if self.position == 0:
            return False, "无持仓"
        
        # 计算当前盈亏
        pnl_amount, price_change_ratio = self.calculate_pnl(current_price)
        
        # 使用当前风险参数
        if hasattr(self, 'current_risk_params'):
            risk_params = self.current_risk_params
        else:
            risk_params = self.adaptive_params['risk_params']['weak_signal']
        
        # 止损检查
        if price_change_ratio < -risk_params['stop_loss']:
            return True, f"止损 ({price_change_ratio:.2%})"
        
        # 止盈检查
        if price_change_ratio > risk_params['take_profit']:
            return True, f"止盈 ({price_change_ratio:.2%})"
        
        # 时间止损
        if self.entry_time:
            hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
            if hold_hours > risk_params['max_hold_hours']:
                return True, f"时间止损 ({hold_hours:.1f}h)"
        
        # 信号反转
        if self.position > 0 and up_probability < 0.35:
            return True, f"多头信号反转 ({up_probability:.1%})"
        elif self.position < 0 and up_probability > 0.65:
            return True, f"空头信号反转 ({up_probability:.1%})"
        
        return False, "持有"
    
    def execute_trade(self, action, direction, position_size_ratio, price, timestamp, confidence=None, reason="", risk_params=None):
        """
        执行交易 - 修正版
        """
        if action == 'OPEN':
            # 正确计算仓位
            btc_amount, used_margin = self.calculate_position_size(price, position_size_ratio)
            
            if direction == -1:
                btc_amount = -btc_amount
            
            self.position = btc_amount
            self.entry_price = price
            self.entry_time = timestamp
            self.margin_used = used_margin
            self.current_risk_params = risk_params
            
            # 扣除开仓手续费
            opening_fee = abs(btc_amount) * price * self.commission_rate
            self.capital -= opening_fee
            
            trade_record = {
                'timestamp': timestamp.isoformat(),
                'action': 'OPEN',
                'direction': 'LONG' if direction == 1 else 'SHORT',
                'price': price,
                'position_size': btc_amount,
                'position_ratio': position_size_ratio,
                'margin_used': used_margin,
                'leverage': self.leverage,
                'confidence': confidence,
                'reason': reason,
                'opening_fee': opening_fee
            }
            
            direction_text = "做多" if direction == 1 else "做空"
            print(f"✅ {direction_text} {abs(btc_amount):.6f} BTC @ ${price:,.2f}")
            print(f"   保证金: ${used_margin:.2f}, 杠杆: {self.leverage}x")
            print(f"   手续费: ${opening_fee:.4f}")
            
        elif action == 'CLOSE':
            # 计算最终盈亏
            pnl_amount, price_change_ratio = self.calculate_pnl(price)
            
            # 扣除平仓手续费
            closing_fee = abs(self.position) * price * self.commission_rate
            
            # 计算资金费率
            funding_fee = self.calculate_funding_fee()
            
            # 最终盈亏
            final_pnl = pnl_amount - closing_fee + funding_fee
            
            # 更新资金
            self.capital = self.capital + self.margin_used + final_pnl
            
            hold_time = (timestamp - self.entry_time).total_seconds() / 3600 if self.entry_time else 0
            
            trade_record = {
                'timestamp': timestamp.isoformat(),
                'action': 'CLOSE',
                'direction': 'LONG' if self.position > 0 else 'SHORT',
                'price': price,
                'entry_price': self.entry_price,
                'price_change_ratio': price_change_ratio,
                'pnl_amount': pnl_amount,
                'closing_fee': closing_fee,
                'funding_fee': funding_fee,
                'final_pnl': final_pnl,
                'hold_hours': hold_time,
                'confidence': confidence,
                'reason': reason,
                'capital_after': self.capital
            }
            
            direction_text = "平多" if self.position > 0 else "平空"
            print(f"✅ {direction_text} @ ${price:,.2f}")
            print(f"   价格变动: {price_change_ratio:+.2%}")
            print(f"   盈亏: ${final_pnl:+.2f}")
            print(f"   资金: ${self.capital:.2f}")
            
            # 重置持仓
            self.position = 0
            self.entry_price = 0
            self.entry_time = None
            self.margin_used = 0
            self.current_risk_params = None
        
        self.trades.append(trade_record)
        self.save_state()
    
    def calculate_funding_fee(self):
        """计算资金费率"""
        if self.position == 0 or not self.entry_time:
            return 0
        
        hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
        funding_periods = int(hold_hours / 8)
        
        if funding_periods > 0:
            position_value = abs(self.position) * self.entry_price
            funding_fee = position_value * self.funding_rate * funding_periods
            return -funding_fee if self.position > 0 else funding_fee
        return 0
    
    def update_equity(self, current_price, timestamp):
        """更新权益"""
        if self.position != 0:
            pnl_amount, _ = self.calculate_pnl(current_price)
            funding_fee = self.calculate_funding_fee()
            current_equity = self.capital + self.margin_used + pnl_amount + funding_fee
        else:
            current_equity = self.capital
        
        equity_record = {
            'timestamp': timestamp,
            'equity': current_equity,
            'total_return': (current_equity - self.initial_capital) / self.initial_capital
        }
        
        self.equity_history.append(equity_record)
        
        # 限制历史记录大小
        if len(self.equity_history) > 1000:
            self.equity_history = self.equity_history[-500:]
    
    def save_state(self):
        """保存状态"""
        state = {
            'initial_capital': self.initial_capital,
            'capital': self.capital,
            'position': self.position,
            'entry_price': self.entry_price,
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'margin_used': self.margin_used,
            'trades': self.trades,
            'equity_history': [
                {
                    'timestamp': eq['timestamp'].isoformat() if isinstance(eq['timestamp'], datetime) else eq['timestamp'],
                    'equity': eq['equity'],
                    'total_return': eq.get('total_return', 0)
                } for eq in self.equity_history
            ],
            'last_saved': datetime.now().isoformat()
        }
        
        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
    
    def print_status(self, current_price=None, up_probability=None):
        """打印状态"""
        if self.equity_history:
            latest_equity = self.equity_history[-1]['equity']
            total_return = self.equity_history[-1]['total_return']
        else:
            latest_equity = self.capital
            total_return = 0
        
        absolute_profit = latest_equity - self.initial_capital
        
        print(f"\n🔧 【修正版永续合约交易系统】")
        print("=" * 60)
        
        # 收益概览
        print(f"💰 收益概览:")
        print(f"   初始资金: ${self.initial_capital:.2f}")
        print(f"   当前权益: ${latest_equity:.2f}")
        print(f"   绝对收益: ${absolute_profit:+.2f}")
        print(f"   总收益率: {total_return:+.2%}")
        
        # 资金状况
        print(f"\n💼 资金状况:")
        print(f"   可用资金: ${self.capital:.2f}")
        print(f"   占用保证金: ${self.margin_used:.2f}")
        
        # 持仓信息
        print(f"\n📈 持仓信息:")
        if self.position != 0:
            position_type = "🟢 多头" if self.position > 0 else "🔴 空头"
            print(f"   当前持仓: {position_type}")
            print(f"   持仓数量: {abs(self.position):.6f} BTC")
            print(f"   入场价格: ${self.entry_price:,.2f}")
            
            if current_price:
                pnl_amount, price_change_ratio = self.calculate_pnl(current_price)
                print(f"   当前价格: ${current_price:,.2f}")
                print(f"   价格变动: {price_change_ratio:+.2%}")
                print(f"   未实现盈亏: ${pnl_amount:+.2f}")
        else:
            print(f"   当前持仓: 💤 空仓")
        
        # 交易统计
        completed_trades = [t for t in self.trades if t.get('action') == 'CLOSE']
        print(f"\n📊 交易统计:")
        print(f"   完成交易: {len(completed_trades)}笔")
        
        if current_price and up_probability:
            print(f"\n📈 市场分析:")
            print(f"   BTC永续价格: ${current_price:,.2f}")
            print(f"   上涨概率: {up_probability:.1%}")
        
        print("=" * 60)

def run_corrected_simulation(check_interval=300, leverage=2):
    """运行修正版模拟"""
    print("🔧 启动修正版永续合约模拟交易系统")
    print("=" * 60)
    print("修正内容: 正确的计算逻辑、真实的收益计算")
    print("")
    
    trader = CorrectedFuturesTrader(initial_capital=50, leverage=leverage)
    
    try:
        while True:
            current_time = datetime.now()
            print(f"\n⏰ {current_time.strftime('%Y-%m-%d %H:%M:%S')} - 修正版交易分析...")
            
            up_prob, current_price, _ = trader.get_current_prediction()
            
            if up_prob is None:
                print("❌ 无法获取预测，跳过")
                time.sleep(check_interval)
                continue
            
            trader.update_equity(current_price, current_time)
            
            # 检查平仓
            if trader.position != 0:
                should_close, close_reason = trader.should_close_position(up_prob, current_price)
                if should_close:
                    trader.execute_trade('CLOSE', 0, 0, current_price, current_time, up_prob, close_reason)
            
            # 检查开仓
            should_open, direction, position_size, risk_params, open_reason = trader.should_open_position(up_prob)
            if should_open:
                trader.execute_trade('OPEN', direction, position_size, current_price, current_time, up_prob, open_reason, risk_params)
            
            trader.print_status(current_price, up_prob)
            
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        print(f"\n🛑 停止修正版交易")
        trader.print_status(current_price, up_prob)
        trader.save_state()

if __name__ == "__main__":
    import sys
    
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300
    leverage = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    
    print("🔧 修正版交易系统说明:")
    print("- 修正了初始资金设置错误")
    print("- 修正了盈亏计算逻辑")
    print("- 使用正确的杠杆计算公式")
    print("- 真实的收益率显示")
    print("")
    
    run_corrected_simulation(interval, leverage)
