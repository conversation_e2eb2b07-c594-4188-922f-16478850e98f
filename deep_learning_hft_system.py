#!/usr/bin/env python3
"""
深度学习高频交易系统
目标: 70%+准确率的高频交易
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Conv1D, MaxPooling1D, Flatten, Input, concatenate
from tensorflow.keras.layers import BatchNormalization, Attention, MultiHeadAttention, LayerNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.model_selection import TimeSeriesSplit
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HighFrequencyFeatureEngineer:
    """高频交易专用特征工程"""
    
    def __init__(self):
        self.scalers = {}
        self.feature_names = []
        
    def create_microstructure_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建市场微观结构特征"""
        features = pd.DataFrame(index=data.index)
        
        # 价格变化特征
        for lag in [1, 2, 3, 5, 10]:
            features[f'price_change_{lag}'] = data['close'].pct_change(lag)
            features[f'high_low_ratio_{lag}'] = (data['high'] - data['low']).rolling(lag).mean() / data['close']
        
        # 成交量特征
        features['volume_ratio'] = data['volume'] / data['volume'].rolling(20).mean()
        features['volume_price_trend'] = data['volume'] * data['close'].pct_change()
        
        # 买卖压力 (模拟)
        features['buy_pressure'] = (data['close'] - data['low']) / (data['high'] - data['low'])
        features['sell_pressure'] = (data['high'] - data['close']) / (data['high'] - data['low'])
        
        # 价格位置
        for window in [5, 10, 20]:
            rolling_min = data['low'].rolling(window).min()
            rolling_max = data['high'].rolling(window).max()
            features[f'price_position_{window}'] = (data['close'] - rolling_min) / (rolling_max - rolling_min)
        
        return features
    
    def create_momentum_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建动量特征"""
        features = pd.DataFrame(index=data.index)
        
        # 多时间框架动量
        for period in [3, 5, 10, 15, 20]:
            features[f'momentum_{period}'] = data['close'] / data['close'].shift(period) - 1
            features[f'rsi_{period}'] = self.calculate_rsi(data['close'], period)
        
        # 加速度
        features['acceleration'] = data['close'].pct_change().diff()
        
        # 动量强度
        features['momentum_strength'] = abs(data['close'].pct_change()) * data['volume']
        
        return features
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def create_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建波动率特征"""
        features = pd.DataFrame(index=data.index)
        
        # 实现波动率
        returns = data['close'].pct_change()
        for window in [5, 10, 20]:
            features[f'volatility_{window}'] = returns.rolling(window).std()
            features[f'volatility_ratio_{window}'] = features[f'volatility_{window}'] / features[f'volatility_{window}'].rolling(50).mean()
        
        # Parkinson波动率
        features['parkinson_vol'] = np.sqrt(0.25 * np.log(data['high'] / data['low'])**2)
        
        # 波动率突变
        features['vol_shock'] = features['volatility_5'] / features['volatility_20']
        
        return features
    
    def create_orderbook_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建订单簿特征 (模拟)"""
        features = pd.DataFrame(index=data.index)
        
        # 模拟买卖价差
        features['spread'] = (data['high'] - data['low']) / data['close']
        features['spread_ma'] = features['spread'].rolling(10).mean()
        features['spread_ratio'] = features['spread'] / features['spread_ma']
        
        # 模拟流动性
        features['liquidity'] = data['volume'] / features['spread']
        features['liquidity_ratio'] = features['liquidity'] / features['liquidity'].rolling(20).mean()
        
        return features
    
    def create_all_hft_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建所有高频特征"""
        logger.info("创建高频交易特征...")
        
        # 各类特征
        micro_features = self.create_microstructure_features(data)
        momentum_features = self.create_momentum_features(data)
        volatility_features = self.create_volatility_features(data)
        orderbook_features = self.create_orderbook_features(data)
        
        # 合并特征
        all_features = pd.concat([
            micro_features, momentum_features, 
            volatility_features, orderbook_features
        ], axis=1)
        
        # 处理缺失值和无穷值
        all_features = all_features.replace([np.inf, -np.inf], np.nan)
        all_features = all_features.fillna(method='ffill').fillna(0)
        
        self.feature_names = all_features.columns.tolist()
        logger.info(f"创建了 {len(self.feature_names)} 个高频特征")
        
        return all_features

class DeepLearningHFTModel:
    """深度学习高频交易模型"""
    
    def __init__(self, sequence_length: int = 60):
        self.sequence_length = sequence_length  # 使用过去60分钟的数据
        self.model = None
        self.scaler = MinMaxScaler()
        self.is_trained = False
        
    def create_lstm_model(self, input_shape: Tuple[int, int]) -> Model:
        """创建LSTM模型"""
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            BatchNormalization(),
            
            LSTM(64, return_sequences=True),
            Dropout(0.2),
            BatchNormalization(),
            
            LSTM(32, return_sequences=False),
            Dropout(0.2),
            
            Dense(50, activation='relu'),
            BatchNormalization(),
            Dropout(0.3),
            
            Dense(25, activation='relu'),
            Dropout(0.2),
            
            Dense(3, activation='softmax')  # 3分类: 下跌/横盘/上涨
        ])
        
        return model
    
    def create_cnn_lstm_model(self, input_shape: Tuple[int, int]) -> Model:
        """创建CNN-LSTM混合模型"""
        model = Sequential([
            # CNN层提取局部特征
            Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=input_shape),
            BatchNormalization(),
            Conv1D(filters=64, kernel_size=3, activation='relu'),
            MaxPooling1D(pool_size=2),
            Dropout(0.2),
            
            Conv1D(filters=32, kernel_size=3, activation='relu'),
            BatchNormalization(),
            Dropout(0.2),
            
            # LSTM层捕获时间依赖
            LSTM(100, return_sequences=True),
            Dropout(0.3),
            LSTM(50, return_sequences=False),
            Dropout(0.3),
            
            # 全连接层
            Dense(50, activation='relu'),
            BatchNormalization(),
            Dropout(0.4),
            
            Dense(25, activation='relu'),
            Dropout(0.3),
            
            Dense(3, activation='softmax')
        ])
        
        return model
    
    def create_transformer_model(self, input_shape: Tuple[int, int]) -> Model:
        """创建Transformer模型"""
        inputs = Input(shape=input_shape)
        
        # Multi-head attention
        attention_output = MultiHeadAttention(
            num_heads=8, 
            key_dim=64,
            dropout=0.1
        )(inputs, inputs)
        
        # Add & Norm
        attention_output = LayerNormalization()(inputs + attention_output)
        
        # Feed Forward
        ffn_output = Dense(128, activation='relu')(attention_output)
        ffn_output = Dropout(0.2)(ffn_output)
        ffn_output = Dense(input_shape[1])(ffn_output)
        
        # Add & Norm
        ffn_output = LayerNormalization()(attention_output + ffn_output)
        
        # Global pooling
        pooled = tf.keras.layers.GlobalAveragePooling1D()(ffn_output)
        
        # Classification head
        outputs = Dense(64, activation='relu')(pooled)
        outputs = Dropout(0.3)(outputs)
        outputs = Dense(32, activation='relu')(outputs)
        outputs = Dropout(0.2)(outputs)
        outputs = Dense(3, activation='softmax')(outputs)
        
        model = Model(inputs=inputs, outputs=outputs)
        return model
    
    def prepare_sequences(self, features: pd.DataFrame, target: pd.Series) -> Tuple[np.ndarray, np.ndarray]:
        """准备序列数据"""
        logger.info("准备序列数据...")
        
        # 标准化特征
        features_scaled = self.scaler.fit_transform(features)
        
        X, y = [], []
        
        for i in range(self.sequence_length, len(features_scaled)):
            # 使用过去sequence_length个时间步的数据
            X.append(features_scaled[i-self.sequence_length:i])
            y.append(target.iloc[i])
        
        X = np.array(X)
        y = np.array(y)
        
        # 移除包含NaN的样本
        valid_mask = ~np.isnan(y)
        X = X[valid_mask]
        y = y[valid_mask]
        
        logger.info(f"序列数据形状: X={X.shape}, y={y.shape}")
        return X, y
    
    def create_target_labels(self, data: pd.DataFrame, 
                           future_periods: int = 3,
                           threshold: float = 0.001) -> pd.Series:
        """创建高频交易标签"""
        logger.info(f"创建高频标签，预测{future_periods}分钟后的价格变动...")
        
        # 计算未来收益率
        future_returns = data['close'].pct_change(future_periods).shift(-future_periods)
        
        # 创建标签 (更严格的阈值)
        labels = pd.Series(index=data.index, dtype=int)
        labels[future_returns < -threshold] = 0  # 下跌
        labels[(future_returns >= -threshold) & (future_returns <= threshold)] = 1  # 横盘
        labels[future_returns > threshold] = 2  # 上涨
        
        # 统计分布
        label_counts = labels.value_counts().sort_index()
        logger.info(f"标签分布: 下跌={label_counts.get(0, 0)}, 横盘={label_counts.get(1, 0)}, 上涨={label_counts.get(2, 0)}")
        
        return labels
    
    def train_ensemble_models(self, X: np.ndarray, y: np.ndarray) -> Dict:
        """训练多个深度学习模型"""
        logger.info("开始训练深度学习模型集成...")
        
        input_shape = (X.shape[1], X.shape[2])
        models = {}
        scores = {}
        
        # 时间序列分割
        tscv = TimeSeriesSplit(n_splits=3)
        
        # 模型配置
        model_configs = {
            'lstm': self.create_lstm_model,
            'cnn_lstm': self.create_cnn_lstm_model,
            'transformer': self.create_transformer_model
        }
        
        for model_name, model_creator in model_configs.items():
            logger.info(f"训练 {model_name} 模型...")
            
            try:
                model = model_creator(input_shape)
                model.compile(
                    optimizer=Adam(learning_rate=0.001),
                    loss='sparse_categorical_crossentropy',
                    metrics=['accuracy']
                )
                
                # 交叉验证
                cv_scores = []
                for train_idx, val_idx in tscv.split(X):
                    X_train, X_val = X[train_idx], X[val_idx]
                    y_train, y_val = y[train_idx], y[val_idx]
                    
                    # 训练
                    history = model.fit(
                        X_train, y_train,
                        validation_data=(X_val, y_val),
                        epochs=50,
                        batch_size=32,
                        verbose=0,
                        callbacks=[
                            EarlyStopping(patience=10, restore_best_weights=True),
                            ReduceLROnPlateau(patience=5, factor=0.5)
                        ]
                    )
                    
                    # 评估
                    val_loss, val_acc = model.evaluate(X_val, y_val, verbose=0)
                    cv_scores.append(val_acc)
                
                avg_score = np.mean(cv_scores)
                models[model_name] = model
                scores[model_name] = avg_score
                
                logger.info(f"{model_name} 平均准确率: {avg_score:.4f}")
                
            except Exception as e:
                logger.error(f"{model_name} 训练失败: {e}")
        
        # 选择最佳模型
        if scores:
            best_model_name = max(scores, key=scores.get)
            self.model = models[best_model_name]
            self.is_trained = True
            
            logger.info(f"最佳模型: {best_model_name} (准确率: {scores[best_model_name]:.4f})")
            
            # 如果最佳准确率达到70%，记录成功
            if scores[best_model_name] >= 0.70:
                logger.info("🎉 达到70%+准确率目标！")
            else:
                logger.warning(f"⚠️ 未达到70%目标，当前最佳: {scores[best_model_name]:.1%}")
        
        return {'models': models, 'scores': scores}
    
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """预测"""
        if not self.is_trained:
            raise ValueError("模型未训练")
        
        # 标准化输入
        X_scaled = self.scaler.transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)
        
        predictions = self.model.predict(X_scaled)
        predicted_classes = np.argmax(predictions, axis=1)
        confidence = np.max(predictions, axis=1)
        
        return predicted_classes, confidence

def create_hft_test_data(n_points: int = 10000) -> pd.DataFrame:
    """创建高频测试数据"""
    logger.info(f"创建 {n_points} 条高频测试数据...")
    
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=n_points, freq='1T')  # 1分钟数据
    
    # 创建更真实的高频价格数据
    price = 100.0
    prices = []
    volumes = []
    
    for i in range(n_points):
        # 高频特征：更小的价格变动，更多的噪音
        trend = 0.00001 * np.sin(i / 1000)  # 长期趋势
        mean_reversion = -0.1 * (price - 100) / 100  # 均值回归
        noise = np.random.normal(0, 0.0005)  # 高频噪音
        
        # 偶尔的微观结构冲击
        if np.random.random() < 0.001:  # 0.1%概率
            shock = np.random.normal(0, 0.002)
        else:
            shock = 0
        
        total_change = trend + mean_reversion + noise + shock
        price *= (1 + total_change)
        prices.append(price)
        
        # 成交量与波动率相关
        volatility = abs(total_change)
        volume = np.random.lognormal(8 + volatility * 1000, 0.5)
        volumes.append(volume)
    
    # 创建OHLC数据
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = [prices[max(0, i-1)] for i in range(n_points)]
    
    # 高低价基于微小波动
    highs = []
    lows = []
    for i, price in enumerate(prices):
        micro_range = abs(np.random.normal(0, price * 0.0002))  # 更小的日内波动
        high = price + micro_range * np.random.random()
        low = price - micro_range * np.random.random()
        highs.append(high)
        lows.append(low)
    
    data['high'] = highs
    data['low'] = lows
    data['volume'] = volumes
    
    # 确保OHLC合理性
    data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
    data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))
    
    return data

if __name__ == "__main__":
    print("🚀 深度学习高频交易系统")
    print("🎯 目标: 70%+准确率")
    print("⚡ 模式: 1分钟高频交易")
    
    # 创建测试数据
    data = create_hft_test_data(10000)
    print(f"✅ 创建高频数据: {len(data)} 条1分钟K线")
    
    # 特征工程
    feature_engineer = HighFrequencyFeatureEngineer()
    features = feature_engineer.create_all_hft_features(data)
    
    # 创建模型
    model = DeepLearningHFTModel(sequence_length=60)
    
    # 创建标签
    target = model.create_target_labels(data, future_periods=3, threshold=0.0008)
    
    # 准备序列数据
    X, y = model.prepare_sequences(features, target)
    
    if len(X) > 1000:
        print("🤖 开始深度学习模型训练...")
        print("⏳ 这可能需要较长时间...")
        
        # 训练模型
        results = model.train_ensemble_models(X, y)
        
        print("\n🎉 深度学习高频交易系统训练完成！")
        print(f"📊 模型性能: {results['scores']}")
        
        # 检查是否达到目标
        best_score = max(results['scores'].values()) if results['scores'] else 0
        if best_score >= 0.70:
            print(f"🎉 成功达到70%+准确率目标: {best_score:.1%}")
        else:
            print(f"⚠️ 未达到70%目标，当前最佳: {best_score:.1%}")
            print("💡 建议: 使用更多真实数据和更长训练时间")
    
    else:
        print("⚠️ 数据不足，跳过训练")
    
    print("\n🔧 下一步优化建议:")
    print("  1. 📊 使用真实的1分钟K线数据")
    print("  2. 🎯 增加订单簿深度数据")
    print("  3. 📈 添加市场情绪指标")
    print("  4. ⚡ 优化模型架构和超参数")
    print("  5. 🔄 实现在线学习机制")
