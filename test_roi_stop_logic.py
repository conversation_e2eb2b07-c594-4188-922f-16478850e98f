#!/usr/bin/env python3
"""
Test script to verify the new ROI-based stop logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader
from datetime import datetime

def test_roi_stop_logic():
    """Test the new ROI-based stop logic"""
    print("🧪 Testing ROI-Based Stop Logic")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 Simulating Your Scenario:")
    print("Investment ROI: +10.0% (should trigger 15% ROI take-profit)")
    print("Take-profit gain: 5.0% account (traditional price-based)")
    print("Expected: ROI logic should NOT trigger yet (10% < 15%)")
    
    # Simulate a position with 10% ROI
    trader.position = {
        'side': 'LONG',
        'size': 0.000141,
        'entry_price': 102000.0,
        'entry_time': datetime.now(),
        'stop_loss_price': 101800.0,
        'take_profit_price': 102500.0,
        'unrealized_pnl': 1.59,
        'roi_percent': 10.0
    }
    
    trader.account['unrealized_pnl'] = 1.59
    trader.account['margin_used'] = 15.0
    
    print("\n🔍 Testing Exit Conditions Check:")
    current_price = 102200.0  # Price that gives 10% ROI
    
    # Test the exit conditions
    should_exit = trader.check_exit_conditions(current_price)
    
    print(f"Current Price: ${current_price:,.2f}")
    print(f"Current ROI: {trader.position['roi_percent']:+.1f}%")
    print(f"ROI Take-Profit Threshold: 15.0%")
    print(f"Should Exit: {should_exit}")
    print(f"Expected: False (10% < 15%)")
    
    print("\n" + "="*60)
    print("🎯 Testing ROI Take-Profit Trigger:")
    
    # Now test with 15% ROI (should trigger)
    trader.position['roi_percent'] = 15.5
    trader.position['unrealized_pnl'] = 2.5
    
    should_exit_profit = trader.check_exit_conditions(current_price)
    
    print(f"Updated ROI: {trader.position['roi_percent']:+.1f}%")
    print(f"Should Exit: {should_exit_profit}")
    print(f"Expected: True (15.5% >= 15%)")
    
    print("\n🎯 Testing ROI Stop-Loss Trigger:")
    
    # Test with -5% ROI (should trigger stop-loss)
    trader.position['roi_percent'] = -6.0
    trader.position['unrealized_pnl'] = -1.5
    
    should_exit_loss = trader.check_exit_conditions(current_price)
    
    print(f"Updated ROI: {trader.position['roi_percent']:+.1f}%")
    print(f"Should Exit: {should_exit_loss}")
    print(f"Expected: True (-6% <= -5%)")
    
    print("\n" + "="*60)
    print("✅ ROI-Based Stop Logic Test Complete!")
    print("💡 This should fix the issue where 10% ROI didn't trigger exit")
    print("🚀 Now the system will use ROI thresholds for high-frequency trading")

if __name__ == "__main__":
    test_roi_stop_logic()
