#!/usr/bin/env python3
"""
最终优化策略 - 平衡保守性和交易频率
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, balanced_accuracy_score

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

def create_binary_trend_model(symbol='BTCUSDT', months_back=24):
    """
    创建二元趋势模型：只预测上涨/下跌
    """
    print(f"🚀 创建二元趋势模型 {symbol}...")
    
    # 1. 获取数据
    start_date = (datetime.now() - timedelta(days=months_back*30)).strftime('%Y-%m-%d')
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date)
    print(f"✅ 获取到 {len(df)} 条数据")
    
    # 2. 创建简单的趋势标签
    print("🎯 创建趋势标签...")
    
    # 计算未来4小时的价格变化
    df['future_price'] = df['close'].shift(-4)  # 4小时后的价格
    df['price_change'] = (df['future_price'] - df['close']) / df['close']
    
    # 二元分类：上涨(1) vs 下跌(0)
    threshold = 0.01  # 1%的变化阈值
    df['trend_target'] = (df['price_change'] > threshold).astype(int)
    
    # 移除最后4行（没有未来价格）
    df = df[:-4].copy()
    
    print(f"趋势分布: 上涨={df['trend_target'].sum()}, 下跌={(1-df['trend_target']).sum()}")
    
    # 3. 特征工程
    print("🔧 特征工程...")
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    # 使用趋势标签
    df_features['target'] = df['trend_target']
    
    # 准备特征
    X = df_features.drop(columns=['target'])
    y = df_features['target']
    
    # 移除原始列和未来信息
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp', 
                      'future_price', 'price_change', 'trend_target']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    # 清理数据
    mask = ~(X.isna().any(axis=1) | y.isna())
    X = X[mask]
    y = y[mask]
    df_clean = df[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    print(f"✅ 清理后: {len(X)} 样本")
    
    # 4. 平衡数据
    print("⚖️ 平衡数据...")
    
    # 每个类别最多1000样本
    max_samples = 1000
    
    up_indices = np.where(y == 1)[0]
    down_indices = np.where(y == 0)[0]
    
    if len(up_indices) > max_samples:
        up_indices = np.random.choice(up_indices, max_samples, replace=False)
    if len(down_indices) > max_samples:
        down_indices = np.random.choice(down_indices, max_samples, replace=False)
    
    balanced_indices = np.concatenate([up_indices, down_indices])
    
    X_balanced = X.iloc[balanced_indices]
    y_balanced = y.iloc[balanced_indices]
    df_balanced = df_clean.iloc[balanced_indices]
    
    print(f"平衡后: 上涨={y_balanced.sum()}, 下跌={(1-y_balanced).sum()}")
    
    # 5. 时间序列分割
    time_index = df_balanced.index
    sort_indices = np.argsort(time_index)
    
    X_sorted = X_balanced.iloc[sort_indices]
    y_sorted = y_balanced.iloc[sort_indices]
    
    split_point = int(len(X_sorted) * 0.8)
    
    X_train = X_sorted.iloc[:split_point]
    X_test = X_sorted.iloc[split_point:]
    y_train = y_sorted.iloc[:split_point]
    y_test = y_sorted.iloc[split_point:]
    
    print(f"训练集: {len(X_train)}, 测试集: {len(X_test)}")
    
    # 6. 特征缩放
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 7. 训练模型
    print("🎯 训练二元分类模型...")
    
    if HAS_XGB:
        model = xgb.XGBClassifier(
            n_estimators=50,
            max_depth=4,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=1.0,
            reg_lambda=1.0,
            random_state=42,
            n_jobs=-1
        )
    else:
        model = RandomForestClassifier(
            n_estimators=50,
            max_depth=6,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42,
            n_jobs=-1
        )
    
    model.fit(X_train_scaled, y_train)
    
    # 8. 评估
    y_train_pred = model.predict(X_train_scaled)
    y_test_pred = model.predict(X_test_scaled)
    
    train_acc = accuracy_score(y_train, y_train_pred)
    test_acc = accuracy_score(y_test, y_test_pred)
    test_balanced = balanced_accuracy_score(y_test, y_test_pred)
    
    print(f"\n📊 二元模型性能:")
    print(f"训练准确率: {train_acc:.4f}")
    print(f"测试准确率: {test_acc:.4f}")
    print(f"平衡准确率: {test_balanced:.4f}")
    print(f"过拟合程度: {train_acc - test_acc:.4f}")
    
    print("\n📋 分类报告:")
    print(classification_report(y_test, y_test_pred, target_names=['下跌', '上涨']))
    
    # 9. 保存模型
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = f"models/binary_trend_{symbol}_{timestamp}.joblib"
    
    import os
    os.makedirs("models", exist_ok=True)
    
    joblib.dump({
        'model': model,
        'scaler': scaler,
        'feature_names': X.columns.tolist(),
        'threshold': threshold
    }, model_path)
    
    print(f"💾 二元模型已保存: {model_path}")
    
    return model_path

def adaptive_backtest(model_path, symbol='BTCUSDT', test_months=3):
    """
    自适应回测：动态调整置信度阈值
    """
    print(f"\n🔍 自适应回测 {symbol}...")
    
    # 加载模型
    model_data = joblib.load(model_path)
    model = model_data['model']
    scaler = model_data['scaler']
    threshold = model_data['threshold']
    
    # 获取测试数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=test_months*30)
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date.strftime('%Y-%m-%d'))
    
    # 特征工程
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    X = df_features.drop(columns=['target'])
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    mask = ~X.isna().any(axis=1)
    X = X[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    # 预测
    X_scaled = scaler.transform(X)
    predictions = model.predict(X_scaled)
    prediction_proba = model.predict_proba(X_scaled)
    
    # 获取上涨概率
    up_proba = prediction_proba[:, 1]
    
    # 自适应交易策略
    capital = 10000
    position = 0  # 0=空仓, 1=多头
    trades = []
    equity_curve = []
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    # 动态置信度阈值
    confidence_threshold = 0.6  # 起始阈值
    
    for i in range(len(predictions)):
        price = prices[i]
        up_prob = up_proba[i]
        
        # 买入信号：上涨概率高
        if up_prob > confidence_threshold and position == 0:
            position = 1
            entry_price = price
            trades.append({
                'type': '买入',
                'price': price,
                'time': timestamps[i],
                'confidence': up_prob
            })
        
        # 卖出信号：上涨概率低或止损
        elif (up_prob < (1 - confidence_threshold) or 
              (position == 1 and (price / entry_price - 1) < -0.03)) and position == 1:  # 3%止损
            
            position = 0
            pnl_ratio = (price - entry_price) / entry_price
            capital = capital * (1 + pnl_ratio - 0.002)  # 扣除手续费
            
            trades.append({
                'type': '卖出',
                'price': price,
                'entry_price': entry_price,
                'pnl_ratio': pnl_ratio,
                'time': timestamps[i],
                'confidence': up_prob
            })
            
            # 动态调整置信度阈值
            if pnl_ratio > 0:
                confidence_threshold = max(0.55, confidence_threshold - 0.01)  # 成功则降低阈值
            else:
                confidence_threshold = min(0.75, confidence_threshold + 0.01)  # 失败则提高阈值
        
        # 记录权益
        if position == 1:
            current_value = capital * (price / entry_price)
        else:
            current_value = capital
        
        equity_curve.append({
            'time': timestamps[i],
            'value': current_value,
            'price': price,
            'position': position,
            'up_prob': up_prob,
            'threshold': confidence_threshold
        })
    
    # 最后平仓
    if position == 1:
        final_pnl = (prices[-1] - entry_price) / entry_price
        capital = capital * (1 + final_pnl - 0.002)
        trades.append({
            'type': '强制平仓',
            'price': prices[-1],
            'entry_price': entry_price,
            'pnl_ratio': final_pnl,
            'time': timestamps[-1]
        })
    
    # 计算结果
    total_return = (capital - 10000) / 10000
    buy_hold_return = (prices[-1] - prices[0]) / prices[0]
    
    profitable_trades = len([t for t in trades if t.get('pnl_ratio', 0) > 0])
    total_completed_trades = len([t for t in trades if 'pnl_ratio' in t])
    win_rate = profitable_trades / total_completed_trades if total_completed_trades > 0 else 0
    
    # 计算最大回撤
    equity_values = [eq['value'] for eq in equity_curve]
    peak = np.maximum.accumulate(equity_values)
    drawdown = (np.array(equity_values) - peak) / peak
    max_drawdown = np.min(drawdown)
    
    print(f"\n📊 自适应回测结果:")
    print(f"💰 最终资金: ${capital:,.2f}")
    print(f"📈 总收益率: {total_return:.2%}")
    print(f"📈 基准收益率: {buy_hold_return:.2%}")
    print(f"🎯 超额收益: {total_return - buy_hold_return:.2%}")
    print(f"📊 完成交易: {total_completed_trades}")
    print(f"📊 胜率: {win_rate:.2%}")
    print(f"📊 最大回撤: {max_drawdown:.2%}")
    
    # 绘制简单图表
    plt.figure(figsize=(12, 8))
    
    # 权益曲线
    plt.subplot(2, 1, 1)
    times = [eq['time'] for eq in equity_curve]
    values = [eq['value'] for eq in equity_curve]
    plt.plot(times, values, label='策略权益', linewidth=2)
    
    # 基准线
    benchmark_values = 10000 * (1 + (prices - prices[0]) / prices[0])
    plt.plot(timestamps, benchmark_values, label='买入持有', alpha=0.7, linestyle='--')
    
    plt.title(f'{symbol} 权益曲线对比')
    plt.ylabel('资金')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 置信度阈值变化
    plt.subplot(2, 1, 2)
    thresholds = [eq['threshold'] for eq in equity_curve]
    plt.plot(times, thresholds, label='动态置信度阈值', color='orange')
    plt.title('置信度阈值自适应调整')
    plt.ylabel('阈值')
    plt.xlabel('时间')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = f"adaptive_backtest_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存: {chart_path}")
    
    plt.show()
    
    return {
        'total_return': total_return,
        'benchmark_return': buy_hold_return,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'total_trades': total_completed_trades,
        'final_capital': capital
    }

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    
    print("🎯 最终优化策略")
    print("=" * 50)
    
    # 1. 创建二元趋势模型
    print("第一步：创建二元趋势模型")
    model_path = create_binary_trend_model(symbol, 24)
    
    # 2. 自适应回测
    print("\n第二步：自适应回测")
    results = adaptive_backtest(model_path, symbol, 3)
    
    print(f"\n🎯 总结:")
    if results['total_return'] > 0:
        print("✅ 策略盈利！")
    else:
        print("❌ 策略亏损")
    
    if results['total_return'] > results['benchmark_return']:
        print("✅ 跑赢基准！")
    else:
        print("❌ 跑输基准")
    
    print(f"\n建议:")
    if results['win_rate'] > 0.5 and results['total_return'] > 0:
        print("✅ 策略表现良好，可以考虑小额实盘测试")
    else:
        print("⚠️  策略仍需改进，建议继续优化或考虑其他方法")
