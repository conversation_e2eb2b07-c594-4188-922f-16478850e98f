#!/usr/bin/env python3
"""
Test script to verify P&L calculation fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pnl_calculation_fix():
    """Test the P&L calculation fix"""
    print("🧪 Testing P&L Calculation Fix")
    print("=" * 60)
    
    print("\n📊 Analysis from Trading Log:")
    print("✅ Prediction: All SHORT trades (做空)")
    print("✅ Market movement: Price dropped from $102,499.50 → $102,454.10")
    print("✅ Direction correct: SHORT + Price drop = Should profit")
    print("❌ But actual result: All trades lost money")
    
    # Simulate the problematic scenario
    print(f"\n🔧 Simulating Trade Scenario:")
    
    # Trade parameters from log
    position_size = 0.002930  # BTC
    entry_price = 102499.50
    exit_price = 102454.10
    leverage = 125.0
    
    print(f"   📊 Position Size: {position_size:.6f} BTC")
    print(f"   📍 Entry Price: ${entry_price:,.2f}")
    print(f"   📍 Exit Price: ${exit_price:,.2f}")
    print(f"   ⚡ Leverage: {leverage}x")
    print(f"   🔴 Direction: SHORT (做空)")
    
    # Calculate price difference
    price_diff = entry_price - exit_price  # For SHORT: entry - exit
    price_change_pct = (price_diff / entry_price) * 100
    
    print(f"\n📈 Price Movement Analysis:")
    print(f"   📉 Price Change: ${price_diff:+.2f}")
    print(f"   📊 Price Change %: {price_change_pct:+.2f}%")
    print(f"   ✅ Favorable for SHORT: Price dropped")
    
    # Calculate P&L with old (wrong) method
    print(f"\n❌ Old (Wrong) Calculation:")
    pnl_old = position_size * price_diff * leverage
    position_value_old = position_size * exit_price * leverage  # Wrong: double leverage
    trading_fee_old = position_value_old * 0.0004
    net_pnl_old = pnl_old - trading_fee_old
    
    print(f"   💰 P&L: {position_size:.6f} × {price_diff:+.2f} × {leverage} = ${pnl_old:+.2f}")
    print(f"   💎 Position Value: {position_size:.6f} × ${exit_price:,.2f} × {leverage} = ${position_value_old:,.2f}")
    print(f"   💸 Trading Fee: ${position_value_old:,.2f} × 0.0004 = ${trading_fee_old:+.2f}")
    print(f"   💵 Net P&L: ${pnl_old:+.2f} - ${trading_fee_old:.2f} = ${net_pnl_old:+.2f}")
    print(f"   🚨 Problem: Massive trading fee due to double leverage!")
    
    # Calculate P&L with new (correct) method
    print(f"\n✅ New (Correct) Calculation:")
    pnl_new = position_size * price_diff * leverage  # P&L calculation stays same
    position_value_new = position_size * exit_price  # Fixed: no double leverage
    trading_fee_new = position_value_new * 0.0004
    net_pnl_new = pnl_new - trading_fee_new
    
    print(f"   💰 P&L: {position_size:.6f} × {price_diff:+.2f} × {leverage} = ${pnl_new:+.2f}")
    print(f"   💎 Position Value: {position_size:.6f} × ${exit_price:,.2f} = ${position_value_new:.2f}")
    print(f"   💸 Trading Fee: ${position_value_new:.2f} × 0.0004 = ${trading_fee_new:+.2f}")
    print(f"   💵 Net P&L: ${pnl_new:+.2f} - ${trading_fee_new:.2f} = ${net_pnl_new:+.2f}")
    print(f"   ✅ Fixed: Reasonable trading fee!")
    
    # Compare results
    print(f"\n📊 Comparison:")
    print(f"   ❌ Old Net P&L: ${net_pnl_old:+.2f}")
    print(f"   ✅ New Net P&L: ${net_pnl_new:+.2f}")
    print(f"   📈 Improvement: ${net_pnl_new - net_pnl_old:+.2f}")
    print(f"   🔧 Fee Reduction: ${trading_fee_old - trading_fee_new:+.2f}")
    
    # ROI calculation
    margin_used = 2.5  # 5% of $50
    roi_old = (net_pnl_old / margin_used) * 100
    roi_new = (net_pnl_new / margin_used) * 100
    
    print(f"\n📈 ROI Comparison:")
    print(f"   💰 Margin Used: ${margin_used:.2f}")
    print(f"   ❌ Old ROI: {roi_old:+.1f}%")
    print(f"   ✅ New ROI: {roi_new:+.1f}%")
    print(f"   📈 ROI Improvement: {roi_new - roi_old:+.1f}%")
    
    # Verify against log data
    print(f"\n🔍 Verification Against Log Data:")
    log_roi = 225.6  # From trading log: "动态ROI止盈(+225.6%)"
    log_pnl = -2.38  # From trading log: "盈亏: $-2.38"
    
    print(f"   📊 Log ROI: +{log_roi:.1f}%")
    print(f"   📊 Log P&L: ${log_pnl:+.2f}")
    print(f"   🔧 Calculated ROI: {roi_new:+.1f}%")
    print(f"   🔧 Calculated P&L: ${net_pnl_new:+.2f}")
    
    if abs(roi_new - log_roi) > 50:
        print(f"   ⚠️ Large ROI difference: Still need investigation")
    else:
        print(f"   ✅ ROI reasonably close")
    
    if net_pnl_new > 0 and log_pnl < 0:
        print(f"   🎉 MAJOR FIX: Turned loss into profit!")
    elif abs(net_pnl_new - log_pnl) < 1:
        print(f"   ✅ P&L calculation accurate")
    else:
        print(f"   ⚠️ P&L still different: May need further fixes")
    
    # Expected results for all trades
    print(f"\n🎯 Expected Results for All Trades:")
    
    # All trades were SHORT with price drops
    all_trades_should_profit = net_pnl_new > 0
    
    if all_trades_should_profit:
        print(f"   ✅ All SHORT trades should now be profitable")
        print(f"   📈 Expected win rate improvement: 11.8% → ~88.2%")
        print(f"   💰 Expected profit per trade: ~${net_pnl_new:.2f}")
        print(f"   🎉 Problem solved: Predictions were correct, calculation was wrong!")
    else:
        print(f"   ⚠️ Still showing losses: Need further investigation")
        print(f"   🔧 May need to check other calculation components")
    
    print(f"\n" + "="*60)
    print("🎉 P&L Calculation Fix Test Complete!")
    
    if all_trades_should_profit:
        print("✅ SUCCESS: Fix should turn losses into profits")
        print("✅ Predictions were correct all along")
        print("✅ Problem was in fee calculation (double leverage)")
        print("✅ Expected dramatic improvement in win rate")
    else:
        print("⚠️ PARTIAL: Fix improves but may need more work")
    
    print(f"\n💡 Key Insights:")
    print(f"   🎯 User was right: Predictions were accurate")
    print(f"   🔧 Problem: Double leverage in fee calculation")
    print(f"   💰 Fix: Remove extra leverage from position value")
    print(f"   📈 Result: Turn losses into profits")
    print(f"   🚀 Impact: Dramatically improve win rate")

if __name__ == "__main__":
    test_pnl_calculation_fix()
