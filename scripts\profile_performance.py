"""
性能分析脚本
用于分析系统各个组件的性能表现
"""

import cProfile
import pstats
import io
import time
import pandas as pd
import numpy as np
from memory_profiler import profile
from line_profiler import LineProfiler
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from backtester import Backtester
from online_learner import OnlineLearner
from sentiment_analyzer import SentimentAnalyzer
from sklearn.linear_model import SGDClassifier

def create_test_data(n_samples: int = 10000) -> pd.DataFrame:
    """创建测试数据"""
    dates = pd.date_range(start='2023-01-01', periods=n_samples, freq='H')
    data = pd.DataFrame({
        'open': np.random.randn(n_samples).cumsum() + 100,
        'high': np.random.randn(n_samples).cumsum() + 102,
        'low': np.random.randn(n_samples).cumsum() + 98,
        'close': np.random.randn(n_samples).cumsum() + 100,
        'volume': np.random.randint(1000, 10000, n_samples)
    }, index=dates)
    return data

@profile
def profile_backtester():
    """分析回测器性能"""
    print("\n=== 分析回测器性能 ===")
    data = create_test_data()
    backtester = Backtester(data)
    
    # 创建随机信号
    signals = pd.Series(np.random.choice([-1, 0, 1], len(data)), index=data.index)
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行回测
    backtester.apply_strategy(signals)
    backtester.calculate_metrics()
    backtester.plot_results()
    
    # 计算运行时间
    duration = time.time() - start_time
    print(f"回测完成时间: {duration:.2f} 秒")

@profile
def profile_online_learner():
    """分析在线学习器性能"""
    print("\n=== 分析在线学习器性能 ===")
    # 创建测试数据
    n_samples = 10000
    X = pd.DataFrame(np.random.randn(n_samples, 5),
                    columns=[f'feature_{i}' for i in range(5)])
    y = pd.Series((X.sum(axis=1) > 0).astype(int))
    
    # 初始化学习器
    base_model = SGDClassifier(loss='log_loss', random_state=42)
    learner = OnlineLearner(model=base_model)
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行滑动窗口训练
    learner.sliding_window_train(X, y)
    
    # 计算运行时间
    duration = time.time() - start_time
    print(f"在线学习完成时间: {duration:.2f} 秒")

def profile_sentiment_analyzer():
    """分析情绪分析器性能"""
    print("\n=== 分析情绪分析器性能 ===")
    # 初始化分析器
    analyzer = SentimentAnalyzer()
    
    # 创建性能分析器
    profiler = LineProfiler()
    profiler_wrapper = profiler(analyzer.calculate_combined_sentiment)
    
    # 运行分析
    try:
        profiler_wrapper()
    except Exception as e:
        print(f"注意：需要配置API密钥才能运行完整的情绪分析")
    
    # 显示性能统计
    profiler.print_stats()

def main():
    """主函数"""
    # 使用cProfile分析整体性能
    pr = cProfile.Profile()
    pr.enable()
    
    # 运行各个组件的性能分析
    profile_backtester()
    profile_online_learner()
    profile_sentiment_analyzer()
    
    # 停止性能分析
    pr.disable()
    
    # 输出性能统计
    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats()
    
    # 保存性能报告
    with open('performance_report.txt', 'w') as f:
        f.write(s.getvalue())
    
if __name__ == '__main__':
    main() 