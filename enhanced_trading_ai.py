#!/usr/bin/env python3
"""
增强版交易AI系统 - 基于现有模型构建完整策略生成器
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
from typing import Dict, List, Tuple, Optional
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class TechnicalAnalyzer:
    """
    技术分析模块 - 识别支撑阻力位、形态等
    """

    def __init__(self):
        self.lookback_periods = [20, 50, 100, 200]

    def find_support_resistance_levels(self, df: pd.DataFrame, current_price: float) -> Dict:
        """
        识别关键支撑阻力位
        """
        # 计算移动平均线作为动态支撑阻力
        ma_levels = {}
        for period in self.lookback_periods:
            if len(df) >= period:
                ma_value = df['close'].rolling(period).mean().iloc[-1]
                ma_levels[f'MA{period}'] = ma_value

        # 识别近期高低点
        recent_data = df.tail(100)  # 最近100个数据点

        # 寻找局部高点和低点
        highs = []
        lows = []

        for i in range(2, len(recent_data) - 2):
            # 局部高点：比前后两个点都高
            if (recent_data['high'].iloc[i] > recent_data['high'].iloc[i-1] and
                recent_data['high'].iloc[i] > recent_data['high'].iloc[i-2] and
                recent_data['high'].iloc[i] > recent_data['high'].iloc[i+1] and
                recent_data['high'].iloc[i] > recent_data['high'].iloc[i+2]):
                highs.append(recent_data['high'].iloc[i])

            # 局部低点：比前后两个点都低
            if (recent_data['low'].iloc[i] < recent_data['low'].iloc[i-1] and
                recent_data['low'].iloc[i] < recent_data['low'].iloc[i-2] and
                recent_data['low'].iloc[i] < recent_data['low'].iloc[i+1] and
                recent_data['low'].iloc[i] < recent_data['low'].iloc[i+2]):
                lows.append(recent_data['low'].iloc[i])

        # 筛选有效的支撑阻力位
        resistance_levels = [h for h in highs if h > current_price]
        support_levels = [l for l in lows if l < current_price]

        # 按距离当前价格排序
        resistance_levels.sort()
        support_levels.sort(reverse=True)

        return {
            'support_levels': support_levels[:3],  # 最近的3个支撑位
            'resistance_levels': resistance_levels[:3],  # 最近的3个阻力位
            'ma_levels': ma_levels,
            'current_price': current_price
        }

    def calculate_volatility_forecast(self, df: pd.DataFrame, periods: int = 24) -> float:
        """
        计算波动率预测
        """
        returns = df['close'].pct_change().dropna()
        recent_returns = returns.tail(periods)
        volatility = recent_returns.std() * np.sqrt(24)  # 24小时波动率
        return volatility

    def identify_market_regime(self, df: pd.DataFrame) -> str:
        """
        识别市场状态
        """
        if len(df) < 50:
            return "insufficient_data"

        # 计算短期和长期趋势
        short_ma = df['close'].rolling(20).mean().iloc[-1]
        long_ma = df['close'].rolling(50).mean().iloc[-1]
        current_price = df['close'].iloc[-1]

        # 计算价格相对位置
        recent_high = df['high'].tail(20).max()
        recent_low = df['low'].tail(20).min()
        price_position = (current_price - recent_low) / (recent_high - recent_low)

        # 计算趋势强度
        price_change_20 = (current_price - df['close'].iloc[-20]) / df['close'].iloc[-20]

        # 判断市场状态
        if current_price > short_ma > long_ma and price_change_20 > 0.02:
            return "strong_uptrend"
        elif current_price > short_ma and price_change_20 > 0.01:
            return "weak_uptrend"
        elif current_price < short_ma < long_ma and price_change_20 < -0.02:
            return "strong_downtrend"
        elif current_price < short_ma and price_change_20 < -0.01:
            return "weak_downtrend"
        elif abs(price_change_20) < 0.01 and 0.3 < price_position < 0.7:
            return "sideways"
        else:
            return "transitional"

    def calculate_rsi(self, df: pd.DataFrame, period: int = 14) -> float:
        """
        计算RSI指标
        """
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1] if not rsi.empty else 50.0

class RiskManager:
    """
    风险管理模块
    """

    def __init__(self, max_risk_per_trade: float = 0.02):
        self.max_risk_per_trade = max_risk_per_trade  # 单笔最大风险2%
        self.max_total_risk = 0.06  # 总风险6%

    def calculate_position_size(self,
                              account_balance: float,
                              entry_price: float,
                              stop_loss_price: float,
                              confidence: float,
                              leverage: int = 2) -> Dict:
        """
        计算仓位大小
        """
        # 基础风险金额
        risk_amount = account_balance * self.max_risk_per_trade

        # 根据置信度调整
        confidence_multiplier = min(confidence * 2, 1.0)  # 最大不超过1
        adjusted_risk = risk_amount * confidence_multiplier

        # 计算价格风险
        price_risk = abs(entry_price - stop_loss_price) / entry_price

        # 计算仓位大小
        position_value = adjusted_risk / price_risk
        margin_required = position_value / leverage
        position_size_btc = position_value / entry_price

        # 确保不超过账户限制
        max_margin = account_balance * 0.8  # 最多使用80%资金
        if margin_required > max_margin:
            margin_required = max_margin
            position_value = margin_required * leverage
            position_size_btc = position_value / entry_price

        return {
            'position_size_btc': position_size_btc,
            'position_value': position_value,
            'margin_required': margin_required,
            'risk_amount': adjusted_risk,
            'risk_percentage': (adjusted_risk / account_balance) * 100,
            'confidence_multiplier': confidence_multiplier
        }

    def calculate_stop_loss_take_profit(self,
                                      entry_price: float,
                                      direction: str,
                                      volatility: float,
                                      market_regime: str) -> Dict:
        """
        计算止损止盈位
        """
        # 基础止损比例
        base_stop_loss = 0.025  # 2.5%

        # 根据波动率调整
        volatility_adjustment = min(volatility * 2, 0.01)  # 最多增加1%
        adjusted_stop_loss = base_stop_loss + volatility_adjustment

        # 根据市场状态调整
        regime_adjustments = {
            'strong_uptrend': {'stop': 0.8, 'profit': 1.5},
            'weak_uptrend': {'stop': 1.0, 'profit': 1.2},
            'strong_downtrend': {'stop': 0.8, 'profit': 1.5},
            'weak_downtrend': {'stop': 1.0, 'profit': 1.2},
            'sideways': {'stop': 1.2, 'profit': 0.8},
            'transitional': {'stop': 1.1, 'profit': 1.0}
        }

        adjustment = regime_adjustments.get(market_regime, {'stop': 1.0, 'profit': 1.0})
        final_stop_loss = adjusted_stop_loss * adjustment['stop']

        # 计算止盈 (风险收益比 2:1)
        base_take_profit = final_stop_loss * 2 * adjustment['profit']

        if direction.lower() == 'long':
            stop_loss_price = entry_price * (1 - final_stop_loss)
            take_profit_price = entry_price * (1 + base_take_profit)
        else:  # short
            stop_loss_price = entry_price * (1 + final_stop_loss)
            take_profit_price = entry_price * (1 - base_take_profit)

        return {
            'stop_loss_price': stop_loss_price,
            'take_profit_price': take_profit_price,
            'stop_loss_percentage': final_stop_loss * 100,
            'take_profit_percentage': base_take_profit * 100,
            'risk_reward_ratio': base_take_profit / final_stop_loss
        }

class StrategyGenerator:
    """
    策略生成模块
    """

    def __init__(self):
        self.signal_thresholds = {
            'strong_long': 0.70,
            'weak_long': 0.55,
            'weak_short': 0.45,
            'strong_short': 0.30
        }

    def classify_signal_strength(self, probability: float) -> Tuple[str, str]:
        """
        分类信号强度
        """
        if probability > self.signal_thresholds['strong_long']:
            return 'long', 'strong'
        elif probability > self.signal_thresholds['weak_long']:
            return 'long', 'weak'
        elif probability < self.signal_thresholds['strong_short']:
            return 'short', 'strong'
        elif probability < self.signal_thresholds['weak_short']:
            return 'short', 'weak'
        else:
            return 'neutral', 'none'

    def generate_entry_conditions(self,
                                direction: str,
                                strength: str,
                                current_price: float,
                                support_resistance: Dict,
                                market_regime: str) -> List[Dict]:
        """
        生成入场条件
        """
        conditions = []

        if direction == 'long':
            if strength == 'strong':
                # 强做多：立即入场
                conditions.append({
                    'type': 'immediate',
                    'action': '立即做多',
                    'entry_price': current_price,
                    'reason': '强烈看涨信号',
                    'urgency': 'high',
                    'position_ratio': 0.7
                })
            else:
                # 弱做多：等待回调
                support_levels = support_resistance.get('support_levels', [])
                if support_levels:
                    target_price = max(support_levels)
                    conditions.append({
                        'type': 'conditional',
                        'action': '等待回调做多',
                        'entry_price': target_price,
                        'trigger_condition': f'价格回调至${target_price:,.0f}',
                        'reason': '轻微看涨，等待更好价位',
                        'urgency': 'medium',
                        'position_ratio': 0.5
                    })

        elif direction == 'short':
            if strength == 'strong':
                # 强做空：立即入场
                conditions.append({
                    'type': 'immediate',
                    'action': '立即做空',
                    'entry_price': current_price,
                    'reason': '强烈看跌信号',
                    'urgency': 'high',
                    'position_ratio': 0.7
                })
            else:
                # 弱做空：等待反弹
                resistance_levels = support_resistance.get('resistance_levels', [])
                if resistance_levels:
                    target_price = min(resistance_levels)
                    conditions.append({
                        'type': 'conditional',
                        'action': '等待反弹做空',
                        'entry_price': target_price,
                        'trigger_condition': f'价格反弹至${target_price:,.0f}',
                        'reason': '轻微看跌，等待更好价位',
                        'urgency': 'medium',
                        'position_ratio': 0.5
                    })
                else:
                    # 没有明显阻力位，谨慎做空
                    conditions.append({
                        'type': 'immediate',
                        'action': '谨慎做空',
                        'entry_price': current_price,
                        'reason': '轻微看跌信号',
                        'urgency': 'low',
                        'position_ratio': 0.3
                    })

        else:  # neutral
            # 中性信号：观望
            support_levels = support_resistance.get('support_levels', [])
            resistance_levels = support_resistance.get('resistance_levels', [])

            conditions.append({
                'type': 'wait',
                'action': '观望等待',
                'reason': '信号不明确',
                'wait_conditions': [
                    f'跌破${max(support_levels):,.0f}→考虑做空' if support_levels else '等待支撑位确认',
                    f'突破${min(resistance_levels):,.0f}→考虑做多' if resistance_levels else '等待阻力位确认'
                ],
                'urgency': 'none'
            })

        return conditions

    def estimate_time_horizon(self,
                            strength: str,
                            market_regime: str,
                            volatility: float) -> str:
        """
        估计持仓时间
        """
        base_hours = {
            'strong': 24,  # 强信号持仓更久
            'weak': 12,    # 弱信号快进快出
            'none': 0
        }

        regime_multipliers = {
            'strong_uptrend': 1.5,
            'strong_downtrend': 1.5,
            'weak_uptrend': 1.0,
            'weak_downtrend': 1.0,
            'sideways': 0.5,
            'transitional': 0.8
        }

        volatility_adjustment = 1 - min(volatility * 10, 0.5)  # 高波动率缩短持仓

        estimated_hours = (base_hours.get(strength, 12) *
                          regime_multipliers.get(market_regime, 1.0) *
                          volatility_adjustment)

        if estimated_hours < 4:
            return "短期 (4小时内)"
        elif estimated_hours < 24:
            return f"中短期 ({estimated_hours:.0f}小时)"
        elif estimated_hours < 72:
            return f"中期 ({estimated_hours/24:.1f}天)"
        else:
            return "长期 (3天以上)"

class EnhancedTradingAI:
    """
    增强版交易AI - 基于现有模型构建完整策略生成器
    """

    def __init__(self, initial_capital: float = 50, leverage: int = 2):
        """
        初始化增强版交易AI
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = leverage

        # 加载现有AI模型
        self.ai_model = None
        self.scaler = None
        self._load_existing_model()

        # 初始化各个模块
        self.technical_analyzer = TechnicalAnalyzer()
        self.risk_manager = RiskManager()
        self.strategy_generator = StrategyGenerator()

        # 数据获取
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()

        # 交易状态
        self.position = 0
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0

        print(f"🚀 增强版交易AI初始化完成")
        print(f"   基础模型: {'已加载' if self.ai_model else '未找到'}")
        print(f"   技术分析: 已集成")
        print(f"   风险管理: 已集成")
        print(f"   策略生成: 已集成")

    def _load_existing_model(self):
        """
        加载现有的AI模型
        """
        try:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if model_files:
                model_path = max(model_files, key=lambda x: x.split('_')[-1])
                model_data = joblib.load(model_path)
                self.ai_model = model_data['model']
                self.scaler = model_data['scaler']
                print(f"✅ 成功加载模型: {model_path}")
            else:
                print(f"⚠️ 未找到模型文件，将使用模拟预测")
        except Exception as e:
            print(f"❌ 模型加载失败: {str(e)}")

    def get_ai_prediction(self, symbol: str = 'BTCUSDT') -> Tuple[float, float]:
        """
        获取AI模型预测
        """
        try:
            if self.ai_model is None:
                # 模拟预测
                return 0.372, self.fetcher.get_current_price(symbol, is_futures=True)

            # 获取数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)

            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )

            if len(df) < 200:
                return None, None

            # 特征工程
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')

            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)

            # 预测
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.ai_model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]

            current_price = self.fetcher.get_current_price(symbol, is_futures=True)

            return up_probability, current_price

        except Exception as e:
            print(f"❌ AI预测失败: {str(e)}")
            return None, None

    def comprehensive_market_analysis(self, symbol: str = 'BTCUSDT') -> Dict:
        """
        全方位市场分析
        """
        print(f"🔍 执行全方位市场分析...")

        # 1. 获取AI预测
        up_probability, current_price = self.get_ai_prediction(symbol)
        if up_probability is None:
            return None

        # 2. 获取历史数据进行技术分析
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        df = self.fetcher.get_historical_data(
            symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
        )

        # 3. 技术分析
        support_resistance = self.technical_analyzer.find_support_resistance_levels(df, current_price)
        volatility = self.technical_analyzer.calculate_volatility_forecast(df)
        market_regime = self.technical_analyzer.identify_market_regime(df)
        rsi = self.technical_analyzer.calculate_rsi(df)

        # 4. 信号分类
        direction, strength = self.strategy_generator.classify_signal_strength(up_probability)

        # 5. 风险评估
        risk_metrics = self.risk_manager.calculate_stop_loss_take_profit(
            current_price, direction, volatility, market_regime
        )

        # 6. 时间预估
        time_horizon = self.strategy_generator.estimate_time_horizon(
            strength, market_regime, volatility
        )

        return {
            'ai_prediction': {
                'up_probability': up_probability,
                'down_probability': 1 - up_probability,
                'direction': direction,
                'strength': strength
            },
            'market_data': {
                'current_price': current_price,
                'volatility': volatility,
                'market_regime': market_regime,
                'rsi': rsi
            },
            'technical_analysis': support_resistance,
            'risk_metrics': risk_metrics,
            'time_horizon': time_horizon,
            'analysis_timestamp': datetime.now()
        }

    def generate_trading_strategies(self, analysis: Dict) -> List[Dict]:
        """
        生成具体的交易策略
        """
        if not analysis:
            return []

        ai_pred = analysis['ai_prediction']
        market_data = analysis['market_data']
        technical = analysis['technical_analysis']
        risk_metrics = analysis['risk_metrics']

        # 生成入场条件
        entry_conditions = self.strategy_generator.generate_entry_conditions(
            ai_pred['direction'],
            ai_pred['strength'],
            market_data['current_price'],
            technical,
            market_data['market_regime']
        )

        # 为每个入场条件生成完整策略
        strategies = []
        for condition in entry_conditions:
            if condition['type'] == 'immediate' or condition['type'] == 'conditional':
                # 计算仓位大小
                position_calc = self.risk_manager.calculate_position_size(
                    self.capital,
                    condition['entry_price'],
                    risk_metrics['stop_loss_price'],
                    ai_pred['up_probability'] if ai_pred['direction'] == 'long' else (1 - ai_pred['up_probability']),
                    self.leverage
                )

                strategy = {
                    'strategy_id': f"{ai_pred['direction']}_{ai_pred['strength']}_{condition['type']}",
                    'action': condition['action'],
                    'direction': ai_pred['direction'],
                    'strength': ai_pred['strength'],
                    'urgency': condition['urgency'],
                    'entry_conditions': condition,
                    'position_sizing': position_calc,
                    'risk_management': risk_metrics,
                    'time_horizon': analysis['time_horizon'],
                    'expected_outcome': self._calculate_expected_outcome(condition, risk_metrics, position_calc),
                    'confidence_score': self._calculate_confidence_score(analysis)
                }
                strategies.append(strategy)
            else:
                # 观望策略
                strategy = {
                    'strategy_id': 'wait_and_see',
                    'action': condition['action'],
                    'direction': 'neutral',
                    'strength': 'none',
                    'urgency': 'none',
                    'wait_conditions': condition.get('wait_conditions', []),
                    'reason': condition['reason'],
                    'monitoring_points': self._generate_monitoring_points(technical, market_data['current_price'])
                }
                strategies.append(strategy)

        return strategies

    def _calculate_expected_outcome(self, condition: Dict, risk_metrics: Dict, position_calc: Dict) -> Dict:
        """
        计算预期结果
        """
        entry_price = condition['entry_price']
        stop_loss = risk_metrics['stop_loss_price']
        take_profit = risk_metrics['take_profit_price']
        position_value = position_calc['position_value']

        # 计算潜在盈亏
        if condition.get('direction') == 'long':
            max_loss = (entry_price - stop_loss) / entry_price * position_value
            max_profit = (take_profit - entry_price) / entry_price * position_value
        else:
            max_loss = (stop_loss - entry_price) / entry_price * position_value
            max_profit = (entry_price - take_profit) / entry_price * position_value

        return {
            'max_profit': max_profit,
            'max_loss': max_loss,
            'risk_reward_ratio': max_profit / max_loss if max_loss > 0 else 0,
            'profit_percentage': (max_profit / self.capital) * 100,
            'loss_percentage': (max_loss / self.capital) * 100
        }

    def _calculate_confidence_score(self, analysis: Dict) -> float:
        """
        计算策略置信度
        """
        ai_pred = analysis['ai_prediction']
        market_data = analysis['market_data']

        # 基础置信度来自AI概率
        base_confidence = abs(ai_pred['up_probability'] - 0.5) * 2  # 转换为0-1范围

        # 技术指标确认
        rsi = market_data['rsi']
        rsi_confirmation = 0
        if ai_pred['direction'] == 'long' and rsi < 40:
            rsi_confirmation = 0.2
        elif ai_pred['direction'] == 'short' and rsi > 60:
            rsi_confirmation = 0.2

        # 市场状态确认
        regime_confirmation = 0
        market_regime = market_data['market_regime']
        if (ai_pred['direction'] == 'long' and 'uptrend' in market_regime) or \
           (ai_pred['direction'] == 'short' and 'downtrend' in market_regime):
            regime_confirmation = 0.3

        # 波动率调整
        volatility = market_data['volatility']
        volatility_penalty = min(volatility * 5, 0.2)  # 高波动率降低置信度

        final_confidence = min(base_confidence + rsi_confirmation + regime_confirmation - volatility_penalty, 1.0)
        return max(final_confidence, 0.1)  # 最低10%置信度

    def _generate_monitoring_points(self, technical: Dict, current_price: float) -> List[str]:
        """
        生成监控要点
        """
        points = []

        support_levels = technical.get('support_levels', [])
        resistance_levels = technical.get('resistance_levels', [])

        if support_levels:
            nearest_support = max(support_levels)
            distance = (current_price - nearest_support) / current_price
            points.append(f"关注支撑位 ${nearest_support:,.0f} (距离: {distance:.1%})")

        if resistance_levels:
            nearest_resistance = min(resistance_levels)
            distance = (nearest_resistance - current_price) / current_price
            points.append(f"关注阻力位 ${nearest_resistance:,.0f} (距离: {distance:.1%})")

        points.append("监控成交量变化")
        points.append("关注AI概率变化")

        return points

    def print_comprehensive_analysis(self, analysis: Dict, strategies: List[Dict]):
        """
        打印全方位分析结果
        """
        if not analysis:
            print("❌ 分析数据不可用")
            return

        ai_pred = analysis['ai_prediction']
        market_data = analysis['market_data']
        technical = analysis['technical_analysis']

        print(f"\n🎯 【增强版AI交易策略分析】")
        print("=" * 70)

        # AI预测部分
        print(f"🤖 AI模型预测:")
        print(f"   上涨概率: {ai_pred['up_probability']:.1%}")
        print(f"   下跌概率: {ai_pred['down_probability']:.1%}")
        print(f"   信号方向: {ai_pred['direction'].upper()}")
        print(f"   信号强度: {ai_pred['strength'].upper()}")

        # 市场数据
        print(f"\n📊 市场状况:")
        print(f"   当前价格: ${market_data['current_price']:,.2f}")
        print(f"   市场状态: {market_data['market_regime']}")
        print(f"   波动率: {market_data['volatility']:.2%}")
        print(f"   RSI指标: {market_data['rsi']:.1f}")

        # 技术分析
        print(f"\n📈 技术分析:")
        support_levels = technical.get('support_levels', [])
        resistance_levels = technical.get('resistance_levels', [])

        if support_levels:
            print(f"   支撑位: {', '.join([f'${level:,.0f}' for level in support_levels[:3]])}")
        if resistance_levels:
            print(f"   阻力位: {', '.join([f'${level:,.0f}' for level in resistance_levels[:3]])}")

        # 策略建议
        print(f"\n🚀 交易策略建议:")
        for i, strategy in enumerate(strategies, 1):
            self._print_strategy_details(strategy, i)

        print("=" * 70)

    def _print_strategy_details(self, strategy: Dict, index: int):
        """
        打印策略详情
        """
        urgency_emojis = {
            'high': '🚨',
            'medium': '⚠️',
            'low': '📊',
            'none': '💤'
        }

        urgency = strategy.get('urgency', 'none')
        emoji = urgency_emojis.get(urgency, '📊')

        print(f"\n   {emoji} 策略 {index}: {strategy['action']}")

        if 'entry_conditions' in strategy:
            entry = strategy['entry_conditions']
            print(f"      方向: {strategy['direction'].upper()}")
            print(f"      强度: {strategy['strength'].upper()}")
            print(f"      入场价: ${entry['entry_price']:,.2f}")

            if 'trigger_condition' in entry:
                print(f"      触发条件: {entry['trigger_condition']}")

            if 'position_sizing' in strategy:
                pos = strategy['position_sizing']
                print(f"      仓位大小: {pos['position_size_btc']:.6f} BTC")
                print(f"      保证金: ${pos['margin_required']:.2f}")
                print(f"      风险金额: ${pos['risk_amount']:.2f}")

            if 'risk_management' in strategy:
                risk = strategy['risk_management']
                print(f"      止损价: ${risk['stop_loss_price']:,.2f}")
                print(f"      止盈价: ${risk['take_profit_price']:,.2f}")
                print(f"      风险收益比: 1:{risk['risk_reward_ratio']:.1f}")

            if 'expected_outcome' in strategy:
                outcome = strategy['expected_outcome']
                print(f"      预期收益: ${outcome['max_profit']:+.2f} ({outcome['profit_percentage']:+.1f}%)")
                print(f"      最大亏损: ${outcome['max_loss']:+.2f} ({outcome['loss_percentage']:+.1f}%)")

            if 'confidence_score' in strategy:
                confidence = strategy['confidence_score']
                print(f"      策略置信度: {confidence:.1%}")

            print(f"      持仓时间: {strategy.get('time_horizon', '未知')}")
            print(f"      理由: {entry.get('reason', '无')}")

        elif 'wait_conditions' in strategy:
            print(f"      理由: {strategy.get('reason', '信号不明确')}")
            print(f"      等待条件:")
            for condition in strategy['wait_conditions']:
                print(f"        • {condition}")

            if 'monitoring_points' in strategy:
                print(f"      监控要点:")
                for point in strategy['monitoring_points']:
                    print(f"        • {point}")

def run_enhanced_trading_ai(check_interval: int = 300, symbol: str = 'BTCUSDT'):
    """
    运行增强版交易AI系统
    """
    print("🚀 启动增强版AI交易策略系统")
    print("=" * 70)
    print("功能特点:")
    print("• 集成现有AI模型预测")
    print("• 技术分析支撑阻力位识别")
    print("• 智能风险管理和仓位计算")
    print("• 多场景策略生成")
    print("• 实时置信度评估")
    print("")

    # 初始化系统
    ai_system = EnhancedTradingAI(initial_capital=50, leverage=2)

    try:
        while True:
            current_time = datetime.now()
            print(f"\n⏰ {current_time.strftime('%Y-%m-%d %H:%M:%S')} - 增强AI分析...")

            # 执行全方位分析
            analysis = ai_system.comprehensive_market_analysis(symbol)

            if analysis is None:
                print("❌ 分析失败，跳过本次循环")
                time.sleep(check_interval)
                continue

            # 生成交易策略
            strategies = ai_system.generate_trading_strategies(analysis)

            # 显示分析结果
            ai_system.print_comprehensive_analysis(analysis, strategies)

            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟后重新分析...")
            time.sleep(check_interval)

    except KeyboardInterrupt:
        print(f"\n🛑 停止增强版AI交易系统")
        print(f"感谢使用增强版交易AI！")

if __name__ == "__main__":
    import sys

    # 解析命令行参数
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300  # 默认5分钟
    symbol = sys.argv[2] if len(sys.argv) > 2 else 'BTCUSDT'

    print("🎯 增强版AI交易系统说明:")
    print("=" * 50)
    print("这个系统将您现有的AI模型升级为完整的交易策略生成器：")
    print("")
    print("🤖 AI模型集成:")
    print("  • 使用您现有的概率预测模型")
    print("  • 37.2%概率 → 完整策略分析")
    print("")
    print("📊 技术分析增强:")
    print("  • 自动识别支撑阻力位")
    print("  • 市场状态分类")
    print("  • 波动率预测")
    print("  • RSI等指标确认")
    print("")
    print("🛡️ 风险管理:")
    print("  • 智能仓位计算")
    print("  • 动态止损止盈")
    print("  • 风险收益比优化")
    print("")
    print("🎯 策略生成:")
    print("  • 多场景分析")
    print("  • 具体入场条件")
    print("  • 时间框架预估")
    print("  • 置信度评分")
    print("")

    # 启动系统
    run_enhanced_trading_ai(interval, symbol)