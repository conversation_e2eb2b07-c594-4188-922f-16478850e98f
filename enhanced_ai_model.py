#!/usr/bin/env python3
"""
增强版AI模型训练系统
基于新特征工程的高质量模型训练
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
from sklearn.pipeline import Pipeline
import joblib
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedAIModel:
    """增强版AI模型训练器"""
    
    def __init__(self):
        self.models = {}
        self.model_scores = {}
        self.best_model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.is_trained = False
        self.feature_importance = {}
        
        # 模型配置
        self.model_configs = {
            'random_forest': {
                'model': RandomForestClassifier(random_state=42),
                'params': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 20, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4]
                }
            },
            'gradient_boosting': {
                'model': GradientBoostingClassifier(random_state=42),
                'params': {
                    'n_estimators': [100, 200],
                    'learning_rate': [0.05, 0.1, 0.2],
                    'max_depth': [3, 5, 7],
                    'subsample': [0.8, 0.9, 1.0]
                }
            },
            'logistic_regression': {
                'model': LogisticRegression(random_state=42, max_iter=1000),
                'params': {
                    'C': [0.1, 1.0, 10.0],
                    'penalty': ['l1', 'l2'],
                    'solver': ['liblinear', 'saga']
                }
            },
            'svm': {
                'model': SVC(random_state=42, probability=True),
                'params': {
                    'C': [0.1, 1.0, 10.0],
                    'kernel': ['rbf', 'linear'],
                    'gamma': ['scale', 'auto']
                }
            },
            'neural_network': {
                'model': MLPClassifier(random_state=42, max_iter=500),
                'params': {
                    'hidden_layer_sizes': [(50,), (100,), (50, 50)],
                    'activation': ['relu', 'tanh'],
                    'alpha': [0.0001, 0.001, 0.01],
                    'learning_rate': ['constant', 'adaptive']
                }
            }
        }
    
    def create_target_labels(self, data: pd.DataFrame, 
                           future_periods: int = 5,
                           threshold: float = 0.002) -> pd.Series:
        """创建目标标签"""
        logger.info(f"创建目标标签，预测{future_periods}期后的价格变动...")
        
        # 计算未来收益率
        future_returns = data['close'].pct_change(future_periods).shift(-future_periods)
        
        # 创建三分类标签
        # 0: 下跌 (< -threshold)
        # 1: 横盘 (-threshold <= x <= threshold)  
        # 2: 上涨 (> threshold)
        labels = pd.Series(index=data.index, dtype=int)
        labels[future_returns < -threshold] = 0  # 下跌
        labels[(future_returns >= -threshold) & (future_returns <= threshold)] = 1  # 横盘
        labels[future_returns > threshold] = 2  # 上涨
        
        # 统计标签分布
        label_counts = labels.value_counts().sort_index()
        logger.info(f"标签分布: 下跌={label_counts.get(0, 0)}, 横盘={label_counts.get(1, 0)}, 上涨={label_counts.get(2, 0)}")
        
        return labels
    
    def prepare_training_data(self, features: pd.DataFrame, 
                            target: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """准备训练数据"""
        logger.info("准备训练数据...")
        
        # 确保数据对齐
        common_index = features.index.intersection(target.index)
        features_aligned = features.loc[common_index]
        target_aligned = target.loc[common_index]
        
        # 去除缺失值
        valid_mask = ~(features_aligned.isnull().any(axis=1) | target_aligned.isnull())
        features_clean = features_aligned[valid_mask]
        target_clean = target_aligned[valid_mask]
        
        logger.info(f"有效训练样本: {len(features_clean)}")
        logger.info(f"特征数量: {len(features_clean.columns)}")
        
        return features_clean, target_clean
    
    def train_single_model(self, model_name: str, X: pd.DataFrame, y: pd.Series) -> Dict:
        """训练单个模型"""
        logger.info(f"训练 {model_name} 模型...")
        
        config = self.model_configs[model_name]
        model = config['model']
        param_grid = config['params']
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=5)
        
        # 创建管道
        if model_name in ['svm', 'neural_network', 'logistic_regression']:
            # 需要标准化的模型
            pipeline = Pipeline([
                ('scaler', StandardScaler()),
                ('model', model)
            ])
            # 调整参数网格
            param_grid = {f'model__{k}': v for k, v in param_grid.items()}
        else:
            pipeline = Pipeline([
                ('model', model)
            ])
            param_grid = {f'model__{k}': v for k, v in param_grid.items()}
        
        # 网格搜索
        grid_search = GridSearchCV(
            pipeline, 
            param_grid, 
            cv=tscv, 
            scoring='accuracy',
            n_jobs=-1,
            verbose=0
        )
        
        try:
            grid_search.fit(X, y)
            
            # 获取最佳模型
            best_model = grid_search.best_estimator_
            best_score = grid_search.best_score_
            best_params = grid_search.best_params_
            
            # 交叉验证评估
            cv_scores = cross_val_score(best_model, X, y, cv=tscv, scoring='accuracy')
            
            result = {
                'model': best_model,
                'best_score': best_score,
                'best_params': best_params,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'cv_scores': cv_scores
            }
            
            logger.info(f"{model_name} - 最佳得分: {best_score:.4f} ± {cv_scores.std():.4f}")
            
            return result
            
        except Exception as e:
            logger.error(f"{model_name} 训练失败: {e}")
            return None
    
    def train_ensemble_models(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """训练集成模型"""
        logger.info("开始训练多个模型...")
        
        results = {}
        
        # 训练各个模型
        for model_name in self.model_configs.keys():
            result = self.train_single_model(model_name, X, y)
            if result:
                results[model_name] = result
                self.models[model_name] = result['model']
                self.model_scores[model_name] = result['cv_mean']
        
        # 选择最佳模型
        if self.model_scores:
            best_model_name = max(self.model_scores, key=self.model_scores.get)
            self.best_model = self.models[best_model_name]
            logger.info(f"最佳单模型: {best_model_name} (得分: {self.model_scores[best_model_name]:.4f})")
        
        # 创建投票集成模型
        if len(self.models) >= 3:
            # 选择前3个最佳模型
            top_models = sorted(self.model_scores.items(), key=lambda x: x[1], reverse=True)[:3]
            
            voting_estimators = [(name, self.models[name]) for name, _ in top_models]
            
            voting_classifier = VotingClassifier(
                estimators=voting_estimators,
                voting='soft'  # 使用概率投票
            )
            
            # 训练投票分类器
            voting_classifier.fit(X, y)
            
            # 评估投票分类器
            tscv = TimeSeriesSplit(n_splits=5)
            voting_scores = cross_val_score(voting_classifier, X, y, cv=tscv, scoring='accuracy')
            
            self.models['voting_ensemble'] = voting_classifier
            self.model_scores['voting_ensemble'] = voting_scores.mean()
            
            logger.info(f"投票集成模型得分: {voting_scores.mean():.4f} ± {voting_scores.std():.4f}")
            
            # 如果集成模型更好，则使用集成模型
            if voting_scores.mean() > self.model_scores[best_model_name]:
                self.best_model = voting_classifier
                logger.info("选择投票集成模型作为最佳模型")
        
        self.is_trained = True
        return results
    
    def evaluate_model(self, model, X: pd.DataFrame, y: pd.Series) -> Dict:
        """评估模型性能"""
        logger.info("评估模型性能...")
        
        # 时间序列分割
        tscv = TimeSeriesSplit(n_splits=5)
        
        all_predictions = []
        all_true_labels = []
        all_probabilities = []
        
        for train_idx, test_idx in tscv.split(X):
            X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
            y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
            
            # 训练
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            y_prob = model.predict_proba(X_test)
            
            all_predictions.extend(y_pred)
            all_true_labels.extend(y_test)
            all_probabilities.extend(y_prob)
        
        # 计算指标
        accuracy = accuracy_score(all_true_labels, all_predictions)
        precision = precision_score(all_true_labels, all_predictions, average='weighted')
        recall = recall_score(all_true_labels, all_predictions, average='weighted')
        f1 = f1_score(all_true_labels, all_predictions, average='weighted')
        
        # 分类报告
        class_report = classification_report(all_true_labels, all_predictions, output_dict=True)
        
        evaluation = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'classification_report': class_report,
            'predictions': all_predictions,
            'true_labels': all_true_labels,
            'probabilities': all_probabilities
        }
        
        logger.info(f"模型评估结果:")
        logger.info(f"  准确率: {accuracy:.4f}")
        logger.info(f"  精确率: {precision:.4f}")
        logger.info(f"  召回率: {recall:.4f}")
        logger.info(f"  F1得分: {f1:.4f}")
        
        return evaluation
    
    def extract_feature_importance(self, model, feature_names: List[str]) -> Dict[str, float]:
        """提取特征重要性"""
        importance_dict = {}
        
        try:
            if hasattr(model, 'feature_importances_'):
                # 树模型
                importance_dict = dict(zip(feature_names, model.feature_importances_))
            elif hasattr(model, 'coef_'):
                # 线性模型
                importance_dict = dict(zip(feature_names, np.abs(model.coef_[0])))
            elif hasattr(model, 'estimators_'):
                # 集成模型
                if hasattr(model.estimators_[0], 'feature_importances_'):
                    avg_importance = np.mean([est.feature_importances_ for est in model.estimators_], axis=0)
                    importance_dict = dict(zip(feature_names, avg_importance))
            
            # 归一化重要性
            if importance_dict:
                total_importance = sum(importance_dict.values())
                if total_importance > 0:
                    importance_dict = {k: v/total_importance for k, v in importance_dict.items()}
        
        except Exception as e:
            logger.warning(f"无法提取特征重要性: {e}")
        
        return importance_dict
    
    def save_model(self, filepath: str):
        """保存模型"""
        if not self.is_trained:
            raise ValueError("模型未训练")
        
        model_data = {
            'best_model': self.best_model,
            'models': self.models,
            'model_scores': self.model_scores,
            'feature_importance': self.feature_importance,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder
        }
        
        joblib.dump(model_data, filepath)
        logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        model_data = joblib.load(filepath)
        
        self.best_model = model_data['best_model']
        self.models = model_data['models']
        self.model_scores = model_data['model_scores']
        self.feature_importance = model_data.get('feature_importance', {})
        self.scaler = model_data.get('scaler', StandardScaler())
        self.label_encoder = model_data.get('label_encoder', LabelEncoder())
        self.is_trained = True
        
        logger.info(f"模型已从 {filepath} 加载")
    
    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """预测"""
        if not self.is_trained:
            raise ValueError("模型未训练")
        
        predictions = self.best_model.predict(X)
        probabilities = self.best_model.predict_proba(X)
        
        return predictions, probabilities
    
    def get_training_report(self) -> Dict:
        """获取训练报告"""
        return {
            'is_trained': self.is_trained,
            'models_trained': list(self.models.keys()),
            'model_scores': self.model_scores,
            'best_model_type': type(self.best_model).__name__,
            'feature_importance': self.feature_importance
        }

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)

    # 创建模拟的OHLC数据
    n_points = 2000
    dates = pd.date_range('2024-01-01', periods=n_points, freq='5T')

    # 生成价格序列（带趋势和噪音）
    price = 100.0
    prices = []
    volumes = []

    for i in range(n_points):
        # 添加趋势和随机性
        trend = 0.0001 * np.sin(i / 100)  # 长期趋势
        noise = np.random.normal(0, 0.002)  # 随机噪音
        price *= (1 + trend + noise)
        prices.append(price)

        # 成交量
        volume = np.random.lognormal(10, 1)
        volumes.append(volume)

    # 创建DataFrame
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = [prices[max(0, i-1)] for i in range(n_points)]
    data['high'] = [p * (1 + abs(np.random.normal(0, 0.003))) for p in prices]
    data['low'] = [p * (1 - abs(np.random.normal(0, 0.003))) for p in prices]
    data['volume'] = volumes

    # 确保OHLC数据合理性
    data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
    data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))

    return data

if __name__ == "__main__":
    logger.info("测试增强版AI模型训练...")

    # 创建测试数据
    data = create_test_data()
    print(f"✅ 创建测试数据: {len(data)} 条记录")

    # 创建AI模型训练器
    ai_model = EnhancedAIModel()

    # 创建目标标签
    target = ai_model.create_target_labels(data)

    # 创建简单特征（用于测试）
    features = pd.DataFrame(index=data.index)
    features['returns_1'] = data['close'].pct_change()
    features['returns_5'] = data['close'].pct_change(5)
    features['ma_10'] = data['close'].rolling(10).mean()
    features['ma_20'] = data['close'].rolling(20).mean()
    features['vol_10'] = data['close'].pct_change().rolling(10).std()
    features['rsi'] = 50  # 简化的RSI

    # 准备训练数据
    X, y = ai_model.prepare_training_data(features, target)

    if len(X) > 100:  # 确保有足够的数据
        print("🤖 开始模型训练...")

        # 训练模型（只训练快速的模型进行测试）
        ai_model.model_configs = {
            'random_forest': ai_model.model_configs['random_forest'],
            'logistic_regression': ai_model.model_configs['logistic_regression']
        }

        results = ai_model.train_ensemble_models(X, y)

        # 获取训练报告
        report = ai_model.get_training_report()

        print("🎉 AI模型训练测试完成！")
        print(f"📊 训练的模型: {report['models_trained']}")
        print(f"🏆 最佳模型: {report['best_model_type']}")
        print(f"📈 模型得分: {report['model_scores']}")

    else:
        print("⚠️ 数据不足，跳过训练测试")

    print("\n📊 主要特性:")
    print("  ✅ 多模型训练和比较")
    print("  ✅ 时间序列交叉验证")
    print("  ✅ 超参数自动优化")
    print("  ✅ 模型集成和投票")
    print("  ✅ 特征重要性分析")
    print("  ✅ 完整的性能评估")
