#!/usr/bin/env python3
"""
Test script to verify realistic position size calculation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader

def test_position_size_realistic():
    """Test realistic position size calculation"""
    print("🧪 Testing Realistic Position Size Calculation")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 User Expectation vs Current System:")
    print("🎯 User's typical position: 0.016 BTC")
    print("❌ Current system: 0.000140 BTC (100x smaller!)")
    print("💰 User's nominal value: ~$1,632")
    print("❌ Current nominal value: ~$14.28")
    
    # Test position calculation with new logic
    mock_signal = {
        'direction': 'LONG',
        'confidence': 0.4,
        'trading_style': 'right_side',
        'signal_count': 2,
        'reasons': ['Test signal']
    }
    
    mock_market_data = {
        'current_price': 102000.0,
        'volatility': 0.005,
        'volume_ratio': 1.0
    }
    
    print(f"\n🔧 Testing New Position Calculation:")
    print(f"   💰 Available Balance: ${trader.account['balance']:.2f}")
    print(f"   ⚡ Leverage: {trader.leverage}x")
    print(f"   📊 Current Price: ${mock_market_data['current_price']:,.2f}")
    
    # Calculate position size using the new method
    position_size = trader.calculate_dynamic_position_size(mock_signal, mock_market_data)
    
    # Calculate related values
    nominal_value = position_size * mock_market_data['current_price']
    margin_required = nominal_value / trader.leverage
    margin_usage = (margin_required / trader.account['balance']) * 100
    
    print(f"\n✅ New Position Size Results:")
    print(f"   📊 BTC Position Size: {position_size:.6f} BTC")
    print(f"   💎 Nominal Value: ${nominal_value:,.2f}")
    print(f"   💰 Margin Required: ${margin_required:.2f}")
    print(f"   📊 Margin Usage: {margin_usage:.1f}%")
    
    # Compare with user expectation
    user_target = 0.016
    achievement_ratio = position_size / user_target
    
    print(f"\n📊 Comparison with User Expectation:")
    print(f"   🎯 User Target: {user_target:.6f} BTC")
    print(f"   📊 Actual Position: {position_size:.6f} BTC")
    print(f"   📈 Achievement Ratio: {achievement_ratio:.1%}")
    
    if achievement_ratio >= 0.8:  # At least 80% of target
        print(f"   ✅ EXCELLENT: Very close to user expectation!")
    elif achievement_ratio >= 0.5:  # At least 50% of target
        print(f"   ✅ GOOD: Reasonable approximation")
    elif achievement_ratio >= 0.2:  # At least 20% of target
        print(f"   ⚠️ MODERATE: Better but still conservative")
    else:
        print(f"   ❌ STILL TOO SMALL: Needs further adjustment")
    
    # Risk analysis
    print(f"\n⚖️ Risk Analysis:")
    if margin_usage > 90:
        print(f"   ⚠️ HIGH RISK: {margin_usage:.1f}% margin usage")
        print(f"   💡 Consider reducing position size")
    elif margin_usage > 70:
        print(f"   ⚡ AGGRESSIVE: {margin_usage:.1f}% margin usage")
        print(f"   ✅ Suitable for experienced traders")
    elif margin_usage > 50:
        print(f"   ✅ BALANCED: {margin_usage:.1f}% margin usage")
        print(f"   ✅ Good risk/reward balance")
    else:
        print(f"   💚 CONSERVATIVE: {margin_usage:.1f}% margin usage")
        print(f"   ✅ Safe for beginners")
    
    # Calculate potential P&L scenarios
    print(f"\n💰 Potential P&L Scenarios (125x leverage):")
    price_moves = [0.001, 0.002, 0.005, 0.01]  # 0.1%, 0.2%, 0.5%, 1.0%
    
    for move in price_moves:
        price_change = mock_market_data['current_price'] * move
        pnl = position_size * price_change * trader.leverage
        roi = (pnl / margin_required) * 100 if margin_required > 0 else 0
        account_impact = (pnl / trader.account['balance']) * 100
        
        print(f"   📈 {move:.1%} price move:")
        print(f"      💰 P&L: ${pnl:+.2f}")
        print(f"      📊 ROI: {roi:+.1f}%")
        print(f"      🏦 Account Impact: {account_impact:+.1f}%")
    
    # Compare old vs new
    old_position = 0.000140
    old_nominal = old_position * mock_market_data['current_price']
    improvement_factor = position_size / old_position
    
    print(f"\n🚀 Improvement Analysis:")
    print(f"   ❌ Old Position: {old_position:.6f} BTC (${old_nominal:.2f})")
    print(f"   ✅ New Position: {position_size:.6f} BTC (${nominal_value:,.2f})")
    print(f"   📈 Improvement: {improvement_factor:.1f}x larger")
    print(f"   💰 Value Increase: ${nominal_value - old_nominal:+,.2f}")
    
    print(f"\n" + "="*60)
    print("🎉 Position Size Realistic Test Complete!")
    
    if achievement_ratio >= 0.5:
        print("✅ SUCCESS: Position size now much more realistic!")
        print("✅ Closer to user expectations")
        print("✅ Better utilization of 125x leverage")
        print("✅ More meaningful profit potential")
    else:
        print("⚠️ PARTIAL: Improvement made but still conservative")
        print("💡 May need further adjustment for optimal results")
    
    print(f"\n💡 Key Benefits:")
    print(f"   🎯 Target-based calculation: Aims for user's typical 0.016 BTC")
    print(f"   ⚡ Higher margin usage: 30% → 80% (more aggressive)")
    print(f"   💰 Larger positions: Better profit potential")
    print(f"   🚀 Improved leverage utilization: Makes 125x meaningful")

if __name__ == "__main__":
    test_position_size_realistic()
