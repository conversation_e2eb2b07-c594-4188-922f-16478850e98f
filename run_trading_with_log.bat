@echo off
chcp 65001 >nul
echo 🚀 智能交易系统 - 自动日志保存
echo ================================

:: 创建logs目录
if not exist logs mkdir logs

:: 生成时间戳
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set mydate=%%c%%a%%b
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
set timestamp=%mydate%_%mytime%

:: 设置日志文件名
set logfile=logs\trading_log_%timestamp%.log

echo 📝 日志将保存到: %logfile%
echo ⏰ 开始时间: %date% %time%
echo ================================

:: 运行交易系统并保存日志
python smart_auto_trading.py 2>&1 | tee %logfile%

echo.
echo 📝 日志已保存到: %logfile%
pause
