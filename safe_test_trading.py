#!/usr/bin/env python3
"""
Safe testing script with emergency protections
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader

def safe_test_trading():
    """Safe testing with emergency protections"""
    print("🛡️ Safe Trading Test with Emergency Protections")
    print("=" * 60)
    
    # Create a fresh trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n🔧 Emergency Protections Applied:")
    print("✅ Position size reduced to 0.001 BTC (was 0.016)")
    print("✅ Maximum loss protection: $10 per trade")
    print("✅ Enhanced stop-loss debugging")
    print("✅ Balance validation before trades")
    
    # Test position calculation with new safety measures
    mock_signal = {
        'direction': 'LONG',
        'confidence': 0.4,
        'trading_style': 'right_side',
        'signal_count': 2,
        'reasons': ['Safe test']
    }
    
    mock_market_data = {
        'current_price': 102600.0,
        'volatility': 0.005,
        'volume_ratio': 1.0
    }
    
    print(f"\n🧪 Testing Safe Position Calculation:")
    position_size = trader.calculate_dynamic_position_size(mock_signal, mock_market_data)
    
    if position_size > 0:
        nominal_value = position_size * mock_market_data['current_price']
        margin_required = nominal_value / trader.leverage
        max_loss_2_percent = margin_required * 0.02  # 2% of margin
        
        print(f"   📊 Safe Position Size: {position_size:.6f} BTC")
        print(f"   💎 Nominal Value: ${nominal_value:.2f}")
        print(f"   💰 Margin Required: ${margin_required:.2f}")
        print(f"   🛑 Max Loss (2%): ${max_loss_2_percent:.2f}")
        print(f"   📊 Margin Usage: {(margin_required / trader.account['balance']) * 100:.1f}%")
        
        if margin_required < 5:  # Less than $5 margin
            print(f"   ✅ SAFE: Margin requirement under $5")
        else:
            print(f"   ⚠️ WARNING: Margin requirement over $5")
        
        if max_loss_2_percent < 1:  # Less than $1 max loss
            print(f"   ✅ SAFE: Maximum loss under $1")
        else:
            print(f"   ⚠️ WARNING: Maximum loss over $1")
    else:
        print(f"   ❌ No position calculated (balance protection active)")
    
    # Test stop-loss calculation
    print(f"\n🛑 Stop-Loss Calculation Test:")
    
    # Simulate different ROI scenarios
    test_scenarios = [
        {'roi': 1.5, 'should_trigger': False, 'action': 'Continue'},
        {'roi': -1.0, 'should_trigger': False, 'action': 'Continue'},
        {'roi': -2.1, 'should_trigger': True, 'action': 'Stop-Loss'},
        {'roi': 2.1, 'should_trigger': True, 'action': 'Take-Profit'},
        {'roi': -5.0, 'should_trigger': True, 'action': 'Emergency Stop'}
    ]
    
    for scenario in test_scenarios:
        roi = scenario['roi']
        
        # Calculate stop-loss thresholds
        fee_cost_roi = 1.0
        safety_margin = 0.8
        slippage_buffer = 0.2
        base_profit_roi = fee_cost_roi + safety_margin + slippage_buffer
        target_profit_roi = base_profit_roi  # 2.0%
        max_loss_roi = -target_profit_roi * 0.9  # -1.8%
        
        # Check conditions
        emergency_trigger = roi < -3.0  # Emergency if ROI < -3%
        stop_loss_trigger = roi <= max_loss_roi
        take_profit_trigger = roi >= target_profit_roi
        
        print(f"\n   📊 ROI: {roi:+.1f}%")
        print(f"      🚀 Take-Profit: {target_profit_roi:+.1f}% ({'✅' if take_profit_trigger else '❌'})")
        print(f"      🛑 Stop-Loss: {max_loss_roi:+.1f}% ({'✅' if stop_loss_trigger else '❌'})")
        print(f"      🚨 Emergency: -3.0% ({'✅' if emergency_trigger else '❌'})")
        
        if emergency_trigger:
            action = "🚨 Emergency Stop"
        elif take_profit_trigger:
            action = "🚀 Take-Profit"
        elif stop_loss_trigger:
            action = "🛑 Stop-Loss"
        else:
            action = "📊 Continue"
        
        expected = scenario['should_trigger']
        actual = emergency_trigger or take_profit_trigger or stop_loss_trigger
        result = "✅" if expected == actual else "❌"
        
        print(f"      🎯 Action: {action}")
        print(f"      📋 Expected: {scenario['action']} {result}")
    
    print(f"\n🔧 Emergency Protection Features:")
    print(f"   1. 🛡️ Maximum loss per trade: $10")
    print(f"   2. 📊 Tiny position size: 0.001 BTC")
    print(f"   3. 🛑 Enhanced stop-loss: -1.8% ROI")
    print(f"   4. 🚨 Emergency stop: -3.0% ROI")
    print(f"   5. 💰 Balance validation: Before each trade")
    
    print(f"\n📊 Risk Assessment:")
    if position_size > 0:
        worst_case_loss = margin_required * 0.05  # 5% of margin (extreme case)
        account_impact = (worst_case_loss / trader.account['balance']) * 100
        
        print(f"   💰 Worst Case Loss: ${worst_case_loss:.2f}")
        print(f"   📊 Account Impact: {account_impact:.1f}%")
        
        if account_impact < 10:
            print(f"   ✅ SAFE: Account impact under 10%")
        else:
            print(f"   ⚠️ WARNING: Account impact over 10%")
    
    print(f"\n" + "="*60)
    print("🛡️ Safe Trading Test Complete!")
    print("✅ Emergency protections are active")
    print("✅ Position sizes are minimized")
    print("✅ Stop-loss logic is enhanced")
    print("✅ Maximum loss is limited to $10")
    
    print(f"\n💡 Next Steps:")
    print(f"   1. Test with these safe settings")
    print(f"   2. Monitor actual vs expected losses")
    print(f"   3. Gradually increase position size if stable")
    print(f"   4. Keep emergency protections active")
    
    return trader

if __name__ == "__main__":
    safe_test_trading()
