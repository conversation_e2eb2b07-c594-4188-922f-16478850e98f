#!/usr/bin/env python3
"""
真实市场数据AI交易系统
连接真实市场数据，显示详细价格信息
"""

import pandas as pd
import numpy as np
import time
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings
import requests

# 禁用警告
warnings.filterwarnings('ignore')
logging.getLogger().setLevel(logging.ERROR)

class RealMarketTrader:
    """
    真实市场数据AI交易系统
    
    特点：
    - 连接币安API获取真实BTC价格
    - 显示详细价格信息（开仓价、目标价、止损价）
    - 基于真实技术指标分析
    - 透明的交易决策过程
    """
    
    def __init__(self, initial_balance: float = 50.0, leverage: float = 10.0):
        self.initial_balance = initial_balance
        self.leverage = leverage
        
        # 账户状态
        self.account = {
            'balance': initial_balance,
            'equity': initial_balance,
            'unrealized_pnl': 0.0,
            'margin_used': 0.0
        }
        
        # 持仓状态
        self.position = {
            'side': None,
            'size': 0.0,
            'entry_price': 0.0,
            'entry_time': None,
            'unrealized_pnl': 0.0,
            'stop_loss_price': 0.0,
            'take_profit_price': 0.0
        }
        
        # 交易状态
        self.last_trade_time = None
        self.trade_history = []
        self.price_history = []
        self.consecutive_wait_cycles = 0
        
        # 交易参数
        self.trading_params = {
            'base_confidence_threshold': 0.60,
            'forced_trade_threshold': 0.45,
            'max_wait_hours': 2,
            'min_volatility': 0.005,
            'position_size_ratio': 0.4,
            'stop_loss_pct': 0.025,    # 2.5%止损
            'take_profit_pct': 0.08,   # 8%止盈
            'max_hold_hours': 6
        }
        
        print(f"🎯 真实市场AI交易系统已启动")
        print(f"💰 初始资金: ${initial_balance} | ⚡ 杠杆: {leverage}x")
        print(f"📡 连接币安API获取真实BTC价格...")
    
    def get_real_btc_price(self) -> Dict:
        """获取真实的BTC价格数据"""
        try:
            # 币安API获取BTC价格
            url = "https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT"
            response = requests.get(url, timeout=5)
            data = response.json()
            
            current_price = float(data['lastPrice'])
            price_change_24h = float(data['priceChangePercent']) / 100
            volume = float(data['volume'])
            high_24h = float(data['highPrice'])
            low_24h = float(data['lowPrice'])
            
            # 计算波动率
            volatility = (high_24h - low_24h) / current_price
            
            market_data = {
                'timestamp': datetime.now(),
                'current_price': current_price,
                'price_change_24h': price_change_24h,
                'volatility': volatility,
                'volume': volume,
                'high_24h': high_24h,
                'low_24h': low_24h,
                'source': 'binance_api'
            }
            
            # 存储价格历史
            self.price_history.append(market_data)
            if len(self.price_history) > 100:
                self.price_history.pop(0)
            
            return market_data
            
        except Exception as e:
            print(f"⚠️ 获取真实价格失败: {e}")
            # 备用：使用最后已知价格
            if self.price_history:
                last_price = self.price_history[-1]['current_price']
                return {
                    'timestamp': datetime.now(),
                    'current_price': last_price,
                    'price_change_24h': 0,
                    'volatility': 0.02,
                    'volume': 0,
                    'high_24h': last_price * 1.02,
                    'low_24h': last_price * 0.98,
                    'source': 'fallback'
                }
            else:
                # 默认价格
                return {
                    'timestamp': datetime.now(),
                    'current_price': 103925.5,  # 您提到的真实价格
                    'price_change_24h': 0,
                    'volatility': 0.02,
                    'volume': 0,
                    'high_24h': 105000,
                    'low_24h': 102000,
                    'source': 'default'
                }
    
    def calculate_technical_indicators(self) -> Dict:
        """计算真实的技术指标"""
        if len(self.price_history) < 14:
            return {
                'rsi': 50,
                'bb_position': 0.5,
                'macd_signal': 0,
                'trend': 'neutral'
            }
        
        # 获取最近价格
        prices = [p['current_price'] for p in self.price_history[-20:]]
        
        # 简化RSI计算
        price_changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [max(0, change) for change in price_changes]
        losses = [max(0, -change) for change in price_changes]
        
        avg_gain = sum(gains[-14:]) / 14 if len(gains) >= 14 else sum(gains) / len(gains)
        avg_loss = sum(losses[-14:]) / 14 if len(losses) >= 14 else sum(losses) / len(losses)
        
        if avg_loss == 0:
            rsi = 100
        else:
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
        
        # 布林带位置
        recent_prices = prices[-20:]
        sma = sum(recent_prices) / len(recent_prices)
        std = np.std(recent_prices)
        upper_band = sma + (2 * std)
        lower_band = sma - (2 * std)
        
        current_price = prices[-1]
        if upper_band == lower_band:
            bb_position = 0.5
        else:
            bb_position = (current_price - lower_band) / (upper_band - lower_band)
        
        # 趋势判断
        short_ma = sum(prices[-5:]) / 5
        long_ma = sum(prices[-10:]) / 10 if len(prices) >= 10 else sum(prices) / len(prices)
        
        if short_ma > long_ma * 1.002:
            trend = 'bullish'
        elif short_ma < long_ma * 0.998:
            trend = 'bearish'
        else:
            trend = 'neutral'
        
        return {
            'rsi': rsi,
            'bb_position': bb_position,
            'macd_signal': (short_ma - long_ma) / current_price,
            'trend': trend,
            'sma_5': short_ma,
            'sma_10': long_ma
        }
    
    def generate_trading_signals(self, market_data: Dict, indicators: Dict) -> Dict:
        """基于真实数据生成交易信号"""
        signals = []
        reasons = []
        
        current_price = market_data['current_price']
        rsi = indicators['rsi']
        bb_position = indicators['bb_position']
        trend = indicators['trend']
        price_change_24h = market_data['price_change_24h']
        volatility = market_data['volatility']
        
        # RSI信号
        if rsi < 30:
            signals.append(('LONG', 0.8))
            reasons.append(f"RSI超卖({rsi:.1f})")
        elif rsi > 70:
            signals.append(('SHORT', 0.8))
            reasons.append(f"RSI超买({rsi:.1f})")
        elif rsi < 40:
            signals.append(('LONG', 0.6))
            reasons.append(f"RSI偏低({rsi:.1f})")
        elif rsi > 60:
            signals.append(('SHORT', 0.6))
            reasons.append(f"RSI偏高({rsi:.1f})")
        
        # 布林带信号
        if bb_position < 0.2:
            signals.append(('LONG', 0.7))
            reasons.append("布林带下轨支撑")
        elif bb_position > 0.8:
            signals.append(('SHORT', 0.7))
            reasons.append("布林带上轨阻力")
        
        # 趋势信号
        if trend == 'bullish':
            signals.append(('LONG', 0.6))
            reasons.append("短期上升趋势")
        elif trend == 'bearish':
            signals.append(('SHORT', 0.6))
            reasons.append("短期下降趋势")
        
        # 24小时变动信号
        if price_change_24h > 0.03:  # 3%以上涨幅
            signals.append(('LONG', 0.65))
            reasons.append(f"24h强势上涨({price_change_24h:.1%})")
        elif price_change_24h < -0.03:  # 3%以上跌幅
            signals.append(('SHORT', 0.65))
            reasons.append(f"24h大幅下跌({price_change_24h:.1%})")
        
        # 波动率信号
        if volatility > 0.05:  # 高波动率
            if price_change_24h > 0:
                signals.append(('LONG', 0.6))
                reasons.append(f"高波动率上涨({volatility:.1%})")
            else:
                signals.append(('SHORT', 0.6))
                reasons.append(f"高波动率下跌({volatility:.1%})")
        
        if not signals:
            return {
                'direction': 'WAIT',
                'confidence': 0.3,
                'reasons': ['市场信号不明确'],
                'details': indicators
            }
        
        # 计算最终信号
        long_strength = sum(s[1] for s in signals if s[0] == 'LONG')
        short_strength = sum(s[1] for s in signals if s[0] == 'SHORT')
        
        if long_strength > short_strength:
            return {
                'direction': 'LONG',
                'confidence': min(0.9, long_strength / len(signals)),
                'reasons': [r for i, r in enumerate(reasons) if signals[i][0] == 'LONG'],
                'details': indicators
            }
        elif short_strength > long_strength:
            return {
                'direction': 'SHORT',
                'confidence': min(0.9, short_strength / len(signals)),
                'reasons': [r for i, r in enumerate(reasons) if signals[i][0] == 'SHORT'],
                'details': indicators
            }
        else:
            return {
                'direction': 'WAIT',
                'confidence': 0.4,
                'reasons': ['多空信号平衡'],
                'details': indicators
            }
    
    def calculate_target_prices(self, entry_price: float, direction: str) -> Dict:
        """计算目标价格和止损价格"""
        if direction == 'LONG':
            stop_loss_price = entry_price * (1 - self.trading_params['stop_loss_pct'])
            take_profit_price = entry_price * (1 + self.trading_params['take_profit_pct'])
        else:  # SHORT
            stop_loss_price = entry_price * (1 + self.trading_params['stop_loss_pct'])
            take_profit_price = entry_price * (1 - self.trading_params['take_profit_pct'])
        
        return {
            'stop_loss_price': stop_loss_price,
            'take_profit_price': take_profit_price
        }
    
    def should_force_trade(self) -> bool:
        """检查是否需要强制交易"""
        if self.last_trade_time is None:
            return self.consecutive_wait_cycles > 15  # 约45分钟
        
        hours_since_last_trade = (datetime.now() - self.last_trade_time).total_seconds() / 3600
        return hours_since_last_trade >= self.trading_params['max_wait_hours']
    
    def calculate_position_size(self, confidence: float, current_price: float) -> float:
        """计算仓位大小"""
        base_ratio = self.trading_params['position_size_ratio']
        confidence_multiplier = 0.7 + (confidence - 0.6) * 2
        
        final_ratio = base_ratio * confidence_multiplier
        final_ratio = max(0.2, min(0.6, final_ratio))
        
        position_value = self.account['balance'] * final_ratio
        btc_size = position_value / current_price / self.leverage
        
        return btc_size

    def open_position(self, direction: str, size: float, price: float, signal: Dict) -> bool:
        """开仓并设置目标价格"""
        if self.position['side'] is not None:
            return False

        position_value = size * price * self.leverage
        margin_required = position_value / self.leverage
        trading_fee = position_value * 0.0004

        if margin_required + trading_fee > self.account['balance']:
            return False

        # 计算目标价格
        targets = self.calculate_target_prices(price, direction)

        # 开仓
        self.position.update({
            'side': direction,
            'size': size,
            'entry_price': price,
            'entry_time': datetime.now(),
            'unrealized_pnl': 0.0,
            'stop_loss_price': targets['stop_loss_price'],
            'take_profit_price': targets['take_profit_price']
        })

        # 更新账户
        self.account['balance'] -= trading_fee
        self.account['margin_used'] = margin_required

        # 记录交易
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'OPEN',
            'side': direction,
            'size': size,
            'price': price,
            'stop_loss_price': targets['stop_loss_price'],
            'take_profit_price': targets['take_profit_price'],
            'fee': trading_fee,
            'signal': signal,
            'balance_after': self.account['balance']
        }

        self.trade_history.append(trade_record)
        self.last_trade_time = datetime.now()
        self.consecutive_wait_cycles = 0

        return True

    def close_position(self, price: float, reason: str) -> bool:
        """平仓"""
        if self.position['side'] is None:
            return False

        if self.position['side'] == 'LONG':
            price_diff = price - self.position['entry_price']
        else:
            price_diff = self.position['entry_price'] - price

        pnl = self.position['size'] * price_diff * self.leverage
        position_value = self.position['size'] * price * self.leverage
        trading_fee = position_value * 0.0004
        net_pnl = pnl - trading_fee

        # 更新账户
        self.account['balance'] += net_pnl + self.account['margin_used']
        self.account['margin_used'] = 0
        self.account['equity'] = self.account['balance']
        self.account['unrealized_pnl'] = 0

        # 记录交易
        hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'CLOSE',
            'side': self.position['side'],
            'size': self.position['size'],
            'entry_price': self.position['entry_price'],
            'exit_price': price,
            'pnl': pnl,
            'net_pnl': net_pnl,
            'fee': trading_fee,
            'reason': reason,
            'hold_time': hold_time,
            'balance_after': self.account['balance']
        }

        self.trade_history.append(trade_record)
        self.last_trade_time = datetime.now()

        # 清除持仓
        self.position = {
            'side': None,
            'size': 0.0,
            'entry_price': 0.0,
            'entry_time': None,
            'unrealized_pnl': 0.0,
            'stop_loss_price': 0.0,
            'take_profit_price': 0.0
        }

        return True

    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.position['side'] is None:
            self.account['unrealized_pnl'] = 0
            self.account['equity'] = self.account['balance']
            return

        if self.position['side'] == 'LONG':
            price_diff = current_price - self.position['entry_price']
        else:
            price_diff = self.position['entry_price'] - current_price

        unrealized_pnl = self.position['size'] * price_diff * self.leverage
        self.position['unrealized_pnl'] = unrealized_pnl
        self.account['unrealized_pnl'] = unrealized_pnl
        self.account['equity'] = self.account['balance'] + unrealized_pnl

    def check_exit_conditions(self, current_price: float) -> bool:
        """检查平仓条件"""
        if self.position['side'] is None:
            return False

        # 止损检查
        if self.position['side'] == 'LONG':
            if current_price <= self.position['stop_loss_price']:
                self.close_position(current_price, '止损')
                return True
            if current_price >= self.position['take_profit_price']:
                self.close_position(current_price, '止盈')
                return True
        else:  # SHORT
            if current_price >= self.position['stop_loss_price']:
                self.close_position(current_price, '止损')
                return True
            if current_price <= self.position['take_profit_price']:
                self.close_position(current_price, '止盈')
                return True

        # 超时检查
        if self.position['entry_time']:
            hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            if hold_hours >= self.trading_params['max_hold_hours']:
                self.close_position(current_price, '超时平仓')
                return True

        return False

    def print_detailed_status(self, cycle_count: int, market_data: Dict, signal: Dict, action: str):
        """打印详细的交易状态"""
        current_time = datetime.now().strftime('%H:%M:%S')
        current_price = market_data['current_price']

        print(f"\n" + "="*100)
        print(f"🎯 真实市场AI交易 | 第{cycle_count}轮 | {current_time}")
        print("="*100)

        # 市场数据
        print(f"📊 市场数据 (来源: {market_data['source']}):")
        print(f"   💰 当前BTC价格: ${current_price:,.2f}")
        print(f"   📈 24小时变动: {market_data['price_change_24h']:+.2%}")
        print(f"   📊 24小时波动率: {market_data['volatility']:.2%}")
        print(f"   🔝 24小时最高: ${market_data['high_24h']:,.2f}")
        print(f"   🔻 24小时最低: ${market_data['low_24h']:,.2f}")

        # 技术指标
        if 'details' in signal:
            indicators = signal['details']
            print(f"\n🔍 技术指标:")
            print(f"   RSI: {indicators['rsi']:.1f}")
            print(f"   布林带位置: {indicators['bb_position']:.2f}")
            print(f"   趋势: {indicators['trend']}")
            if 'sma_5' in indicators:
                print(f"   5日均线: ${indicators['sma_5']:,.2f}")
            if 'sma_10' in indicators:
                print(f"   10日均线: ${indicators['sma_10']:,.2f}")

        # 账户状态
        total_return = (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        balance_color = "💚" if total_return >= 0 else "❤️"

        print(f"\n💰 账户状态:")
        print(f"   可用余额: ${self.account['balance']:.2f}")
        print(f"   未实现盈亏: ${self.account['unrealized_pnl']:+.2f}")
        print(f"   总权益: {balance_color} ${self.account['equity']:.2f} ({total_return:+.2f}%)")
        print(f"   已用保证金: ${self.account['margin_used']:.2f}")

        # 持仓详情
        if self.position['side'] is not None:
            side_text = "🟢 做多" if self.position['side'] == 'LONG' else "🔴 做空"
            entry_price = self.position['entry_price']
            stop_loss = self.position['stop_loss_price']
            take_profit = self.position['take_profit_price']
            hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 3600

            # 计算距离目标的百分比
            if self.position['side'] == 'LONG':
                to_stop_loss = (current_price - stop_loss) / entry_price * 100
                to_take_profit = (take_profit - current_price) / entry_price * 100
            else:
                to_stop_loss = (stop_loss - current_price) / entry_price * 100
                to_take_profit = (current_price - take_profit) / entry_price * 100

            print(f"\n📊 当前持仓:")
            print(f"   方向: {side_text}")
            print(f"   仓位大小: {self.position['size']:.6f} BTC")
            print(f"   开仓价格: ${entry_price:,.2f}")
            print(f"   当前价格: ${current_price:,.2f}")
            print(f"   止损价格: ${stop_loss:,.2f} (距离{to_stop_loss:+.1f}%)")
            print(f"   止盈价格: ${take_profit:,.2f} (距离{to_take_profit:+.1f}%)")
            print(f"   持仓时间: {hold_time:.1f}小时")
            print(f"   未实现盈亏: ${self.position['unrealized_pnl']:+.2f}")
        else:
            print(f"\n📊 当前持仓: 空仓")

        # 交易信号
        print(f"\n🎯 交易信号:")
        print(f"   信号方向: {signal['direction']}")
        print(f"   信号强度: {signal['confidence']:.1%}")
        print(f"   信号原因: {', '.join(signal['reasons'])}")

        # 当前动作
        if action == '已开仓':
            print(f"\n🚀 交易动作: ✅ 已开仓 {side_text}")
        elif action == '已平仓':
            last_trade = self.trade_history[-1] if self.trade_history else {}
            reason = last_trade.get('reason', '未知')
            pnl = last_trade.get('net_pnl', 0)
            print(f"\n🏁 交易动作: ✅ 已平仓 ({reason}) 盈亏: ${pnl:+.2f}")
        elif action == '等待中':
            print(f"\n⏳ 交易动作: 等待交易机会 (第{self.consecutive_wait_cycles}轮)")
        elif action == '监控中':
            print(f"\n👁️ 交易动作: 监控持仓中")

        # 交易统计
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']
        if closed_trades:
            total_pnl = sum(t['net_pnl'] for t in closed_trades)
            win_rate = len([t for t in closed_trades if t['net_pnl'] > 0]) / len(closed_trades)
            print(f"\n📈 交易统计:")
            print(f"   完成交易: {len(closed_trades)}笔")
            print(f"   胜率: {win_rate:.1%}")
            print(f"   总盈亏: ${total_pnl:+.2f}")

        print("-" * 100)

    def run_trading_cycle(self) -> Dict:
        """运行交易循环"""
        # 获取真实市场数据
        market_data = self.get_real_btc_price()
        current_price = market_data['current_price']

        # 计算技术指标
        indicators = self.calculate_technical_indicators()

        # 更新未实现盈亏
        self.update_unrealized_pnl(current_price)

        # 检查平仓条件
        if self.check_exit_conditions(current_price):
            return {
                'action': '已平仓',
                'market_data': market_data,
                'indicators': indicators
            }

        # 如果有持仓，监控
        if self.position['side'] is not None:
            return {
                'action': '监控中',
                'market_data': market_data,
                'indicators': indicators
            }

        # 生成交易信号
        signal = self.generate_trading_signals(market_data, indicators)

        # 交易决策
        should_trade = False
        confidence_threshold = self.trading_params['base_confidence_threshold']
        force_trade = self.should_force_trade()

        if force_trade:
            confidence_threshold = self.trading_params['forced_trade_threshold']
            should_trade = signal['confidence'] >= confidence_threshold
        else:
            should_trade = (
                signal['direction'] in ['LONG', 'SHORT'] and
                signal['confidence'] >= confidence_threshold and
                market_data['volatility'] >= self.trading_params['min_volatility']
            )

        if should_trade:
            position_size = self.calculate_position_size(signal['confidence'], current_price)
            success = self.open_position(signal['direction'], position_size, current_price, signal)

            if success:
                return {
                    'action': '已开仓',
                    'market_data': market_data,
                    'signal': signal,
                    'indicators': indicators
                }

        self.consecutive_wait_cycles += 1
        return {
            'action': '等待中',
            'market_data': market_data,
            'signal': signal,
            'indicators': indicators
        }

    def get_statistics(self) -> Dict:
        """获取统计数据"""
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']

        if not closed_trades:
            return {
                'total_trades': 0,
                'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100,
                'current_balance': self.account['equity']
            }

        total_pnl = sum(t['net_pnl'] for t in closed_trades)
        winning_trades = [t for t in closed_trades if t['net_pnl'] > 0]

        return {
            'total_trades': len(closed_trades),
            'winning_trades': len(winning_trades),
            'win_rate': len(winning_trades) / len(closed_trades) if closed_trades else 0,
            'total_pnl': total_pnl,
            'avg_pnl': total_pnl / len(closed_trades) if closed_trades else 0,
            'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100,
            'current_balance': self.account['equity'],
            'avg_hold_time': sum(t['hold_time'] for t in closed_trades) / len(closed_trades) if closed_trades else 0
        }

def run_real_market_trading():
    """运行真实市场数据AI交易系统"""
    print("🎯 真实市场数据AI交易系统")
    print("连接币安API，获取真实BTC价格")
    print("=" * 100)

    trader = RealMarketTrader(initial_balance=50.0, leverage=10.0)

    # 测试API连接
    print(f"\n🔍 测试API连接...")
    test_data = trader.get_real_btc_price()
    print(f"✅ 成功获取真实BTC价格: ${test_data['current_price']:,.2f}")
    print(f"📊 数据来源: {test_data['source']}")

    print(f"\n🔧 系统配置:")
    print(f"✅ 真实市场数据: 币安API")
    print(f"✅ 置信度阈值: 60%")
    print(f"✅ 强制交易: 最多等待2小时")
    print(f"✅ 详细价格显示: 开仓价/目标价/止损价")
    print(f"✅ 真实技术指标: RSI/布林带/趋势")

    try:
        duration = float(input("\n运行时长（小时，默认3）: ") or "3")
        interval = int(input("检查间隔（分钟，默认5）: ") or "5")
    except:
        duration = 3
        interval = 5

    print(f"\n🎯 开始{duration}小时真实市场交易测试...")
    print(f"⏰ 检查间隔: {interval}分钟")
    print(f"📡 将每{interval}分钟获取最新BTC价格")

    start_time = datetime.now()
    end_time = start_time + timedelta(hours=duration)
    cycle_count = 0

    try:
        while datetime.now() < end_time:
            cycle_count += 1

            # 运行交易循环
            result = trader.run_trading_cycle()

            # 显示详细状态
            trader.print_detailed_status(
                cycle_count,
                result['market_data'],
                result.get('signal', {'direction': 'WAIT', 'confidence': 0, 'reasons': []}),
                result['action']
            )

            # 等待下一轮
            remaining_time = (end_time - datetime.now()).total_seconds()
            interval_seconds = interval * 60

            if remaining_time > interval_seconds:
                print(f"\n⏳ 等待{interval}分钟获取下一轮数据...")
                time.sleep(min(interval_seconds, 60))  # 演示最多60秒
            else:
                print(f"\n⏳ 即将完成...")
                time.sleep(max(0, min(remaining_time, 10)))
                break

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断交易")

    # 最终报告
    print(f"\n🏁 真实市场AI交易完成")
    print("=" * 100)

    stats = trader.get_statistics()
    actual_runtime = (datetime.now() - start_time).total_seconds() / 3600

    print(f"⏰ 实际运行: {actual_runtime:.1f} 小时")
    print(f"🔄 总轮数: {cycle_count}")
    print(f"💰 最终余额: ${stats['current_balance']:.2f}")
    print(f"📈 总收益率: {stats['total_return']:+.2f}%")
    print(f"📊 完成交易: {stats['total_trades']}笔")

    if stats['total_trades'] > 0:
        print(f"🎯 交易胜率: {stats['win_rate']:.1%}")
        print(f"💵 平均盈亏: ${stats['avg_pnl']:+.2f}")
        print(f"⏱️ 平均持仓: {stats['avg_hold_time']:.1f}小时")

        if stats['total_return'] > 10:
            print(f"🎉 优秀表现: 超过10%收益")
        elif stats['total_return'] > 5:
            print(f"✅ 良好表现: 超过5%收益")
        elif stats['total_return'] > 0:
            print(f"✅ 盈利: 正收益")
        else:
            print(f"📉 亏损: 需要优化策略")
    else:
        print(f"❌ 无交易: 市场条件不满足交易要求")

    # 显示最后的真实价格
    final_data = trader.get_real_btc_price()
    print(f"\n📊 最终BTC价格: ${final_data['current_price']:,.2f}")
    print(f"📡 数据来源: {final_data['source']}")

    return trader

if __name__ == "__main__":
    print("🎯 真实市场数据AI交易系统")
    print("解决模拟数据问题，连接真实市场")
    print("")

    try:
        trader = run_real_market_trading()
        print(f"\n🎉 真实市场交易测试完成！")

    except KeyboardInterrupt:
        print(f"\n👋 用户退出")
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()
