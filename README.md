# 币安加密货币价格预测系统

这是一个基于机器学习的加密货币价格预测系统，集成了回测功能、在线学习和市场情绪分析。

## 主要功能

### 1. 价格预测
- 支持多种机器学习模型（随机森林、XGBoost、LightGBM等）
- 特征工程（技术指标、时间特征等）
- 模型评估和性能监控
- 模型解释性分析

### 2. 回测系统
- 支持自定义交易策略
- 计算各种性能指标（夏普比率、最大回撤等）
- 交易成本和滑点模拟
- 可视化回测结果

### 3. 在线学习
- 增量模型更新
- 滑动窗口训练
- 性能监控和自动调整
- 模型状态保存和加载

### 4. 市场情绪分析
- Twitter情绪分析
- 新闻情绪分析
- 综合情绪指标
- 情绪趋势可视化

## 安装

1. 克隆仓库：
```bash
git clone https://github.com/yourusername/crypto-price-prediction.git
cd crypto-price-prediction
```

2. 创建虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

## 配置

1. 创建配置文件 `.env`：
```
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_SECRET=your_twitter_access_secret
NEWSAPI_KEY=your_newsapi_key
```

2. 配置模型参数（可选）：
- 修改 `config.py` 中的模型参数
- 调整回测参数
- 设置在线学习参数

## 使用方法

### 1. 训练模型

```python
from model_trainer import ModelTrainer

# 初始化训练器
trainer = ModelTrainer(model_type='ensemble')

# 训练模型
results = trainer.train(X, y)

# 保存模型
trainer.save_model()
```

### 2. 回测策略

```python
from backtester import Backtester

# 初始化回测器
backtester = Backtester(data, initial_capital=10000)

# 运行回测
backtester.apply_strategy(signals)

# 计算性能指标
metrics = backtester.calculate_metrics()

# 生成回测报告
backtester.generate_report()
```

### 3. 在线学习

```python
from online_learner import OnlineLearner

# 初始化在线学习器
learner = OnlineLearner(model, window_size=1000)

# 增量更新
performance = learner.update(X_new, y_new)

# 保存状态
learner.save_state()
```

### 4. 情绪分析

```python
from sentiment_analyzer import SentimentAnalyzer

# 初始化情绪分析器
analyzer = SentimentAnalyzer(
    twitter_api_key='your_key',
    newsapi_key='your_key'
)

# 分析市场情绪
sentiment = analyzer.calculate_combined_sentiment()

# 生成情绪报告
analyzer.plot_sentiment_history()
```

## 项目结构

```
crypto-price-prediction/
├── data/                    # 数据目录
├── models/                  # 保存的模型
├── results/                 # 结果输出
├── src/                    # 源代码
│   ├── model_trainer.py    # 模型训练
│   ├── backtester.py      # 回测系统
│   ├── online_learner.py  # 在线学习
│   ├── sentiment_analyzer.py # 情绪分析
│   └── utils.py           # 工具函数
├── tests/                  # 测试代码
├── .env                    # 环境变量
├── config.py              # 配置文件
├── requirements.txt       # 项目依赖
└── README.md             # 项目文档
```

## 注意事项

1. API 密钥安全：
   - 不要将 API 密钥提交到版本控制系统
   - 使用环境变量或配置文件管理密钥

2. 数据安全：
   - 定期备份训练数据和模型
   - 注意数据的时效性

3. 风险提示：
   - 该系统仅供研究和学习使用
   - 实际交易需要考虑更多风险因素
   - 建议在模拟环境中充分测试

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License 