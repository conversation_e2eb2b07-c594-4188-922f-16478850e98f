#!/usr/bin/env python3
"""
增强风险管理模块 - 动态参数调整和回撤控制
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Tuple, Optional, List

class EnhancedRiskManager:
    """
    增强风险管理器 - 动态调整交易参数
    """
    
    def __init__(self, initial_capital: float = 50.0):
        self.initial_capital = initial_capital
        self.max_drawdown_limit = 0.15  # 15%最大回撤限制
        self.peak_equity = initial_capital
        self.consecutive_losses = 0
        self.max_consecutive_losses = 3
        
        # 动态参数基准
        self.base_stop_loss = 0.025  # 2.5%
        self.base_take_profit = 0.05  # 5%
        self.base_position_size = 0.02  # 2%资金风险
        
        # 市场状态记录
        self.volatility_history = []
        self.performance_history = []
        
    def calculate_dynamic_parameters(self, 
                                   current_equity: float,
                                   market_volatility: float,
                                   recent_performance: Dict) -> Dict:
        """
        根据市场状态和账户表现动态调整参数
        """
        # 1. 回撤控制检查
        drawdown_status = self._check_drawdown_limit(current_equity)
        
        # 2. 波动率调整
        volatility_adjustment = self._calculate_volatility_adjustment(market_volatility)
        
        # 3. 连续亏损调整
        loss_adjustment = self._calculate_loss_adjustment(recent_performance)
        
        # 4. 综合调整
        adjusted_params = self._apply_adjustments(
            volatility_adjustment, 
            loss_adjustment, 
            drawdown_status
        )
        
        return adjusted_params
    
    def _check_drawdown_limit(self, current_equity: float) -> str:
        """检查回撤限制"""
        if current_equity > self.peak_equity:
            self.peak_equity = current_equity
        
        current_drawdown = (self.peak_equity - current_equity) / self.peak_equity
        
        if current_drawdown > self.max_drawdown_limit:
            return "STOP_TRADING"
        elif current_drawdown > self.max_drawdown_limit * 0.8:
            return "REDUCE_RISK"
        elif current_drawdown > self.max_drawdown_limit * 0.6:
            return "CONSERVATIVE"
        else:
            return "NORMAL"
    
    def _calculate_volatility_adjustment(self, volatility: float) -> Dict:
        """根据波动率调整参数"""
        # 记录波动率历史
        self.volatility_history.append(volatility)
        if len(self.volatility_history) > 20:
            self.volatility_history = self.volatility_history[-20:]
        
        avg_volatility = np.mean(self.volatility_history)
        volatility_ratio = volatility / avg_volatility if avg_volatility > 0 else 1.0
        
        # 高波动率 -> 增加止损，减少仓位
        # 低波动率 -> 减少止损，增加仓位
        if volatility > 0.04:  # 高波动
            stop_loss_multiplier = 1.5
            take_profit_multiplier = 1.3
            position_size_multiplier = 0.7
            confidence_threshold = 0.75  # 提高开仓门槛
        elif volatility < 0.02:  # 低波动
            stop_loss_multiplier = 0.8
            take_profit_multiplier = 0.9
            position_size_multiplier = 1.2
            confidence_threshold = 0.6  # 降低开仓门槛
        else:  # 正常波动
            stop_loss_multiplier = 1.0
            take_profit_multiplier = 1.0
            position_size_multiplier = 1.0
            confidence_threshold = 0.65
        
        return {
            'stop_loss_multiplier': stop_loss_multiplier,
            'take_profit_multiplier': take_profit_multiplier,
            'position_size_multiplier': position_size_multiplier,
            'confidence_threshold': confidence_threshold,
            'volatility_level': 'high' if volatility > 0.04 else 'low' if volatility < 0.02 else 'normal'
        }
    
    def _calculate_loss_adjustment(self, recent_performance: Dict) -> Dict:
        """根据近期表现调整参数"""
        recent_trades = recent_performance.get('recent_trades', [])
        
        # 计算连续亏损
        consecutive_losses = 0
        for trade in reversed(recent_trades[-10:]):  # 检查最近10笔交易
            if trade.get('final_pnl', 0) < 0:
                consecutive_losses += 1
            else:
                break
        
        self.consecutive_losses = consecutive_losses
        
        # 连续亏损调整
        if consecutive_losses >= self.max_consecutive_losses:
            # 连续亏损 -> 降低风险
            risk_multiplier = 0.5
            confidence_boost = 0.1  # 提高开仓门槛
            status = "HIGH_RISK"
        elif consecutive_losses >= 2:
            # 小幅连续亏损 -> 谨慎
            risk_multiplier = 0.8
            confidence_boost = 0.05
            status = "CAUTIOUS"
        else:
            # 正常状态
            risk_multiplier = 1.0
            confidence_boost = 0.0
            status = "NORMAL"
        
        return {
            'risk_multiplier': risk_multiplier,
            'confidence_boost': confidence_boost,
            'consecutive_losses': consecutive_losses,
            'status': status
        }
    
    def _apply_adjustments(self, 
                          volatility_adj: Dict, 
                          loss_adj: Dict, 
                          drawdown_status: str) -> Dict:
        """应用所有调整"""
        
        # 基础参数
        stop_loss = self.base_stop_loss
        take_profit = self.base_take_profit
        position_size_risk = self.base_position_size
        confidence_threshold = 0.65
        
        # 应用波动率调整
        stop_loss *= volatility_adj['stop_loss_multiplier']
        take_profit *= volatility_adj['take_profit_multiplier']
        position_size_risk *= volatility_adj['position_size_multiplier']
        confidence_threshold = max(confidence_threshold, volatility_adj['confidence_threshold'])
        
        # 应用亏损调整
        position_size_risk *= loss_adj['risk_multiplier']
        confidence_threshold += loss_adj['confidence_boost']
        
        # 应用回撤调整
        if drawdown_status == "STOP_TRADING":
            return {
                'action': 'STOP_TRADING',
                'reason': f'达到最大回撤限制 ({self.max_drawdown_limit:.1%})',
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'position_size_risk': 0,
                'confidence_threshold': 1.0
            }
        elif drawdown_status == "REDUCE_RISK":
            position_size_risk *= 0.5
            confidence_threshold += 0.1
        elif drawdown_status == "CONSERVATIVE":
            position_size_risk *= 0.8
            confidence_threshold += 0.05
        
        # 确保参数在合理范围内
        stop_loss = max(0.015, min(0.05, stop_loss))  # 1.5% - 5%
        take_profit = max(0.03, min(0.1, take_profit))  # 3% - 10%
        position_size_risk = max(0.005, min(0.03, position_size_risk))  # 0.5% - 3%
        confidence_threshold = max(0.6, min(0.9, confidence_threshold))  # 60% - 90%
        
        return {
            'action': 'CONTINUE',
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'position_size_risk': position_size_risk,
            'confidence_threshold': confidence_threshold,
            'adjustments': {
                'volatility': volatility_adj,
                'performance': loss_adj,
                'drawdown': drawdown_status
            },
            'risk_level': self._calculate_risk_level(drawdown_status, loss_adj['status'], volatility_adj['volatility_level'])
        }
    
    def _calculate_risk_level(self, drawdown_status: str, performance_status: str, volatility_level: str) -> str:
        """计算综合风险等级"""
        risk_score = 0
        
        # 回撤风险
        if drawdown_status == "STOP_TRADING":
            risk_score += 4
        elif drawdown_status == "REDUCE_RISK":
            risk_score += 3
        elif drawdown_status == "CONSERVATIVE":
            risk_score += 2
        
        # 表现风险
        if performance_status == "HIGH_RISK":
            risk_score += 3
        elif performance_status == "CAUTIOUS":
            risk_score += 1
        
        # 波动率风险
        if volatility_level == "high":
            risk_score += 2
        elif volatility_level == "low":
            risk_score -= 1
        
        # 风险等级分类
        if risk_score >= 6:
            return "EXTREME"
        elif risk_score >= 4:
            return "HIGH"
        elif risk_score >= 2:
            return "MEDIUM"
        else:
            return "LOW"
    
    def get_position_size_kelly(self, 
                               win_rate: float, 
                               avg_win: float, 
                               avg_loss: float, 
                               current_capital: float,
                               confidence: float) -> float:
        """
        使用凯利公式计算最优仓位大小
        """
        if win_rate <= 0 or avg_loss >= 0 or avg_win <= 0:
            return self.base_position_size * current_capital
        
        # 凯利公式: f = (bp - q) / b
        # b = avg_win / abs(avg_loss) (赔率)
        # p = win_rate (胜率)
        # q = 1 - p (败率)
        
        b = avg_win / abs(avg_loss)
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # 限制凯利比例在合理范围内
        kelly_fraction = max(0, min(0.25, kelly_fraction))  # 最大25%
        
        # 根据置信度调整
        adjusted_fraction = kelly_fraction * confidence
        
        # 应用保守系数
        conservative_fraction = adjusted_fraction * 0.5  # 使用一半凯利
        
        return conservative_fraction * current_capital
    
    def should_trade(self, signal_confidence: float, adjusted_params: Dict) -> bool:
        """
        判断是否应该交易
        """
        if adjusted_params.get('action') == 'STOP_TRADING':
            return False
        
        required_confidence = adjusted_params.get('confidence_threshold', 0.65)
        
        return signal_confidence >= required_confidence
    
    def get_risk_report(self, current_equity: float, recent_performance: Dict) -> Dict:
        """
        生成风险报告
        """
        current_drawdown = (self.peak_equity - current_equity) / self.peak_equity
        
        return {
            'current_equity': current_equity,
            'peak_equity': self.peak_equity,
            'current_drawdown': current_drawdown,
            'max_drawdown_limit': self.max_drawdown_limit,
            'consecutive_losses': self.consecutive_losses,
            'risk_capacity_remaining': max(0, self.max_drawdown_limit - current_drawdown),
            'trading_status': 'ACTIVE' if current_drawdown < self.max_drawdown_limit else 'SUSPENDED',
            'recommendations': self._generate_recommendations(current_drawdown, recent_performance)
        }
    
    def _generate_recommendations(self, drawdown: float, performance: Dict) -> List[str]:
        """生成风险管理建议"""
        recommendations = []
        
        if drawdown > self.max_drawdown_limit * 0.8:
            recommendations.append("建议暂停交易，等待市场机会")
        elif drawdown > self.max_drawdown_limit * 0.6:
            recommendations.append("建议降低仓位，提高开仓门槛")
        
        if self.consecutive_losses >= 3:
            recommendations.append("连续亏损，建议检查策略有效性")
        
        recent_volatility = np.mean(self.volatility_history[-5:]) if self.volatility_history else 0
        if recent_volatility > 0.05:
            recommendations.append("市场高波动，建议增加止损幅度")
        
        if not recommendations:
            recommendations.append("风险控制良好，可正常交易")
        
        return recommendations

if __name__ == "__main__":
    # 测试增强风险管理
    risk_manager = EnhancedRiskManager(50.0)
    
    # 模拟测试
    test_params = risk_manager.calculate_dynamic_parameters(
        current_equity=45.0,
        market_volatility=0.045,
        recent_performance={
            'recent_trades': [
                {'final_pnl': -1.2},
                {'final_pnl': -0.8},
                {'final_pnl': 2.1}
            ]
        }
    )
    
    print("增强风险管理测试结果:")
    print(f"动作: {test_params['action']}")
    print(f"止损: {test_params['stop_loss']:.1%}")
    print(f"止盈: {test_params['take_profit']:.1%}")
    print(f"仓位风险: {test_params['position_size_risk']:.1%}")
    print(f"置信度门槛: {test_params['confidence_threshold']:.1%}")
    print(f"风险等级: {test_params['risk_level']}")
