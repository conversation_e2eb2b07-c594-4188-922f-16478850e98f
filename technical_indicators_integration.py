#!/usr/bin/env python3
"""
技术指标集成系统 - MACD, RSI, 布林带等指标的综合分析
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class TechnicalIndicatorsIntegration:
    """
    技术指标集成系统 - 多指标确认和过滤
    """
    
    def __init__(self, ai_probability: float = 0.372):
        """
        初始化技术指标系统
        """
        self.ai_probability = ai_probability
        
        # 指标参数配置
        self.indicator_params = {
            'macd': {'fast': 12, 'slow': 26, 'signal': 9},
            'rsi': {'period': 14, 'overbought': 70, 'oversold': 30},
            'bollinger': {'period': 20, 'std_dev': 2},
            'stochastic': {'k_period': 14, 'd_period': 3},
            'volume': {'sma_period': 20},
            'atr': {'period': 14}
        }
        
        # 指标权重配置
        self.indicator_weights = {
            'macd': 0.25,
            'rsi': 0.20,
            'bollinger': 0.20,
            'stochastic': 0.15,
            'volume': 0.10,
            'atr': 0.10
        }
        
        print(f"📊 技术指标集成系统初始化完成")
        print(f"   AI概率输入: {self.ai_probability:.1%}")
        print(f"   集成指标数量: {len(self.indicator_params)}")
    
    def calculate_macd(self, data: pd.DataFrame) -> Dict:
        """
        计算MACD指标
        """
        params = self.indicator_params['macd']
        
        # 计算EMA
        ema_fast = data['close'].ewm(span=params['fast']).mean()
        ema_slow = data['close'].ewm(span=params['slow']).mean()
        
        # MACD线
        macd_line = ema_fast - ema_slow
        
        # 信号线
        signal_line = macd_line.ewm(span=params['signal']).mean()
        
        # 柱状图
        histogram = macd_line - signal_line
        
        # 当前值
        current_macd = macd_line.iloc[-1]
        current_signal = signal_line.iloc[-1]
        current_histogram = histogram.iloc[-1]
        prev_histogram = histogram.iloc[-2]
        
        # 信号分析
        signals = []
        
        # 金叉信号
        if (current_macd > current_signal and 
            macd_line.iloc[-2] <= signal_line.iloc[-2]):
            signals.append({
                'type': 'golden_cross',
                'direction': 'LONG',
                'strength': abs(current_macd - current_signal) / data['close'].iloc[-1],
                'confidence': 0.7
            })
        
        # 死叉信号
        if (current_macd < current_signal and 
            macd_line.iloc[-2] >= signal_line.iloc[-2]):
            signals.append({
                'type': 'death_cross',
                'direction': 'SHORT',
                'strength': abs(current_macd - current_signal) / data['close'].iloc[-1],
                'confidence': 0.7
            })
        
        # 柱状图背离
        if current_histogram > prev_histogram > 0:
            signals.append({
                'type': 'histogram_bullish',
                'direction': 'LONG',
                'strength': (current_histogram - prev_histogram) / data['close'].iloc[-1],
                'confidence': 0.5
            })
        elif current_histogram < prev_histogram < 0:
            signals.append({
                'type': 'histogram_bearish',
                'direction': 'SHORT',
                'strength': abs(current_histogram - prev_histogram) / data['close'].iloc[-1],
                'confidence': 0.5
            })
        
        return {
            'macd_line': current_macd,
            'signal_line': current_signal,
            'histogram': current_histogram,
            'signals': signals,
            'trend': 'bullish' if current_macd > current_signal else 'bearish'
        }
    
    def calculate_rsi(self, data: pd.DataFrame) -> Dict:
        """
        计算RSI指标
        """
        params = self.indicator_params['rsi']
        
        # 计算价格变化
        delta = data['close'].diff()
        
        # 分离涨跌
        gain = (delta.where(delta > 0, 0)).rolling(window=params['period']).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=params['period']).mean()
        
        # 计算RSI
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        current_rsi = rsi.iloc[-1]
        prev_rsi = rsi.iloc[-2]
        
        # 信号分析
        signals = []
        
        # 超买超卖信号
        if current_rsi > params['overbought']:
            signals.append({
                'type': 'overbought',
                'direction': 'SHORT',
                'strength': (current_rsi - params['overbought']) / 100,
                'confidence': 0.6
            })
        elif current_rsi < params['oversold']:
            signals.append({
                'type': 'oversold',
                'direction': 'LONG',
                'strength': (params['oversold'] - current_rsi) / 100,
                'confidence': 0.6
            })
        
        # RSI背离
        price_higher = data['close'].iloc[-1] > data['close'].iloc[-5]
        rsi_lower = current_rsi < rsi.iloc[-5]
        
        if price_higher and rsi_lower:
            signals.append({
                'type': 'bearish_divergence',
                'direction': 'SHORT',
                'strength': 0.5,
                'confidence': 0.7
            })
        
        price_lower = data['close'].iloc[-1] < data['close'].iloc[-5]
        rsi_higher = current_rsi > rsi.iloc[-5]
        
        if price_lower and rsi_higher:
            signals.append({
                'type': 'bullish_divergence',
                'direction': 'LONG',
                'strength': 0.5,
                'confidence': 0.7
            })
        
        return {
            'rsi': current_rsi,
            'signals': signals,
            'condition': 'overbought' if current_rsi > 70 else 'oversold' if current_rsi < 30 else 'neutral'
        }
    
    def calculate_bollinger_bands(self, data: pd.DataFrame) -> Dict:
        """
        计算布林带指标
        """
        params = self.indicator_params['bollinger']
        
        # 计算移动平均和标准差
        sma = data['close'].rolling(window=params['period']).mean()
        std = data['close'].rolling(window=params['period']).std()
        
        # 布林带
        upper_band = sma + (std * params['std_dev'])
        lower_band = sma - (std * params['std_dev'])
        
        current_price = data['close'].iloc[-1]
        current_upper = upper_band.iloc[-1]
        current_lower = lower_band.iloc[-1]
        current_sma = sma.iloc[-1]
        
        # 价格位置
        bb_position = (current_price - current_lower) / (current_upper - current_lower)
        
        # 带宽
        bb_width = (current_upper - current_lower) / current_sma
        
        # 信号分析
        signals = []
        
        # 突破上轨
        if current_price > current_upper:
            signals.append({
                'type': 'upper_breakout',
                'direction': 'LONG',
                'strength': (current_price - current_upper) / current_upper,
                'confidence': 0.6
            })
        
        # 跌破下轨
        elif current_price < current_lower:
            signals.append({
                'type': 'lower_breakout',
                'direction': 'SHORT',
                'strength': (current_lower - current_price) / current_lower,
                'confidence': 0.6
            })
        
        # 回归中轨
        elif bb_position > 0.8:
            signals.append({
                'type': 'mean_reversion_short',
                'direction': 'SHORT',
                'strength': bb_position - 0.5,
                'confidence': 0.5
            })
        elif bb_position < 0.2:
            signals.append({
                'type': 'mean_reversion_long',
                'direction': 'LONG',
                'strength': 0.5 - bb_position,
                'confidence': 0.5
            })
        
        return {
            'upper_band': current_upper,
            'lower_band': current_lower,
            'middle_band': current_sma,
            'bb_position': bb_position,
            'bb_width': bb_width,
            'signals': signals,
            'squeeze': bb_width < 0.1  # 布林带收缩
        }
    
    def calculate_stochastic(self, data: pd.DataFrame) -> Dict:
        """
        计算随机指标
        """
        params = self.indicator_params['stochastic']
        
        # 计算%K
        lowest_low = data['low'].rolling(window=params['k_period']).min()
        highest_high = data['high'].rolling(window=params['k_period']).max()
        
        k_percent = 100 * ((data['close'] - lowest_low) / (highest_high - lowest_low))
        
        # 计算%D
        d_percent = k_percent.rolling(window=params['d_period']).mean()
        
        current_k = k_percent.iloc[-1]
        current_d = d_percent.iloc[-1]
        prev_k = k_percent.iloc[-2]
        prev_d = d_percent.iloc[-2]
        
        # 信号分析
        signals = []
        
        # 金叉死叉
        if current_k > current_d and prev_k <= prev_d:
            signals.append({
                'type': 'stoch_golden_cross',
                'direction': 'LONG',
                'strength': abs(current_k - current_d) / 100,
                'confidence': 0.6
            })
        elif current_k < current_d and prev_k >= prev_d:
            signals.append({
                'type': 'stoch_death_cross',
                'direction': 'SHORT',
                'strength': abs(current_k - current_d) / 100,
                'confidence': 0.6
            })
        
        # 超买超卖
        if current_k > 80 and current_d > 80:
            signals.append({
                'type': 'stoch_overbought',
                'direction': 'SHORT',
                'strength': (current_k - 80) / 20,
                'confidence': 0.5
            })
        elif current_k < 20 and current_d < 20:
            signals.append({
                'type': 'stoch_oversold',
                'direction': 'LONG',
                'strength': (20 - current_k) / 20,
                'confidence': 0.5
            })
        
        return {
            'k_percent': current_k,
            'd_percent': current_d,
            'signals': signals,
            'condition': 'overbought' if current_k > 80 else 'oversold' if current_k < 20 else 'neutral'
        }
    
    def calculate_volume_indicators(self, data: pd.DataFrame) -> Dict:
        """
        计算成交量指标
        """
        params = self.indicator_params['volume']
        
        # 成交量移动平均
        volume_sma = data['volume'].rolling(window=params['sma_period']).mean()
        current_volume = data['volume'].iloc[-1]
        avg_volume = volume_sma.iloc[-1]
        
        # 成交量比率
        volume_ratio = current_volume / avg_volume
        
        # 价量关系
        price_change = data['close'].pct_change().iloc[-1]
        
        signals = []
        
        # 放量突破
        if volume_ratio > 1.5:
            if price_change > 0.01:
                signals.append({
                    'type': 'volume_breakout_up',
                    'direction': 'LONG',
                    'strength': min(volume_ratio - 1, 2) / 2,
                    'confidence': 0.7
                })
            elif price_change < -0.01:
                signals.append({
                    'type': 'volume_breakout_down',
                    'direction': 'SHORT',
                    'strength': min(volume_ratio - 1, 2) / 2,
                    'confidence': 0.7
                })
        
        # 缩量整理
        elif volume_ratio < 0.7:
            signals.append({
                'type': 'low_volume_consolidation',
                'direction': 'NEUTRAL',
                'strength': 0.3,
                'confidence': 0.4
            })
        
        return {
            'volume_ratio': volume_ratio,
            'avg_volume': avg_volume,
            'current_volume': current_volume,
            'signals': signals,
            'condition': 'high' if volume_ratio > 1.5 else 'low' if volume_ratio < 0.7 else 'normal'
        }
    
    def calculate_atr(self, data: pd.DataFrame) -> Dict:
        """
        计算平均真实波幅(ATR)
        """
        params = self.indicator_params['atr']
        
        # 真实波幅计算
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(window=params['period']).mean()
        
        current_atr = atr.iloc[-1]
        current_price = data['close'].iloc[-1]
        atr_percentage = current_atr / current_price
        
        # 波动率分析
        signals = []
        
        if atr_percentage > 0.04:  # 高波动
            signals.append({
                'type': 'high_volatility',
                'direction': 'NEUTRAL',
                'strength': min(atr_percentage, 0.08) / 0.08,
                'confidence': 0.6,
                'note': '高波动环境，适合突破策略'
            })
        elif atr_percentage < 0.02:  # 低波动
            signals.append({
                'type': 'low_volatility',
                'direction': 'NEUTRAL',
                'strength': (0.02 - atr_percentage) / 0.02,
                'confidence': 0.6,
                'note': '低波动环境，适合均值回归'
            })
        
        return {
            'atr': current_atr,
            'atr_percentage': atr_percentage,
            'signals': signals,
            'volatility_level': 'high' if atr_percentage > 0.04 else 'low' if atr_percentage < 0.02 else 'normal'
        }
    
    def integrate_all_indicators(self, data: pd.DataFrame) -> Dict:
        """
        整合所有技术指标
        """
        # 计算各个指标
        indicators = {
            'macd': self.calculate_macd(data),
            'rsi': self.calculate_rsi(data),
            'bollinger': self.calculate_bollinger_bands(data),
            'stochastic': self.calculate_stochastic(data),
            'volume': self.calculate_volume_indicators(data),
            'atr': self.calculate_atr(data)
        }
        
        # 收集所有信号
        all_signals = []
        for indicator_name, indicator_data in indicators.items():
            weight = self.indicator_weights[indicator_name]
            for signal in indicator_data.get('signals', []):
                signal['indicator'] = indicator_name
                signal['weighted_confidence'] = signal['confidence'] * weight
                all_signals.append(signal)
        
        # 按方向分组信号
        long_signals = [s for s in all_signals if s['direction'] == 'LONG']
        short_signals = [s for s in all_signals if s['direction'] == 'SHORT']
        
        # 计算综合信号强度
        long_strength = sum(s['weighted_confidence'] * s['strength'] for s in long_signals)
        short_strength = sum(s['weighted_confidence'] * s['strength'] for s in short_signals)
        
        # 结合AI概率
        ai_long_strength = self.ai_probability * 0.3  # AI权重30%
        ai_short_strength = (1 - self.ai_probability) * 0.3
        
        total_long = long_strength + ai_long_strength
        total_short = short_strength + ai_short_strength
        
        # 生成最终建议
        if total_long > total_short and total_long > 0.4:
            final_direction = 'LONG'
            final_confidence = min(total_long, 1.0)
        elif total_short > total_long and total_short > 0.4:
            final_direction = 'SHORT'
            final_confidence = min(total_short, 1.0)
        else:
            final_direction = 'NEUTRAL'
            final_confidence = 0.5
        
        return {
            'indicators': indicators,
            'all_signals': all_signals,
            'long_signals': long_signals,
            'short_signals': short_signals,
            'signal_summary': {
                'long_strength': total_long,
                'short_strength': total_short,
                'ai_contribution': {'long': ai_long_strength, 'short': ai_short_strength}
            },
            'final_recommendation': {
                'direction': final_direction,
                'confidence': final_confidence,
                'supporting_indicators': len([s for s in all_signals if s['direction'] == final_direction]),
                'conflicting_indicators': len([s for s in all_signals if s['direction'] != final_direction and s['direction'] != 'NEUTRAL'])
            }
        }
    
    def print_technical_analysis(self, analysis: Dict):
        """
        打印技术分析结果
        """
        print(f"\n📊 【技术指标综合分析】")
        print("=" * 70)
        
        # 各指标状态
        indicators = analysis['indicators']
        print(f"📈 各指标当前状态:")
        
        # MACD
        macd = indicators['macd']
        print(f"   MACD: {macd['trend']} (MACD: {macd['macd_line']:.2f}, Signal: {macd['signal_line']:.2f})")
        
        # RSI
        rsi = indicators['rsi']
        print(f"   RSI: {rsi['condition']} ({rsi['rsi']:.1f})")
        
        # 布林带
        bb = indicators['bollinger']
        print(f"   布林带: 位置{bb['bb_position']:.1%} {'(收缩)' if bb['squeeze'] else ''}")
        
        # 随机指标
        stoch = indicators['stochastic']
        print(f"   随机指标: {stoch['condition']} (K: {stoch['k_percent']:.1f}, D: {stoch['d_percent']:.1f})")
        
        # 成交量
        vol = indicators['volume']
        print(f"   成交量: {vol['condition']} (比率: {vol['volume_ratio']:.1f})")
        
        # ATR
        atr = indicators['atr']
        print(f"   波动率: {atr['volatility_level']} (ATR: {atr['atr_percentage']:.2%})")
        
        # 信号汇总
        summary = analysis['signal_summary']
        print(f"\n🎯 信号强度汇总:")
        print(f"   做多信号强度: {summary['long_strength']:.2f}")
        print(f"   做空信号强度: {summary['short_strength']:.2f}")
        print(f"   AI贡献 - 做多: {summary['ai_contribution']['long']:.2f}, 做空: {summary['ai_contribution']['short']:.2f}")
        
        # 最终建议
        rec = analysis['final_recommendation']
        direction_emoji = {"LONG": "🟢", "SHORT": "🔴", "NEUTRAL": "🟡"}
        print(f"\n🎯 最终技术分析建议:")
        print(f"   {direction_emoji[rec['direction']]} 方向: {rec['direction']}")
        print(f"   置信度: {rec['confidence']:.1%}")
        print(f"   支持指标数: {rec['supporting_indicators']}")
        print(f"   冲突指标数: {rec['conflicting_indicators']}")
        
        # 详细信号
        if analysis['long_signals'] or analysis['short_signals']:
            print(f"\n📋 详细信号列表:")
            for signal in sorted(analysis['all_signals'], key=lambda x: x['weighted_confidence'], reverse=True)[:5]:
                direction_emoji = {"LONG": "🟢", "SHORT": "🔴", "NEUTRAL": "🟡"}
                print(f"   {direction_emoji[signal['direction']]} {signal['indicator'].upper()}: {signal['type']}")
                print(f"      置信度: {signal['weighted_confidence']:.2f}, 强度: {signal['strength']:.2f}")

def demonstrate_technical_indicators():
    """
    演示技术指标集成
    """
    print("📊 技术指标集成系统演示")
    print("=" * 50)
    
    # 创建模拟数据
    dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
    np.random.seed(42)
    
    # 模拟更真实的价格数据
    price_changes = np.random.normal(0, 0.015, 100)
    prices = [104426.90]
    volumes = []
    
    for i, change in enumerate(price_changes):
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
        # 成交量与价格变化相关
        volume = 1000 + abs(change) * 5000 + np.random.normal(0, 200)
        volumes.append(max(volume, 100))
    
    data = pd.DataFrame({
        'timestamp': dates,
        'open': prices[:-1],
        'high': [p * (1 + abs(np.random.normal(0, 0.008))) for p in prices[:-1]],
        'low': [p * (1 - abs(np.random.normal(0, 0.008))) for p in prices[:-1]],
        'close': prices[1:],
        'volume': volumes
    })
    
    # 初始化技术指标系统
    tech_system = TechnicalIndicatorsIntegration(ai_probability=0.372)
    
    # 执行综合分析
    analysis = tech_system.integrate_all_indicators(data)
    
    # 打印结果
    tech_system.print_technical_analysis(analysis)

if __name__ == "__main__":
    demonstrate_technical_indicators()
