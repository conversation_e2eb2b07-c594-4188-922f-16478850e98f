from binance.client import Client
import pandas as pd
import os
from dotenv import load_dotenv
import logging
import time
from datetime import datetime, timedelta
import pytz
import pickle
from pathlib import Path
import hashlib
from concurrent.futures import ProcessPoolExecutor
from functools import partial
from typing import Optional, List, Callable, Tuple, Any

# 配置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# 辅助函数，用于 ProcessPoolExecutor.map，以避免 pickling lambda 的问题
def _execute_fetch_func_wrapper(func_and_args: Tuple[Callable, Tuple[Any, ...]]) -> Any:
    fetch_function, args_tuple = func_and_args
    return fetch_function(*args_tuple)

class BinanceDataFetcher:
    def __init__(self, cache_dir: str = 'data/cache'):
        """初始化Binance数据获取器"""
        try:
            # 尝试从环境变量获取API密钥
            api_key = os.getenv('BINANCE_API_KEY')
            api_secret = os.getenv('BINANCE_API_SECRET')
            
            # 设置代理
            proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }

            if api_key and api_secret:
                self.client = Client(api_key, api_secret, requests_params={'proxies': proxies})
                logger.info("已使用API密钥和代理配置初始化Binance客户端")
            else:
                logger.warning("API密钥未设置！请在.env文件中设置BINANCE_API_KEY和BINANCE_API_SECRET")
                logger.info("尝试使用无验证模式和代理配置连接...")
                self.client = Client("", "", requests_params={'proxies': proxies})
            
            self.cache_dir = Path(cache_dir)
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            
            # 测试连接
            self.client.get_system_status()
            logger.info("成功连接到Binance API")
            
        except Exception as e:
            logger.error(f"初始化Binance客户端时发生错误: {str(e)}")
            raise
            
    def _get_cache_path(self, symbol: str, interval: str, start_date: str, is_futures: bool) -> Path:
        """生成缓存文件路径"""
        cache_key = f"{symbol}_{interval}_{start_date}_{is_futures}"
        cache_hash = hashlib.md5(cache_key.encode()).hexdigest()
        return self.cache_dir / f"{cache_hash}.pkl"
        
    def _load_cache(self, cache_path: Path) -> Optional[pd.DataFrame]:
        """从缓存加载数据"""
        if not cache_path.exists():
            return None
            
        try:
            cache_data = pickle.load(cache_path.open('rb'))
            cache_time = cache_path.stat().st_mtime
            
            # 缩短缓存有效期至5分钟
            if time.time() - cache_time > 300:  # 5分钟 = 300秒
                logger.info("缓存已过期（超过5分钟），将重新获取数据")
                return None
                
            return cache_data
        except Exception as e:
            logger.warning(f"加载缓存失败: {str(e)}")
            return None
            
    def _save_cache(self, cache_path: Path, data: pd.DataFrame):
        """保存数据到缓存"""
        try:
            pickle.dump(data, cache_path.open('wb'))
        except Exception as e:
            logger.warning(f"保存缓存失败: {str(e)}")
            
    def _fetch_klines_chunk(self, symbol: str, interval: str, start_time: int, 
                          end_time: int, is_futures: bool) -> List:
        """获取一个时间段的K线数据"""
        max_retries = 3
        retry_delay = 1 # 初始重试延时1秒
        
        for retry in range(max_retries):
            try:
                if is_futures:
                    klines = self.client.futures_klines(
                        symbol=symbol,
                        interval=interval,
                        startTime=start_time,
                        endTime=end_time,
                        limit=1000
                    )
                else:
                    klines = self.client.get_klines(
                        symbol=symbol,
                        interval=interval,
                        startTime=start_time,
                        endTime=end_time,
                        limit=1000
                    )
                time.sleep(0.2) # 在每次API调用成功后加入0.2秒延时
                return klines
            except Exception as e:
                logger.warning(f"获取数据块 ({symbol}, {interval}, {start_time}-{end_time}) 失败，尝试次数 {retry + 1}/{max_retries}: {str(e)}")
                if retry < max_retries - 1:
                    # 逐渐增加延时
                    current_retry_delay = retry_delay * (2 ** retry) # 指数退避
                    logger.info(f"等待 {current_retry_delay} 秒后重试...")
                    time.sleep(current_retry_delay)
                else:
                    logger.error(f"获取数据块失败达到最大重试次数。")
                    raise
                    
    def get_historical_data(self, symbol: str, interval: str, 
                          start_date: str, is_futures: bool = False, 
                          force_refresh: bool = False) -> pd.DataFrame:
        """获取历史K线数据，支持缓存和并行处理
        
        Args:
            force_refresh: 是否强制刷新缓存
        """
        cache_path = self._get_cache_path(symbol, interval, start_date, is_futures)
        
        # 如果强制刷新，跳过缓存
        if not force_refresh:
            cached_data = self._load_cache(cache_path)
            if cached_data is not None:
                logger.info(f"从缓存加载数据: {symbol}")
                return cached_data
        else:
            logger.info("强制刷新模式：跳过缓存加载")
        
        # 计算时间范围
        start_time = int(pd.Timestamp(start_date).timestamp() * 1000)
        end_time = int(datetime.now().timestamp() * 1000)
        
        # 将时间范围分成多个块
        # 根据interval动态调整chunk_size，目标是每个chunk尽可能接近1000条记录
        limit_per_call = 1000 # API单次调用限制
        ms_per_interval = {
            '1m': 60 * 1000,
            '5m': 5 * 60 * 1000,
            '15m': 15 * 60 * 1000,
            '30m': 30 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000
        }
        
        interval_ms = ms_per_interval.get(interval, 60 * 60 * 1000) # 默认为1h
        chunk_size = limit_per_call * interval_ms # 计算使每个请求接近1000条的chunk_size

        time_chunks = []
        current_start = start_time
        
        while current_start < end_time:
            chunk_end = min(current_start + chunk_size, end_time)
            time_chunks.append((current_start, chunk_end))
            current_start = chunk_end
            
        # 使用进程池并行获取数据 (暂时改为串行以减少API压力)
        with ProcessPoolExecutor(max_workers=2) as executor: # 将 max_workers 设置为 2
            # 将 fetch_func 和其参数打包成元组列表，传递给 _execute_fetch_func_wrapper
            # 注意：这里的partial应用方式也调整一下，确保参数正确传递
            tasks = [
                (
                    partial(self._fetch_klines_chunk, symbol, interval, chunk[0], chunk[1], is_futures)
                ) for chunk in time_chunks
            ]
            # 由于 _execute_fetch_func_wrapper 设计为接收 (func, args_tuple)
            # 我们需要重新包装 tasks
            wrapped_tasks = [(task_func, tuple()) for task_func in tasks]


            chunk_results_futures = [executor.submit(_execute_fetch_func_wrapper, wrapped_task) for wrapped_task in wrapped_tasks]
            
            all_klines = []
            for future in chunk_results_futures:
                try:
                    result = future.result() # 获取结果
                    if result: # 确保结果不是None或空的
                        all_klines.extend(result)
                except Exception as e:
                    logger.error(f"处理数据块时发生错误: {e}")
                    # 可以选择是否抛出异常或继续处理其他块
                    # raise # 如果希望一个块失败导致整个过程失败

        # 合并所有数据
        # all_klines = [item for sublist in chunk_results for item in sublist if sublist] # 确保 sublist 不是 None
        
        if not all_klines:
            logger.warning(f"未能获取任何K线数据: {symbol}, {interval}, {start_date}")
            # 返回一个空的DataFrame以避免后续错误
            return pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 
                                           'volume', 'close_time', 'quote_volume', 'trades',
                                           'taker_buy_base', 'taker_buy_quote', 'ignored'])
        
        # 转换为DataFrame
        df = pd.DataFrame(all_klines, columns=['timestamp', 'open', 'high', 'low', 'close', 
                                             'volume', 'close_time', 'quote_volume', 'trades',
                                             'taker_buy_base', 'taker_buy_quote', 'ignored'])
                                             
        # 数据处理
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume', 'quote_volume',
                   'taker_buy_base', 'taker_buy_quote']:
            df[col] = df[col].astype(float)
            
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)
        
        # 保存到缓存
        self._save_cache(cache_path, df)
        
        return df
            
    def _convert_time_str_to_timestamp(self, time_str: str) -> int:
        """将时间字符串转换为毫秒时间戳"""
        try:
            from dateutil import parser
            
            # 如果是相对时间（例如 '7 days ago UTC'）
            if 'ago' in time_str.lower():
                parts = time_str.lower().split()
                number = int(parts[0])
                unit = parts[1]
                
                now = datetime.now(pytz.UTC)
                if unit == 'days':
                    delta = timedelta(days=number)
                elif unit == 'hours':
                    delta = timedelta(hours=number)
                elif unit == 'minutes':
                    delta = timedelta(minutes=number)
                else:
                    raise ValueError(f"不支持的时间单位: {unit}")
                    
                target_time = now - delta
                
            else:
                # 如果是具体日期
                target_time = parser.parse(time_str)
                if target_time.tzinfo is None:
                    target_time = pytz.UTC.localize(target_time)
                    
            return int(target_time.timestamp() * 1000)  # 转换为毫秒时间戳
            
        except Exception as e:
            logger.error(f"时间转换错误: {str(e)}")
            raise

    def get_current_price(self, symbol: str, is_futures: bool = False) -> float:
        """获取当前价格"""
        try:
            if is_futures:
                ticker = self.client.futures_symbol_ticker(symbol=symbol)
            else:
                ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except Exception as e:
            logger.error(f"获取{symbol}{'永续合约' if is_futures else '现货'}价格时发生错误: {str(e)}")
            raise

if __name__ == "__main__":
    # 测试代码
    fetcher = BinanceDataFetcher()
    symbol = os.getenv('SYMBOL', 'BTCUSDT')
    interval = os.getenv('INTERVAL', '4h')
    start_date = os.getenv('DATA_START_DATE', '2022-01-01')
    
    try:
        df = fetcher.get_historical_data(symbol, interval, start_date)
        print("\n数据预览:")
        print(df.head())
        print("\n数据统计信息:")
        print(df.describe())
    except Exception as e:
        print(f"测试时发生错误: {str(e)}") 