#!/usr/bin/env python3
"""
测试增强版显示效果
"""

from enhanced_display import print_enhanced_status, print_trade_execution
from datetime import datetime

# 模拟交易器对象
class MockTrader:
    def __init__(self):
        self.capital = 47.85
        self.initial_capital = 50.0
        self.position = {
            'size': 0.0,
            'side': None,
            'entry_price': 0.0,
            'entry_time': None,
            'margin_used': 0.0
        }
        self.performance_stats = {
            'total_trades': 3,
            'winning_trades': 2,
            'losing_trades': 1,
            'total_pnl': -2.15,
            'win_rate': 0.667,
            'max_drawdown': 0.043,
            'avg_win': 1.25,
            'avg_loss': -4.65,
            'profit_factor': 0.27
        }
    
    def calculate_unrealized_pnl(self, current_price):
        return 0.0
    
    def update_performance_stats(self):
        pass
    
    def calculate_position_size(self, entry_price, confidence):
        return {
            'btc_size': 0.000956,
            'position_value': 99.75,
            'margin_required': 24.94,
            'risk_amount': 1.00
        }

def test_enhanced_display():
    """测试增强版显示"""
    print("🎯 测试增强版显示效果")
    print("=" * 60)
    
    # 创建模拟数据
    trader = MockTrader()
    
    # 模拟强看跌信号
    ai_probability = 0.285  # 28.5%上涨，71.5%下跌
    
    indicators = {
        'price': 104500.0,
        'rsi': 78.2,
        'macd_trend': 'bearish',
        'bb_position': 0.92,
        'volume_ratio': 2.1
    }
    
    signal = {
        'direction': 'SHORT',
        'strength': 0.85,
        'confidence': 0.82,
        'reason': 'AI强看跌(71.5%)+4个指标确认'
    }
    
    # 显示增强版状态
    print_enhanced_status(trader, ai_probability, indicators, signal)
    
    # 测试交易执行显示
    print("\n" + "="*60)
    print("测试交易执行显示:")
    
    # 开仓显示
    print_trade_execution("OPEN", {
        'side': 'SHORT',
        'size': 0.000956,
        'price': 104500.0,
        'margin': 24.94,
        'fee': 0.0418,
        'reason': 'AI强看跌(71.5%)+4个指标确认',
        'confidence': 0.82
    })
    
    # 平仓显示
    print_trade_execution("CLOSE", {
        'side': 'SHORT',
        'entry_price': 104500.0,
        'exit_price': 99275.0,
        'hold_hours': 3.2,
        'final_pnl': 5.18,
        'reason': '止盈触发'
    })

if __name__ == "__main__":
    test_enhanced_display()
