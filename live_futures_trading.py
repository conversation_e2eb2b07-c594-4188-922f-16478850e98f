#!/usr/bin/env python3
"""
实时永续合约模拟交易系统 - 基于实时永续合约数据进行预测和交易
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class LiveFuturesSimulationTrader:
    """
    实时永续合约模拟交易器
    """
    
    def __init__(self, initial_capital=50, leverage=2, model_path=None):
        """
        初始化永续合约模拟交易器
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = min(max(leverage, 1), 3)  # 限制1-3倍杠杆
        
        # 持仓状态
        self.position = 0  # 0=空仓, >0=多头数量, <0=空头数量
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0
        self.position_value = 0
        
        # 永续合约策略参数 - 优化后的阈值
        self.long_threshold = 0.58   # 做多阈值 (降低)
        self.short_threshold = 0.42  # 做空阈值 (提高)
        self.neutral_zone = 0.16     # 中性区间缩小到16%
        self.stop_loss_ratio = 0.025 # 2.5%止损
        self.take_profit_ratio = 0.06 # 6%止盈
        self.commission_rate = 0.0004 # 永续合约手续费0.04%
        self.funding_rate = 0.0001   # 模拟资金费率0.01%
        self.max_hold_hours = 16     # 最大持仓时间避免多次资金费率
        
        # 风险控制
        self.max_margin_ratio = 0.8  # 最大保证金使用比例
        self.liquidation_threshold = 0.9  # 强制平仓阈值
        
        # 交易记录
        self.trades = []
        self.equity_history = []
        self.prediction_history = []
        self.funding_payments = []
        
        # 加载模型
        if model_path is None:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if not model_files:
                raise ValueError("未找到BTCUSDT模型文件")
            model_path = max(model_files, key=lambda x: x.split('_')[-1])
        
        print(f"📦 加载模型: {model_path}")
        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']
        
        # 初始化数据获取器
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        
        print(f"🚀 永续合约模拟交易器初始化完成")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   做多阈值: {self.long_threshold:.2f} (>{self.long_threshold:.0%})")
        print(f"   做空阈值: {self.short_threshold:.2f} (<{self.short_threshold:.0%})")
        print(f"   中性区间: {self.neutral_zone:.1%} ({self.short_threshold:.0%}-{self.long_threshold:.0%})")
        print(f"   止损: {self.stop_loss_ratio:.1%}")
        print(f"   止盈: {self.take_profit_ratio:.1%}")
        print(f"   手续费: {self.commission_rate:.2%}")
    
    def get_current_prediction(self, symbol='BTCUSDT'):
        """
        获取当前实时预测 - 使用永续合约数据
        """
        try:
            # 获取永续合约历史数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            # 使用永续合约数据
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                print("⚠️  永续合约数据不足，无法进行预测")
                return None, None, None
            
            # 特征工程
            df_features = self.engineer.create_features(df)
            
            # 准备最新数据进行预测
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            # 只使用最新的数据点
            X_latest = X.iloc[-1:].copy()
            
            # 数据清理
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            # 预测
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            # 获取当前永续合约价格
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            current_time = datetime.now()
            
            return up_probability, current_price, current_time
            
        except Exception as e:
            print(f"❌ 获取永续合约预测时发生错误: {str(e)}")
            return None, None, None
    
    def should_open_position(self, up_probability):
        """
        判断是否应该开仓
        """
        if self.position != 0:
            return False, 0, "已有持仓"
        
        # 检查可用保证金
        available_margin = self.capital * self.max_margin_ratio
        if available_margin < 10:  # 最小保证金要求
            return False, 0, "可用保证金不足"
        
        # 检查做多信号
        if up_probability > self.long_threshold:
            return True, 1, f"做多信号 (置信度: {up_probability:.3f})"
        
        # 检查做空信号
        elif up_probability < self.short_threshold:
            return True, -1, f"做空信号 (置信度: {up_probability:.3f})"
        
        return False, 0, f"信号不明确 ({up_probability:.3f})"
    
    def should_close_position(self, up_probability, current_price):
        """
        判断是否应该平仓
        """
        if self.position == 0:
            return False, "无持仓"
        
        # 计算当前盈亏
        if self.position > 0:  # 多头
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:  # 空头
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        # 强制平仓检查（防止爆仓）
        leveraged_loss = -pnl_ratio * self.leverage
        if leveraged_loss > self.liquidation_threshold:
            return True, f"强制平仓 (亏损: {leveraged_loss:.2%})"
        
        # 止损检查
        if pnl_ratio < -self.stop_loss_ratio:
            return True, f"止损 (亏损: {pnl_ratio:.2%})"
        
        # 止盈检查
        if pnl_ratio > self.take_profit_ratio:
            return True, f"止盈 (盈利: {pnl_ratio:.2%})"
        
        # 时间止损（避免长时间持仓产生过多资金费率）
        if self.entry_time:
            hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
            if hold_hours > self.max_hold_hours:
                return True, f"时间止损 (持仓{hold_hours:.1f}小时)"
        
        # 信号反转检查
        if self.position > 0 and up_probability < 0.4:
            return True, f"多头信号反转 (置信度: {up_probability:.3f})"
        elif self.position < 0 and up_probability > 0.6:
            return True, f"空头信号反转 (置信度: {up_probability:.3f})"
        
        return False, "持有"
    
    def calculate_funding_fee(self):
        """
        计算资金费率费用（每8小时）
        """
        if self.position == 0 or not self.entry_time:
            return 0
        
        # 计算持仓时间
        hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
        funding_periods = int(hold_hours / 8)  # 每8小时收取一次
        
        if funding_periods > 0:
            position_value = abs(self.position) * self.entry_price
            funding_fee = position_value * self.funding_rate * funding_periods
            
            # 多头支付，空头收取
            if self.position > 0:
                return -funding_fee  # 多头支付
            else:
                return funding_fee   # 空头收取
        
        return 0
    
    def execute_trade(self, action, direction, price, timestamp, confidence=None, reason=""):
        """
        执行永续合约交易
        """
        if action == 'OPEN':
            # 计算仓位大小
            available_margin = self.capital * self.max_margin_ratio
            position_value = available_margin * self.leverage
            position_size = position_value / price
            
            if direction == -1:  # 做空
                position_size = -position_size
            
            self.position = position_size
            self.entry_price = price
            self.entry_time = timestamp
            self.margin_used = available_margin
            self.position_value = position_value
            
            # 扣除开仓手续费
            opening_fee = abs(position_size) * price * self.commission_rate
            self.capital -= opening_fee
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'OPEN',
                'direction': 'LONG' if direction == 1 else 'SHORT',
                'price': price,
                'position_size': position_size,
                'margin_used': self.margin_used,
                'leverage': self.leverage,
                'confidence': confidence,
                'reason': reason,
                'opening_fee': opening_fee
            }
            
            direction_text = "做多" if direction == 1 else "做空"
            print(f"✅ {direction_text} {abs(position_size):.6f} BTC @ ${price:,.2f} (杠杆: {self.leverage}x, 置信度: {confidence:.3f})")
            
        elif action == 'CLOSE':
            # 计算盈亏
            if self.position > 0:  # 平多
                pnl_ratio = (price - self.entry_price) / self.entry_price
            else:  # 平空
                pnl_ratio = (self.entry_price - price) / self.entry_price
            
            # 杠杆放大盈亏
            leveraged_pnl = pnl_ratio * self.leverage * self.margin_used
            
            # 扣除平仓手续费
            closing_fee = abs(self.position) * price * self.commission_rate
            
            # 计算资金费率
            funding_fee = self.calculate_funding_fee()
            
            # 最终盈亏
            final_pnl = leveraged_pnl - closing_fee + funding_fee
            
            # 更新资金
            self.capital = self.capital + self.margin_used + final_pnl
            
            # 计算持仓时间
            hold_time = (timestamp - self.entry_time).total_seconds() / 3600 if self.entry_time else 0
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'CLOSE',
                'direction': 'LONG' if self.position > 0 else 'SHORT',
                'price': price,
                'entry_price': self.entry_price,
                'position_size': self.position,
                'pnl_ratio': pnl_ratio,
                'leveraged_pnl': leveraged_pnl,
                'closing_fee': closing_fee,
                'funding_fee': funding_fee,
                'final_pnl': final_pnl,
                'hold_hours': hold_time,
                'confidence': confidence,
                'reason': reason,
                'capital_after': self.capital
            }
            
            direction_text = "平多" if self.position > 0 else "平空"
            print(f"✅ {direction_text} @ ${price:,.2f} (盈亏: {final_pnl:+.2f}, 资金: ${self.capital:.2f})")
            
            if funding_fee != 0:
                fee_type = "收取" if funding_fee > 0 else "支付"
                print(f"   资金费率{fee_type}: ${abs(funding_fee):.4f}")
            
            # 重置持仓
            self.position = 0
            self.entry_price = 0
            self.entry_time = None
            self.margin_used = 0
            self.position_value = 0
        
        self.trades.append(trade_record)
    
    def update_equity(self, current_price, timestamp):
        """
        更新权益记录
        """
        if self.position != 0:
            # 计算未实现盈亏
            if self.position > 0:  # 多头
                unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
            else:  # 空头
                unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
            
            unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
            
            # 计算当前资金费率
            funding_fee = self.calculate_funding_fee()
            
            current_equity = self.capital + self.margin_used + unrealized_pnl + funding_fee
        else:
            current_equity = self.capital
        
        equity_record = {
            'timestamp': timestamp,
            'price': current_price,
            'position': self.position,
            'capital': self.capital,
            'margin_used': self.margin_used,
            'equity': current_equity,
            'total_return': (current_equity - self.initial_capital) / self.initial_capital
        }
        
        self.equity_history.append(equity_record)
    
    def get_current_status(self):
        """
        获取当前状态
        """
        if self.equity_history:
            latest_equity = self.equity_history[-1]['equity']
            total_return = (latest_equity - self.initial_capital) / self.initial_capital
        else:
            latest_equity = self.capital
            total_return = 0
        
        completed_trades = [t for t in self.trades if t['action'] == 'CLOSE']
        profitable_trades = [t for t in completed_trades if t.get('final_pnl', 0) > 0]
        win_rate = len(profitable_trades) / len(completed_trades) if completed_trades else 0
        
        return {
            'current_equity': latest_equity,
            'total_return': total_return,
            'position': self.position,
            'entry_price': self.entry_price,
            'margin_used': self.margin_used,
            'available_capital': self.capital,
            'total_trades': len(completed_trades),
            'win_rate': win_rate
        }
    
    def print_status(self, current_price=None, up_probability=None):
        """
        打印当前状态
        """
        status = self.get_current_status()
        
        print(f"\n📊 永续合约模拟交易状态")
        print("=" * 50)
        print(f"当前权益: ${status['current_equity']:.2f}")
        print(f"总收益率: {status['total_return']:+.2%}")
        print(f"可用资金: ${status['available_capital']:.2f}")
        print(f"占用保证金: ${status['margin_used']:.2f}")
        
        if status['position'] != 0:
            position_type = "多头" if status['position'] > 0 else "空头"
            print(f"当前持仓: {position_type} {abs(status['position']):.6f} BTC")
            print(f"入场价格: ${status['entry_price']:,.2f}")
            
            if current_price:
                if status['position'] > 0:
                    unrealized_pnl_ratio = (current_price - status['entry_price']) / status['entry_price']
                else:
                    unrealized_pnl_ratio = (status['entry_price'] - current_price) / status['entry_price']
                
                unrealized_pnl = unrealized_pnl_ratio * self.leverage * status['margin_used']
                funding_fee = self.calculate_funding_fee()
                
                print(f"未实现盈亏: {unrealized_pnl:+.2f} ({unrealized_pnl_ratio * self.leverage:+.2%})")
                if funding_fee != 0:
                    fee_type = "应收" if funding_fee > 0 else "应付"
                    print(f"资金费率{fee_type}: ${abs(funding_fee):.4f}")
        else:
            print(f"当前持仓: 空仓")
        
        print(f"完成交易: {status['total_trades']}")
        print(f"胜率: {status['win_rate']:.2%}")
        print(f"杠杆倍数: {self.leverage}x")
        
        if current_price and up_probability:
            print(f"\n📈 当前市场信息:")
            print(f"BTC永续价格: ${current_price:,.2f}")
            print(f"上涨概率: {up_probability:.3f} ({up_probability:.1%})")
            
            if up_probability > self.long_threshold:
                signal = "🟢 强烈看涨 (做多信号)"
            elif up_probability > 0.5:
                signal = "🟡 轻微看涨"
            elif up_probability > self.short_threshold:
                signal = "🟡 轻微看跌"
            else:
                signal = "🔴 强烈看跌 (做空信号)"
            
            print(f"市场信号: {signal}")
    
    def save_session(self, filename=None):
        """
        保存交易会话
        """
        if filename is None:
            filename = f"futures_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        session_data = {
            'initial_capital': self.initial_capital,
            'leverage': self.leverage,
            'current_status': self.get_current_status(),
            'trades': self.trades,
            'equity_history': self.equity_history[-100:],
            'parameters': {
                'long_threshold': self.long_threshold,
                'short_threshold': self.short_threshold,
                'stop_loss_ratio': self.stop_loss_ratio,
                'take_profit_ratio': self.take_profit_ratio,
                'commission_rate': self.commission_rate,
                'funding_rate': self.funding_rate
            }
        }
        
        # 转换datetime对象为字符串
        def convert_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, pd.Timestamp):
                return obj.isoformat()
            return obj
        
        def convert_dict(d):
            if isinstance(d, dict):
                return {k: convert_dict(v) for k, v in d.items()}
            elif isinstance(d, list):
                return [convert_dict(item) for item in d]
            else:
                return convert_datetime(d)
        
        session_data = convert_dict(session_data)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
        
        print(f"📝 永续合约交易会话已保存: {filename}")

def run_futures_simulation(check_interval=300, leverage=2):
    """
    运行永续合约实时模拟交易
    """
    print("🚀 启动永续合约实时模拟交易系统")
    print("=" * 60)
    print(f"杠杆倍数: {leverage}x")
    print(f"检查间隔: {check_interval}秒 ({check_interval/60:.1f}分钟)")
    print("支持做多做空，基于永续合约数据，按 Ctrl+C 停止交易")
    print("")
    
    trader = LiveFuturesSimulationTrader(initial_capital=50, leverage=leverage)
    
    try:
        while True:
            print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 检查永续合约市场...")
            
            # 获取当前预测
            up_prob, current_price, current_time = trader.get_current_prediction()
            
            if up_prob is None:
                print("❌ 无法获取永续合约预测，跳过本次检查")
                time.sleep(check_interval)
                continue
            
            # 记录预测历史
            trader.prediction_history.append({
                'timestamp': current_time,
                'price': current_price,
                'up_probability': up_prob
            })
            
            # 更新权益
            trader.update_equity(current_price, current_time)
            
            # 检查平仓信号
            should_close, close_reason = trader.should_close_position(up_prob, current_price)
            if should_close:
                trader.execute_trade('CLOSE', 0, current_price, current_time, up_prob, close_reason)
            
            # 检查开仓信号
            should_open, direction, open_reason = trader.should_open_position(up_prob)
            if should_open:
                trader.execute_trade('OPEN', direction, current_price, current_time, up_prob, open_reason)
            
            # 打印状态
            trader.print_status(current_price, up_prob)
            
            # 每小时保存一次会话
            if len(trader.equity_history) % 12 == 0:
                trader.save_session()
            
            # 等待下次检查
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟后进行下次检查...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 用户停止永续合约交易")
        trader.print_status(current_price, up_prob)
        trader.save_session()
        print(f"感谢使用永续合约模拟交易系统！")

if __name__ == "__main__":
    import sys
    
    # 命令行参数: 检查间隔(秒) 杠杆倍数
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300  # 默认5分钟
    leverage = int(sys.argv[2]) if len(sys.argv) > 2 else 2    # 默认2倍杠杆
    leverage = min(max(leverage, 1), 3)  # 限制1-3倍
    
    print("⚠️  永续合约模拟交易说明:")
    print("- 基于永续合约价格数据进行预测和交易")
    print("- 支持做多做空和杠杆交易")
    print("- 包含资金费率模拟")
    print("- 这是模拟交易，不会产生真实资金损失")
    print("- 杠杆会放大盈亏，请注意风险")
    print("- 建议先观察表现再考虑实盘")
    print("")
    
    run_futures_simulation(interval, leverage)
