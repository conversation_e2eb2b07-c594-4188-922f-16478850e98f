#!/usr/bin/env python3
"""
高频交易模式 - 预测下一个5分钟K线涨跌
专注于短期价格预测和快速交易执行
"""

import pandas as pd
import numpy as np
import time
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings

# 禁用所有警告和详细日志
warnings.filterwarnings('ignore')
logging.getLogger().setLevel(logging.ERROR)
logging.getLogger('data_fetcher').setLevel(logging.ERROR)
logging.getLogger('feature_engineering').setLevel(logging.ERROR)

# 导入组件
from enhanced_real_sentiment import EnhancedRealSentimentAnalyzer
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class HighFrequencyTrader:
    """
    高频交易系统 - 5分钟K线涨跌预测
    
    特点：
    - 🎯 专注预测下一个5分钟K线涨跌
    - ⚡ 快速交易执行（30秒检测间隔）
    - 📊 基于短期技术指标
    - 💰 50美元模拟账户
    """
    
    def __init__(self, initial_balance: float = 50.0):
        self.initial_balance = initial_balance
        
        # 账户状态
        self.account = {
            'balance': initial_balance,
            'equity': initial_balance,
            'unrealized_pnl': 0.0
        }
        
        # 持仓信息
        self.position = {
            'side': None,  # 'LONG' or 'SHORT'
            'size': 0.0,   # BTC数量
            'entry_price': 0.0,
            'entry_time': None,
            'target_time': None,  # 预测的5分钟目标时间
            'leverage': 3  # 高频交易使用3倍杠杆
        }
        
        # 高频交易配置
        self.config = {
            'leverage': 3,              # 3倍杠杆
            'prediction_interval': 5,   # 预测5分钟K线
            'check_interval': 30,       # 30秒检查一次
            'stop_loss_pct': 0.015,     # 1.5%止损（更紧）
            'take_profit_pct': 0.025,   # 2.5%止盈（更紧）
            'min_confidence': 0.70,     # 70%置信度（更高要求）
            'trading_fee': 0.0004,      # 0.04%交易手续费
            'min_price_change': 0.002   # 最小预期价格变化0.2%
        }
        
        # 初始化组件
        print("⚡ 初始化高频交易系统...")
        
        self.data_fetcher = BinanceDataFetcher()
        self.feature_engineer = FeatureEngineer()
        self.sentiment_analyzer = EnhancedRealSentimentAnalyzer()
        
        # 交易记录
        self.trade_history = []
        self.prediction_history = []
        self.last_trade_time = None
        
        # 状态文件
        self.state_file = "hft_trader_state.json"
        self.load_state()
        
        print("✅ 高频交易系统初始化完成")
        print(f"   预测目标: 下一个{self.config['prediction_interval']}分钟K线涨跌")
        print(f"   检查间隔: {self.config['check_interval']}秒")
        print(f"   杠杆倍数: {self.config['leverage']}x")
    
    def get_short_term_data(self) -> Dict:
        """获取短期数据用于高频预测"""
        try:
            # 获取当前价格
            current_price = self.data_fetcher.get_current_price('BTCUSDT', is_futures=True)
            
            # 获取最近的5分钟K线数据
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=12)  # 12小时数据确保足够

            df = self.data_fetcher.get_historical_data(
                'BTCUSDT', '5m', start_time.strftime('%Y-%m-%d'), is_futures=True
            )

            if len(df) < 50:
                raise Exception("数据不足")

            # 简化技术指标计算，避免复杂的特征工程
            # 直接计算基础指标
            def calculate_rsi(prices, window=14):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
                rs = gain / loss
                return 100 - (100 / (1 + rs))

            def calculate_bb_position(prices, window=20):
                sma = prices.rolling(window=window).mean()
                std = prices.rolling(window=window).std()
                upper = sma + (std * 2)
                lower = sma - (std * 2)
                return (prices - lower) / (upper - lower)

            # 计算基础指标
            rsi = calculate_rsi(df['close']).iloc[-1]
            bb_position = calculate_bb_position(df['close']).iloc[-1]

            # 简单MACD
            ema12 = df['close'].ewm(span=12).mean()
            ema26 = df['close'].ewm(span=26).mean()
            macd = ema12 - ema26
            macd_signal = macd.ewm(span=9).mean()
            macd_diff = (macd - macd_signal).iloc[-1]
            
            # 计算额外的短期指标
            recent_prices = df['close'].tail(12)  # 最近12个5分钟K线（1小时）
            
            # 短期动量
            momentum_5m = (current_price - df['close'].iloc[-2]) / df['close'].iloc[-2]
            momentum_15m = (current_price - df['close'].iloc[-4]) / df['close'].iloc[-4]
            momentum_30m = (current_price - df['close'].iloc[-7]) / df['close'].iloc[-7]
            
            # 价格波动率
            volatility = recent_prices.pct_change().std()
            
            # 成交量变化
            volume_ratio = df['volume'].iloc[-1] / df['volume'].tail(12).mean()
            
            return {
                'current_price': current_price,
                'timestamp': datetime.now(),
                'rsi': rsi if not pd.isna(rsi) else 50,
                'bb_position': bb_position if not pd.isna(bb_position) else 0.5,
                'macd_signal': macd_diff if not pd.isna(macd_diff) else 0,
                'momentum_5m': momentum_5m,
                'momentum_15m': momentum_15m,
                'momentum_30m': momentum_30m,
                'volatility': volatility,
                'volume_ratio': volume_ratio,
                'recent_high': recent_prices.max(),
                'recent_low': recent_prices.min(),
                'price_position': (current_price - recent_prices.min()) / (recent_prices.max() - recent_prices.min()) if recent_prices.max() != recent_prices.min() else 0.5
            }
            
        except Exception as e:
            print(f"❌ 短期数据获取失败: {str(e)}")
            return None
    
    def predict_next_5min_candle(self, market_data: Dict) -> Dict:
        """预测下一个5分钟K线涨跌"""
        try:
            # 提取关键指标
            current_price = market_data['current_price']
            rsi = market_data['rsi']
            bb_position = market_data['bb_position']
            momentum_5m = market_data['momentum_5m']
            momentum_15m = market_data['momentum_15m']
            momentum_30m = market_data['momentum_30m']
            volatility = market_data['volatility']
            volume_ratio = market_data['volume_ratio']
            price_position = market_data['price_position']
            
            # 预测因子计算
            factors = []
            factor_weights = []
            
            # 1. 短期动量因子（权重30%）
            momentum_score = 0.5
            if momentum_5m > 0.002:  # 5分钟上涨超过0.2%
                momentum_score += 0.3
            elif momentum_5m < -0.002:  # 5分钟下跌超过0.2%
                momentum_score -= 0.3
            
            if momentum_15m > 0.005:  # 15分钟上涨超过0.5%
                momentum_score += 0.2
            elif momentum_15m < -0.005:  # 15分钟下跌超过0.5%
                momentum_score -= 0.2
            
            factors.append(max(0, min(1, momentum_score)))
            factor_weights.append(0.30)
            
            # 2. RSI超买超卖因子（权重20%）
            rsi_score = 0.5
            if rsi < 30:
                rsi_score = 0.8  # 超卖，看涨
            elif rsi > 70:
                rsi_score = 0.2  # 超买，看跌
            elif rsi < 40:
                rsi_score = 0.65  # 偏超卖
            elif rsi > 60:
                rsi_score = 0.35  # 偏超买
            
            factors.append(rsi_score)
            factor_weights.append(0.20)
            
            # 3. 布林带位置因子（权重15%）
            bb_score = 0.5
            if bb_position < 0.2:
                bb_score = 0.75  # 接近下轨，看涨
            elif bb_position > 0.8:
                bb_score = 0.25  # 接近上轨，看跌
            elif bb_position < 0.3:
                bb_score = 0.65
            elif bb_position > 0.7:
                bb_score = 0.35
            
            factors.append(bb_score)
            factor_weights.append(0.15)
            
            # 4. 成交量确认因子（权重15%）
            volume_score = 0.5
            if volume_ratio > 1.5:  # 成交量放大
                if momentum_5m > 0:
                    volume_score = 0.7  # 放量上涨
                else:
                    volume_score = 0.3  # 放量下跌
            elif volume_ratio < 0.7:  # 成交量萎缩
                volume_score = 0.45  # 缺乏确认
            
            factors.append(volume_score)
            factor_weights.append(0.15)
            
            # 5. 价格位置因子（权重10%）
            position_score = 0.5
            if price_position > 0.8:
                position_score = 0.3  # 接近高点，看跌
            elif price_position < 0.2:
                position_score = 0.7  # 接近低点，看涨
            
            factors.append(position_score)
            factor_weights.append(0.10)
            
            # 6. 波动率因子（权重10%）
            volatility_score = 0.5
            if volatility > 0.01:  # 高波动
                volatility_score = 0.4  # 高波动时谨慎
            elif volatility < 0.003:  # 低波动
                volatility_score = 0.6  # 低波动时可能突破
            
            factors.append(volatility_score)
            factor_weights.append(0.10)
            
            # 加权计算最终预测概率
            prediction_probability = sum(f * w for f, w in zip(factors, factor_weights))
            
            # 计算置信度
            factor_std = np.std(factors)
            base_confidence = max(0.5, min(0.95, 1 - factor_std * 2))
            
            # 根据波动率调整置信度
            if volatility > 0.015:  # 极高波动
                base_confidence *= 0.8
            elif volatility < 0.002:  # 极低波动
                base_confidence *= 0.9
            
            # 确定预测方向和强度
            if prediction_probability > 0.6:
                direction = 'LONG'
                strength = (prediction_probability - 0.5) * 2
                expected_change = strength * 0.01  # 预期变化幅度
            elif prediction_probability < 0.4:
                direction = 'SHORT'
                strength = (0.5 - prediction_probability) * 2
                expected_change = strength * 0.01
            else:
                direction = 'WAIT'
                strength = 0
                expected_change = 0
            
            # 计算目标时间（下一个5分钟K线结束时间）
            current_time = datetime.now()
            next_5min = current_time.replace(second=0, microsecond=0)
            next_5min = next_5min.replace(minute=(next_5min.minute // 5 + 1) * 5)
            if next_5min.minute >= 60:
                next_5min = next_5min.replace(hour=next_5min.hour + 1, minute=0)
            
            prediction = {
                'timestamp': current_time.isoformat(),
                'target_time': next_5min.isoformat(),
                'direction': direction,
                'probability': prediction_probability,
                'confidence': base_confidence,
                'strength': strength,
                'expected_change': expected_change,
                'current_price': current_price,
                'factors': {
                    'momentum': factors[0],
                    'rsi': factors[1],
                    'bollinger': factors[2],
                    'volume': factors[3],
                    'position': factors[4],
                    'volatility': factors[5]
                },
                'market_conditions': {
                    'volatility': volatility,
                    'volume_ratio': volume_ratio,
                    'momentum_5m': momentum_5m,
                    'rsi': rsi
                }
            }
            
            # 记录预测历史
            self.prediction_history.append(prediction)
            
            return prediction
            
        except Exception as e:
            print(f"❌ 预测生成失败: {str(e)}")
            return {
                'direction': 'WAIT',
                'probability': 0.5,
                'confidence': 0.3,
                'strength': 0,
                'expected_change': 0,
                'error': str(e)
            }
    
    def calculate_hft_position_size(self, current_price: float, expected_change: float) -> float:
        """计算高频交易仓位大小"""
        # 基于预期变化和风险调整仓位
        base_risk = self.account['equity'] * 0.03  # 3%基础风险
        
        # 根据预期变化调整风险
        if abs(expected_change) > 0.005:  # 预期变化大于0.5%
            risk_multiplier = 1.2
        elif abs(expected_change) < 0.002:  # 预期变化小于0.2%
            risk_multiplier = 0.8
        else:
            risk_multiplier = 1.0
        
        adjusted_risk = base_risk * risk_multiplier
        
        # 计算仓位价值
        position_value = adjusted_risk / self.config['stop_loss_pct']
        
        # 限制最大仓位
        max_position_value = self.account['equity'] * 0.95
        position_value = min(position_value, max_position_value)
        
        # 计算BTC数量
        btc_size = position_value / current_price / self.config['leverage']
        
        return btc_size

    def open_hft_position(self, prediction: Dict, current_price: float) -> bool:
        """开启高频交易仓位"""
        try:
            if self.position['size'] != 0:
                return False

            # 检查交易间隔（高频交易允许更短间隔）
            if self.last_trade_time:
                time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
                if time_since_last < 60:  # 最少1分钟间隔
                    return False

            # 计算仓位
            btc_size = self.calculate_hft_position_size(current_price, prediction['expected_change'])

            # 计算交易费用
            position_value = btc_size * current_price * self.config['leverage']
            trading_fee = position_value * self.config['trading_fee']

            # 更新持仓
            target_time = datetime.fromisoformat(prediction['target_time'])

            self.position.update({
                'side': prediction['direction'],
                'size': btc_size,
                'entry_price': current_price,
                'entry_time': datetime.now(),
                'target_time': target_time
            })

            # 扣除手续费
            self.account['balance'] -= trading_fee

            # 记录交易
            self.trade_history.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'OPEN',
                'side': prediction['direction'],
                'size': btc_size,
                'price': current_price,
                'trading_fee': trading_fee,
                'prediction': prediction,
                'target_time': prediction['target_time']
            })

            self.last_trade_time = datetime.now()

            return True

        except Exception as e:
            print(f"❌ 开仓失败: {str(e)}")
            return False

    def close_hft_position(self, current_price: float, reason: str) -> bool:
        """平仓高频交易"""
        try:
            if self.position['size'] == 0:
                return False

            # 计算盈亏
            if self.position['side'] == 'LONG':
                price_diff = current_price - self.position['entry_price']
            else:
                price_diff = self.position['entry_price'] - current_price

            # 计算实际盈亏（考虑杠杆）
            pnl = self.position['size'] * price_diff * self.config['leverage']

            # 计算交易费用
            position_value = self.position['size'] * current_price * self.config['leverage']
            trading_fee = position_value * self.config['trading_fee']

            # 净盈亏
            net_pnl = pnl - trading_fee

            # 更新账户
            self.account['balance'] += net_pnl
            self.account['equity'] = self.account['balance']
            self.account['unrealized_pnl'] = 0

            # 计算预测准确性
            actual_change = price_diff / self.position['entry_price']
            predicted_direction = self.position['side']
            prediction_correct = (predicted_direction == 'LONG' and actual_change > 0) or (predicted_direction == 'SHORT' and actual_change < 0)

            # 记录交易
            self.trade_history.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'CLOSE',
                'side': self.position['side'],
                'size': self.position['size'],
                'entry_price': self.position['entry_price'],
                'exit_price': current_price,
                'net_pnl': net_pnl,
                'reason': reason,
                'hold_time': (datetime.now() - self.position['entry_time']).total_seconds() / 60,  # 分钟
                'actual_change': actual_change,
                'prediction_correct': prediction_correct,
                'target_reached': datetime.now() >= self.position['target_time']
            })

            self.last_trade_time = datetime.now()

            # 清空持仓
            self.position.update({
                'side': None,
                'size': 0.0,
                'entry_price': 0.0,
                'entry_time': None,
                'target_time': None
            })

            return True

        except Exception as e:
            print(f"❌ 平仓失败: {str(e)}")
            return False

    def check_hft_exit_conditions(self, current_price: float) -> bool:
        """检查高频交易退出条件"""
        if self.position['size'] == 0:
            return False

        # 1. 检查止损止盈
        if self.position['side'] == 'LONG':
            pnl_pct = (current_price - self.position['entry_price']) / self.position['entry_price']
        else:
            pnl_pct = (self.position['entry_price'] - current_price) / self.position['entry_price']

        # 止损
        if pnl_pct <= -self.config['stop_loss_pct']:
            self.close_hft_position(current_price, 'STOP_LOSS')
            return True

        # 止盈
        if pnl_pct >= self.config['take_profit_pct']:
            self.close_hft_position(current_price, 'TAKE_PROFIT')
            return True

        # 2. 检查时间到期（5分钟K线结束）
        if datetime.now() >= self.position['target_time']:
            self.close_hft_position(current_price, 'TIME_EXPIRED')
            return True

        # 3. 检查反向信号（可选，更激进的策略）
        # 这里可以添加反向信号检测逻辑

        return False

    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.position['size'] == 0:
            self.account['unrealized_pnl'] = 0
            self.account['equity'] = self.account['balance']
            return

        # 计算未实现盈亏
        if self.position['side'] == 'LONG':
            price_diff = current_price - self.position['entry_price']
        else:
            price_diff = self.position['entry_price'] - current_price

        unrealized_pnl = self.position['size'] * price_diff * self.config['leverage']

        self.account['unrealized_pnl'] = unrealized_pnl
        self.account['equity'] = self.account['balance'] + unrealized_pnl

    def print_hft_status(self, market_data: Dict, prediction: Dict):
        """打印高频交易状态"""
        current_time = datetime.now().strftime('%H:%M:%S')
        current_price = market_data['current_price']

        print(f"\n⚡ {current_time} | BTC: ${current_price:,.0f}")
        print("=" * 100)

        # 账户状态
        total_return = (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        print(f"💰 账户: ${self.account['balance']:.2f} + ${self.account['unrealized_pnl']:+.2f} = ${self.account['equity']:.2f} ({total_return:+.2f}%)")

        # 持仓状态
        if self.position['size'] != 0:
            hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 60
            remaining_time = (self.position['target_time'] - datetime.now()).total_seconds() / 60
            pnl_pct = self.account['unrealized_pnl'] / (self.position['size'] * self.position['entry_price'] * self.config['leverage']) * 100

            print(f"📊 持仓: 🔥 {self.position['side']} {self.position['size']:.6f} BTC @ ${self.position['entry_price']:,.0f}")
            print(f"   ⏱️ 持仓: {hold_time:.1f}分钟 | 剩余: {remaining_time:.1f}分钟 | 盈亏: {pnl_pct:+.1f}%")
        else:
            print(f"📊 持仓: 💤 空仓")

        # 预测状态
        target_time = datetime.fromisoformat(prediction['target_time']).strftime('%H:%M:%S')
        print(f"🎯 5分钟预测: {prediction['direction']} (概率{prediction['probability']:.1%}, 置信度{prediction['confidence']:.1%})")
        print(f"   📈 预期变化: {prediction['expected_change']:+.2%} | 目标时间: {target_time}")

        # 市场条件
        conditions = prediction.get('market_conditions', {})
        print(f"📊 市场: 波动率{conditions.get('volatility', 0):.3f} | 成交量比{conditions.get('volume_ratio', 1):.1f} | 5分钟动量{conditions.get('momentum_5m', 0):+.2%}")

        # 因子分解
        factors = prediction.get('factors', {})
        print(f"🔍 因子: 动量{factors.get('momentum', 0.5):.2f} | RSI{factors.get('rsi', 0.5):.2f} | 布林{factors.get('bollinger', 0.5):.2f} | 成交量{factors.get('volume', 0.5):.2f}")

    def save_state(self):
        """保存状态"""
        state_data = {
            'account': self.account,
            'position': {k: v.isoformat() if isinstance(v, datetime) else v
                        for k, v in self.position.items()},
            'trade_history': self.trade_history,
            'prediction_history': self.prediction_history[-50:],  # 只保存最近50个预测
            'last_trade_time': self.last_trade_time.isoformat() if self.last_trade_time else None
        }

        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, default=str)

    def load_state(self):
        """加载状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                self.account = state_data.get('account', self.account)

                position_data = state_data.get('position', {})
                if position_data.get('entry_time'):
                    position_data['entry_time'] = datetime.fromisoformat(position_data['entry_time'])
                if position_data.get('target_time'):
                    position_data['target_time'] = datetime.fromisoformat(position_data['target_time'])
                self.position.update(position_data)

                self.trade_history = state_data.get('trade_history', [])
                self.prediction_history = state_data.get('prediction_history', [])

                if state_data.get('last_trade_time'):
                    self.last_trade_time = datetime.fromisoformat(state_data['last_trade_time'])

                if self.account['balance'] != self.initial_balance or self.trade_history:
                    print(f"📂 加载历史状态: 余额${self.account['balance']:.2f}, {len(self.trade_history)}笔交易")

            except Exception as e:
                print(f"⚠️ 状态加载失败: {str(e)}")

    def run_hft_cycle(self) -> bool:
        """运行一个高频交易周期"""
        try:
            # 1. 获取短期市场数据
            market_data = self.get_short_term_data()
            if not market_data:
                print("❌ 市场数据获取失败")
                return False

            current_price = market_data['current_price']

            # 2. 更新未实现盈亏
            self.update_unrealized_pnl(current_price)

            # 3. 检查退出条件
            if self.check_hft_exit_conditions(current_price):
                print(f"🔔 触发退出条件，已自动平仓")
                self.save_state()
                return True

            # 4. 生成5分钟预测
            prediction = self.predict_next_5min_candle(market_data)

            # 5. 执行交易决策
            if self.position['size'] == 0:  # 无持仓时考虑开仓
                if prediction['direction'] in ['LONG', 'SHORT']:
                    if prediction['confidence'] >= self.config['min_confidence']:
                        if abs(prediction['expected_change']) >= self.config['min_price_change']:
                            if self.open_hft_position(prediction, current_price):
                                target_time = datetime.fromisoformat(prediction['target_time']).strftime('%H:%M:%S')
                                print(f"🔔 开仓: {prediction['direction']} {self.position['size']:.6f} BTC @ ${current_price:,.0f} (目标{target_time})")
                            else:
                                print("⚠️ 开仓失败")
                        else:
                            print(f"⚠️ 预期变化太小: {prediction['expected_change']:+.2%}")
                    else:
                        print(f"⚠️ 置信度不足: {prediction['confidence']:.1%} < {self.config['min_confidence']:.1%}")

            # 6. 打印状态
            self.print_hft_status(market_data, prediction)

            # 7. 保存状态
            self.save_state()

            return True

        except Exception as e:
            print(f"❌ 高频交易周期失败: {str(e)}")
            return False

    def get_hft_statistics(self) -> Dict:
        """获取高频交易统计"""
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']

        if not closed_trades:
            return {
                'total_trades': 0,
                'prediction_accuracy': 0,
                'avg_hold_time': 0,
                'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
            }

        # 基础统计
        total_trades = len(closed_trades)
        winning_trades = [t for t in closed_trades if t['net_pnl'] > 0]
        correct_predictions = [t for t in closed_trades if t.get('prediction_correct', False)]

        # 计算各种统计指标
        win_rate = len(winning_trades) / total_trades
        prediction_accuracy = len(correct_predictions) / total_trades

        total_pnl = sum(t['net_pnl'] for t in closed_trades)
        avg_pnl = total_pnl / total_trades
        avg_hold_time = sum(t['hold_time'] for t in closed_trades) / total_trades

        max_win = max((t['net_pnl'] for t in winning_trades), default=0)
        max_loss = min((t['net_pnl'] for t in closed_trades), default=0)

        # 按退出原因分类
        exit_reasons = {}
        for trade in closed_trades:
            reason = trade['reason']
            if reason not in exit_reasons:
                exit_reasons[reason] = 0
            exit_reasons[reason] += 1

        # 时间相关统计
        time_expired_trades = [t for t in closed_trades if t['reason'] == 'TIME_EXPIRED']
        target_reached_rate = len([t for t in closed_trades if t.get('target_reached', False)]) / total_trades

        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'win_rate': win_rate,
            'prediction_accuracy': prediction_accuracy,
            'total_pnl': total_pnl,
            'avg_pnl': avg_pnl,
            'max_win': max_win,
            'max_loss': max_loss,
            'avg_hold_time': avg_hold_time,
            'exit_reasons': exit_reasons,
            'time_expired_trades': len(time_expired_trades),
            'target_reached_rate': target_reached_rate,
            'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        }

def run_high_frequency_trading():
    """运行高频交易系统"""
    print("⚡ 高频交易系统 - 5分钟K线涨跌预测")
    print("=" * 80)
    print("🎯 专注预测下一个5分钟K线涨跌")
    print("⚡ 30秒检测间隔，快速响应市场")
    print("💰 50美元模拟账户，3倍杠杆")
    print("")

    # 获取参数
    try:
        duration = input("运行时长（小时，默认2）: ").strip()
        duration = int(duration) if duration else 2

        check_interval = input("检测间隔（秒，默认30）: ").strip()
        check_interval = int(check_interval) if check_interval else 30

    except:
        duration = 2
        check_interval = 30

    print(f"\n⚡ 启动高频交易系统...")
    print(f"⏰ 运行时长: {duration}小时")
    print(f"🔄 检测间隔: {check_interval}秒")
    print(f"📊 预计检测次数: {duration * 3600 // check_interval}次")

    # 创建高频交易器
    trader = HighFrequencyTrader(50.0)

    start_time = datetime.now()
    end_time = start_time + timedelta(hours=duration)
    cycle_count = 0

    try:
        while datetime.now() < end_time:
            cycle_count += 1

            # 计算进度
            elapsed_time = (datetime.now() - start_time).total_seconds() / 3600
            progress = elapsed_time / duration * 100
            remaining_hours = duration - elapsed_time

            print(f"\n⚡ 第 {cycle_count} 次检测 | 进度: {progress:.1f}% | 剩余: {remaining_hours:.1f}小时")

            # 运行高频交易周期
            trader.run_hft_cycle()

            # 显示简要统计
            stats = trader.get_hft_statistics()
            print(f"📈 当前统计: 权益${trader.account['equity']:.2f} | 收益{stats['total_return']:+.2f}% | 交易{stats['total_trades']}笔")
            if stats['total_trades'] > 0:
                print(f"   胜率{stats['win_rate']:.1%} | 预测准确率{stats['prediction_accuracy']:.1%} | 平均持仓{stats['avg_hold_time']:.1f}分钟")

            # 等待下一个检测周期
            remaining_time = (end_time - datetime.now()).total_seconds()
            if remaining_time > check_interval:
                print(f"⏳ 等待 {check_interval} 秒...")
                time.sleep(check_interval)
            else:
                print(f"⏳ 剩余时间不足，等待 {remaining_time:.0f} 秒...")
                time.sleep(max(0, remaining_time))
                break

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断")

    # 显示最终结果
    print(f"\n🏁 高频交易结束")
    print("=" * 80)

    final_stats = trader.get_hft_statistics()
    actual_runtime = (datetime.now() - start_time).total_seconds() / 3600

    print(f"📊 最终统计:")
    print(f"   运行时长: {actual_runtime:.1f}小时")
    print(f"   检测次数: {cycle_count}次")
    print(f"   最终权益: ${trader.account['equity']:.2f}")
    print(f"   总收益率: {final_stats['total_return']:+.2f}%")
    print(f"   总交易次数: {final_stats['total_trades']}")

    if final_stats['total_trades'] > 0:
        print(f"   胜率: {final_stats['win_rate']:.1%}")
        print(f"   预测准确率: {final_stats['prediction_accuracy']:.1%}")
        print(f"   平均盈亏: ${final_stats['avg_pnl']:+.2f}")
        print(f"   最大盈利: ${final_stats['max_win']:+.2f}")
        print(f"   最大亏损: ${final_stats['max_loss']:+.2f}")
        print(f"   平均持仓时间: {final_stats['avg_hold_time']:.1f}分钟")
        print(f"   目标时间到达率: {final_stats['target_reached_rate']:.1%}")

        print(f"\n📊 退出原因分布:")
        for reason, count in final_stats['exit_reasons'].items():
            percentage = count / final_stats['total_trades'] * 100
            print(f"   {reason}: {count}次 ({percentage:.1f}%)")

    print(f"\n💾 数据已保存到: {trader.state_file}")

    return trader

if __name__ == "__main__":
    print("⚡ 高频交易系统 - 5分钟K线涨跌预测")
    print("基于第三阶段完全真实化系统")
    print("")

    try:
        trader = run_high_frequency_trading()
        print(f"\n🎉 高频交易完成！")

    except KeyboardInterrupt:
        print(f"\n👋 用户退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
