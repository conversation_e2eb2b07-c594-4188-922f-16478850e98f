#!/usr/bin/env python3
"""
改进的训练和回测脚本 - 解决数据泄露和过拟合问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, balanced_accuracy_score
from sklearn.model_selection import TimeSeriesSplit

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

def improved_train_with_walk_forward(symbol='BTCUSDT', months_back=36):
    """
    改进的训练方法：使用前向验证避免数据泄露
    """
    print(f"🚀 改进训练 {symbol}...")
    print("⚠️  使用前向验证避免数据泄露")
    
    # 1. 获取长期数据
    print("📊 获取长期数据...")
    start_date = (datetime.now() - timedelta(days=months_back*30)).strftime('%Y-%m-%d')
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date)
    print(f"✅ 获取到 {len(df)} 条数据")
    
    # 2. 特征工程
    print("🔧 特征工程...")
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    # 3. 数据准备
    X = df_features.drop(columns=['target'])
    y = df_features['target']
    
    # 移除原始列和时间戳（避免数据泄露）
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    # 清理数据
    mask = ~(X.isna().any(axis=1) | y.isna())
    X = X[mask]
    y = y[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    print(f"✅ 清理后: {len(X)} 样本")
    
    # 4. 简化类别（只保留3个主要状态）
    print("🔄 简化为3个交易状态...")
    
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)
    
    unique, counts = np.unique(y_encoded, return_counts=True)
    
    # 只保留最大的2个类别，其他合并为"观望"
    top2_indices = np.argsort(counts)[-2:]
    top2_classes = unique[top2_indices]
    
    print(f"保留前2大类别: {top2_classes}")
    
    # 重新映射为3个状态：0=买入, 1=卖出, 2=观望
    y_simple = np.full_like(y_encoded, 2)  # 默认观望
    
    # 假设最大的类别是横盘（观望），第二大的是上涨（买入）
    largest_class = unique[np.argmax(counts)]
    second_largest_class = top2_classes[0] if top2_classes[1] == largest_class else top2_classes[1]
    
    # 重新定义：
    # 0 = 买入信号（原第二大类别）
    # 1 = 卖出信号（其他小类别合并）
    # 2 = 观望信号（原最大类别）
    
    y_simple[y_encoded == second_largest_class] = 0  # 买入
    y_simple[y_encoded == largest_class] = 2         # 观望
    
    # 将其他小类别合并为卖出信号
    other_mask = ~np.isin(y_encoded, [largest_class, second_largest_class])
    y_simple[other_mask] = 1  # 卖出
    
    unique_simple, counts_simple = np.unique(y_simple, return_counts=True)
    print(f"简化后分布: {dict(zip(['买入', '卖出', '观望'], counts_simple))}")
    
    # 5. 平衡数据（保守方法）
    print("⚖️ 平衡数据...")
    
    # 每个类别最多500样本（减少过拟合）
    max_samples = 500
    
    balanced_indices = []
    for class_id in unique_simple:
        class_indices = np.where(y_simple == class_id)[0]
        if len(class_indices) > max_samples:
            selected_indices = np.random.choice(class_indices, max_samples, replace=False)
        else:
            selected_indices = class_indices
        balanced_indices.extend(selected_indices)
    
    balanced_indices = np.array(balanced_indices)
    X_balanced = X.iloc[balanced_indices]
    y_balanced = y_simple[balanced_indices]
    df_balanced = df_clean.iloc[balanced_indices]
    
    print(f"平衡后样本: {len(X_balanced)}")
    
    # 6. 前向验证训练
    print("🔍 前向验证训练...")
    
    # 按时间排序
    time_index = df_balanced.index
    sort_indices = np.argsort(time_index)
    
    X_sorted = X_balanced.iloc[sort_indices]
    y_sorted = y_balanced[sort_indices]
    time_sorted = time_index[sort_indices]
    
    # 使用前80%数据训练，后20%测试
    split_point = int(len(X_sorted) * 0.8)
    
    X_train = X_sorted.iloc[:split_point]
    X_test = X_sorted.iloc[split_point:]
    y_train = y_sorted[:split_point]
    y_test = y_sorted[split_point:]
    
    print(f"训练集: {len(X_train)}, 测试集: {len(X_test)}")
    
    # 7. 特征缩放
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 8. 训练保守模型
    print("🎯 训练保守模型...")
    
    if HAS_XGB:
        model = xgb.XGBClassifier(
            n_estimators=30,        # 大幅减少
            max_depth=3,            # 减少深度
            learning_rate=0.05,     # 降低学习率
            subsample=0.7,          # 增加随机性
            colsample_bytree=0.7,
            reg_alpha=2.0,          # 强正则化
            reg_lambda=2.0,
            random_state=42,
            n_jobs=-1
        )
    else:
        model = RandomForestClassifier(
            n_estimators=30,
            max_depth=5,
            min_samples_split=20,
            min_samples_leaf=10,
            random_state=42,
            n_jobs=-1
        )
    
    model.fit(X_train_scaled, y_train)
    
    # 9. 评估
    y_train_pred = model.predict(X_train_scaled)
    y_test_pred = model.predict(X_test_scaled)
    
    train_acc = accuracy_score(y_train, y_train_pred)
    test_acc = accuracy_score(y_test, y_test_pred)
    test_balanced = balanced_accuracy_score(y_test, y_test_pred)
    
    print(f"\n📊 模型性能:")
    print(f"训练准确率: {train_acc:.4f}")
    print(f"测试准确率: {test_acc:.4f}")
    print(f"平衡准确率: {test_balanced:.4f}")
    print(f"过拟合程度: {train_acc - test_acc:.4f}")
    
    print("\n📋 分类报告:")
    target_names = ['买入', '卖出', '观望']
    print(classification_report(y_test, y_test_pred, target_names=target_names))
    
    # 10. 保存改进模型
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = f"models/improved_model_{symbol}_{timestamp}.joblib"
    
    import os
    os.makedirs("models", exist_ok=True)
    
    joblib.dump({
        'model': model,
        'scaler': scaler,
        'label_encoder': label_encoder,
        'feature_names': X.columns.tolist(),
        'class_mapping': {0: '买入', 1: '卖出', 2: '观望'}
    }, model_path)
    
    print(f"💾 改进模型已保存: {model_path}")
    
    return model_path

def conservative_backtest(model_path, symbol='BTCUSDT', test_months=3):
    """
    保守回测策略
    """
    print(f"\n🔍 保守回测 {symbol}...")
    
    # 加载模型
    model_data = joblib.load(model_path)
    model = model_data['model']
    scaler = model_data['scaler']
    
    # 获取测试数据（确保不与训练数据重叠）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=test_months*30)
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date.strftime('%Y-%m-%d'))
    
    # 特征工程
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    X = df_features.drop(columns=['target'])
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    mask = ~X.isna().any(axis=1)
    X = X[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    # 预测
    X_scaled = scaler.transform(X)
    predictions = model.predict(X_scaled)
    prediction_proba = model.predict_proba(X_scaled)
    confidences = np.max(prediction_proba, axis=1)
    
    # 保守交易策略
    capital = 10000
    position = 0  # 0=空仓, 1=多头
    trades = []
    equity_curve = []
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    for i in range(len(predictions)):
        signal = predictions[i]
        price = prices[i]
        confidence = confidences[i]
        
        # 只在高置信度时交易
        if confidence < 0.8:
            signal = 2  # 强制观望
        
        # 买入信号
        if signal == 0 and position == 0 and confidence > 0.85:
            position = 1
            entry_price = price
            trades.append({
                'type': '买入',
                'price': price,
                'time': timestamps[i],
                'confidence': confidence
            })
        
        # 卖出信号或止损
        elif (signal == 1 or confidence < 0.7) and position == 1:
            position = 0
            pnl_ratio = (price - entry_price) / entry_price
            capital = capital * (1 + pnl_ratio - 0.002)  # 扣除手续费
            
            trades.append({
                'type': '卖出',
                'price': price,
                'entry_price': entry_price,
                'pnl_ratio': pnl_ratio,
                'time': timestamps[i],
                'confidence': confidence
            })
        
        # 记录权益
        if position == 1:
            current_value = capital * (price / entry_price)
        else:
            current_value = capital
        
        equity_curve.append({
            'time': timestamps[i],
            'value': current_value,
            'price': price,
            'position': position,
            'signal': signal,
            'confidence': confidence
        })
    
    # 计算结果
    final_capital = capital
    if position == 1:  # 最后强制平仓
        final_capital = capital * (prices[-1] / entry_price)
    
    total_return = (final_capital - 10000) / 10000
    buy_hold_return = (prices[-1] - prices[0]) / prices[0]
    
    profitable_trades = len([t for t in trades if t.get('pnl_ratio', 0) > 0])
    total_completed_trades = len([t for t in trades if 'pnl_ratio' in t])
    win_rate = profitable_trades / total_completed_trades if total_completed_trades > 0 else 0
    
    print(f"\n📊 保守回测结果:")
    print(f"总收益率: {total_return:.2%}")
    print(f"基准收益率: {buy_hold_return:.2%}")
    print(f"超额收益: {total_return - buy_hold_return:.2%}")
    print(f"完成交易: {total_completed_trades}")
    print(f"胜率: {win_rate:.2%}")
    
    return {
        'total_return': total_return,
        'benchmark_return': buy_hold_return,
        'trades': trades,
        'equity_curve': equity_curve
    }

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    
    # 1. 改进训练
    print("第一步：改进训练")
    model_path = improved_train_with_walk_forward(symbol, 36)
    
    # 2. 保守回测
    print("\n第二步：保守回测")
    results = conservative_backtest(model_path, symbol, 3)
    
    print(f"\n🎯 如果结果仍然不理想，说明:")
    print(f"1. 市场可能不适合这种预测方法")
    print(f"2. 需要重新考虑特征工程和目标定义")
    print(f"3. 可能需要更复杂的策略或更长的训练期")
