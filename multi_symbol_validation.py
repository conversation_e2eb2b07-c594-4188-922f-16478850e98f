#!/usr/bin/env python3
"""
多币种验证脚本 - 测试模型泛化能力
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from final_optimized_strategy import create_binary_trend_model, adaptive_backtest

def validate_multiple_symbols():
    """
    在多个币种上验证策略表现
    """
    print("🚀 开始多币种验证测试")
    print("=" * 60)
    
    # 测试的币种列表
    symbols = [
        'BTCUSDT',   # 比特币 - 已知表现良好
        'ETHUSDT',   # 以太坊 - 第二大币种
        'BNBUSDT',   # 币安币 - 交易所代币
        'ADAUSDT',   # 卡尔达诺 - 智能合约平台
        'SOLUSDT',   # Solana - 高性能区块链
        'XRPUSDT',   # 瑞波币 - 支付代币
    ]
    
    results = {}
    
    for i, symbol in enumerate(symbols, 1):
        print(f"\n📊 [{i}/{len(symbols)}] 测试 {symbol}...")
        print("-" * 40)
        
        try:
            # 训练模型
            print(f"🎯 训练 {symbol} 模型...")
            model_path = create_binary_trend_model(symbol, 24)
            
            # 回测验证
            print(f"📈 回测 {symbol} 策略...")
            result = adaptive_backtest(model_path, symbol, 3)
            
            # 保存结果
            results[symbol] = {
                'total_return': result['total_return'],
                'benchmark_return': result['benchmark_return'],
                'excess_return': result['total_return'] - result['benchmark_return'],
                'win_rate': result['win_rate'],
                'max_drawdown': result['max_drawdown'],
                'total_trades': result['total_trades'],
                'final_capital': result['final_capital'],
                'status': 'success'
            }
            
            print(f"✅ {symbol} 完成: 收益{result['total_return']:.2%}, 胜率{result['win_rate']:.2%}")
            
        except Exception as e:
            print(f"❌ {symbol} 失败: {str(e)}")
            results[symbol] = {
                'status': 'failed',
                'error': str(e)
            }
            continue
    
    # 生成综合报告
    print("\n" + "=" * 60)
    print("📊 多币种验证报告")
    print("=" * 60)
    
    successful_results = {k: v for k, v in results.items() if v.get('status') == 'success'}
    
    if successful_results:
        # 创建结果表格
        print(f"{'币种':<10} {'收益率':<10} {'超额收益':<10} {'胜率':<10} {'回撤':<10} {'交易次数':<10}")
        print("-" * 70)
        
        total_returns = []
        excess_returns = []
        win_rates = []
        drawdowns = []
        
        for symbol, result in successful_results.items():
            total_return = result['total_return']
            excess_return = result['excess_return']
            win_rate = result['win_rate']
            max_drawdown = result['max_drawdown']
            total_trades = result['total_trades']
            
            print(f"{symbol:<10} {total_return:>8.2%} {excess_return:>8.2%} {win_rate:>8.2%} "
                  f"{max_drawdown:>8.2%} {total_trades:>8d}")
            
            total_returns.append(total_return)
            excess_returns.append(excess_return)
            win_rates.append(win_rate)
            drawdowns.append(max_drawdown)
        
        # 统计摘要
        print("-" * 70)
        print(f"{'平均':<10} {np.mean(total_returns):>8.2%} {np.mean(excess_returns):>8.2%} "
              f"{np.mean(win_rates):>8.2%} {np.mean(drawdowns):>8.2%}")
        print(f"{'中位数':<10} {np.median(total_returns):>8.2%} {np.median(excess_returns):>8.2%} "
              f"{np.median(win_rates):>8.2%} {np.median(drawdowns):>8.2%}")
        print(f"{'标准差':<10} {np.std(total_returns):>8.2%} {np.std(excess_returns):>8.2%} "
              f"{np.std(win_rates):>8.2%} {np.std(drawdowns):>8.2%}")
        
        # 成功率分析
        profitable_count = sum(1 for r in total_returns if r > 0)
        beat_benchmark_count = sum(1 for r in excess_returns if r > 0)
        good_winrate_count = sum(1 for r in win_rates if r > 0.5)
        
        print(f"\n📈 策略表现分析:")
        print(f"   盈利币种: {profitable_count}/{len(successful_results)} ({profitable_count/len(successful_results)*100:.1f}%)")
        print(f"   跑赢基准: {beat_benchmark_count}/{len(successful_results)} ({beat_benchmark_count/len(successful_results)*100:.1f}%)")
        print(f"   胜率>50%: {good_winrate_count}/{len(successful_results)} ({good_winrate_count/len(successful_results)*100:.1f}%)")
        
        # 泛化能力评估
        print(f"\n🎯 泛化能力评估:")
        if profitable_count >= len(successful_results) * 0.7:
            print("✅ 优秀 - 大部分币种都能盈利")
        elif profitable_count >= len(successful_results) * 0.5:
            print("✅ 良好 - 半数以上币种盈利")
        else:
            print("⚠️  一般 - 需要进一步优化")
        
        if np.std(total_returns) < 0.1:
            print("✅ 稳定性良好 - 不同币种表现一致")
        else:
            print("⚠️  稳定性一般 - 不同币种表现差异较大")
    
    else:
        print("❌ 没有成功的测试结果")
    
    # 失败分析
    failed_results = {k: v for k, v in results.items() if v.get('status') == 'failed'}
    if failed_results:
        print(f"\n❌ 失败币种分析:")
        for symbol, result in failed_results.items():
            print(f"   {symbol}: {result['error']}")
    
    return results

def quick_validation():
    """
    快速验证 - 只测试几个主要币种
    """
    print("⚡ 快速验证模式")
    print("=" * 40)
    
    # 只测试主要币种
    symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
    
    results = {}
    
    for symbol in symbols:
        print(f"\n📊 测试 {symbol}...")
        
        try:
            # 使用昨天已训练好的BTCUSDT模型，或重新训练
            if symbol == 'BTCUSDT':
                # 尝试使用已有模型
                import glob
                model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
                if model_files:
                    model_path = max(model_files, key=lambda x: x.split('_')[-1])
                    print(f"使用已有模型: {model_path}")
                else:
                    model_path = create_binary_trend_model(symbol, 24)
            else:
                model_path = create_binary_trend_model(symbol, 24)
            
            result = adaptive_backtest(model_path, symbol, 3)
            results[symbol] = result
            
            print(f"✅ {symbol}: 收益{result['total_return']:.2%}, 胜率{result['win_rate']:.2%}")
            
        except Exception as e:
            print(f"❌ {symbol} 失败: {str(e)}")
            results[symbol] = {'error': str(e)}
    
    return results

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'quick':
        # 快速模式
        results = quick_validation()
    else:
        # 完整验证
        print("提示: 完整验证需要较长时间，如需快速测试请使用: python multi_symbol_validation.py quick")
        
        user_input = input("是否继续完整验证? (y/n): ")
        if user_input.lower() in ['y', 'yes', '是']:
            results = validate_multiple_symbols()
        else:
            print("执行快速验证...")
            results = quick_validation()
    
    print(f"\n🎯 验证完成! 结果已保存到内存中。")
    print(f"建议: 根据结果选择表现最好的币种进行进一步优化。")
