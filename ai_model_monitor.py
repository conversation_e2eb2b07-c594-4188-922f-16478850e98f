#!/usr/bin/env python3
"""
AI模型性能监控系统 - 第二阶段模型管理
自动监控模型性能、检测漂移、触发重训练
"""

import pandas as pd
import numpy as np
import joblib
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import os
from scipy import stats
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import warnings
warnings.filterwarnings('ignore')

class AIModelMonitor:
    """
    AI模型性能监控器
    """
    
    def __init__(self, model_path: str = "models/", monitoring_window: int = 100):
        self.model_path = model_path
        self.monitoring_window = monitoring_window  # 监控窗口大小
        
        # 性能阈值
        self.performance_thresholds = {
            'accuracy_min': 0.55,      # 最低准确率
            'precision_min': 0.50,     # 最低精确率
            'recall_min': 0.50,        # 最低召回率
            'f1_min': 0.52,           # 最低F1分数
            'drift_threshold': 0.1,    # 漂移阈值
            'performance_decline': 0.05 # 性能下降阈值
        }
        
        # 监控数据
        self.prediction_history = []
        self.performance_history = []
        self.drift_history = []
        
        # 基准性能 (训练时的性能)
        self.baseline_performance = None
        
        # 模型信息
        self.current_model = None
        self.model_metadata = {}
        
        # 监控状态
        self.monitoring_active = True
        self.last_evaluation = None
        self.alerts = []
        
        print(f"🔍 AI模型监控系统初始化")
        print(f"   监控窗口: {self.monitoring_window}个预测")
        print(f"   性能阈值: 准确率>{self.performance_thresholds['accuracy_min']:.1%}")
    
    def load_model_metadata(self, model_file: str) -> bool:
        """加载模型元数据"""
        try:
            if os.path.exists(model_file):
                model_data = joblib.load(model_file)
                
                if isinstance(model_data, dict) and 'metadata' in model_data:
                    self.model_metadata = model_data['metadata']
                    self.baseline_performance = self.model_metadata.get('training_performance', {})
                    
                    print(f"✅ 模型元数据加载成功")
                    print(f"   训练时间: {self.model_metadata.get('training_date', 'Unknown')}")
                    print(f"   基准准确率: {self.baseline_performance.get('accuracy', 0):.1%}")
                    
                    return True
                else:
                    print(f"⚠️ 模型文件格式不包含元数据")
                    return False
            else:
                print(f"❌ 模型文件不存在: {model_file}")
                return False
                
        except Exception as e:
            print(f"❌ 模型元数据加载失败: {str(e)}")
            return False
    
    def record_prediction(self, features: np.ndarray, prediction: float, 
                         actual_result: Optional[float] = None, 
                         confidence: Optional[float] = None) -> Dict:
        """记录预测结果"""
        
        prediction_record = {
            'timestamp': datetime.now().isoformat(),
            'prediction': prediction,
            'actual_result': actual_result,
            'confidence': confidence,
            'features_hash': hash(str(features.flatten())),  # 特征哈希用于漂移检测
            'features_mean': np.mean(features),
            'features_std': np.std(features)
        }
        
        self.prediction_history.append(prediction_record)
        
        # 保持监控窗口大小
        if len(self.prediction_history) > self.monitoring_window * 2:
            self.prediction_history = self.prediction_history[-self.monitoring_window:]
        
        # 如果有实际结果，进行性能评估
        if actual_result is not None:
            self._evaluate_prediction_performance()
        
        # 检测特征漂移
        drift_score = self._detect_feature_drift()
        
        return {
            'prediction_recorded': True,
            'drift_score': drift_score,
            'total_predictions': len(self.prediction_history),
            'performance_evaluation': self.last_evaluation
        }
    
    def _evaluate_prediction_performance(self) -> Dict:
        """评估预测性能"""
        # 获取有实际结果的预测
        valid_predictions = [p for p in self.prediction_history 
                           if p['actual_result'] is not None]
        
        if len(valid_predictions) < 10:
            return {'status': 'insufficient_data', 'count': len(valid_predictions)}
        
        # 提取预测和实际结果
        predictions = [p['prediction'] for p in valid_predictions]
        actuals = [p['actual_result'] for p in valid_predictions]
        
        # 转换为二分类 (上涨/下跌)
        pred_binary = [1 if p > 0.5 else 0 for p in predictions]
        actual_binary = [1 if a > 0.5 else 0 for a in actuals]
        
        # 计算性能指标
        try:
            accuracy = accuracy_score(actual_binary, pred_binary)
            precision = precision_score(actual_binary, pred_binary, zero_division=0)
            recall = recall_score(actual_binary, pred_binary, zero_division=0)
            f1 = f1_score(actual_binary, pred_binary, zero_division=0)
            
            # 计算置信度校准
            confidence_scores = [p['confidence'] for p in valid_predictions if p['confidence'] is not None]
            calibration_score = self._calculate_calibration_score(pred_binary, actual_binary, confidence_scores)
            
            performance = {
                'timestamp': datetime.now().isoformat(),
                'sample_size': len(valid_predictions),
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'calibration_score': calibration_score,
                'window_start': valid_predictions[0]['timestamp'],
                'window_end': valid_predictions[-1]['timestamp']
            }
            
            # 记录性能历史
            self.performance_history.append(performance)
            
            # 保持历史记录在合理范围
            if len(self.performance_history) > 50:
                self.performance_history = self.performance_history[-50:]
            
            # 检查性能警报
            alerts = self._check_performance_alerts(performance)
            
            self.last_evaluation = performance
            
            return {
                'status': 'success',
                'performance': performance,
                'alerts': alerts,
                'baseline_comparison': self._compare_with_baseline(performance)
            }
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _calculate_calibration_score(self, predictions: List[int], 
                                   actuals: List[int], 
                                   confidences: List[float]) -> float:
        """计算置信度校准分数"""
        if not confidences or len(confidences) != len(predictions):
            return 0.5
        
        # 简化的校准评估
        # 高置信度预测应该有更高的准确率
        high_conf_indices = [i for i, c in enumerate(confidences) if c > 0.7]
        low_conf_indices = [i for i, c in enumerate(confidences) if c < 0.5]
        
        if len(high_conf_indices) > 5 and len(low_conf_indices) > 5:
            high_conf_accuracy = sum(predictions[i] == actuals[i] for i in high_conf_indices) / len(high_conf_indices)
            low_conf_accuracy = sum(predictions[i] == actuals[i] for i in low_conf_indices) / len(low_conf_indices)
            
            # 校准分数：高置信度准确率应该高于低置信度
            calibration_score = high_conf_accuracy - low_conf_accuracy + 0.5
            return max(0, min(1, calibration_score))
        
        return 0.5
    
    def _detect_feature_drift(self) -> float:
        """检测特征漂移"""
        if len(self.prediction_history) < 20:
            return 0.0
        
        # 获取最近的特征统计
        recent_records = self.prediction_history[-20:]
        older_records = self.prediction_history[-40:-20] if len(self.prediction_history) >= 40 else []
        
        if not older_records:
            return 0.0
        
        # 比较特征分布
        recent_means = [r['features_mean'] for r in recent_records]
        recent_stds = [r['features_std'] for r in recent_records]
        
        older_means = [r['features_mean'] for r in older_records]
        older_stds = [r['features_std'] for r in older_records]
        
        # 使用KS检验检测分布变化
        try:
            ks_stat_mean, p_value_mean = stats.ks_2samp(recent_means, older_means)
            ks_stat_std, p_value_std = stats.ks_2samp(recent_stds, older_stds)
            
            # 综合漂移分数
            drift_score = (ks_stat_mean + ks_stat_std) / 2
            
            # 记录漂移历史
            drift_record = {
                'timestamp': datetime.now().isoformat(),
                'drift_score': drift_score,
                'ks_stat_mean': ks_stat_mean,
                'ks_stat_std': ks_stat_std,
                'p_value_mean': p_value_mean,
                'p_value_std': p_value_std
            }
            
            self.drift_history.append(drift_record)
            
            # 保持历史记录
            if len(self.drift_history) > 100:
                self.drift_history = self.drift_history[-100:]
            
            return drift_score
            
        except Exception as e:
            print(f"⚠️ 漂移检测失败: {str(e)}")
            return 0.0
    
    def _check_performance_alerts(self, performance: Dict) -> List[Dict]:
        """检查性能警报"""
        alerts = []
        
        # 检查各项性能指标
        if performance['accuracy'] < self.performance_thresholds['accuracy_min']:
            alerts.append({
                'type': 'LOW_ACCURACY',
                'severity': 'HIGH',
                'message': f"准确率过低: {performance['accuracy']:.1%} < {self.performance_thresholds['accuracy_min']:.1%}",
                'timestamp': datetime.now().isoformat(),
                'value': performance['accuracy'],
                'threshold': self.performance_thresholds['accuracy_min']
            })
        
        if performance['f1_score'] < self.performance_thresholds['f1_min']:
            alerts.append({
                'type': 'LOW_F1_SCORE',
                'severity': 'MEDIUM',
                'message': f"F1分数过低: {performance['f1_score']:.1%} < {self.performance_thresholds['f1_min']:.1%}",
                'timestamp': datetime.now().isoformat(),
                'value': performance['f1_score'],
                'threshold': self.performance_thresholds['f1_min']
            })
        
        # 检查性能下降趋势
        if len(self.performance_history) >= 3:
            recent_accuracies = [p['accuracy'] for p in self.performance_history[-3:]]
            if all(recent_accuracies[i] > recent_accuracies[i+1] for i in range(len(recent_accuracies)-1)):
                alerts.append({
                    'type': 'PERFORMANCE_DECLINE',
                    'severity': 'MEDIUM',
                    'message': f"性能持续下降趋势",
                    'timestamp': datetime.now().isoformat(),
                    'trend': recent_accuracies
                })
        
        # 检查特征漂移
        if self.drift_history:
            latest_drift = self.drift_history[-1]['drift_score']
            if latest_drift > self.performance_thresholds['drift_threshold']:
                alerts.append({
                    'type': 'FEATURE_DRIFT',
                    'severity': 'HIGH',
                    'message': f"检测到特征漂移: {latest_drift:.3f} > {self.performance_thresholds['drift_threshold']:.3f}",
                    'timestamp': datetime.now().isoformat(),
                    'drift_score': latest_drift,
                    'threshold': self.performance_thresholds['drift_threshold']
                })
        
        # 添加到全局警报列表
        self.alerts.extend(alerts)
        
        # 保持警报历史在合理范围
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
        
        return alerts
    
    def _compare_with_baseline(self, current_performance: Dict) -> Dict:
        """与基准性能比较"""
        if not self.baseline_performance:
            return {'status': 'no_baseline'}
        
        baseline_accuracy = self.baseline_performance.get('accuracy', 0)
        current_accuracy = current_performance['accuracy']
        
        accuracy_change = current_accuracy - baseline_accuracy
        
        if accuracy_change < -self.performance_thresholds['performance_decline']:
            status = 'significant_decline'
            recommendation = 'RETRAIN_RECOMMENDED'
        elif accuracy_change < -0.02:
            status = 'minor_decline'
            recommendation = 'MONITOR_CLOSELY'
        elif accuracy_change > 0.02:
            status = 'improvement'
            recommendation = 'PERFORMING_WELL'
        else:
            status = 'stable'
            recommendation = 'CONTINUE_MONITORING'
        
        return {
            'status': status,
            'accuracy_change': accuracy_change,
            'baseline_accuracy': baseline_accuracy,
            'current_accuracy': current_accuracy,
            'recommendation': recommendation
        }
    
    def should_retrain_model(self) -> Dict:
        """判断是否需要重训练模型"""
        retrain_reasons = []
        
        # 检查最近性能
        if self.last_evaluation:
            perf = self.last_evaluation
            
            if perf['accuracy'] < self.performance_thresholds['accuracy_min']:
                retrain_reasons.append(f"准确率过低 ({perf['accuracy']:.1%})")
            
            if perf['f1_score'] < self.performance_thresholds['f1_min']:
                retrain_reasons.append(f"F1分数过低 ({perf['f1_score']:.1%})")
        
        # 检查特征漂移
        if self.drift_history:
            latest_drift = self.drift_history[-1]['drift_score']
            if latest_drift > self.performance_thresholds['drift_threshold']:
                retrain_reasons.append(f"特征漂移严重 ({latest_drift:.3f})")
        
        # 检查性能下降趋势
        if len(self.performance_history) >= 5:
            recent_trend = [p['accuracy'] for p in self.performance_history[-5:]]
            slope = np.polyfit(range(len(recent_trend)), recent_trend, 1)[0]
            if slope < -0.01:  # 下降趋势
                retrain_reasons.append(f"性能持续下降 (斜率: {slope:.3f})")
        
        # 检查与基准的差距
        if self.baseline_performance and self.last_evaluation:
            baseline_acc = self.baseline_performance.get('accuracy', 0)
            current_acc = self.last_evaluation['accuracy']
            if current_acc < baseline_acc - self.performance_thresholds['performance_decline']:
                retrain_reasons.append(f"与基准差距过大 ({current_acc:.1%} vs {baseline_acc:.1%})")
        
        should_retrain = len(retrain_reasons) >= 2  # 至少2个理由才建议重训练
        
        return {
            'should_retrain': should_retrain,
            'reasons': retrain_reasons,
            'reason_count': len(retrain_reasons),
            'confidence': min(len(retrain_reasons) * 0.3, 1.0),
            'recommendation': 'RETRAIN_IMMEDIATELY' if should_retrain else 'CONTINUE_MONITORING'
        }
    
    def get_monitoring_report(self) -> Dict:
        """获取监控报告"""
        report = {
            'monitoring_status': 'active' if self.monitoring_active else 'inactive',
            'total_predictions': len(self.prediction_history),
            'performance_evaluations': len(self.performance_history),
            'drift_detections': len(self.drift_history),
            'active_alerts': len([a for a in self.alerts if 
                                (datetime.now() - datetime.fromisoformat(a['timestamp'])).days < 1]),
            'last_evaluation': self.last_evaluation,
            'retrain_recommendation': self.should_retrain_model(),
            'model_metadata': self.model_metadata
        }
        
        # 添加性能趋势
        if len(self.performance_history) >= 3:
            recent_accuracies = [p['accuracy'] for p in self.performance_history[-5:]]
            trend_slope = np.polyfit(range(len(recent_accuracies)), recent_accuracies, 1)[0]
            
            if trend_slope > 0.01:
                trend = 'improving'
            elif trend_slope < -0.01:
                trend = 'declining'
            else:
                trend = 'stable'
            
            report['performance_trend'] = {
                'trend': trend,
                'slope': trend_slope,
                'recent_accuracies': recent_accuracies
            }
        
        return report
    
    def reset_monitoring(self):
        """重置监控状态"""
        self.prediction_history = []
        self.performance_history = []
        self.drift_history = []
        self.alerts = []
        self.last_evaluation = None
        
        print("🔄 监控状态已重置")

if __name__ == "__main__":
    # 测试AI模型监控
    print("🧪 测试AI模型监控系统")
    
    monitor = AIModelMonitor()
    
    # 模拟预测记录
    for i in range(50):
        # 模拟特征和预测
        features = np.random.randn(10)
        prediction = np.random.uniform(0.2, 0.8)
        actual = np.random.choice([0, 1], p=[0.4, 0.6])
        confidence = np.random.uniform(0.5, 0.9)
        
        # 记录预测
        result = monitor.record_prediction(features, prediction, actual, confidence)
    
    # 获取监控报告
    report = monitor.get_monitoring_report()
    
    print(f"📊 监控报告:")
    print(f"   总预测数: {report['total_predictions']}")
    print(f"   性能评估次数: {report['performance_evaluations']}")
    print(f"   活跃警报数: {report['active_alerts']}")
    
    if report['last_evaluation']:
        perf = report['last_evaluation']
        print(f"   最新准确率: {perf['accuracy']:.1%}")
        print(f"   最新F1分数: {perf['f1_score']:.1%}")
    
    retrain_rec = report['retrain_recommendation']
    print(f"   重训练建议: {retrain_rec['recommendation']}")
    if retrain_rec['reasons']:
        print(f"   理由: {', '.join(retrain_rec['reasons'])}")
    
    print(f"\n✅ AI模型监控系统测试完成")
