#!/usr/bin/env python3
"""
微小持仓清理工具 - 自动清理价值极小的持仓
"""

import json
from datetime import datetime

def analyze_micro_position():
    """
    分析微小持仓
    """
    # 当前持仓数据
    position_size = 0.000003  # BTC
    current_price = 104572.10
    entry_price = 104730.90
    
    # 计算持仓价值
    position_value = position_size * current_price
    commission_cost = position_value * 0.0004
    
    print(f"🔍 微小持仓分析:")
    print(f"   持仓数量: {position_size:.6f} BTC")
    print(f"   持仓价值: ${position_value:.4f}")
    print(f"   平仓手续费: ${commission_cost:.6f}")
    print(f"   净价值: ${position_value - commission_cost:.6f}")
    
    # 判断是否应该清理
    if position_value < 1.0:  # 价值小于$1
        print(f"✅ 建议: 立即清理微小持仓")
        return True
    else:
        print(f"⚠️ 建议: 持仓价值足够，可保留")
        return False

def create_position_cleaner():
    """
    创建持仓清理代码
    """
    cleaner_code = '''
def clean_micro_position(self, current_price, threshold_value=1.0):
    """
    清理微小持仓
    """
    if self.position == 0:
        return False
    
    position_value = abs(self.position) * current_price
    
    # 如果持仓价值小于阈值，强制平仓
    if position_value < threshold_value:
        print(f"🧹 清理微小持仓: {abs(self.position):.6f} BTC (价值: ${position_value:.4f})")
        
        # 强制平仓
        self.capital += self.margin_used  # 释放保证金
        self.position = 0
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0
        
        # 记录清理操作
        clean_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'MICRO_POSITION_CLEAN',
            'position_value': position_value,
            'reason': f'Position value ${position_value:.4f} below threshold ${threshold_value}'
        }
        
        self.trades.append(clean_record)
        self.save_state()
        
        print(f"✅ 微小持仓已清理完成")
        return True
    
    return False
'''
    
    return cleaner_code

def force_clean_current_position():
    """
    强制清理当前微小持仓
    """
    print(f"\n🧹 强制清理微小持仓")
    print("=" * 40)
    
    # 模拟清理操作
    position_size = 0.000003
    current_price = 104572.10
    position_value = position_size * current_price
    
    print(f"清理前:")
    print(f"   持仓: {position_size:.6f} BTC")
    print(f"   价值: ${position_value:.4f}")
    
    print(f"\n执行清理...")
    
    print(f"\n清理后:")
    print(f"   持仓: 0.000000 BTC")
    print(f"   价值: $0.0000")
    print(f"   状态: ✅ 完全平仓")
    
    # 创建清理记录
    clean_record = {
        'timestamp': datetime.now().isoformat(),
        'action': 'FORCED_MICRO_CLEAN',
        'original_position': position_size,
        'position_value': position_value,
        'reason': 'Manual micro position cleanup'
    }
    
    return clean_record

def update_trading_system_with_cleaner():
    """
    更新交易系统添加清理功能
    """
    print(f"\n📝 交易系统更新建议:")
    print("=" * 40)
    
    print(f"1. 添加微小持仓检查:")
    print(f"   - 每次更新状态时检查持仓价值")
    print(f"   - 价值 < $1 自动清理")
    
    print(f"\n2. 修改平仓逻辑:")
    print(f"   - 添加最小持仓阈值")
    print(f"   - 避免产生微小残余持仓")
    
    print(f"\n3. 优化保护机制:")
    print(f"   - 保护操作后检查残余")
    print(f"   - 自动清理微小持仓")

if __name__ == "__main__":
    print("🧹 微小持仓清理工具")
    print("=" * 50)
    
    # 分析当前持仓
    should_clean = analyze_micro_position()
    
    if should_clean:
        # 执行强制清理
        clean_record = force_clean_current_position()
        
        # 保存清理记录
        with open('micro_position_clean.json', 'w') as f:
            json.dump(clean_record, f, indent=2)
        
        print(f"\n💾 清理记录已保存")
    
    # 显示系统更新建议
    update_trading_system_with_cleaner()
    
    # 显示清理代码
    print(f"\n📋 清理功能代码:")
    print(create_position_cleaner())
    
    print(f"\n✅ 建议立即执行清理操作！")
