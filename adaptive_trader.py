#!/usr/bin/env python3
"""
自适应交易系统 - 根据市场条件智能选择左侧或右侧交易
"""

import pandas as pd
import numpy as np
import time
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings

# 禁用所有警告和详细日志
warnings.filterwarnings('ignore')
logging.getLogger().setLevel(logging.ERROR)

# 导入组件
from enhanced_real_sentiment import EnhancedRealSentimentAnalyzer
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class AdaptiveTrader:
    """
    自适应交易系统
    
    特点：
    - 🎯 智能判断市场状态
    - 📊 自动选择左侧/右侧交易
    - 💰 50美元模拟账户
    - 🔄 动态调整策略
    """
    
    def __init__(self, initial_balance: float = 50.0):
        self.initial_balance = initial_balance
        
        # 账户状态
        self.account = {
            'balance': initial_balance,
            'equity': initial_balance,
            'unrealized_pnl': 0.0
        }
        
        # 持仓信息
        self.position = {
            'side': None,
            'size': 0.0,
            'entry_price': 0.0,
            'entry_time': None,
            'leverage': 2,
            'trading_mode': None,  # 'left_side' or 'right_side'
            'batches': []  # 左侧交易分批记录
        }
        
        # 自适应配置
        self.config = {
            'leverage': 2,
            'trading_fee': 0.0004,
            'mode_switch_threshold': 0.7,  # 模式切换阈值
            
            # 左侧交易配置
            'left_side': {
                'stop_loss_pct': 0.025,
                'take_profit_pct': 0.05,
                'min_confidence': 0.45,
                'batch_size_ratio': 0.3,
                'max_batches': 3,
                'rsi_oversold': 35,
                'rsi_overbought': 65
            },
            
            # 右侧交易配置
            'right_side': {
                'stop_loss_pct': 0.03,
                'take_profit_pct': 0.06,
                'min_confidence': 0.65,
                'rsi_oversold': 30,
                'rsi_overbought': 70
            }
        }
        
        # 市场状态历史
        self.market_state_history = []
        self.current_trading_mode = None
        
        # 初始化组件
        print("🎯 初始化自适应交易系统...")
        
        self.data_fetcher = BinanceDataFetcher()
        self.feature_engineer = FeatureEngineer()
        self.sentiment_analyzer = EnhancedRealSentimentAnalyzer()
        
        # 交易记录
        self.trade_history = []
        self.mode_switch_history = []
        self.last_trade_time = None
        
        # 状态文件
        self.state_file = "adaptive_trader_state.json"
        self.load_state()
        
        print("✅ 自适应交易系统初始化完成")
        print(f"   智能模式: 根据市场条件自动选择")
        print(f"   支持策略: 左侧交易 + 右侧交易")
    
    def analyze_market_state(self, market_data: Dict) -> Dict:
        """分析市场状态，决定使用哪种交易模式"""
        try:
            # 提取关键指标
            rsi = market_data['rsi']
            bb_position = market_data['bb_position']
            volatility = market_data['volatility']
            price_change_24h = market_data['price_change_24h']
            volume_ratio = market_data.get('volume_ratio', 1.0)
            
            # 市场状态评分因子
            factors = {}
            
            # 1. 趋势强度分析
            trend_strength = 0.5
            if abs(price_change_24h) > 0.05:  # 24小时变化超过5%
                trend_strength = min(1.0, abs(price_change_24h) * 10)
            factors['trend_strength'] = trend_strength
            
            # 2. 波动率分析
            volatility_score = min(1.0, volatility * 50)  # 标准化波动率
            factors['volatility'] = volatility_score
            
            # 3. RSI极值分析
            rsi_extreme = 0
            if rsi < 30 or rsi > 70:
                rsi_extreme = abs(rsi - 50) / 50
            factors['rsi_extreme'] = rsi_extreme
            
            # 4. 成交量确认
            volume_confirmation = min(1.0, volume_ratio / 2)  # 成交量放大程度
            factors['volume_confirmation'] = volume_confirmation
            
            # 5. 布林带位置
            bb_extreme = 0
            if bb_position < 0.2 or bb_position > 0.8:
                bb_extreme = abs(bb_position - 0.5) * 2
            factors['bb_extreme'] = bb_extreme
            
            # 计算市场状态分数
            # 高分 = 趋势市场，适合右侧交易
            # 低分 = 震荡市场，适合左侧交易
            market_score = (
                trend_strength * 0.3 +
                volume_confirmation * 0.25 +
                (1 - rsi_extreme) * 0.2 +  # RSI极值时更适合左侧
                (1 - bb_extreme) * 0.15 +   # 布林带极值时更适合左侧
                volatility_score * 0.1
            )
            
            # 确定推荐交易模式
            if market_score > self.config['mode_switch_threshold']:
                recommended_mode = 'right_side'
                mode_confidence = market_score
                mode_reason = "趋势明确，适合右侧交易"
            else:
                recommended_mode = 'left_side'
                mode_confidence = 1 - market_score
                mode_reason = "震荡或反转，适合左侧交易"
            
            # 特殊情况判断
            special_conditions = []
            
            # RSI极值 + 低波动 = 强烈左侧信号
            if (rsi < 25 or rsi > 75) and volatility < 0.02:
                recommended_mode = 'left_side'
                mode_confidence = 0.9
                mode_reason = "RSI极值+低波动，强烈左侧信号"
                special_conditions.append("RSI_EXTREME_LOW_VOL")
            
            # 强趋势 + 高成交量 = 强烈右侧信号
            if abs(price_change_24h) > 0.08 and volume_ratio > 2:
                recommended_mode = 'right_side'
                mode_confidence = 0.9
                mode_reason = "强趋势+放量，强烈右侧信号"
                special_conditions.append("STRONG_TREND_HIGH_VOL")
            
            market_state = {
                'timestamp': datetime.now().isoformat(),
                'market_score': market_score,
                'recommended_mode': recommended_mode,
                'mode_confidence': mode_confidence,
                'mode_reason': mode_reason,
                'factors': factors,
                'special_conditions': special_conditions,
                'market_data': {
                    'rsi': rsi,
                    'bb_position': bb_position,
                    'volatility': volatility,
                    'price_change_24h': price_change_24h,
                    'volume_ratio': volume_ratio
                }
            }
            
            # 记录市场状态历史
            self.market_state_history.append(market_state)
            if len(self.market_state_history) > 100:  # 只保留最近100条
                self.market_state_history.pop(0)
            
            return market_state
            
        except Exception as e:
            print(f"❌ 市场状态分析失败: {str(e)}")
            return {
                'recommended_mode': 'right_side',
                'mode_confidence': 0.5,
                'mode_reason': '分析失败，默认右侧',
                'error': str(e)
            }
    
    def switch_trading_mode(self, new_mode: str, reason: str):
        """切换交易模式"""
        if self.current_trading_mode != new_mode:
            old_mode = self.current_trading_mode
            self.current_trading_mode = new_mode
            
            # 记录模式切换
            switch_record = {
                'timestamp': datetime.now().isoformat(),
                'from_mode': old_mode,
                'to_mode': new_mode,
                'reason': reason
            }
            
            self.mode_switch_history.append(switch_record)
            
            print(f"🔄 交易模式切换: {old_mode} → {new_mode}")
            print(f"   切换原因: {reason}")
            
            return True
        return False
    
    def get_market_data(self) -> Dict:
        """获取市场数据"""
        try:
            # 获取当前价格
            current_price = self.data_fetcher.get_current_price('BTCUSDT', is_futures=True)
            
            # 获取历史数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            
            df = self.data_fetcher.get_historical_data(
                'BTCUSDT', '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            # 计算技术指标
            features_df = self.feature_engineer.create_features(df)
            latest_features = features_df.iloc[-1]
            
            # 计算额外指标
            recent_prices = df['close'].tail(24)
            recent_volumes = df['volume'].tail(24)
            
            # 波动率
            volatility = recent_prices.pct_change().std()
            
            # 成交量比率
            volume_ratio = recent_volumes.iloc[-1] / recent_volumes.mean() if recent_volumes.mean() > 0 else 1
            
            # 价格位置
            recent_high = recent_prices.max()
            recent_low = recent_prices.min()
            price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5
            
            return {
                'current_price': current_price,
                'rsi': latest_features.get('RSI_14', 50),
                'bb_position': latest_features.get('BB_position', 0.5),
                'macd_signal': latest_features.get('MACD_signal', 0),
                'volatility': volatility,
                'volume_ratio': volume_ratio,
                'price_position': price_position,
                'recent_high': recent_high,
                'recent_low': recent_low,
                'price_change_24h': ((current_price - df['close'].iloc[-24]) / df['close'].iloc[-24]) if len(df) >= 24 else 0
            }
            
        except Exception as e:
            print(f"❌ 市场数据获取失败: {str(e)}")
            return None
    
    def generate_left_side_signal(self, market_data: Dict) -> Dict:
        """生成左侧交易信号"""
        try:
            rsi = market_data['rsi']
            bb_position = market_data['bb_position']
            price_position = market_data['price_position']
            price_change_24h = market_data['price_change_24h']
            
            config = self.config['left_side']
            
            # 左侧信号计算
            signals = []
            
            # RSI逆向信号
            if rsi <= config['rsi_oversold']:
                rsi_signal = 0.8
            elif rsi >= config['rsi_overbought']:
                rsi_signal = 0.2
            else:
                rsi_signal = 0.5
            signals.append(rsi_signal)
            
            # 价格位置逆向信号
            if price_position < 0.2:
                position_signal = 0.8
            elif price_position > 0.8:
                position_signal = 0.2
            else:
                position_signal = 0.5
            signals.append(position_signal)
            
            # 24小时变化逆向信号
            if price_change_24h < -0.05:
                change_signal = 0.75
            elif price_change_24h > 0.05:
                change_signal = 0.25
            else:
                change_signal = 0.5
            signals.append(change_signal)
            
            # 布林带逆向信号
            if bb_position < 0.2:
                bb_signal = 0.75
            elif bb_position > 0.8:
                bb_signal = 0.25
            else:
                bb_signal = 0.5
            signals.append(bb_signal)
            
            # 计算最终信号
            final_signal = np.mean(signals)
            confidence = max(0.3, min(0.9, 1 - np.std(signals)))
            
            if final_signal > 0.6:
                direction = 'LONG'
                strength = (final_signal - 0.5) * 2
            elif final_signal < 0.4:
                direction = 'SHORT'
                strength = (0.5 - final_signal) * 2
            else:
                direction = 'WAIT'
                strength = 0
            
            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'signal_value': final_signal,
                'mode': 'left_side'
            }
            
        except Exception as e:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'mode': 'left_side',
                'error': str(e)
            }
    
    def generate_right_side_signal(self, market_data: Dict) -> Dict:
        """生成右侧交易信号"""
        try:
            rsi = market_data['rsi']
            bb_position = market_data['bb_position']
            macd_signal = market_data['macd_signal']
            price_change_24h = market_data['price_change_24h']
            volume_ratio = market_data['volume_ratio']
            
            config = self.config['right_side']
            
            # 右侧信号计算
            signals = []
            
            # RSI趋势信号
            if rsi < config['rsi_oversold']:
                rsi_signal = 0.2  # 超卖，等待确认
            elif rsi > config['rsi_overbought']:
                rsi_signal = 0.8  # 超买，等待确认
            elif 40 <= rsi <= 60:
                rsi_signal = 0.6  # 中性偏多
            else:
                rsi_signal = 0.5
            signals.append(rsi_signal)
            
            # MACD趋势信号
            if macd_signal > 0:
                macd_sig = 0.7
            elif macd_signal < 0:
                macd_sig = 0.3
            else:
                macd_sig = 0.5
            signals.append(macd_sig)
            
            # 价格变化趋势信号
            if price_change_24h > 0.02:
                change_signal = 0.7
            elif price_change_24h < -0.02:
                change_signal = 0.3
            else:
                change_signal = 0.5
            signals.append(change_signal)
            
            # 成交量确认信号
            if volume_ratio > 1.5:
                if price_change_24h > 0:
                    volume_signal = 0.7
                else:
                    volume_signal = 0.3
            else:
                volume_signal = 0.5
            signals.append(volume_signal)
            
            # 计算最终信号
            final_signal = np.mean(signals)
            confidence = max(0.3, min(0.9, 1 - np.std(signals)))
            
            if final_signal > 0.6:
                direction = 'LONG'
                strength = (final_signal - 0.5) * 2
            elif final_signal < 0.4:
                direction = 'SHORT'
                strength = (0.5 - final_signal) * 2
            else:
                direction = 'WAIT'
                strength = 0
            
            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'signal_value': final_signal,
                'mode': 'right_side'
            }
            
        except Exception as e:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'mode': 'right_side',
                'error': str(e)
            }

    def get_sentiment_signal(self) -> Dict:
        """获取情绪信号"""
        try:
            sentiment_data = self.sentiment_analyzer.get_comprehensive_sentiment()

            return {
                'direction': sentiment_data['trading_signal']['direction'],
                'score': sentiment_data['overall_sentiment_score'],
                'classification': sentiment_data['sentiment_classification'],
                'confidence': sentiment_data['trading_signal']['confidence']
            }

        except Exception as e:
            return {
                'direction': 'WAIT',
                'score': 0.5,
                'classification': 'Neutral',
                'confidence': 0.3
            }

    def fuse_adaptive_signals(self, technical_signal: Dict, sentiment_signal: Dict,
                             trading_mode: str) -> Dict:
        """融合自适应信号"""

        # 根据交易模式调整权重
        if trading_mode == 'left_side':
            technical_weight = 0.8
            sentiment_weight = 0.2
        else:  # right_side
            technical_weight = 0.7
            sentiment_weight = 0.3

        # 转换信号为数值
        def signal_to_value(signal):
            if signal['direction'] == 'LONG':
                return 0.5 + signal.get('strength', 0.5) * 0.5
            elif signal['direction'] == 'SHORT':
                return 0.5 - signal.get('strength', 0.5) * 0.5
            else:
                return 0.5

        technical_value = technical_signal['signal_value']
        sentiment_value = sentiment_signal['score']

        # 加权融合
        final_value = technical_value * technical_weight + sentiment_value * sentiment_weight
        final_confidence = technical_signal['confidence'] * technical_weight + sentiment_signal['confidence'] * sentiment_weight

        # 根据交易模式调整阈值
        if trading_mode == 'left_side':
            long_threshold = 0.55
            short_threshold = 0.45
        else:
            long_threshold = 0.6
            short_threshold = 0.4

        # 确定方向
        if final_value > long_threshold:
            direction = 'LONG'
            strength = (final_value - 0.5) * 2
        elif final_value < short_threshold:
            direction = 'SHORT'
            strength = (0.5 - final_value) * 2
        else:
            direction = 'WAIT'
            strength = 0

        return {
            'direction': direction,
            'strength': strength,
            'confidence': final_confidence,
            'final_value': final_value,
            'trading_mode': trading_mode,
            'signal_breakdown': {
                'technical': {'value': technical_value, 'weight': technical_weight},
                'sentiment': {'value': sentiment_value, 'weight': sentiment_weight}
            }
        }

    def calculate_position_size(self, current_price: float, trading_mode: str) -> float:
        """计算仓位大小"""
        available_balance = self.account['balance']

        if trading_mode == 'left_side':
            # 左侧交易：分批建仓
            batch_ratio = self.config['left_side']['batch_size_ratio']
            batch_amount = available_balance * batch_ratio
        else:
            # 右侧交易：一次性建仓
            risk_amount = available_balance * 0.03  # 3%风险
            config = self.config['right_side']
            position_value = risk_amount / config['stop_loss_pct']
            batch_amount = min(position_value, available_balance * 0.9)

        return batch_amount / current_price / self.config['leverage']

    def open_adaptive_position(self, signal: Dict, current_price: float) -> bool:
        """开启自适应仓位"""
        try:
            trading_mode = signal['trading_mode']

            # 检查是否已有持仓
            if self.position['size'] != 0 and self.position['trading_mode'] != trading_mode:
                print(f"⚠️ 当前持仓模式({self.position['trading_mode']})与信号模式({trading_mode})不匹配")
                return False

            # 检查交易间隔
            if self.last_trade_time:
                time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
                min_interval = 300 if trading_mode == 'left_side' else 600  # 左侧5分钟，右侧10分钟
                if time_since_last < min_interval:
                    return False

            # 计算仓位大小
            btc_size = self.calculate_position_size(current_price, trading_mode)

            # 计算交易费用
            position_value = btc_size * current_price * self.config['leverage']
            trading_fee = position_value * self.config['trading_fee']

            if trading_fee > self.account['balance']:
                return False

            # 更新持仓信息
            if self.position['size'] == 0:
                # 首次开仓
                self.position.update({
                    'side': signal['direction'],
                    'size': btc_size,
                    'entry_price': current_price,
                    'entry_time': datetime.now(),
                    'trading_mode': trading_mode,
                    'batches': []
                })

                if trading_mode == 'left_side':
                    # 左侧交易记录批次
                    batch_info = {
                        'batch_number': 1,
                        'size': btc_size,
                        'price': current_price,
                        'time': datetime.now()
                    }
                    self.position['batches'].append(batch_info)
            else:
                # 左侧交易加仓
                if trading_mode == 'left_side' and len(self.position['batches']) < self.config['left_side']['max_batches']:
                    total_size = self.position['size'] + btc_size
                    total_cost = self.position['entry_price'] * self.position['size'] + current_price * btc_size
                    avg_price = total_cost / total_size

                    self.position['size'] = total_size
                    self.position['entry_price'] = avg_price

                    batch_info = {
                        'batch_number': len(self.position['batches']) + 1,
                        'size': btc_size,
                        'price': current_price,
                        'time': datetime.now()
                    }
                    self.position['batches'].append(batch_info)
                else:
                    return False

            # 扣除手续费
            self.account['balance'] -= trading_fee

            # 记录交易
            self.trade_history.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'OPEN',
                'trading_mode': trading_mode,
                'side': signal['direction'],
                'size': btc_size,
                'price': current_price,
                'trading_fee': trading_fee,
                'signal': signal,
                'batch_number': len(self.position['batches']) if trading_mode == 'left_side' else 1
            })

            self.last_trade_time = datetime.now()

            return True

        except Exception as e:
            print(f"❌ 自适应开仓失败: {str(e)}")
            return False

    def close_adaptive_position(self, current_price: float, reason: str) -> bool:
        """平仓自适应交易"""
        try:
            if self.position['size'] == 0:
                return False

            # 计算盈亏
            if self.position['side'] == 'LONG':
                price_diff = current_price - self.position['entry_price']
            else:
                price_diff = self.position['entry_price'] - current_price

            pnl = self.position['size'] * price_diff * self.config['leverage']
            position_value = self.position['size'] * current_price * self.config['leverage']
            trading_fee = position_value * self.config['trading_fee']
            net_pnl = pnl - trading_fee

            # 更新账户
            self.account['balance'] += net_pnl
            self.account['equity'] = self.account['balance']
            self.account['unrealized_pnl'] = 0

            # 记录交易
            self.trade_history.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'CLOSE',
                'trading_mode': self.position['trading_mode'],
                'side': self.position['side'],
                'size': self.position['size'],
                'entry_price': self.position['entry_price'],
                'exit_price': current_price,
                'net_pnl': net_pnl,
                'reason': reason,
                'batches_count': len(self.position['batches']),
                'hold_time': (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            })

            self.last_trade_time = datetime.now()

            # 清空持仓
            self.position.update({
                'side': None,
                'size': 0.0,
                'entry_price': 0.0,
                'entry_time': None,
                'trading_mode': None,
                'batches': []
            })

            return True

        except Exception as e:
            print(f"❌ 自适应平仓失败: {str(e)}")
            return False

    def check_exit_conditions(self, current_price: float) -> bool:
        """检查退出条件"""
        if self.position['size'] == 0:
            return False

        trading_mode = self.position['trading_mode']
        config = self.config[trading_mode]

        # 计算盈亏百分比
        if self.position['side'] == 'LONG':
            pnl_pct = (current_price - self.position['entry_price']) / self.position['entry_price']
        else:
            pnl_pct = (self.position['entry_price'] - current_price) / self.position['entry_price']

        # 止损
        if pnl_pct <= -config['stop_loss_pct']:
            self.close_adaptive_position(current_price, 'STOP_LOSS')
            return True

        # 止盈
        if pnl_pct >= config['take_profit_pct']:
            self.close_adaptive_position(current_price, 'TAKE_PROFIT')
            return True

        return False

    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.position['size'] == 0:
            self.account['unrealized_pnl'] = 0
            self.account['equity'] = self.account['balance']
            return

        if self.position['side'] == 'LONG':
            price_diff = current_price - self.position['entry_price']
        else:
            price_diff = self.position['entry_price'] - current_price

        unrealized_pnl = self.position['size'] * price_diff * self.config['leverage']
        self.account['unrealized_pnl'] = unrealized_pnl
        self.account['equity'] = self.account['balance'] + unrealized_pnl

    def print_adaptive_status(self, market_data: Dict, market_state: Dict,
                             technical_signal: Dict, sentiment_signal: Dict, final_signal: Dict):
        """打印自适应交易状态"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_price = market_data['current_price']

        print(f"\n🎯 {current_time} | BTC: ${current_price:,.0f} | 自适应交易模式")
        print("=" * 120)

        # 账户状态
        total_return = (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        print(f"💰 账户: ${self.account['balance']:.2f} + ${self.account['unrealized_pnl']:+.2f} = ${self.account['equity']:.2f} ({total_return:+.2f}%)")

        # 市场状态分析
        print(f"\n📊 市场状态分析:")
        print(f"   市场评分: {market_state['market_score']:.2f} | 推荐模式: {market_state['recommended_mode']}")
        print(f"   模式置信度: {market_state['mode_confidence']:.1%} | 原因: {market_state['mode_reason']}")
        print(f"   当前交易模式: {self.current_trading_mode or '未设定'}")

        # 持仓状态
        if self.position['size'] != 0:
            hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            pnl_pct = self.account['unrealized_pnl'] / (self.position['size'] * self.position['entry_price'] * self.config['leverage']) * 100

            print(f"\n📊 持仓状态:")
            print(f"   🔥 {self.position['side']} {self.position['size']:.6f} BTC ({self.position['trading_mode']})")
            print(f"   平均成本: ${self.position['entry_price']:,.0f} | 当前价: ${current_price:,.0f}")
            print(f"   持仓时间: {hold_time:.1f}小时 | 盈亏: {pnl_pct:+.1f}%")

            if self.position['trading_mode'] == 'left_side' and self.position['batches']:
                print(f"   分批详情: {len(self.position['batches'])}批")
                for batch in self.position['batches']:
                    batch_pnl = (current_price - batch['price']) / batch['price'] * 100
                    if self.position['side'] == 'SHORT':
                        batch_pnl = -batch_pnl
                    print(f"      批次{batch['batch_number']}: {batch['size']:.6f} BTC @ ${batch['price']:,.0f} ({batch_pnl:+.1f}%)")
        else:
            print(f"\n📊 持仓状态: 💤 空仓")

        # 信号分析
        print(f"\n🎯 信号分析:")
        print(f"   技术信号({technical_signal['mode']}): {technical_signal['direction']} (值{technical_signal['signal_value']:.2f}, 置信度{technical_signal['confidence']:.1%})")
        print(f"   情绪信号: {sentiment_signal['classification']} (分数{sentiment_signal['score']:.2f})")
        print(f"   最终决策: {final_signal['direction']} (置信度{final_signal['confidence']:.1%}, 模式{final_signal['trading_mode']})")

        # 市场因子
        factors = market_state.get('factors', {})
        print(f"\n📈 市场因子:")
        print(f"   趋势强度: {factors.get('trend_strength', 0):.2f} | 波动率: {factors.get('volatility', 0):.2f}")
        print(f"   RSI极值: {factors.get('rsi_extreme', 0):.2f} | 成交量确认: {factors.get('volume_confirmation', 0):.2f}")

    def save_state(self):
        """保存状态"""
        state_data = {
            'account': self.account,
            'position': {k: v.isoformat() if isinstance(v, datetime) else v
                        for k, v in self.position.items() if k != 'batches'},
            'batches': [{k: v.isoformat() if isinstance(v, datetime) else v
                        for k, v in batch.items()} for batch in self.position['batches']],
            'trade_history': self.trade_history,
            'mode_switch_history': self.mode_switch_history,
            'current_trading_mode': self.current_trading_mode,
            'last_trade_time': self.last_trade_time.isoformat() if self.last_trade_time else None,
            'config': self.config
        }

        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, default=str)

    def load_state(self):
        """加载状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                self.account = state_data.get('account', self.account)

                position_data = state_data.get('position', {})
                if position_data.get('entry_time'):
                    position_data['entry_time'] = datetime.fromisoformat(position_data['entry_time'])
                self.position.update(position_data)

                # 加载批次信息
                batches_data = state_data.get('batches', [])
                self.position['batches'] = []
                for batch in batches_data:
                    if batch.get('time'):
                        batch['time'] = datetime.fromisoformat(batch['time'])
                    self.position['batches'].append(batch)

                self.trade_history = state_data.get('trade_history', [])
                self.mode_switch_history = state_data.get('mode_switch_history', [])
                self.current_trading_mode = state_data.get('current_trading_mode')

                if state_data.get('last_trade_time'):
                    self.last_trade_time = datetime.fromisoformat(state_data['last_trade_time'])

                if self.account['balance'] != self.initial_balance or self.trade_history:
                    print(f"📂 加载历史状态: 余额${self.account['balance']:.2f}, {len(self.trade_history)}笔交易")
                    if self.current_trading_mode:
                        print(f"   当前交易模式: {self.current_trading_mode}")

            except Exception as e:
                print(f"⚠️ 状态加载失败: {str(e)}")

    def run_adaptive_cycle(self) -> bool:
        """运行一个自适应交易周期"""
        try:
            # 1. 获取市场数据
            market_data = self.get_market_data()
            if not market_data:
                print("❌ 市场数据获取失败")
                return False

            current_price = market_data['current_price']

            # 2. 分析市场状态，决定交易模式
            market_state = self.analyze_market_state(market_data)
            recommended_mode = market_state['recommended_mode']

            # 3. 检查是否需要切换交易模式
            if market_state['mode_confidence'] > 0.8:  # 高置信度时才切换
                self.switch_trading_mode(recommended_mode, market_state['mode_reason'])
            elif not self.current_trading_mode:
                # 首次设定交易模式
                self.current_trading_mode = recommended_mode
                print(f"🎯 设定初始交易模式: {recommended_mode}")

            # 4. 更新未实现盈亏
            self.update_unrealized_pnl(current_price)

            # 5. 检查退出条件
            if self.check_exit_conditions(current_price):
                print(f"🔔 触发退出条件，已平仓")
                self.save_state()
                return True

            # 6. 根据当前交易模式生成信号
            if self.current_trading_mode == 'left_side':
                technical_signal = self.generate_left_side_signal(market_data)
            else:
                technical_signal = self.generate_right_side_signal(market_data)

            # 7. 获取情绪信号
            sentiment_signal = self.get_sentiment_signal()

            # 8. 融合信号
            final_signal = self.fuse_adaptive_signals(technical_signal, sentiment_signal, self.current_trading_mode)

            # 9. 执行交易决策
            current_config = self.config[self.current_trading_mode]

            if final_signal['direction'] in ['LONG', 'SHORT']:
                if final_signal['confidence'] >= current_config['min_confidence']:
                    if self.open_adaptive_position(final_signal, current_price):
                        mode_emoji = "🎯" if self.current_trading_mode == 'left_side' else "📈"
                        batch_info = f"第{len(self.position['batches'])}批" if self.current_trading_mode == 'left_side' else ""
                        print(f"🔔 {mode_emoji} {self.current_trading_mode}交易: {final_signal['direction']} {batch_info} @ ${current_price:,.0f}")
                    else:
                        print("⚠️ 开仓条件不满足")
                else:
                    print(f"⚠️ 置信度不足: {final_signal['confidence']:.1%} < {current_config['min_confidence']:.1%}")

            # 10. 打印状态
            self.print_adaptive_status(market_data, market_state, technical_signal, sentiment_signal, final_signal)

            # 11. 保存状态
            self.save_state()

            return True

        except Exception as e:
            print(f"❌ 自适应交易周期失败: {str(e)}")
            return False

    def get_statistics(self) -> Dict:
        """获取交易统计"""
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']

        if not closed_trades:
            return {
                'total_trades': 0,
                'mode_switches': len(self.mode_switch_history),
                'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
            }

        total_trades = len(closed_trades)
        winning_trades = [t for t in closed_trades if t['net_pnl'] > 0]

        # 按交易模式分类统计
        left_side_trades = [t for t in closed_trades if t['trading_mode'] == 'left_side']
        right_side_trades = [t for t in closed_trades if t['trading_mode'] == 'right_side']

        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'win_rate': len(winning_trades) / total_trades,
            'total_pnl': sum(t['net_pnl'] for t in closed_trades),
            'avg_pnl': sum(t['net_pnl'] for t in closed_trades) / total_trades,
            'avg_hold_time': sum(t['hold_time'] for t in closed_trades) / total_trades,
            'left_side_trades': len(left_side_trades),
            'right_side_trades': len(right_side_trades),
            'mode_switches': len(self.mode_switch_history),
            'current_mode': self.current_trading_mode,
            'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        }

def run_adaptive_trading():
    """运行自适应交易系统"""
    print("🎯 自适应交易系统")
    print("=" * 80)
    print("📊 智能选择左侧/右侧交易策略")
    print("🔄 根据市场条件自动调整")
    print("💰 50美元模拟账户")
    print("")

    # 获取参数
    try:
        duration = float(input("运行时长（小时，默认4）: ") or "4")
        interval = int(input("检测间隔（分钟，默认5）: ") or "5")

        print(f"\n📊 系统将根据以下条件智能选择交易模式:")
        print(f"   🎯 左侧交易: 震荡市场、RSI极值、价格偏离")
        print(f"   📈 右侧交易: 趋势市场、成交量确认、动量强劲")
        print(f"   🔄 自动切换: 高置信度时自动调整策略")

        confirm = input("\n🚀 是否开始自适应交易？(y/n，默认y): ").strip().lower()
        if confirm in ['n', 'no']:
            print("⏸️ 用户取消")
            return None

    except:
        duration = 4
        interval = 5

    print(f"\n🎯 启动自适应交易系统...")
    print(f"⏰ 运行时长: {duration}小时")
    print(f"🔄 检测间隔: {interval}分钟")

    # 创建自适应交易器
    trader = AdaptiveTrader(50.0)

    start_time = datetime.now()
    end_time = start_time + timedelta(hours=duration)
    cycle_count = 0

    try:
        while datetime.now() < end_time:
            cycle_count += 1

            # 计算进度
            elapsed_time = (datetime.now() - start_time).total_seconds() / 3600
            progress = elapsed_time / duration * 100
            remaining_hours = duration - elapsed_time

            print(f"\n🎯 第 {cycle_count} 个自适应交易周期 | 进度: {progress:.1f}% | 剩余: {remaining_hours:.1f}小时")

            # 运行自适应交易周期
            trader.run_adaptive_cycle()

            # 显示简要统计
            stats = trader.get_statistics()
            print(f"\n📈 当前统计: 权益${trader.account['equity']:.2f} | 收益{stats['total_return']:+.2f}% | 交易{stats['total_trades']}笔 | 模式切换{stats['mode_switches']}次")
            if stats['total_trades'] > 0:
                print(f"   胜率{stats['win_rate']:.1%} | 左侧{stats['left_side_trades']}笔 | 右侧{stats['right_side_trades']}笔 | 当前模式: {stats['current_mode']}")

            # 等待下一个周期
            remaining_time = (end_time - datetime.now()).total_seconds()
            interval_seconds = interval * 60

            if remaining_time > interval_seconds:
                print(f"⏳ 等待 {interval} 分钟...")
                time.sleep(interval_seconds)
            else:
                print(f"⏳ 剩余时间不足，等待 {remaining_time:.0f} 秒...")
                time.sleep(max(0, remaining_time))
                break

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断")

    # 显示最终结果
    print(f"\n🏁 自适应交易结束")
    print("=" * 80)

    final_stats = trader.get_statistics()
    actual_runtime = (datetime.now() - start_time).total_seconds() / 3600

    print(f"📊 最终统计:")
    print(f"   运行时长: {actual_runtime:.1f}小时")
    print(f"   交易周期: {cycle_count}个")
    print(f"   最终权益: ${trader.account['equity']:.2f}")
    print(f"   总收益率: {final_stats['total_return']:+.2f}%")
    print(f"   总交易次数: {final_stats['total_trades']}")
    print(f"   模式切换次数: {final_stats['mode_switches']}")

    if final_stats['total_trades'] > 0:
        print(f"   胜率: {final_stats['win_rate']:.1%}")
        print(f"   平均盈亏: ${final_stats['avg_pnl']:+.2f}")
        print(f"   平均持仓时间: {final_stats['avg_hold_time']:.1f}小时")
        print(f"   左侧交易: {final_stats['left_side_trades']}笔")
        print(f"   右侧交易: {final_stats['right_side_trades']}笔")

    print(f"\n💾 数据已保存到: {trader.state_file}")

    # 自适应交易总结
    print(f"\n🎯 自适应交易总结:")
    if final_stats['mode_switches'] > 0:
        print(f"✅ 系统成功进行了{final_stats['mode_switches']}次模式切换")
        print(f"✅ 智能适应市场变化")

    if final_stats['total_return'] > 0:
        print(f"✅ 自适应策略获得正收益")
    else:
        print(f"📊 积累宝贵的自适应交易经验")

    print(f"✅ 验证了智能交易模式选择的有效性")

    return trader

if __name__ == "__main__":
    print("🎯 自适应交易系统")
    print("基于第三阶段完全真实化系统 - 智能模式选择版")
    print("")

    try:
        trader = run_adaptive_trading()
        if trader:
            print(f"\n🎉 自适应交易完成！")

    except KeyboardInterrupt:
        print(f"\n👋 用户退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
