#!/usr/bin/env python3
"""
真实市场情绪分析模块 - 使用真实API数据
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
import time
import warnings
warnings.filterwarnings('ignore')

class RealSentimentAnalyzer:
    """
    真实市场情绪分析器 - 使用真实API数据
    """
    
    def __init__(self, api_keys: Dict = None):
        self.api_keys = api_keys or {}
        
        # API配置
        self.apis = {
            'fear_greed': {
                'url': 'https://api.alternative.me/fng/',
                'free': True,
                'enabled': True
            },
            'coinmarketcap': {
                'url': 'https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest',
                'free': False,
                'enabled': "950fff17-df41-462f-a19c-fdfbfe895c44",  # 需要API密钥
                'key_required': True
            },
            'newsapi': {
                'url': 'https://newsapi.org/v2/everything',
                'free': False,  # 有限制
                'enabled': '757e29fcd2f74993997f59cd420aefac',  # 需要API密钥
                'key_required': True
            },
            'reddit': {
                'url': 'https://www.reddit.com/r/Bitcoin.json',
                'free': True,
                'enabled': True
            }
        }
        
        self.cache = {}
        self.cache_duration = 300  # 5分钟缓存
        
        print(f"🔍 真实情绪分析器初始化")
        print(f"   免费API: 恐慌贪婪指数, Reddit")
        print(f"   付费API: NewsAPI, CoinMarketCap (需要密钥)")
    
    def get_real_fear_greed_index(self) -> Dict:
        """获取真实恐慌贪婪指数"""
        cache_key = 'fear_greed'
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            url = self.apis['fear_greed']['url']
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'data' in data and len(data['data']) > 0:
                fng_data = data['data'][0]
                
                index_value = int(fng_data['value'])
                classification = fng_data['value_classification']
                
                # 转换为标准化分数
                sentiment_score = index_value / 100
                
                result = {
                    'index': index_value,
                    'classification': classification,
                    'sentiment_score': sentiment_score,
                    'timestamp': datetime.now().isoformat(),
                    'source': 'Alternative.me Fear & Greed Index',
                    'raw_data': fng_data
                }
                
                # 缓存结果
                self._cache_data(cache_key, result)
                
                print(f"✅ 真实恐慌贪婪指数: {index_value} ({classification})")
                return result
            
            else:
                raise Exception("API响应格式错误")
                
        except Exception as e:
            print(f"❌ 恐慌贪婪指数获取失败: {str(e)}")
            # 返回默认值
            return self._get_default_fear_greed()
    
    def get_real_reddit_sentiment(self) -> Dict:
        """获取真实Reddit情绪"""
        cache_key = 'reddit'
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            url = self.apis['reddit']['url']
            headers = {'User-Agent': 'TradingBot/1.0'}
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'data' in data and 'children' in data['data']:
                posts = data['data']['children']
                
                # 分析帖子标题情绪
                sentiment_scores = []
                bullish_keywords = ['moon', 'bull', 'pump', 'up', 'rise', 'high', 'buy', 'hodl', 'bullish']
                bearish_keywords = ['dump', 'crash', 'bear', 'down', 'fall', 'sell', 'bearish', 'drop']
                
                for post in posts[:20]:  # 分析前20个帖子
                    title = post['data']['title'].lower()
                    score = post['data']['score']
                    
                    # 简单关键词情绪分析
                    bullish_count = sum(1 for word in bullish_keywords if word in title)
                    bearish_count = sum(1 for word in bearish_keywords if word in title)
                    
                    if bullish_count > bearish_count:
                        sentiment = 0.7
                    elif bearish_count > bullish_count:
                        sentiment = 0.3
                    else:
                        sentiment = 0.5
                    
                    # 根据帖子热度加权
                    weighted_sentiment = sentiment * min(score / 100, 2)  # 最大权重2
                    sentiment_scores.append(weighted_sentiment)
                
                avg_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0.5
                avg_sentiment = max(0, min(1, avg_sentiment))  # 限制在0-1之间
                
                # 分类
                if avg_sentiment < 0.4:
                    classification = "Bearish"
                elif avg_sentiment < 0.6:
                    classification = "Neutral"
                else:
                    classification = "Bullish"
                
                result = {
                    'sentiment_score': avg_sentiment,
                    'classification': classification,
                    'posts_analyzed': len(posts),
                    'avg_score': np.mean([p['data']['score'] for p in posts]),
                    'timestamp': datetime.now().isoformat(),
                    'source': 'Reddit r/Bitcoin'
                }
                
                # 缓存结果
                self._cache_data(cache_key, result)
                
                print(f"✅ Reddit情绪: {classification} ({avg_sentiment:.2f})")
                return result
            
            else:
                raise Exception("Reddit API响应格式错误")
                
        except Exception as e:
            print(f"❌ Reddit情绪获取失败: {str(e)}")
            return self._get_default_social_sentiment()
    
    def get_real_news_sentiment(self) -> Dict:
        """获取真实新闻情绪 (需要NewsAPI密钥)"""
        if not self.api_keys.get('newsapi'):
            print(f"⚠️ NewsAPI密钥未配置，使用默认值")
            return self._get_default_news_sentiment()
        
        cache_key = 'news'
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            url = self.apis['newsapi']['url']
            params = {
                'q': 'bitcoin OR cryptocurrency OR crypto',
                'language': 'en',
                'sortBy': 'publishedAt',
                'pageSize': 20,
                'apiKey': self.api_keys['newsapi']
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'articles' in data:
                articles = data['articles']
                
                sentiment_scores = []
                
                for article in articles:
                    title = article.get('title', '')
                    description = article.get('description', '')
                    
                    # 简单关键词情绪分析
                    text = (title + ' ' + description).lower()
                    
                    positive_words = ['rise', 'gain', 'bull', 'up', 'high', 'positive', 'growth', 'surge']
                    negative_words = ['fall', 'drop', 'bear', 'down', 'low', 'negative', 'crash', 'decline']
                    
                    positive_count = sum(1 for word in positive_words if word in text)
                    negative_count = sum(1 for word in negative_words if word in text)
                    
                    if positive_count > negative_count:
                        sentiment = 0.7
                    elif negative_count > positive_count:
                        sentiment = 0.3
                    else:
                        sentiment = 0.5
                    
                    sentiment_scores.append(sentiment)
                
                avg_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0.5
                
                # 分类
                if avg_sentiment < 0.4:
                    classification = "Bearish"
                elif avg_sentiment < 0.6:
                    classification = "Neutral"
                else:
                    classification = "Bullish"
                
                result = {
                    'sentiment_score': avg_sentiment,
                    'classification': classification,
                    'articles_analyzed': len(articles),
                    'timestamp': datetime.now().isoformat(),
                    'source': 'NewsAPI'
                }
                
                # 缓存结果
                self._cache_data(cache_key, result)
                
                print(f"✅ 新闻情绪: {classification} ({avg_sentiment:.2f})")
                return result
            
            else:
                raise Exception("NewsAPI响应格式错误")
                
        except Exception as e:
            print(f"❌ 新闻情绪获取失败: {str(e)}")
            return self._get_default_news_sentiment()
    
    def get_comprehensive_real_sentiment(self) -> Dict:
        """获取综合真实情绪分析"""
        sentiment_data = {}
        
        # 1. 恐慌贪婪指数 (免费)
        fear_greed = self.get_real_fear_greed_index()
        sentiment_data['fear_greed'] = fear_greed
        
        # 2. Reddit情绪 (免费)
        reddit_sentiment = self.get_real_reddit_sentiment()
        sentiment_data['reddit'] = reddit_sentiment
        
        # 3. 新闻情绪 (需要API密钥)
        news_sentiment = self.get_real_news_sentiment()
        sentiment_data['news'] = news_sentiment
        
        # 4. 链上数据 (使用免费来源或默认值)
        onchain_sentiment = self._get_simple_onchain_sentiment()
        sentiment_data['onchain'] = onchain_sentiment
        
        # 综合情绪计算
        combined_sentiment = self._combine_real_sentiment_signals(sentiment_data)
        
        return combined_sentiment
    
    def _combine_real_sentiment_signals(self, sentiment_data: Dict) -> Dict:
        """组合真实情绪信号"""
        # 动态权重 (根据数据可用性调整)
        weights = {
            'fear_greed': 0.4,
            'reddit': 0.3,
            'news': 0.2,
            'onchain': 0.1
        }
        
        weighted_score = 0
        total_weight = 0
        sentiment_breakdown = {}
        
        for source, weight in weights.items():
            if source in sentiment_data:
                data = sentiment_data[source]
                score = data.get('sentiment_score', 0.5)
                
                weighted_score += score * weight
                total_weight += weight
                
                sentiment_breakdown[source] = {
                    'score': score,
                    'weight': weight,
                    'classification': data.get('classification', 'Unknown'),
                    'source': data.get('source', 'Unknown')
                }
        
        # 计算最终情绪分数
        if total_weight > 0:
            final_sentiment_score = weighted_score / total_weight
        else:
            final_sentiment_score = 0.5
        
        # 最终分类
        if final_sentiment_score < 0.3:
            final_classification = "Strong Bearish"
        elif final_sentiment_score < 0.45:
            final_classification = "Bearish"
        elif final_sentiment_score < 0.55:
            final_classification = "Neutral"
        elif final_sentiment_score < 0.7:
            final_classification = "Bullish"
        else:
            final_classification = "Strong Bullish"
        
        # 计算情绪强度
        sentiment_strength = abs(final_sentiment_score - 0.5) * 2
        
        # 生成交易信号
        trading_signal = self._generate_real_sentiment_signal(final_sentiment_score, sentiment_strength)
        
        return {
            'overall_sentiment_score': final_sentiment_score,
            'sentiment_classification': final_classification,
            'sentiment_strength': sentiment_strength,
            'trading_signal': trading_signal,
            'sentiment_breakdown': sentiment_breakdown,
            'raw_data': sentiment_data,
            'timestamp': datetime.now().isoformat(),
            'sources_used': len(sentiment_data),
            'data_freshness': 'real_time'
        }
    
    def _generate_real_sentiment_signal(self, sentiment_score: float, strength: float) -> Dict:
        """基于真实情绪生成交易信号"""
        if sentiment_score < 0.25:  # 极度恐慌
            return {
                'direction': 'LONG',
                'strength': strength,
                'confidence': 0.75,
                'reason': '极度恐慌，逆向投资机会',
                'signal_type': 'contrarian'
            }
        elif sentiment_score < 0.4:  # 恐慌
            return {
                'direction': 'LONG',
                'strength': strength * 0.7,
                'confidence': 0.65,
                'reason': '市场恐慌，潜在买入机会',
                'signal_type': 'contrarian'
            }
        elif sentiment_score > 0.75:  # 极度贪婪
            return {
                'direction': 'SHORT',
                'strength': strength,
                'confidence': 0.75,
                'reason': '极度贪婪，逆向做空机会',
                'signal_type': 'contrarian'
            }
        elif sentiment_score > 0.6:  # 贪婪
            return {
                'direction': 'SHORT',
                'strength': strength * 0.7,
                'confidence': 0.65,
                'reason': '市场贪婪，潜在做空机会',
                'signal_type': 'contrarian'
            }
        else:  # 中性
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.4,
                'reason': '情绪中性，等待明确信号',
                'signal_type': 'neutral'
            }
    
    def _is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
        if key not in self.cache:
            return False
        
        cache_time = self.cache[key]['timestamp']
        return (datetime.now() - cache_time).total_seconds() < self.cache_duration
    
    def _cache_data(self, key: str, data: Dict):
        """缓存数据"""
        self.cache[key] = {
            'data': data,
            'timestamp': datetime.now()
        }
    
    def _get_simple_onchain_sentiment(self) -> Dict:
        """获取简单链上情绪 (使用公开数据或默认值)"""
        # 这里可以集成免费的链上数据API
        # 例如: blockchain.info, blockchair.com等
        
        return {
            'sentiment_score': 0.5,
            'classification': 'Neutral',
            'timestamp': datetime.now().isoformat(),
            'source': 'Default On-Chain'
        }
    
    # 默认值方法
    def _get_default_fear_greed(self) -> Dict:
        return {
            'index': 50,
            'classification': 'Neutral',
            'sentiment_score': 0.5,
            'timestamp': datetime.now().isoformat(),
            'source': 'Default Fear & Greed'
        }
    
    def _get_default_news_sentiment(self) -> Dict:
        return {
            'sentiment_score': 0.5,
            'classification': 'Neutral',
            'articles_analyzed': 0,
            'timestamp': datetime.now().isoformat(),
            'source': 'Default News'
        }
    
    def _get_default_social_sentiment(self) -> Dict:
        return {
            'sentiment_score': 0.5,
            'classification': 'Neutral',
            'posts_analyzed': 0,
            'timestamp': datetime.now().isoformat(),
            'source': 'Default Social'
        }

if __name__ == "__main__":
    # 测试真实情绪分析
    print("🧪 测试真实市场情绪分析")
    
    # API密钥配置 (可选)
    api_keys = {
        # 'newsapi': 'your_newsapi_key_here',
        # 'coinmarketcap': 'your_cmc_key_here'
    }
    
    analyzer = RealSentimentAnalyzer(api_keys)
    
    # 获取真实综合情绪
    sentiment = analyzer.get_comprehensive_real_sentiment()
    
    print(f"\n📊 真实综合情绪分析结果:")
    print(f"   总体情绪分数: {sentiment['overall_sentiment_score']:.2f}")
    print(f"   情绪分类: {sentiment['sentiment_classification']}")
    print(f"   数据新鲜度: {sentiment['data_freshness']}")
    print(f"   数据源数量: {sentiment['sources_used']}")
    
    print(f"\n🎯 交易信号建议:")
    signal = sentiment['trading_signal']
    print(f"   方向: {signal['direction']}")
    print(f"   强度: {signal['strength']:.2f}")
    print(f"   置信度: {signal['confidence']:.2f}")
    print(f"   理由: {signal['reason']}")
    
    print(f"\n📈 各数据源详情:")
    for source, data in sentiment['sentiment_breakdown'].items():
        print(f"   {source}: {data['score']:.2f} ({data['classification']}) - {data['source']}")
    
    print(f"\n✅ 真实情绪分析测试完成")
    print(f"\n💡 使用说明:")
    print(f"   • 恐慌贪婪指数: 免费API，实时数据")
    print(f"   • Reddit情绪: 免费API，实时数据")
    print(f"   • 新闻情绪: 需要NewsAPI密钥")
    print(f"   • 链上数据: 可集成免费API")
