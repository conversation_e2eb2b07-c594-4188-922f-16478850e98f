# 基础依赖
numpy==1.26.4
pandas==2.2.2
scikit-learn==1.4.2
matplotlib==3.8.4
seaborn==0.12.2

# 机器学习模型
xgboost==1.7.5
lightgbm==3.3.5
catboost>=0.26.0

# API客户端
python-binance==1.0.16
tweepy>=4.4.0
newsapi-python>=0.2.6

# 文本处理和情感分析
textblob>=0.15.3
nltk>=3.6.0

# 工具库
tqdm==4.65.0
requests==2.28.2
python-dotenv==1.0.0

# 开发工具
pytest>=6.2.0
pytest-cov>=2.12.0
black>=21.6b0
flake8>=3.9.0
mypy>=0.910
isort>=5.9.0

# 文档工具
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.2
mkdocs>=1.2.0
mkdocs-material>=7.2.0

# 性能分析
memory-profiler>=0.58.0
line-profiler>=3.3.0

# 监控和日志
sentry-sdk>=1.3.0
python-json-logger>=2.0.0

# 并行处理
joblib==1.2.0
dask>=2021.6.0

# 新增依赖
ta==0.10.2
shap==0.45.0
optuna==3.1.1
psutil==5.9.5
plotly==5.14.1
statsmodels==0.14.1
scipy==1.11.4
pytz==2023.3
websocket-client==1.5.1
aiohttp>=3.10.11
asyncio==3.4.3
ccxt==3.0.72
quantstats==0.0.61
river==0.20.0
praw==7.7.1
vaderSentiment==3.3.2

# 神经网络
tensorflow 