import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Optional, Union, List
import logging
from pathlib import Path
from datetime import datetime
import seaborn as sns

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Backtester:
    """
    加密货币交易策略回测器
    
    功能：
    1. 回测交易策略
    2. 计算各种性能指标
    3. 生成回测报告和可视化图表
    """
    
    def __init__(self, 
                 data: pd.DataFrame, 
                 initial_capital: float = 10000,
                 commission: float = 0.001,
                 slippage: float = 0.001,
                 output_dir: Union[str, Path] = 'backtest_results'):
        """
        初始化回测器
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
            initial_capital (float): 初始资金
            commission (float): 交易手续费率
            slippage (float): 滑点率
            output_dir (Union[str, Path]): 回测结果输出目录
        """
        self.data = data.copy()
        self.initial_capital = initial_capital
        self.commission = commission
        self.slippage = slippage
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.results = None
        self.metrics = None
        self.trades = []
        
        # 验证数据
        self._validate_data()
        
    def _validate_data(self):
        """验证输入数据的格式和完整性"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")
            
        # 确保数据按时间排序
        if not isinstance(self.data.index, pd.DatetimeIndex):
            raise ValueError("数据索引必须是DatetimeIndex类型")
            
        self.data = self.data.sort_index()
        
    def apply_strategy(self, signals: pd.Series) -> None:
        """
        根据交易信号应用策略
        
        参数:
            signals (pd.Series): 交易信号序列 (1: 买入, 0: 持有, -1: 卖出)
        """
        try:
            logger.info("开始应用交易策略...")
            
            # 验证信号
            if len(signals) != len(self.data):
                raise ValueError("信号长度与数据长度不匹配")
                
            # 计算交易成本
            self.data['positions'] = signals
            self.data['position_changes'] = self.data['positions'].diff().fillna(0.0)
            
            # 计算交易成本（手续费 + 滑点）
            trade_costs = abs(self.data['position_changes']) * (self.commission + self.slippage)
            
            # 计算每日收益
            self.data['daily_returns'] = self.data['close'].pct_change()
            self.data['strategy_returns'] = (self.data['daily_returns'] * 
                                           self.data['positions'].shift(1) - 
                                           trade_costs)
            
            # 计算投资组合价值
            self.data['portfolio_value'] = self.initial_capital * (1 + self.data['strategy_returns']).cumprod()
            
            # 记录交易
            self._record_trades()
            
            self.results = self.data
            logger.info("策略应用完成")
            
        except Exception as e:
            logger.error(f"应用策略时发生错误: {str(e)}")
            raise
            
    def _record_trades(self) -> None:
        """记录交易详情"""
        position_changes = self.data['position_changes']
        trades = position_changes[position_changes != 0]
        
        for date, change in trades.items():
            price = self.data.loc[date, 'close']
            cost = abs(change) * price * (self.commission + self.slippage)
            
            self.trades.append({
                'date': date,
                'type': '买入' if change > 0 else '卖出',
                'price': price,
                'cost': cost,
                'portfolio_value': self.data.loc[date, 'portfolio_value']
            })
            
    def calculate_metrics(self) -> Dict:
        """
        计算回测指标
        
        返回:
            Dict: 包含各种性能指标的字典
        """
        if self.results is None:
            raise ValueError("请先运行回测")
            
        try:
            # 计算基础指标
            total_return = (self.results['portfolio_value'].iloc[-1] - self.initial_capital) / self.initial_capital
            daily_returns = self.results['strategy_returns']
            
            # 年化收益率 (假设每年252个交易日)
            annualized_return = (1 + total_return) ** (252 / len(self.results)) - 1
            
            # 波动率
            volatility = daily_returns.std() * np.sqrt(252)
            
            # 最大回撤
            max_drawdown = self._calculate_max_drawdown()
            
            # 夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio()
            
            # 交易统计
            total_trades = len(self.trades)
            winning_trades = sum(1 for trade in self.trades if trade['type'] == '卖出' and 
                               trade['portfolio_value'] > self.initial_capital)
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            self.metrics = {
                '总收益率': total_return,
                '年化收益率': annualized_return,
                '波动率': volatility,
                '最大回撤': max_drawdown,
                '夏普比率': sharpe_ratio,
                '总交易次数': total_trades,
                '胜率': win_rate,
                '平均交易成本': np.mean([trade['cost'] for trade in self.trades]) if self.trades else 0
            }
            
            logger.info("性能指标计算完成")
            return self.metrics
            
        except Exception as e:
            logger.error(f"计算性能指标时发生错误: {str(e)}")
            raise
            
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        portfolio_values = self.results['portfolio_value']
        running_max = portfolio_values.cummax()
        drawdowns = (portfolio_values - running_max) / running_max
        return abs(drawdowns.min())
        
    def _calculate_sharpe_ratio(self, risk_free_rate: float = 0.02) -> float:
        """
        计算夏普比率
        
        参数:
            risk_free_rate (float): 无风险利率（年化）
        """
        daily_rf_rate = (1 + risk_free_rate) ** (1/252) - 1
        excess_returns = self.results['strategy_returns'] - daily_rf_rate
        return np.sqrt(252) * (excess_returns.mean() / excess_returns.std())
        
    def plot_results(self, save: bool = True) -> None:
        """
        生成回测结果可视化图表
        
        参数:
            save (bool): 是否保存图表
        """
        if self.results is None:
            raise ValueError("请先运行回测")
            
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 1. 投资组合价值曲线
            plt.figure(figsize=(15, 8))
            plt.plot(self.results.index, self.results['portfolio_value'], label='策略收益')
            plt.plot(self.results.index, self.initial_capital * (1 + self.results['daily_returns']).cumprod(), 
                    label='买入持有', alpha=0.7)
            plt.title('策略回测结果对比')
            plt.xlabel('日期')
            plt.ylabel('投资组合价值')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            if save:
                save_path = self.output_dir / f'portfolio_value_{timestamp}.png'
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            # 2. 回撤图
            plt.figure(figsize=(15, 8))
            portfolio_values = self.results['portfolio_value']
            running_max = portfolio_values.cummax()
            drawdowns = (portfolio_values - running_max) / running_max
            
            plt.plot(self.results.index, drawdowns, color='red', alpha=0.7)
            plt.fill_between(self.results.index, drawdowns, 0, color='red', alpha=0.3)
            plt.title('策略回撤')
            plt.xlabel('日期')
            plt.ylabel('回撤幅度')
            plt.grid(True, alpha=0.3)
            
            if save:
                save_path = self.output_dir / f'drawdown_{timestamp}.png'
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            # 3. 收益分布图
            plt.figure(figsize=(12, 6))
            sns.histplot(self.results['strategy_returns'], bins=50, kde=True)
            plt.title('策略收益分布')
            plt.xlabel('日收益率')
            plt.ylabel('频次')
            
            if save:
                save_path = self.output_dir / f'returns_dist_{timestamp}.png'
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info("回测结果可视化完成")
            
        except Exception as e:
            logger.error(f"生成可视化图表时发生错误: {str(e)}")
            raise
            
    def generate_report(self) -> None:
        """生成回测报告"""
        if self.metrics is None:
            self.calculate_metrics()
            
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_path = self.output_dir / f'backtest_report_{timestamp}.txt'
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("=== 回测报告 ===\n\n")
                f.write(f"回测期间: {self.data.index[0]} 至 {self.data.index[-1]}\n")
                f.write(f"初始资金: {self.initial_capital:,.2f}\n")
                f.write(f"最终资金: {self.results['portfolio_value'].iloc[-1]:,.2f}\n\n")
                
                f.write("--- 性能指标 ---\n")
                for metric, value in self.metrics.items():
                    f.write(f"{metric}: {value:,.4f}\n")
                    
                f.write("\n--- 交易记录 ---\n")
                for trade in self.trades:
                    f.write(f"日期: {trade['date']}, 类型: {trade['type']}, "
                           f"价格: {trade['price']:.2f}, 成本: {trade['cost']:.2f}\n")
                           
            logger.info(f"回测报告已生成: {report_path}")
            
        except Exception as e:
            logger.error(f"生成回测报告时发生错误: {str(e)}")
            raise
            
if __name__ == "__main__":
    # 测试代码
    try:
        # 创建示例数据
        dates = pd.date_range(start='2022-01-01', end='2022-12-31', freq='D')
        data = pd.DataFrame({
            'open': np.random.randn(len(dates)).cumsum() + 100,
            'high': np.random.randn(len(dates)).cumsum() + 102,
            'low': np.random.randn(len(dates)).cumsum() + 98,
            'close': np.random.randn(len(dates)).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
        
        # 创建示例信号
        signals = pd.Series(np.random.choice([-1, 0, 1], len(dates)), index=dates)
        
        # 初始化回测器
        backtester = Backtester(data, initial_capital=10000)
        
        # 运行回测
        backtester.apply_strategy(signals)
        
        # 计算指标
        metrics = backtester.calculate_metrics()
        print("\n回测指标:")
        for metric, value in metrics.items():
            print(f"{metric}: {value:,.4f}")
            
        # 生成图表
        backtester.plot_results()
        
        # 生成报告
        backtester.generate_report()
        
    except Exception as e:
        logger.error(f"测试代码执行时发生错误: {str(e)}") 