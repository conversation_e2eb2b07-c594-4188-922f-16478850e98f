#!/usr/bin/env python3
"""
ADA剥头皮系统测试版
"""

import requests
import pandas as pd
import numpy as np
import time
from datetime import datetime

def test_ada_connection():
    """测试ADA连接"""
    try:
        # 测试价格获取
        url = "https://fapi.binance.com/fapi/v1/ticker/price"
        params = {'symbol': 'ADAUSDT'}
        
        response = requests.get(url, params=params, timeout=5)
        response.raise_for_status()
        
        data = response.json()
        price = float(data['price'])
        
        print(f"✅ ADA当前价格: {price:.4f} USDT")
        
        # 测试K线获取
        url2 = "https://fapi.binance.com/fapi/v1/klines"
        params2 = {
            'symbol': 'ADAUSDT',
            'interval': '1m',
            'limit': 10
        }
        
        response2 = requests.get(url2, params=params2, timeout=5)
        response2.raise_for_status()
        
        klines = response2.json()
        print(f"✅ 获取K线数据: {len(klines)} 条")
        
        # 显示最新几条K线
        for i, kline in enumerate(klines[-3:]):
            timestamp = pd.to_datetime(kline[0], unit='ms')
            open_price = float(kline[1])
            high_price = float(kline[2])
            low_price = float(kline[3])
            close_price = float(kline[4])
            volume = float(kline[5])
            
            print(f"   K线{i+1}: {timestamp} OHLC: {open_price:.4f}/{high_price:.4f}/{low_price:.4f}/{close_price:.4f} 量: {volume:.0f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def calculate_ada_scalping_score():
    """计算ADA剥头皮适合度"""
    try:
        # 获取更多K线数据进行分析
        url = "https://fapi.binance.com/fapi/v1/klines"
        params = {
            'symbol': 'ADAUSDT',
            'interval': '1m',
            'limit': 100
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        # 转换为DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'trades_count',
            'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
        ])
        
        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # 计算剥头皮指标
        returns_1m = df['close'].pct_change()
        
        # 1分钟平均波动
        avg_move_1m = abs(returns_1m).mean()
        
        # 有效波动次数 (>0.1%变动)
        significant_moves = abs(returns_1m) > 0.001
        moves_per_hour = significant_moves.sum()
        
        # 波动率
        volatility = returns_1m.std()
        
        # 价差分析
        spreads = (df['high'] - df['low']) / df['close']
        avg_spread = spreads.mean()
        
        # 成交量稳定性
        volume_cv = df['volume'].std() / df['volume'].mean() if df['volume'].mean() > 0 else 0
        volume_stability = 1 / (1 + volume_cv)
        
        # 剥头皮评分
        scalping_score = (
            avg_move_1m * 100 +
            moves_per_hour * 0.1 +
            volume_stability * 20 +
            (1 - (spreads.std() / avg_spread if avg_spread > 0 else 0)) * 10
        )
        
        print(f"\n📊 ADA剥头皮分析:")
        print(f"   1分钟平均波动: {avg_move_1m*100:.3f}%")
        print(f"   有效波动次数: {moves_per_hour} 次/100分钟")
        print(f"   波动率: {volatility:.4f}")
        print(f"   平均价差: {avg_spread*100:.3f}%")
        print(f"   成交量稳定性: {volume_stability:.3f}")
        print(f"   剥头皮评分: {scalping_score:.2f}")
        
        if scalping_score > 25:
            print(f"   🔥 评级: 极佳剥头皮标的")
        elif scalping_score > 20:
            print(f"   ✅ 评级: 优秀剥头皮标的")
        elif scalping_score > 15:
            print(f"   ⚡ 评级: 良好剥头皮标的")
        else:
            print(f"   ⚠️ 评级: 一般剥头皮标的")
        
        return scalping_score
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return 0

def simulate_ada_scalping():
    """模拟ADA剥头皮交易"""
    print(f"\n🎯 模拟ADA剥头皮交易 (5分钟)")
    
    initial_balance = 50.0
    current_balance = initial_balance
    leverage = 125.0
    position_size_pct = 0.015
    stop_loss_pct = 0.004
    take_profit_pct = 0.008
    
    trades = 0
    wins = 0
    
    for i in range(10):  # 模拟10次交易机会
        try:
            # 获取当前价格
            url = "https://fapi.binance.com/fapi/v1/ticker/price"
            params = {'symbol': 'ADAUSDT'}
            
            response = requests.get(url, params=params, timeout=3)
            response.raise_for_status()
            
            data = response.json()
            entry_price = float(data['price'])
            
            # 模拟AI预测 (基于83.6%准确率)
            is_correct = np.random.random() < 0.836
            direction = np.random.choice(['LONG', 'SHORT'])
            confidence = np.random.uniform(0.70, 0.90) if is_correct else np.random.uniform(0.60, 0.80)
            
            if confidence < 0.68:  # 置信度门槛
                print(f"   机会{i+1}: 置信度{confidence:.1%}不足，跳过")
                continue
            
            # 计算仓位
            risk_amount = current_balance * position_size_pct
            position_value = risk_amount * leverage
            position_size = position_value / entry_price
            
            # 计算止损止盈
            if direction == "LONG":
                stop_loss = entry_price * (1 - stop_loss_pct)
                take_profit = entry_price * (1 + take_profit_pct)
            else:
                stop_loss = entry_price * (1 + stop_loss_pct)
                take_profit = entry_price * (1 - take_profit_pct)
            
            # 模拟市场变动
            market_move = np.random.normal(0, 0.002)  # ADA典型波动
            
            if is_correct:
                # 预测正确，价格朝预测方向移动
                if direction == "LONG":
                    exit_price = entry_price * (1 + abs(market_move))
                else:
                    exit_price = entry_price * (1 - abs(market_move))
            else:
                # 预测错误，价格朝相反方向移动
                if direction == "LONG":
                    exit_price = entry_price * (1 - abs(market_move))
                else:
                    exit_price = entry_price * (1 + abs(market_move))
            
            # 应用止损止盈
            if direction == "LONG":
                if exit_price <= stop_loss:
                    exit_price = stop_loss
                    exit_reason = "止损"
                elif exit_price >= take_profit:
                    exit_price = take_profit
                    exit_reason = "止盈"
                else:
                    exit_reason = "正常"
            else:
                if exit_price >= stop_loss:
                    exit_price = stop_loss
                    exit_reason = "止损"
                elif exit_price <= take_profit:
                    exit_price = take_profit
                    exit_reason = "止盈"
                else:
                    exit_reason = "正常"
            
            # 计算盈亏
            if direction == "LONG":
                pnl_pct = (exit_price - entry_price) / entry_price
            else:
                pnl_pct = (entry_price - exit_price) / entry_price
            
            leveraged_pnl = pnl_pct * leverage
            pnl_amount = risk_amount * leveraged_pnl
            
            current_balance += pnl_amount
            trades += 1
            
            is_winner = pnl_amount > 0
            if is_winner:
                wins += 1
            
            status = "✅" if is_winner else "❌"
            print(f"   交易{trades}: {direction} @ {entry_price:.4f} → {exit_price:.4f} ({exit_reason})")
            print(f"           {status} 盈亏: ${pnl_amount:+.2f} ({leveraged_pnl:+.2%}) 置信度: {confidence:.1%}")
            
            time.sleep(2)  # 模拟交易间隔
            
        except Exception as e:
            print(f"   交易{i+1}: 获取数据失败 - {e}")
    
    # 显示结果
    win_rate = wins / trades if trades > 0 else 0
    total_return = (current_balance - initial_balance) / initial_balance
    
    print(f"\n📈 模拟结果:")
    print(f"   总交易: {trades}")
    print(f"   胜率: {win_rate:.1%}")
    print(f"   最终余额: ${current_balance:.2f}")
    print(f"   总收益: {total_return:+.1%}")

if __name__ == "__main__":
    print("🎯 ADA剥头皮系统测试")
    print("="*50)
    
    # 测试连接
    if test_ada_connection():
        # 分析适合度
        score = calculate_ada_scalping_score()
        
        if score > 20:
            # 模拟交易
            simulate_ada_scalping()
        else:
            print("⚠️ ADA剥头皮评分较低，建议选择其他币种")
    
    print("\n🎉 测试完成！")
