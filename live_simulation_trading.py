#!/usr/bin/env python3
"""
实时永续合约模拟交易系统 - 基于实时数据进行预测和永续合约交易
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class LiveSimulationTrader:
    """
    实时模拟交易器
    """
    
    def __init__(self, initial_capital=50, model_path=None):
        """
        初始化实时模拟交易器
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.position = 0  # 0=空仓, 1=持仓
        self.entry_price = 0
        self.entry_time = None
        
        # 策略参数 (使用最佳参数)
        self.confidence_threshold = 0.58  # 降低阈值增加交易频率
        self.stop_loss_ratio = 0.04
        self.commission_rate = 0.002
        
        # 交易记录
        self.trades = []
        self.equity_history = []
        self.prediction_history = []
        
        # 加载模型
        if model_path is None:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if not model_files:
                raise ValueError("未找到BTCUSDT模型文件")
            model_path = max(model_files, key=lambda x: x.split('_')[-1])
        
        print(f"📦 加载模型: {model_path}")
        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']
        
        # 初始化数据获取器
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        
        print(f"🚀 实时模拟交易器初始化完成")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   置信度阈值: {self.confidence_threshold}")
        print(f"   止损比例: {self.stop_loss_ratio:.1%}")
        print(f"   手续费: {self.commission_rate:.1%}")
    
    def get_current_prediction(self, symbol='BTCUSDT'):
        """
        获取当前实时预测
        """
        try:
            # 获取最近的历史数据 (需要足够的数据来计算技术指标)
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)  # 获取30天数据确保技术指标计算
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d')
            )
            
            if len(df) < 200:  # 确保有足够数据
                print("⚠️  数据不足，无法进行预测")
                return None, None, None
            
            # 特征工程
            df_features = self.engineer.create_features(df)
            
            # 准备最新数据进行预测
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            # 只使用最新的数据点
            X_latest = X.iloc[-1:].copy()
            
            # 数据清理
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            # 预测
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            # 获取当前价格
            current_price = df['close'].iloc[-1]
            current_time = df.index[-1]
            
            return up_probability, current_price, current_time
            
        except Exception as e:
            print(f"❌ 获取预测时发生错误: {str(e)}")
            return None, None, None
    
    def should_buy(self, up_probability):
        """
        判断是否应该买入
        """
        if self.position != 0:
            return False, "已有持仓"
        
        if up_probability > self.confidence_threshold:
            return True, f"买入信号 (置信度: {up_probability:.3f})"
        
        return False, f"置信度不足 ({up_probability:.3f} < {self.confidence_threshold})"
    
    def should_sell(self, up_probability, current_price):
        """
        判断是否应该卖出
        """
        if self.position == 0:
            return False, "无持仓"
        
        # 计算当前盈亏
        pnl_ratio = (current_price - self.entry_price) / self.entry_price
        
        # 止损检查
        if pnl_ratio < -self.stop_loss_ratio:
            return True, f"止损 (亏损: {pnl_ratio:.2%})"
        
        # 信号反转检查
        if up_probability < (1 - self.confidence_threshold):
            return True, f"信号反转 (置信度: {up_probability:.3f})"
        
        return False, "持有"
    
    def execute_trade(self, action, price, timestamp, confidence=None, reason=""):
        """
        执行交易
        """
        if action == 'BUY':
            # 计算可买入数量 (扣除手续费)
            available_capital = self.capital * (1 - self.commission_rate)
            shares = available_capital / price
            
            self.position = shares
            self.entry_price = price
            self.entry_time = timestamp
            self.capital = 0  # 全仓买入
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'BUY',
                'price': price,
                'shares': shares,
                'confidence': confidence,
                'reason': reason,
                'capital_before': self.capital + available_capital
            }
            
            print(f"✅ 买入 {shares:.6f} BTC @ ${price:,.2f} (置信度: {confidence:.3f})")
            
        elif action == 'SELL':
            # 计算卖出收益 (扣除手续费)
            proceeds = self.position * price * (1 - self.commission_rate)
            pnl = proceeds - (self.position * self.entry_price)
            pnl_ratio = pnl / (self.position * self.entry_price)
            
            self.capital = proceeds
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'SELL',
                'price': price,
                'shares': self.position,
                'proceeds': proceeds,
                'pnl': pnl,
                'pnl_ratio': pnl_ratio,
                'confidence': confidence,
                'reason': reason,
                'hold_time': (timestamp - self.entry_time).total_seconds() / 3600 if self.entry_time else 0
            }
            
            self.position = 0
            self.entry_price = 0
            self.entry_time = None
            
            print(f"✅ 卖出 @ ${price:,.2f} (盈亏: {pnl_ratio:+.2%}, 资金: ${self.capital:.2f})")
        
        self.trades.append(trade_record)
    
    def update_equity(self, current_price, timestamp):
        """
        更新权益记录
        """
        if self.position > 0:
            current_value = self.position * current_price
        else:
            current_value = self.capital
        
        equity_record = {
            'timestamp': timestamp,
            'price': current_price,
            'position': self.position,
            'capital': self.capital,
            'equity': current_value,
            'total_return': (current_value - self.initial_capital) / self.initial_capital
        }
        
        self.equity_history.append(equity_record)
    
    def get_current_status(self):
        """
        获取当前状态
        """
        if self.equity_history:
            latest_equity = self.equity_history[-1]['equity']
            total_return = (latest_equity - self.initial_capital) / self.initial_capital
        else:
            latest_equity = self.capital
            total_return = 0
        
        completed_trades = [t for t in self.trades if t['action'] == 'SELL']
        profitable_trades = [t for t in completed_trades if t.get('pnl', 0) > 0]
        win_rate = len(profitable_trades) / len(completed_trades) if completed_trades else 0
        
        return {
            'current_equity': latest_equity,
            'total_return': total_return,
            'position': self.position,
            'entry_price': self.entry_price,
            'total_trades': len(completed_trades),
            'win_rate': win_rate,
            'capital': self.capital
        }
    
    def print_status(self, current_price=None, up_probability=None):
        """
        打印当前状态
        """
        status = self.get_current_status()
        
        print(f"\n📊 实时模拟交易状态")
        print("=" * 50)
        print(f"当前权益: ${status['current_equity']:.2f}")
        print(f"总收益率: {status['total_return']:+.2%}")
        print(f"当前资金: ${status['capital']:.2f}")
        print(f"持仓数量: {status['position']:.6f} BTC")
        if status['position'] > 0:
            print(f"入场价格: ${status['entry_price']:,.2f}")
            if current_price:
                unrealized_pnl = (current_price - status['entry_price']) / status['entry_price']
                print(f"未实现盈亏: {unrealized_pnl:+.2%}")
        print(f"完成交易: {status['total_trades']}")
        print(f"胜率: {status['win_rate']:.2%}")
        
        if current_price and up_probability:
            print(f"\n📈 当前市场信息:")
            print(f"BTC价格: ${current_price:,.2f}")
            print(f"上涨概率: {up_probability:.3f} ({up_probability:.1%})")
            
            if up_probability > self.confidence_threshold:
                signal = "🟢 强烈看涨"
            elif up_probability > 0.5:
                signal = "🟡 轻微看涨"
            elif up_probability > (1 - self.confidence_threshold):
                signal = "🟡 轻微看跌"
            else:
                signal = "🔴 强烈看跌"
            
            print(f"市场信号: {signal}")
    
    def save_session(self, filename=None):
        """
        保存交易会话
        """
        if filename is None:
            filename = f"simulation_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        session_data = {
            'initial_capital': self.initial_capital,
            'current_status': self.get_current_status(),
            'trades': self.trades,
            'equity_history': self.equity_history[-100:],  # 只保存最近100条记录
            'parameters': {
                'confidence_threshold': self.confidence_threshold,
                'stop_loss_ratio': self.stop_loss_ratio,
                'commission_rate': self.commission_rate
            }
        }
        
        # 转换datetime对象为字符串
        def convert_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, pd.Timestamp):
                return obj.isoformat()
            return obj
        
        # 递归转换所有datetime对象
        def convert_dict(d):
            if isinstance(d, dict):
                return {k: convert_dict(v) for k, v in d.items()}
            elif isinstance(d, list):
                return [convert_dict(item) for item in d]
            else:
                return convert_datetime(d)
        
        session_data = convert_dict(session_data)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
        
        print(f"📝 交易会话已保存: {filename}")

def run_live_simulation(check_interval=300):  # 默认5分钟检查一次
    """
    运行实时模拟交易
    
    Args:
        check_interval: 检查间隔(秒)，默认300秒(5分钟)
    """
    print("🚀 启动实时模拟交易系统")
    print("=" * 60)
    print(f"检查间隔: {check_interval}秒 ({check_interval/60:.1f}分钟)")
    print("按 Ctrl+C 停止交易")
    print("")
    
    trader = LiveSimulationTrader(initial_capital=50)
    
    try:
        while True:
            print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 检查市场...")
            
            # 获取当前预测
            up_prob, current_price, current_time = trader.get_current_prediction()
            
            if up_prob is None:
                print("❌ 无法获取预测，跳过本次检查")
                time.sleep(check_interval)
                continue
            
            # 记录预测历史
            trader.prediction_history.append({
                'timestamp': current_time,
                'price': current_price,
                'up_probability': up_prob
            })
            
            # 更新权益
            trader.update_equity(current_price, current_time)
            
            # 检查卖出信号
            should_sell, sell_reason = trader.should_sell(up_prob, current_price)
            if should_sell:
                trader.execute_trade('SELL', current_price, current_time, up_prob, sell_reason)
            
            # 检查买入信号
            should_buy, buy_reason = trader.should_buy(up_prob)
            if should_buy:
                trader.execute_trade('BUY', current_price, current_time, up_prob, buy_reason)
            
            # 打印状态
            trader.print_status(current_price, up_prob)
            
            # 每小时保存一次会话
            if len(trader.equity_history) % 12 == 0:  # 5分钟*12 = 1小时
                trader.save_session()
            
            # 等待下次检查
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟后进行下次检查...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 用户停止交易")
        trader.print_status(current_price, up_prob)
        trader.save_session()
        print(f"感谢使用实时模拟交易系统！")

if __name__ == "__main__":
    import sys
    
    # 可以通过命令行参数设置检查间隔
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300  # 默认5分钟
    
    print("⚠️  实时模拟交易说明:")
    print("- 这是模拟交易，不会产生真实资金损失")
    print("- 基于实时数据进行预测和交易决策")
    print("- 会自动保存交易记录")
    print("- 建议先观察一段时间再考虑实盘")
    print("")
    
    run_live_simulation(interval)
