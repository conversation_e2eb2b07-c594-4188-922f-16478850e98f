"""
项目配置文件
"""

import os
from pathlib import Path
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 基础路径配置
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / 'data'
MODELS_DIR = BASE_DIR / 'models'
RESULTS_DIR = BASE_DIR / 'results'
LOGS_DIR = BASE_DIR / 'logs'

# 创建必要的目录
for dir_path in [DATA_DIR, MODELS_DIR, RESULTS_DIR, LOGS_DIR]:
    dir_path.mkdir(parents=True, exist_ok=True)

# 尝试导入本地配置
try:
    from config_local import *
    logging.info("成功加载本地配置")
except ImportError:
    logging.warning("未找到本地配置文件 config_local.py，将使用默认配置")
    from config_example import *

# 确保配置完整性
def validate_config():
    """验证配置的完整性"""
    required_configs = [
        ('BINANCE_CONFIG', ['api_key', 'api_secret']),
        ('DB_CONFIG', ['host', 'port', 'database', 'user', 'password']),
        ('LOG_CONFIG', ['level', 'file', 'format']),
        ('MODEL_CONFIG', ['version', 'path']),
        ('BACKTEST_CONFIG', ['initial_capital', 'commission', 'slippage']),
        ('SENTIMENT_CONFIG', ['twitter_weight', 'news_weight'])
    ]
    
    for config_name, required_fields in required_configs:
        if config_name not in globals():
            raise ValueError(f"缺少必要的配置: {config_name}")
        config = globals()[config_name]
        for field in required_fields:
            if field not in config:
                raise ValueError(f"配置 {config_name} 缺少必要的字段: {field}")

# 验证配置
validate_config()

# API配置
BINANCE_CONFIG = {
    'api_key': os.getenv('BINANCE_API_KEY'),
    'api_secret': os.getenv('BINANCE_API_SECRET'),
    'testnet': os.getenv('USE_TESTNET', 'false').lower() == 'true'
}

TWITTER_CONFIG = {
    'api_key': os.getenv('TWITTER_API_KEY'),
    'api_secret': os.getenv('TWITTER_API_SECRET'),
    'access_token': os.getenv('TWITTER_ACCESS_TOKEN'),
    'access_secret': os.getenv('TWITTER_ACCESS_SECRET')
}

NEWSAPI_CONFIG = {
    'api_key': os.getenv('NEWSAPI_KEY')
}

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'database': os.getenv('DB_NAME', 'crypto_prediction'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD')
}

# 日志配置
LOG_CONFIG = {
    'level': os.getenv('LOG_LEVEL', 'INFO'),
    'file': os.getenv('LOG_FILE', str(LOGS_DIR / 'app.log')),
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 监控配置
MONITORING_CONFIG = {
    'sentry_dsn': os.getenv('SENTRY_DSN'),
    'alert_email': os.getenv('ALERT_EMAIL'),
    'performance_threshold': 0.7,
    'error_threshold': 0.1
}

# 性能配置
PERFORMANCE_CONFIG = {
    'batch_size': int(os.getenv('BATCH_SIZE', 1000)),
    'num_workers': int(os.getenv('NUM_WORKERS', 4)),
    'cache_ttl': int(os.getenv('CACHE_TTL', 3600))
}

# 模型配置
MODEL_CONFIG = {
    'version': os.getenv('MODEL_VERSION', '1.0.0'),
    'path': os.getenv('MODEL_PATH', str(MODELS_DIR)),
    'feature_config': os.getenv('FEATURE_CONFIG', str(BASE_DIR / 'config' / 'features.json')),
    'window_size': 1000,
    'step_size': 100,
    'performance_threshold': 0.7
}

# 回测配置
BACKTEST_CONFIG = {
    'initial_capital': 10000,
    'commission': 0.001,
    'slippage': 0.001,
    'output_dir': str(RESULTS_DIR / 'backtest')
}

# 情绪分析配置
SENTIMENT_CONFIG = {
    'twitter_weight': 0.4,
    'news_weight': 0.6,
    'update_interval': 3600,  # 1小时
    'output_dir': str(RESULTS_DIR / 'sentiment')
}

# 特征工程配置
FEATURE_CONFIG = {
    'technical_indicators': [
        'RSI',
        'MACD',
        'BB',
        'ATR',
        'OBV'
    ],
    'time_features': [
        'hour',
        'day_of_week',
        'day_of_month',
        'month'
    ],
    'sentiment_features': [
        'twitter_sentiment',
        'news_sentiment',
        'combined_sentiment'
    ]
}

# 交易配置
TRADING_CONFIG = {
    'symbols': ['BTC/USDT', 'ETH/USDT'],
    'timeframes': ['1h', '4h', '1d'],
    'max_positions': 5,
    'risk_per_trade': 0.02,
    'stop_loss': 0.02,
    'take_profit': 0.04
} 