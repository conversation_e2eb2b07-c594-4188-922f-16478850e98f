import pandas as pd
import numpy as np
from typing import Dict, Optional, Union, List, Tuple
import logging
from pathlib import Path
from datetime import datetime
import joblib
from sklearn.base import BaseEstimator
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns
from river import drift
from river import base as river_base
from sklearn.utils.validation import check_is_fitted
from sklearn.exceptions import NotFittedError
# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OnlineLearner:
    """
    在线学习器，支持增量学习、滑动窗口训练，并集成概念漂移检测。
    
    功能：
    1. 增量更新模型
    2. 滑动窗口训练
    3. 性能监控和评估
    4. 模型性能可视化
    """
    
    def __init__(self,
                 model: BaseEstimator,
                 window_size: int = 1000,
                 step_size: int = 100,
                 performance_threshold: Optional[float] = 0.6,
                 output_dir: Union[str, Path] = 'online_learning_results',
                 drift_detector_type: str = 'adwin',
                 drift_sensitivity: float = 0.002):
        """
        初始化在线学习器
        
        参数:
            model (BaseEstimator): 支持增量学习的基础模型
            window_size (int): 滑动窗口大小
            step_size (int): 滑动步长
            performance_threshold (Optional[float]): 性能阈值，低于此值时触发模型更新
            output_dir (Union[str, Path]): 输出目录
            drift_detector_type (str): 使用的概念漂移检测器类型
            drift_sensitivity (float): 漂移检测器的配置参数 (如ADWIN的delta)
        """
        self.model = model
        self.window_size = window_size
        self.step_size = step_size
        self.performance_threshold = performance_threshold
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.drift_detector = self._initialize_drift_detector(drift_detector_type, drift_sensitivity)
        self.data_buffer_X = pd.DataFrame()
        self.data_buffer_y = pd.Series(dtype='float64')
        
        self.performance_history: List[Dict[str, Any]] = []
        self.drift_points: List[int] = []
        self.update_count = 0
        
    def _initialize_drift_detector(self, detector_type: str, sensitivity: float) -> Optional[river_base.DriftDetector]:
        """初始化概念漂移检测器"""
        logger.info(f"初始化概念漂移检测器: {detector_type} (sensitivity/delta: {sensitivity})")
        if detector_type == 'adwin':
            return drift.ADWIN(delta=sensitivity)
        elif detector_type == 'ddm':
            return drift.DDM()
        elif detector_type == 'eddm':
            return drift.EDDM()
        elif detector_type == 'hddm_a':
            return drift.HDDM_A(drift_confidence=1-sensitivity)
        elif detector_type == 'kswin':
            return drift.KSWIN(alpha=sensitivity, window_size=100, stat_size=30)
        else:
            logger.warning(f"不支持的漂移检测器类型: {detector_type}. 将不使用漂移检测。")
            return None

    def update(self, X_new: Union[pd.DataFrame, pd.Series], y_new: Union[pd.Series, float, int]) -> Optional[Dict[str, float]]:
        """
        处理新到达的数据点或小批量数据，进行预测、评估、漂移检测和模型更新。
        参数:
            X_new (Union[pd.DataFrame, pd.Series]): 新的单个特征数据点或小批量特征数据。
            y_new (Union[pd.Series, float, int]): 新的单个标签或小批量标签。
        返回:
            Optional[Dict[str, float]]: 如果进行了评估，则返回性能指标。
        """
        try:
            last_calculated_performance: Optional[Dict[str, float]] = None
            if isinstance(X_new, pd.Series):
                X_new = X_new.to_frame().T
            if not hasattr(y_new, '__iter__') or isinstance(y_new, (str, float, int)):
                y_new = pd.Series([y_new], index=X_new.index if isinstance(X_new, pd.DataFrame) else None)
            
            if X_new.empty:
                return None

            for idx in range(len(X_new)):
                x_instance = X_new.iloc[[idx]]
                y_instance = y_new.iloc[idx]
                self.update_count += 1

                y_pred_instance = None
                prediction_error = None
                if hasattr(self.model, 'classes_'):
                    y_pred_instance = self.model.predict(x_instance)[0]
                    prediction_error = 0 if y_pred_instance == y_instance else 1
                
                self.data_buffer_X = pd.concat([self.data_buffer_X, x_instance])
                self.data_buffer_y = pd.concat([
                    self.data_buffer_y, 
                    pd.Series([float(y_instance)], index=x_instance.index, dtype='float64')
                ])
                
                if len(self.data_buffer_X) > self.window_size:
                    self.data_buffer_X = self.data_buffer_X.iloc[-self.window_size:]
                    self.data_buffer_y = self.data_buffer_y.iloc[-self.window_size:]
                
                drift_detected = False
                if self.drift_detector and prediction_error is not None:
                    self.drift_detector.update(prediction_error)
                    if self.drift_detector.drift_detected:
                        drift_detected = True
                        self.drift_points.append(self.update_count)
                        logger.warning(f"概念漂移在数据点 {self.update_count} 处检测到! 类型: {self.drift_detector.__class__.__name__}")
                        self.drift_detector.reset()
                
                current_performance_metrics = None
                
                should_retrain = False
                if drift_detected:
                    logger.info("由于检测到概念漂移，触发模型重新训练。")
                    should_retrain = True
                elif prediction_error is not None and self.performance_threshold is not None:
                    if len(self.data_buffer_X) >= self.window_size:
                        pass
                
                if len(self.data_buffer_X) < self.window_size and should_retrain:
                    logger.info(f"数据缓冲区大小 {len(self.data_buffer_X)} 小于窗口大小 {self.window_size}，暂不重训练。")
                    should_retrain = False
                
                if should_retrain and len(self.data_buffer_X) >= self.window_size:
                    logger.info(f"使用大小为 {len(self.data_buffer_X)} 的当前数据缓冲区重新训练模型...")
                    try:
                        self.model.fit(self.data_buffer_X, self.data_buffer_y)
                        logger.info(f"模型在数据点 {self.update_count} 后重新训练完成。")
                        if hasattr(self.model, 'classes_'):
                            eval_preds = self.model.predict(self.data_buffer_X.tail(min(100, len(self.data_buffer_X))))
                            eval_true = self.data_buffer_y.tail(min(100, len(self.data_buffer_y)))
                            acc = accuracy_score(eval_true, eval_preds)
                            self.performance_history.append({
                                'update_count': self.update_count,
                                'event': 'retrain_after_drift' if drift_detected else 'retrain_periodic',
                                'accuracy_after_retrain_on_buffer_tail': acc,
                                'drift_detected': drift_detected
                            })
                           
                            logger.info(f"重训练后在缓冲区尾部100点准确率: {acc:.4f}")
                    except Exception as e:
                        logger.error(f"在线模型训练失败: {e}", exc_info=True)
                
                if not self.drift_detector and self.performance_threshold and self.update_count % self.window_size == 0:
                    if len(self.data_buffer_X) >= self.window_size:
                        evaluated_metrics = self._evaluate_performance(self.data_buffer_X, self.data_buffer_y)
                        self.performance_history.append({
                                'update_count': self.update_count,
                                'event': 'periodic_evaluation',
                                'accuracy': evaluated_metrics.get('accuracy'),
                                'precision': evaluated_metrics.get('precision'),
                                'recall': evaluated_metrics.get('recall'),
                                'f1': evaluated_metrics.get('f1'),
                                'drift_detected': False
                            })
                        current_performance_metrics = evaluated_metrics
                        last_calculated_performance = current_performance_metrics
                        
                        accuracy_val = evaluated_metrics.get('accuracy', 0.0)
                        if accuracy_val < self.performance_threshold:
                            logger.info(f"周期性评估：缓冲区准确率 {accuracy_val:.4f} 低于阈值 {self.performance_threshold}。触发重训练。")
                            self.model.fit(self.data_buffer_X, self.data_buffer_y)
                            logger.info("周期性模型重训练完成。")
                        else:
                            logger.info(f"周期性评估：缓冲区准确率 {accuracy_val:.4f} 满足要求。")
            
            return last_calculated_performance
            
        except Exception as e:
            logger.error(f"在线更新过程中发生错误: {str(e)}", exc_info=True)
            raise
            
    def sliding_window_train(self, X: pd.DataFrame, y: pd.Series) -> List[Dict[str, float]]:
        """
        使用滑动窗口方法训练模型
        
        参数:
            X (pd.DataFrame): 完整的特征数据
            y (pd.Series): 完整的标签数据
            
        返回:
            List[Dict[str, float]]: 每个窗口的性能指标
        """
        try:
            logger.info("开始滑动窗口训练...")
            performance_history = []
            
            for start_idx in range(0, len(X) - self.window_size, self.step_size):
                end_idx = start_idx + self.window_size
                
                # 获取当前窗口的数据
                X_window = X.iloc[start_idx:end_idx]
                y_window = y.iloc[start_idx:end_idx]
                
                # 训练模型
                self.model.fit(X_window, y_window)
                
                # 在下一个步长的数据上评估性能
                next_start = end_idx
                next_end = min(next_start + self.step_size, len(X))
                X_eval = X.iloc[next_start:next_end]
                y_eval = y.iloc[next_start:next_end]
                
                performance = self._evaluate_performance(X_eval, y_eval)
                performance['window_start'] = start_idx
                performance['window_end'] = end_idx
                performance_history.append(performance)
                
                logger.info(f"窗口 [{start_idx}:{end_idx}] 训练完成，"
                          f"准确率: {performance['accuracy']:.4f}")
                
            self.performance_history = performance_history
            return performance_history
            
        except Exception as e:
            logger.error(f"滑动窗口训练时发生错误: {str(e)}")
            raise
            
    def _evaluate_performance(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]: # 修改返回类型
        """
        评估模型性能
        
        参数:
            X (pd.DataFrame): 特征数据
            y (pd.Series): 标签数据
            
        返回:
            Dict[str, float]: 性能指标
        """
        try:
            check_is_fitted(self.model)
        except NotFittedError:
            logger.warning("模型尚未训练，跳过性能评估。返回默认指标。")
            return {
                'accuracy': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0
            }

        if X.empty or y.empty:
            logger.warning("评估数据为空，返回默认指标。")
            return {
                'accuracy': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0
            }

        y_pred = self.model.predict(X)
        return {
            'accuracy': accuracy_score(y, y_pred),
            'precision': precision_score(y, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y, y_pred, average='weighted', zero_division=0),
            'f1': f1_score(y, y_pred, average='weighted', zero_division=0)
        }
        
    def plot_performance_history(self, save: bool = True) -> None:
        """绘制性能历史和漂移点图表"""
        # 解决中文字体显示问题
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei'] # 指定默认字体为黑体
            plt.rcParams['axes.unicode_minus'] = False # 解决保存图像是负号'-'显示为方块的问题
        except Exception as e:
            logger.warning(f"设置中文字体失败，图表中的中文可能无法正确显示: {e}")

        if not self.performance_history:
            logger.warning("没有可用的性能历史数据")
            return
            
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            history_df = pd.DataFrame(self.performance_history)

            plt.figure(figsize=(15, 8))
            if 'buffer_accuracy' in history_df.columns:
                plt.plot(history_df['update_count'], history_df['buffer_accuracy'], label='缓冲区准确率 (周期性)', marker='.', linestyle='-')
            if 'accuracy_after_retrain_on_buffer_tail' in history_df.columns:
                 plt.plot(history_df['update_count'], history_df['accuracy_after_retrain_on_buffer_tail'], label='重训练后准确率 (缓冲区尾部)', marker='o', linestyle='--')
            
            if self.drift_points:
                for drift_point_update_count in self.drift_points:
                    y_val_for_drift_line = self.performance_threshold or 0.5
                    plt.axvline(x=drift_point_update_count, color='r', linestyle=':', linewidth=2, label=f'Drift @ {drift_point_update_count}' if drift_point_update_count == self.drift_points[0] else "")
            
            if self.performance_threshold is not None:
                plt.axhline(y=self.performance_threshold, color='gray', linestyle='--', label='性能阈值')
            
            plt.title('在线学习性能与概念漂移点')
            plt.xlabel('数据点更新次数')
            plt.ylabel('准确率')
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            if save:
                save_path = self.output_dir / f'online_performance_drift_{timestamp}.png'
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"在线学习性能图已保存到: {save_path}")
            if not save:
                plt.show()
            plt.close()

        except Exception as e:
            logger.error(f"绘制性能历史图表时发生错误: {str(e)}", exc_info=True)
            
            # 2. 性能指标箱线图
            plt.figure(figsize=(10, 6))
            history_df[['accuracy', 'precision', 'recall', 'f1']].boxplot()
            plt.title('性能指标分布')
            plt.ylabel('分数')
            plt.grid(True, alpha=0.3)
            
            if save:
                save_path = self.output_dir / f'performance_dist_{timestamp}.png'
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info("性能历史图表生成完成")
            
        except Exception as e:
            logger.error(f"生成性能历史图表时发生错误: {str(e)}")
            raise
            
    def save_state(self, filename: Optional[str] = None) -> None:
        """
        保存学习器状态
        
        参数:
            filename (Optional[str]): 保存文件名，如果为None则自动生成
        """
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f'online_learner_state_{timestamp}.joblib'
                
            save_path = self.output_dir / filename
            
            state = {
                'model': self.model,
                'window_size': self.window_size,
                'step_size': self.step_size,
                'performance_threshold': self.performance_threshold,
                'performance_history': self.performance_history,
                'data_buffer_X': self.data_buffer_X, # 修改
                'data_buffer_y': self.data_buffer_y  # 修改
            }
            
            joblib.dump(state, save_path)
            logger.info(f"学习器状态已保存到: {save_path}")
            
        except Exception as e:
            logger.error(f"保存学习器状态时发生错误: {str(e)}")
            raise
            
    @classmethod
    def load_state(cls, filepath: Union[str, Path]) -> 'OnlineLearner':
        """
        加载学习器状态
        
        参数:
            filepath (Union[str, Path]): 状态文件路径
            
        返回:
            OnlineLearner: 加载了状态的学习器实例
        """
        try:
            state = joblib.load(filepath)
            
            instance = cls(
                model=state['model'],
                window_size=state['window_size'],
                step_size=state['step_size'],
                performance_threshold=state['performance_threshold']
            )
            
            instance.performance_history = state['performance_history']
            instance.data_buffer_X = state['data_buffer_X'] # 修改
            instance.data_buffer_y = state['data_buffer_y'] # 修改
            
            logger.info(f"成功从 {filepath} 加载学习器状态")
            return instance
            
        except Exception as e:
            logger.error(f"加载学习器状态时发生错误: {str(e)}")
            raise
            
if __name__ == "__main__":
    # 测试代码
    from sklearn.linear_model import SGDClassifier
    
    try:
        # 创建示例数据
        np.random.seed(42)
        n_samples = 2000
        X = pd.DataFrame(np.random.randn(n_samples, 5), 
                        columns=[f'feature_{i}' for i in range(5)])
        y = pd.Series((X.sum(axis=1) > 0).astype(int))
        
        # 创建基础模型
        base_model = SGDClassifier(loss='log_loss', random_state=42)
        
        # 初始化在线学习器
        learner = OnlineLearner(
            model=base_model,
            window_size=500,
            step_size=100,
            performance_threshold=0.7
        )
        
        # 测试滑动窗口训练
        performance_history = learner.sliding_window_train(X, y)
        
        # 测试增量更新
        X_new = pd.DataFrame(np.random.randn(100, 5), 
                            columns=[f'feature_{i}' for i in range(5)])
        y_new = pd.Series((X_new.sum(axis=1) > 0).astype(int))
        
        performance = learner.update(X_new, y_new)
        print("\n增量更新后的性能:")
        if performance:
            for metric, value in performance.items():
                print(f"{metric}: {value:.4f}")
        else:
            print("在这次增量更新中未计算性能指标 (可能是数据量未达到评估周期)。")
            
        # 生成性能图表
        learner.plot_performance_history()
        
        # 保存和加载状态
        learner.save_state()
        loaded_learner = OnlineLearner.load_state(
            next(learner.output_dir.glob('online_learner_state_*.joblib'))
        )
        
    except Exception as e:
        logger.error(f"测试代码执行时发生错误: {str(e)}") 