#!/usr/bin/env python3
"""
优化策略 - 使用最佳参数的交易策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

def optimized_backtest(model_path, symbol='BTCUSDT', test_months=3, 
                      confidence_threshold=0.7, stop_loss_ratio=0.05):
    """
    使用优化参数的回测
    """
    print(f"🚀 优化策略回测 {symbol}")
    print(f"📊 参数: 置信度={confidence_threshold}, 止损={stop_loss_ratio}")
    
    # 加载模型
    model_data = joblib.load(model_path)
    model = model_data['model']
    scaler = model_data['scaler']
    
    # 获取测试数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=test_months*30)
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date.strftime('%Y-%m-%d'))
    
    # 特征工程
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    X = df_features.drop(columns=['target'])
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    mask = ~X.isna().any(axis=1)
    X = X[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    # 预测
    X_scaled = scaler.transform(X)
    prediction_proba = model.predict_proba(X_scaled)
    up_proba = prediction_proba[:, 1]
    
    # 优化交易策略
    capital = 10000
    position = 0
    trades = []
    equity_curve = []
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    for i in range(len(up_proba)):
        price = prices[i]
        up_prob = up_proba[i]
        
        # 买入信号
        if up_prob > confidence_threshold and position == 0:
            position = 1
            entry_price = price
            trades.append({
                'type': '买入',
                'price': price,
                'time': timestamps[i],
                'confidence': up_prob
            })
        
        # 卖出信号
        elif position == 1 and (up_prob < (1 - confidence_threshold) or 
                               (price / entry_price - 1) < -stop_loss_ratio):
            position = 0
            pnl_ratio = (price - entry_price) / entry_price
            capital = capital * (1 + pnl_ratio - 0.002)
            
            trades.append({
                'type': '卖出',
                'price': price,
                'entry_price': entry_price,
                'pnl_ratio': pnl_ratio,
                'time': timestamps[i],
                'confidence': up_prob
            })
        
        # 记录权益
        if position == 1:
            current_value = capital * (price / entry_price)
        else:
            current_value = capital
        
        equity_curve.append({
            'time': timestamps[i],
            'value': current_value,
            'price': price,
            'position': position,
            'up_prob': up_prob
        })
    
    # 最后平仓
    if position == 1:
        final_pnl = (prices[-1] - entry_price) / entry_price
        capital = capital * (1 + final_pnl - 0.002)
        trades.append({
            'type': '强制平仓',
            'price': prices[-1],
            'entry_price': entry_price,
            'pnl_ratio': final_pnl,
            'time': timestamps[-1]
        })
    
    # 计算结果
    total_return = (capital - 10000) / 10000
    buy_hold_return = (prices[-1] - prices[0]) / prices[0]
    
    profitable_trades = len([t for t in trades if t.get('pnl_ratio', 0) > 0])
    total_completed_trades = len([t for t in trades if 'pnl_ratio' in t])
    win_rate = profitable_trades / total_completed_trades if total_completed_trades > 0 else 0
    
    # 最大回撤
    equity_values = [eq['value'] for eq in equity_curve]
    peak = np.maximum.accumulate(equity_values)
    drawdown = (np.array(equity_values) - peak) / peak
    max_drawdown = np.min(drawdown)
    
    # 夏普比率
    returns = np.diff(equity_values) / equity_values[:-1]
    sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(24*365) if np.std(returns) > 0 else 0
    
    print(f"\n📊 优化策略结果:")
    print(f"💰 最终资金: ${capital:,.2f}")
    print(f"📈 总收益率: {total_return:.2%}")
    print(f"📈 基准收益率: {buy_hold_return:.2%}")
    print(f"🎯 超额收益: {total_return - buy_hold_return:.2%}")
    print(f"📊 完成交易: {total_completed_trades}")
    print(f"📊 胜率: {win_rate:.2%}")
    print(f"📊 最大回撤: {max_drawdown:.2%}")
    print(f"📊 夏普比率: {sharpe_ratio:.2f}")
    
    return {
        'total_return': total_return,
        'benchmark_return': buy_hold_return,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'total_trades': total_completed_trades,
        'final_capital': capital,
        'sharpe_ratio': sharpe_ratio,
        'equity_curve': equity_curve,
        'trades': trades
    }

def compare_strategies():
    """
    对比不同策略的表现
    """
    print("📊 策略对比测试")
    print("=" * 50)
    
    # 使用最新的BTCUSDT模型
    import glob
    model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
    if not model_files:
        print("❌ 未找到BTCUSDT模型文件")
        return
    
    model_path = max(model_files, key=lambda x: x.split('_')[-1])
    print(f"📦 使用模型: {model_path}")
    
    strategies = [
        {"name": "原始策略", "confidence": 0.6, "stop_loss": 0.03},
        {"name": "最佳收益策略", "confidence": 0.7, "stop_loss": 0.05},
        {"name": "最佳胜率策略", "confidence": 0.65, "stop_loss": 0.04},
        {"name": "保守策略", "confidence": 0.75, "stop_loss": 0.02},
    ]
    
    results = {}
    
    for strategy in strategies:
        print(f"\n🔍 测试 {strategy['name']}...")
        
        try:
            result = optimized_backtest(
                model_path, 'BTCUSDT', 3,
                strategy['confidence'], strategy['stop_loss']
            )
            results[strategy['name']] = result
            
        except Exception as e:
            print(f"❌ {strategy['name']} 失败: {str(e)}")
            results[strategy['name']] = {'error': str(e)}
    
    # 生成对比报告
    print(f"\n📊 策略对比报告")
    print("=" * 80)
    print(f"{'策略':<15} {'收益率':<10} {'胜率':<10} {'回撤':<10} {'夏普':<10} {'交易次数':<10}")
    print("-" * 80)
    
    for name, result in results.items():
        if 'total_return' in result:
            print(f"{name:<15} {result['total_return']:>8.2%} {result['win_rate']:>8.2%} "
                  f"{result['max_drawdown']:>8.2%} {result['sharpe_ratio']:>8.2f} "
                  f"{result['total_trades']:>8d}")
    
    # 找出最佳策略
    valid_results = {k: v for k, v in results.items() if 'total_return' in v}
    
    if valid_results:
        best_return = max(valid_results.items(), key=lambda x: x[1]['total_return'])
        best_sharpe = max(valid_results.items(), key=lambda x: x[1]['sharpe_ratio'])
        best_winrate = max(valid_results.items(), key=lambda x: x[1]['win_rate'])
        
        print(f"\n🏆 最佳策略:")
        print(f"   最高收益: {best_return[0]} ({best_return[1]['total_return']:.2%})")
        print(f"   最佳夏普: {best_sharpe[0]} ({best_sharpe[1]['sharpe_ratio']:.2f})")
        print(f"   最高胜率: {best_winrate[0]} ({best_winrate[1]['win_rate']:.2%})")
    
    return results

def create_production_config():
    """
    创建生产环境配置
    """
    config = {
        "strategy_name": "Optimized ML Trading Strategy",
        "version": "1.0",
        "created_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        
        "trading_parameters": {
            "confidence_threshold": 0.7,
            "stop_loss_ratio": 0.05,
            "max_position_size": 0.8,
            "min_trade_interval": 1,  # 小时
            "commission_rate": 0.002
        },
        
        "risk_management": {
            "max_daily_trades": 10,
            "max_daily_loss": 0.05,
            "max_drawdown_limit": 0.15,
            "position_sizing": "fixed_fraction",
            "emergency_stop_loss": 0.10
        },
        
        "model_settings": {
            "model_type": "XGBoost",
            "prediction_horizon": "4_hours",
            "feature_count": 122,
            "retrain_frequency": "weekly",
            "performance_threshold": 0.55
        },
        
        "monitoring": {
            "track_metrics": [
                "daily_return",
                "win_rate",
                "max_drawdown",
                "sharpe_ratio",
                "trade_frequency"
            ],
            "alert_conditions": {
                "daily_loss_exceeds": 0.03,
                "drawdown_exceeds": 0.10,
                "win_rate_below": 0.50
            }
        }
    }
    
    import json
    config_path = "production_strategy_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"📝 生产配置已保存: {config_path}")
    return config

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'compare':
        # 策略对比模式
        results = compare_strategies()
    else:
        # 单一优化策略测试
        import glob
        model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
        
        if not model_files:
            print("❌ 未找到BTCUSDT模型文件")
        else:
            model_path = max(model_files, key=lambda x: x.split('_')[-1])
            
            print("🚀 使用最佳参数测试优化策略")
            result = optimized_backtest(model_path, 'BTCUSDT', 3, 0.7, 0.05)
            
            if result['total_return'] > 0.1:
                print("\n✅ 策略表现优秀，可以考虑生产部署")
                create_production_config()
            else:
                print("\n⚠️  策略表现一般，建议进一步优化")
    
    print(f"\n🎯 优化完成!")
    print(f"建议: 使用置信度0.7和5%止损的参数组合")
