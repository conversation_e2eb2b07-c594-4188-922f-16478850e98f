#!/usr/bin/env python3
"""
市场情绪分析模块 - 第二阶段情绪数据集成
整合社交媒体、新闻、恐慌贪婪指数等情绪指标
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
import time
import re
from textblob import TextBlob
import warnings
warnings.filterwarnings('ignore')

class SentimentAnalyzer:
    """
    市场情绪分析器
    """
    
    def __init__(self):
        self.sentiment_sources = {
            'fear_greed_index': True,    # 恐慌贪婪指数
            'crypto_news': True,         # 加密货币新闻
            'social_media': False,       # 社交媒体 (需要API密钥)
            'on_chain_metrics': True     # 链上指标
        }
        
        self.sentiment_history = []
        self.cache_duration = 3600  # 1小时缓存
        self.last_update = None
        
        # 情绪权重配置
        self.sentiment_weights = {
            'fear_greed': 0.4,
            'news': 0.3,
            'social': 0.2,
            'on_chain': 0.1
        }
    
    def get_comprehensive_sentiment(self) -> Dict:
        """
        获取综合市场情绪分析
        """
        sentiment_data = {}
        
        # 1. 恐慌贪婪指数
        if self.sentiment_sources['fear_greed_index']:
            try:
                fear_greed = self._get_fear_greed_index()
                sentiment_data['fear_greed'] = fear_greed
            except Exception as e:
                print(f"⚠️ 恐慌贪婪指数获取失败: {str(e)}")
                sentiment_data['fear_greed'] = self._get_default_fear_greed()
        
        # 2. 加密货币新闻情绪
        if self.sentiment_sources['crypto_news']:
            try:
                news_sentiment = self._get_crypto_news_sentiment()
                sentiment_data['news'] = news_sentiment
            except Exception as e:
                print(f"⚠️ 新闻情绪分析失败: {str(e)}")
                sentiment_data['news'] = self._get_default_news_sentiment()
        
        # 3. 社交媒体情绪 (模拟)
        if self.sentiment_sources['social_media']:
            try:
                social_sentiment = self._get_social_media_sentiment()
                sentiment_data['social'] = social_sentiment
            except Exception as e:
                print(f"⚠️ 社交媒体情绪分析失败: {str(e)}")
                sentiment_data['social'] = self._get_default_social_sentiment()
        
        # 4. 链上指标情绪
        if self.sentiment_sources.get('on_chain_metrics', True):
            try:
                onchain_sentiment = self._get_onchain_sentiment()
                sentiment_data['on_chain'] = onchain_sentiment
            except Exception as e:
                print(f"⚠️ 链上指标获取失败: {str(e)}")
                sentiment_data['on_chain'] = self._get_default_onchain_sentiment()
        
        # 5. 综合情绪计算
        combined_sentiment = self._combine_sentiment_signals(sentiment_data)
        
        # 6. 记录历史
        self._record_sentiment_history(combined_sentiment)
        
        return combined_sentiment
    
    def _get_fear_greed_index(self) -> Dict:
        """获取恐慌贪婪指数"""
        # 模拟恐慌贪婪指数 (实际应该从API获取)
        # 真实API: https://api.alternative.me/fng/
        
        try:
            # 模拟API响应
            current_index = np.random.randint(20, 80)  # 20-80之间的随机值
            
            # 分类情绪
            if current_index <= 25:
                classification = "Extreme Fear"
                sentiment_score = 0.1
            elif current_index <= 45:
                classification = "Fear"
                sentiment_score = 0.3
            elif current_index <= 55:
                classification = "Neutral"
                sentiment_score = 0.5
            elif current_index <= 75:
                classification = "Greed"
                sentiment_score = 0.7
            else:
                classification = "Extreme Greed"
                sentiment_score = 0.9
            
            return {
                'index': current_index,
                'classification': classification,
                'sentiment_score': sentiment_score,
                'timestamp': datetime.now().isoformat(),
                'source': 'Fear & Greed Index'
            }
            
        except Exception as e:
            raise Exception(f"恐慌贪婪指数获取失败: {str(e)}")
    
    def _get_crypto_news_sentiment(self) -> Dict:
        """获取加密货币新闻情绪"""
        # 模拟新闻情绪分析
        # 实际应该从新闻API获取并进行NLP分析
        
        # 模拟新闻标题
        sample_headlines = [
            "Bitcoin reaches new monthly high amid institutional adoption",
            "Crypto market shows strong resilience despite regulatory concerns",
            "Major exchange announces new security measures",
            "Blockchain technology adoption accelerates in enterprise sector",
            "Market volatility expected as Fed meeting approaches"
        ]
        
        sentiment_scores = []
        
        for headline in sample_headlines:
            # 使用TextBlob进行简单情绪分析
            blob = TextBlob(headline)
            polarity = blob.sentiment.polarity  # -1 to 1
            
            # 转换为0-1分数
            sentiment_score = (polarity + 1) / 2
            sentiment_scores.append(sentiment_score)
        
        avg_sentiment = np.mean(sentiment_scores)
        
        # 分类
        if avg_sentiment < 0.3:
            classification = "Bearish"
        elif avg_sentiment < 0.7:
            classification = "Neutral"
        else:
            classification = "Bullish"
        
        return {
            'sentiment_score': avg_sentiment,
            'classification': classification,
            'headlines_analyzed': len(sample_headlines),
            'individual_scores': sentiment_scores,
            'timestamp': datetime.now().isoformat(),
            'source': 'Crypto News Analysis'
        }
    
    def _get_social_media_sentiment(self) -> Dict:
        """获取社交媒体情绪 (模拟)"""
        # 实际应该从Twitter API, Reddit API等获取
        
        # 模拟社交媒体情绪
        bullish_mentions = np.random.randint(100, 500)
        bearish_mentions = np.random.randint(50, 300)
        neutral_mentions = np.random.randint(200, 400)
        
        total_mentions = bullish_mentions + bearish_mentions + neutral_mentions
        
        # 计算情绪分数
        sentiment_score = (bullish_mentions - bearish_mentions) / total_mentions + 0.5
        sentiment_score = max(0, min(1, sentiment_score))
        
        # 分类
        if sentiment_score < 0.4:
            classification = "Bearish"
        elif sentiment_score < 0.6:
            classification = "Neutral"
        else:
            classification = "Bullish"
        
        return {
            'sentiment_score': sentiment_score,
            'classification': classification,
            'bullish_mentions': bullish_mentions,
            'bearish_mentions': bearish_mentions,
            'neutral_mentions': neutral_mentions,
            'total_mentions': total_mentions,
            'timestamp': datetime.now().isoformat(),
            'source': 'Social Media Analysis'
        }
    
    def _get_onchain_sentiment(self) -> Dict:
        """获取链上指标情绪"""
        # 模拟链上指标
        # 实际应该从Glassnode, CoinMetrics等获取
        
        # 模拟指标
        exchange_inflow = np.random.uniform(0.8, 1.2)  # 交易所流入比率
        whale_activity = np.random.uniform(0.7, 1.3)   # 鲸鱼活动
        hodl_ratio = np.random.uniform(0.6, 0.9)       # 持币比率
        
        # 计算综合链上情绪
        # 交易所流入高 = 看跌, 鲸鱼活动高 = 看跌, HODL比率高 = 看涨
        sentiment_factors = [
            1 - (exchange_inflow - 0.8) / 0.4,  # 反向
            1 - (whale_activity - 0.7) / 0.6,   # 反向
            (hodl_ratio - 0.6) / 0.3             # 正向
        ]
        
        sentiment_score = np.mean(sentiment_factors)
        sentiment_score = max(0, min(1, sentiment_score))
        
        # 分类
        if sentiment_score < 0.4:
            classification = "Bearish"
        elif sentiment_score < 0.6:
            classification = "Neutral"
        else:
            classification = "Bullish"
        
        return {
            'sentiment_score': sentiment_score,
            'classification': classification,
            'exchange_inflow_ratio': exchange_inflow,
            'whale_activity_ratio': whale_activity,
            'hodl_ratio': hodl_ratio,
            'timestamp': datetime.now().isoformat(),
            'source': 'On-Chain Metrics'
        }
    
    def _combine_sentiment_signals(self, sentiment_data: Dict) -> Dict:
        """组合情绪信号"""
        weighted_score = 0
        total_weight = 0
        sentiment_breakdown = {}
        
        # 加权平均计算
        for source, weight in self.sentiment_weights.items():
            if source == 'fear_greed' and 'fear_greed' in sentiment_data:
                score = sentiment_data['fear_greed']['sentiment_score']
                weighted_score += score * weight
                total_weight += weight
                sentiment_breakdown['fear_greed'] = {
                    'score': score,
                    'weight': weight,
                    'classification': sentiment_data['fear_greed']['classification']
                }
            
            elif source == 'news' and 'news' in sentiment_data:
                score = sentiment_data['news']['sentiment_score']
                weighted_score += score * weight
                total_weight += weight
                sentiment_breakdown['news'] = {
                    'score': score,
                    'weight': weight,
                    'classification': sentiment_data['news']['classification']
                }
            
            elif source == 'social' and 'social' in sentiment_data:
                score = sentiment_data['social']['sentiment_score']
                weighted_score += score * weight
                total_weight += weight
                sentiment_breakdown['social'] = {
                    'score': score,
                    'weight': weight,
                    'classification': sentiment_data['social']['classification']
                }
            
            elif source == 'on_chain' and 'on_chain' in sentiment_data:
                score = sentiment_data['on_chain']['sentiment_score']
                weighted_score += score * weight
                total_weight += weight
                sentiment_breakdown['on_chain'] = {
                    'score': score,
                    'weight': weight,
                    'classification': sentiment_data['on_chain']['classification']
                }
        
        # 计算最终情绪分数
        if total_weight > 0:
            final_sentiment_score = weighted_score / total_weight
        else:
            final_sentiment_score = 0.5  # 中性
        
        # 最终分类
        if final_sentiment_score < 0.3:
            final_classification = "Strong Bearish"
        elif final_sentiment_score < 0.45:
            final_classification = "Bearish"
        elif final_sentiment_score < 0.55:
            final_classification = "Neutral"
        elif final_sentiment_score < 0.7:
            final_classification = "Bullish"
        else:
            final_classification = "Strong Bullish"
        
        # 计算情绪强度
        sentiment_strength = abs(final_sentiment_score - 0.5) * 2
        
        # 生成交易信号建议
        trading_signal = self._generate_sentiment_trading_signal(final_sentiment_score, sentiment_strength)
        
        return {
            'overall_sentiment_score': final_sentiment_score,
            'sentiment_classification': final_classification,
            'sentiment_strength': sentiment_strength,
            'trading_signal': trading_signal,
            'sentiment_breakdown': sentiment_breakdown,
            'raw_data': sentiment_data,
            'timestamp': datetime.now().isoformat(),
            'sources_used': len(sentiment_data),
            'total_weight': total_weight
        }
    
    def _generate_sentiment_trading_signal(self, sentiment_score: float, strength: float) -> Dict:
        """基于情绪生成交易信号建议"""
        
        if sentiment_score < 0.25:  # 极度恐慌
            return {
                'direction': 'LONG',
                'strength': strength,
                'confidence': 0.7,
                'reason': '极度恐慌，逆向投资机会',
                'signal_type': 'contrarian'
            }
        
        elif sentiment_score < 0.4:  # 恐慌
            return {
                'direction': 'LONG',
                'strength': strength * 0.7,
                'confidence': 0.6,
                'reason': '市场恐慌，潜在买入机会',
                'signal_type': 'contrarian'
            }
        
        elif sentiment_score > 0.75:  # 极度贪婪
            return {
                'direction': 'SHORT',
                'strength': strength,
                'confidence': 0.7,
                'reason': '极度贪婪，逆向做空机会',
                'signal_type': 'contrarian'
            }
        
        elif sentiment_score > 0.6:  # 贪婪
            return {
                'direction': 'SHORT',
                'strength': strength * 0.7,
                'confidence': 0.6,
                'reason': '市场贪婪，潜在做空机会',
                'signal_type': 'contrarian'
            }
        
        elif 0.55 < sentiment_score < 0.65:  # 轻微乐观
            return {
                'direction': 'LONG',
                'strength': strength * 0.5,
                'confidence': 0.5,
                'reason': '情绪轻微乐观，顺势做多',
                'signal_type': 'momentum'
            }
        
        elif 0.35 < sentiment_score < 0.45:  # 轻微悲观
            return {
                'direction': 'SHORT',
                'strength': strength * 0.5,
                'confidence': 0.5,
                'reason': '情绪轻微悲观，顺势做空',
                'signal_type': 'momentum'
            }
        
        else:  # 中性
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.4,
                'reason': '情绪中性，等待明确信号',
                'signal_type': 'neutral'
            }
    
    def _record_sentiment_history(self, sentiment_data: Dict):
        """记录情绪历史"""
        self.sentiment_history.append(sentiment_data)
        
        # 保持最近100条记录
        if len(self.sentiment_history) > 100:
            self.sentiment_history = self.sentiment_history[-100:]
        
        self.last_update = datetime.now()
    
    def get_sentiment_trend(self, lookback_hours: int = 24) -> Dict:
        """获取情绪趋势"""
        if len(self.sentiment_history) < 2:
            return {
                'trend': 'insufficient_data',
                'trend_strength': 0,
                'recent_change': 0
            }
        
        # 计算趋势
        recent_scores = [s['overall_sentiment_score'] for s in self.sentiment_history[-10:]]
        
        if len(recent_scores) >= 3:
            # 线性回归计算趋势
            x = np.arange(len(recent_scores))
            slope = np.polyfit(x, recent_scores, 1)[0]
            
            if slope > 0.01:
                trend = 'improving'
            elif slope < -0.01:
                trend = 'deteriorating'
            else:
                trend = 'stable'
            
            trend_strength = abs(slope) * 10  # 放大趋势强度
            recent_change = recent_scores[-1] - recent_scores[0]
        else:
            trend = 'stable'
            trend_strength = 0
            recent_change = 0
        
        return {
            'trend': trend,
            'trend_strength': min(trend_strength, 1.0),
            'recent_change': recent_change,
            'data_points': len(recent_scores)
        }
    
    # 默认值方法
    def _get_default_fear_greed(self) -> Dict:
        return {
            'index': 50,
            'classification': 'Neutral',
            'sentiment_score': 0.5,
            'timestamp': datetime.now().isoformat(),
            'source': 'Default Fear & Greed'
        }
    
    def _get_default_news_sentiment(self) -> Dict:
        return {
            'sentiment_score': 0.5,
            'classification': 'Neutral',
            'headlines_analyzed': 0,
            'timestamp': datetime.now().isoformat(),
            'source': 'Default News'
        }
    
    def _get_default_social_sentiment(self) -> Dict:
        return {
            'sentiment_score': 0.5,
            'classification': 'Neutral',
            'total_mentions': 0,
            'timestamp': datetime.now().isoformat(),
            'source': 'Default Social'
        }
    
    def _get_default_onchain_sentiment(self) -> Dict:
        return {
            'sentiment_score': 0.5,
            'classification': 'Neutral',
            'timestamp': datetime.now().isoformat(),
            'source': 'Default On-Chain'
        }

if __name__ == "__main__":
    # 测试情绪分析
    print("🧪 测试市场情绪分析模块")
    
    analyzer = SentimentAnalyzer()
    
    # 获取综合情绪
    sentiment = analyzer.get_comprehensive_sentiment()
    
    print(f"📊 综合情绪分析结果:")
    print(f"   总体情绪分数: {sentiment['overall_sentiment_score']:.2f}")
    print(f"   情绪分类: {sentiment['sentiment_classification']}")
    print(f"   情绪强度: {sentiment['sentiment_strength']:.2f}")
    print(f"   数据源数量: {sentiment['sources_used']}")
    
    print(f"\n🎯 交易信号建议:")
    signal = sentiment['trading_signal']
    print(f"   方向: {signal['direction']}")
    print(f"   强度: {signal['strength']:.2f}")
    print(f"   置信度: {signal['confidence']:.2f}")
    print(f"   理由: {signal['reason']}")
    print(f"   信号类型: {signal['signal_type']}")
    
    print(f"\n📈 各数据源详情:")
    for source, data in sentiment['sentiment_breakdown'].items():
        print(f"   {source}: {data['score']:.2f} ({data['classification']}) 权重:{data['weight']:.1%}")
    
    print(f"\n✅ 情绪分析模块测试完成")
