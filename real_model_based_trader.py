#!/usr/bin/env python3
"""
基于已训练模型的真实市场AI交易系统
使用之前训练好的AI模型 + 真实市场数据进行交易
"""

import pandas as pd
import numpy as np
import time
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings
import requests
import joblib
import glob

# 禁用警告
warnings.filterwarnings('ignore')
logging.getLogger().setLevel(logging.ERROR)

# 导入已有的组件
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class RealModelBasedTrader:
    """
    基于已训练模型的真实市场AI交易系统
    
    正确的架构：
    1. 加载之前训练好的AI模型
    2. 获取真实市场数据
    3. 使用模型进行预测
    4. 基于预测结果执行交易
    """
    
    def __init__(self, initial_balance: float = 50.0, leverage: float = 10.0):
        self.initial_balance = initial_balance
        self.leverage = leverage
        
        # 账户状态
        self.account = {
            'balance': initial_balance,
            'equity': initial_balance,
            'unrealized_pnl': 0.0,
            'margin_used': 0.0
        }
        
        # 持仓状态
        self.position = {
            'side': None,
            'size': 0.0,
            'entry_price': 0.0,
            'entry_time': None,
            'unrealized_pnl': 0.0,
            'stop_loss_price': 0.0,
            'take_profit_price': 0.0
        }
        
        # 交易状态
        self.last_trade_time = None
        self.trade_history = []
        self.consecutive_wait_cycles = 0
        
        # 交易参数
        self.trading_params = {
            'confidence_threshold': 0.65,  # 65%置信度
            'forced_trade_threshold': 0.50,
            'max_wait_hours': 2,
            'position_size_ratio': 0.4,
            'stop_loss_pct': 0.025,
            'take_profit_pct': 0.08,
            'max_hold_hours': 6
        }
        
        # 初始化组件
        self.data_fetcher = None
        self.feature_engineer = None
        self.model = None
        self.scaler = None
        self.encoder = None
        self.model_info = {}
        
        print(f"🎯 基于已训练模型的真实市场AI交易系统")
        print(f"💰 模拟账户: ${initial_balance} | ⚡ 杠杆: {leverage}x")
        print(f"🤖 使用已训练的AI模型 + 真实市场数据")
        
        # 加载模型和初始化组件
        self.load_trained_model()
        self.initialize_components()
    
    def find_best_model(self) -> str:
        """找到最佳的已训练模型"""
        model_dir = "./models/"
        if not os.path.exists(model_dir):
            raise FileNotFoundError("模型目录不存在，请先训练模型")
        
        # 查找所有模型文件
        model_files = glob.glob(os.path.join(model_dir, "*BTCUSDT*.joblib"))
        
        if not model_files:
            raise FileNotFoundError("未找到BTCUSDT模型文件")
        
        # 优先选择最新的平衡模型
        preferred_patterns = [
            "balanced_cost_sensitive",
            "improved_model", 
            "realistic",
            "extended"
        ]
        
        for pattern in preferred_patterns:
            for model_file in model_files:
                if pattern in model_file:
                    return model_file
        
        # 如果没有找到首选模型，返回最新的
        model_files.sort(key=os.path.getmtime, reverse=True)
        return model_files[0]
    
    def load_trained_model(self):
        """加载已训练的AI模型"""
        try:
            print(f"🔍 搜索已训练的AI模型...")
            
            # 找到最佳模型
            model_path = self.find_best_model()
            model_name = os.path.basename(model_path)
            
            print(f"📦 找到模型: {model_name}")
            
            # 加载主模型
            self.model = joblib.load(model_path)
            print(f"✅ 成功加载AI模型")
            
            # 尝试加载对应的scaler和encoder
            base_name = model_path.replace('.joblib', '')
            
            # 加载scaler
            scaler_path = base_name.replace('_xgb_', '_scaler_xgb_') + '.joblib'
            if os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
                print(f"✅ 成功加载数据标准化器")
            
            # 加载encoder
            encoder_path = base_name.replace('_xgb_', '_encoder_xgb_') + '.joblib'
            if os.path.exists(encoder_path):
                self.encoder = joblib.load(encoder_path)
                print(f"✅ 成功加载数据编码器")
            
            # 保存模型信息
            self.model_info = {
                'model_path': model_path,
                'model_name': model_name,
                'load_time': datetime.now().isoformat(),
                'has_scaler': self.scaler is not None,
                'has_encoder': self.encoder is not None
            }
            
            print(f"🤖 AI模型加载完成")
            
        except Exception as e:
            print(f"❌ 加载模型失败: {e}")
            print(f"💡 请确保已经训练过模型，或运行训练脚本")
            raise
    
    def initialize_components(self):
        """初始化数据获取和特征工程组件"""
        try:
            print(f"🔧 初始化数据获取组件...")
            
            # 初始化数据获取器
            self.data_fetcher = BinanceDataFetcher()
            print(f"✅ 数据获取器初始化完成")
            
            # 初始化特征工程器
            self.feature_engineer = FeatureEngineer()
            print(f"✅ 特征工程器初始化完成")
            
            # 测试API连接
            print(f"🔍 测试币安API连接...")
            current_price = self.data_fetcher.get_current_price('BTCUSDT', is_futures=True)
            if current_price and current_price > 0:
                print(f"✅ API连接成功，当前BTC价格: ${current_price:,.2f}")
            else:
                raise Exception("无法获取市场数据")
                
        except Exception as e:
            print(f"❌ 组件初始化失败: {e}")
            raise
    
    def get_real_market_features(self) -> Dict:
        """获取真实市场数据并生成特征"""
        try:
            print(f"📡 获取真实市场数据...")
            
            # 获取足够的历史数据用于特征计算
            from datetime import datetime, timedelta
            end_date = datetime.now()
            start_date = end_date - timedelta(days=10)  # 获取10天的数据

            df = self.data_fetcher.get_historical_data(
                'BTCUSDT',
                '1h',
                start_date.strftime('%Y-%m-%d'),
                is_futures=True
            )
            if df is None or len(df) < 50:
                raise Exception("无法获取足够的历史数据")
            
            current_price = df['close'].iloc[-1]
            print(f"📊 当前BTC价格: ${current_price:,.2f}")
            
            # 生成特征
            print(f"🔧 生成AI模型特征...")
            features_df = self.feature_engineer.create_features(df)
            
            if features_df is None or len(features_df) == 0:
                raise Exception("特征生成失败")
            
            # 获取最新的特征向量
            latest_features = features_df.iloc[-1]
            
            # 准备市场数据
            market_data = {
                'timestamp': datetime.now(),
                'current_price': current_price,
                'price_change_24h': (df['close'].iloc[-1] - df['close'].iloc[-25]) / df['close'].iloc[-25],
                'volume_24h': df['volume'].iloc[-24:].sum(),
                'high_24h': df['high'].iloc[-24:].max(),
                'low_24h': df['low'].iloc[-24:].min(),
                'volatility': df['close'].iloc[-24:].std() / df['close'].iloc[-24:].mean(),
                'features': latest_features,
                'raw_data': df,
                'source': 'BINANCE_API_REAL'
            }
            
            print(f"✅ 特征生成完成，共{len(latest_features)}个特征")
            return market_data
            
        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return None
    
    def predict_with_model(self, market_data: Dict) -> Dict:
        """使用已训练模型进行预测"""
        try:
            if not market_data or 'features' not in market_data:
                return {'direction': 'WAIT', 'confidence': 0, 'error': '无市场数据'}
            
            print(f"🤖 使用AI模型进行预测...")
            
            # 准备特征数据
            features = market_data['features']
            
            # 转换为DataFrame（模型期望的格式）
            feature_df = pd.DataFrame([features])

            # 检查特征数量并调整
            expected_features = 122  # 模型期望的特征数量
            current_features = len(feature_df.columns)

            if current_features > expected_features:
                # 如果特征太多，选择前N个特征
                feature_df = feature_df.iloc[:, :expected_features]
                print(f"⚠️ 特征数量调整: {current_features} -> {expected_features}")
            elif current_features < expected_features:
                # 如果特征太少，用0填充
                for i in range(current_features, expected_features):
                    feature_df[f'feature_{i}'] = 0
                print(f"⚠️ 特征数量补充: {current_features} -> {expected_features}")

            # 应用数据预处理（如果有）
            if self.scaler is not None:
                feature_df = pd.DataFrame(
                    self.scaler.transform(feature_df),
                    columns=feature_df.columns
                )
                print(f"✅ 应用数据标准化")
            
            if self.encoder is not None:
                # 注意：encoder通常用于分类特征，这里可能需要特殊处理
                print(f"⚠️ 检测到编码器，跳过（通常用于训练时）")
            
            # 进行预测
            prediction = self.model.predict(feature_df)[0]
            
            # 获取预测概率（如果模型支持）
            try:
                prediction_proba = self.model.predict_proba(feature_df)[0]
                confidence = max(prediction_proba)
            except:
                # 如果模型不支持概率预测，使用决策函数或默认值
                try:
                    decision_score = abs(self.model.decision_function(feature_df)[0])
                    confidence = min(0.95, 0.5 + decision_score * 0.3)
                except:
                    confidence = 0.7  # 默认置信度
            
            # 解释预测结果
            if prediction == 1:  # 上涨
                direction = 'LONG'
                action = '做多'
            elif prediction == 0:  # 下跌
                direction = 'SHORT'
                action = '做空'
            else:  # 其他情况
                direction = 'WAIT'
                action = '等待'
            
            result = {
                'direction': direction,
                'confidence': confidence,
                'prediction': prediction,
                'action': action,
                'model_name': self.model_info.get('model_name', 'Unknown'),
                'feature_count': len(features),
                'data_source': 'TRAINED_AI_MODEL'
            }
            
            print(f"🎯 AI预测: {action} (置信度: {confidence:.1%})")
            return result
            
        except Exception as e:
            print(f"❌ AI预测失败: {e}")
            return {'direction': 'WAIT', 'confidence': 0, 'error': str(e)}
    
    def should_force_trade(self) -> bool:
        """检查是否需要强制交易"""
        if self.last_trade_time is None:
            return self.consecutive_wait_cycles > 15  # 约45分钟
        
        hours_since_last_trade = (datetime.now() - self.last_trade_time).total_seconds() / 3600
        return hours_since_last_trade >= self.trading_params['max_wait_hours']
    
    def calculate_position_size(self, confidence: float, current_price: float) -> float:
        """计算仓位大小"""
        base_ratio = self.trading_params['position_size_ratio']
        
        # 根据AI置信度调整仓位
        confidence_multiplier = 0.5 + confidence  # 50%-150%
        
        final_ratio = base_ratio * confidence_multiplier
        final_ratio = max(0.2, min(0.6, final_ratio))
        
        position_value = self.account['balance'] * final_ratio
        btc_size = position_value / current_price / self.leverage
        
        return btc_size
    
    def calculate_target_prices(self, entry_price: float, direction: str) -> Dict:
        """计算目标价格"""
        if direction == 'LONG':
            stop_loss_price = entry_price * (1 - self.trading_params['stop_loss_pct'])
            take_profit_price = entry_price * (1 + self.trading_params['take_profit_pct'])
        else:  # SHORT
            stop_loss_price = entry_price * (1 + self.trading_params['stop_loss_pct'])
            take_profit_price = entry_price * (1 - self.trading_params['take_profit_pct'])
        
        return {
            'stop_loss_price': stop_loss_price,
            'take_profit_price': take_profit_price
        }
    
    def open_position(self, direction: str, size: float, price: float, prediction: Dict) -> bool:
        """开仓"""
        if self.position['side'] is not None:
            return False
        
        position_value = size * price * self.leverage
        margin_required = position_value / self.leverage
        trading_fee = position_value * 0.0004
        
        if margin_required + trading_fee > self.account['balance']:
            return False
        
        targets = self.calculate_target_prices(price, direction)
        
        self.position.update({
            'side': direction,
            'size': size,
            'entry_price': price,
            'entry_time': datetime.now(),
            'unrealized_pnl': 0.0,
            'stop_loss_price': targets['stop_loss_price'],
            'take_profit_price': targets['take_profit_price']
        })
        
        self.account['balance'] -= trading_fee
        self.account['margin_used'] = margin_required
        
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'OPEN',
            'side': direction,
            'size': size,
            'price': price,
            'stop_loss_price': targets['stop_loss_price'],
            'take_profit_price': targets['take_profit_price'],
            'fee': trading_fee,
            'prediction': prediction,
            'balance_after': self.account['balance'],
            'data_source': 'AI_MODEL_PREDICTION'
        }
        
        self.trade_history.append(trade_record)
        self.last_trade_time = datetime.now()
        self.consecutive_wait_cycles = 0
        
        return True
    
    def close_position(self, price: float, reason: str) -> bool:
        """平仓"""
        if self.position['side'] is None:
            return False
        
        if self.position['side'] == 'LONG':
            price_diff = price - self.position['entry_price']
        else:
            price_diff = self.position['entry_price'] - price
        
        pnl = self.position['size'] * price_diff * self.leverage
        position_value = self.position['size'] * price * self.leverage
        trading_fee = position_value * 0.0004
        net_pnl = pnl - trading_fee
        
        self.account['balance'] += net_pnl + self.account['margin_used']
        self.account['margin_used'] = 0
        self.account['equity'] = self.account['balance']
        self.account['unrealized_pnl'] = 0
        
        hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'CLOSE',
            'side': self.position['side'],
            'size': self.position['size'],
            'entry_price': self.position['entry_price'],
            'exit_price': price,
            'pnl': pnl,
            'net_pnl': net_pnl,
            'fee': trading_fee,
            'reason': reason,
            'hold_time': hold_time,
            'balance_after': self.account['balance']
        }
        
        self.trade_history.append(trade_record)
        self.last_trade_time = datetime.now()
        
        # 清除持仓
        self.position = {
            'side': None,
            'size': 0.0,
            'entry_price': 0.0,
            'entry_time': None,
            'unrealized_pnl': 0.0,
            'stop_loss_price': 0.0,
            'take_profit_price': 0.0
        }
        
        return True

    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.position['side'] is None:
            self.account['unrealized_pnl'] = 0
            self.account['equity'] = self.account['balance']
            return

        if self.position['side'] == 'LONG':
            price_diff = current_price - self.position['entry_price']
        else:
            price_diff = self.position['entry_price'] - current_price

        unrealized_pnl = self.position['size'] * price_diff * self.leverage
        self.position['unrealized_pnl'] = unrealized_pnl
        self.account['unrealized_pnl'] = unrealized_pnl
        self.account['equity'] = self.account['balance'] + unrealized_pnl

    def check_exit_conditions(self, current_price: float) -> bool:
        """检查平仓条件"""
        if self.position['side'] is None:
            return False

        # 止损检查
        if self.position['side'] == 'LONG':
            if current_price <= self.position['stop_loss_price']:
                self.close_position(current_price, '止损')
                return True
            if current_price >= self.position['take_profit_price']:
                self.close_position(current_price, '止盈')
                return True
        else:  # SHORT
            if current_price >= self.position['stop_loss_price']:
                self.close_position(current_price, '止损')
                return True
            if current_price <= self.position['take_profit_price']:
                self.close_position(current_price, '止盈')
                return True

        # 超时检查
        if self.position['entry_time']:
            hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            if hold_hours >= self.trading_params['max_hold_hours']:
                self.close_position(current_price, '超时平仓')
                return True

        return False

    def run_trading_cycle(self) -> Dict:
        """运行交易循环 - 使用已训练模型"""
        print(f"\n🔄 开始新的AI交易周期...")

        # 获取真实市场数据和特征
        market_data = self.get_real_market_features()
        if not market_data:
            print(f"🚨 无法获取市场数据，跳过本轮")
            self.consecutive_wait_cycles += 1
            return {'action': 'DATA_FAILED', 'error': 'Cannot get market data'}

        current_price = market_data['current_price']

        # 更新未实现盈亏
        self.update_unrealized_pnl(current_price)

        # 检查平仓条件
        if self.check_exit_conditions(current_price):
            return {
                'action': '已平仓',
                'market_data': market_data
            }

        # 如果有持仓，监控
        if self.position['side'] is not None:
            return {
                'action': '监控中',
                'market_data': market_data
            }

        # 使用AI模型进行预测
        prediction = self.predict_with_model(market_data)

        # 交易决策
        should_trade = False
        confidence_threshold = self.trading_params['confidence_threshold']
        force_trade = self.should_force_trade()

        if force_trade:
            confidence_threshold = self.trading_params['forced_trade_threshold']
            should_trade = prediction['confidence'] >= confidence_threshold
            print(f"⚡ 触发强制交易机制")
        else:
            should_trade = (
                prediction['direction'] in ['LONG', 'SHORT'] and
                prediction['confidence'] >= confidence_threshold
            )

        if should_trade:
            position_size = self.calculate_position_size(prediction['confidence'], current_price)
            success = self.open_position(prediction['direction'], position_size, current_price, prediction)

            if success:
                return {
                    'action': '已开仓',
                    'market_data': market_data,
                    'prediction': prediction,
                    'force_trade': force_trade
                }
            else:
                return {
                    'action': '开仓失败',
                    'market_data': market_data,
                    'prediction': prediction,
                    'reason': '余额不足'
                }

        self.consecutive_wait_cycles += 1
        return {
            'action': '等待中',
            'market_data': market_data,
            'prediction': prediction
        }

    def print_detailed_status(self, cycle_count: int, result: Dict):
        """打印详细的交易状态"""
        current_time = datetime.now().strftime('%H:%M:%S')

        print(f"\n" + "="*120)
        print(f"🤖 基于已训练AI模型的真实市场交易 | 第{cycle_count}轮 | {current_time}")
        print(f"📦 模型: {self.model_info.get('model_name', 'Unknown')}")
        print("="*120)

        if 'market_data' not in result:
            print(f"❌ 错误: {result.get('error', '未知错误')}")
            return

        market_data = result['market_data']
        current_price = market_data['current_price']

        # 数据来源验证
        print(f"🔍 数据来源验证:")
        print(f"   📡 市场数据: {market_data['source']}")
        print(f"   🤖 AI模型: {self.model_info.get('model_name', 'Unknown')}")
        print(f"   🕐 数据时间: {market_data['timestamp'].strftime('%H:%M:%S')}")
        print(f"   📊 特征数量: {len(market_data.get('features', {}))}")

        # 真实市场数据
        print(f"\n📊 真实市场数据:")
        print(f"   💰 当前BTC价格: ${current_price:,.2f}")
        print(f"   📈 24小时变动: {market_data['price_change_24h']:+.2%}")
        print(f"   📊 波动率: {market_data['volatility']:.2%}")
        print(f"   🔝 24小时最高: ${market_data['high_24h']:,.2f}")
        print(f"   🔻 24小时最低: ${market_data['low_24h']:,.2f}")
        print(f"   📦 24小时成交量: {market_data['volume_24h']:,.0f} BTC")

        # AI预测结果
        if 'prediction' in result:
            prediction = result['prediction']
            print(f"\n🤖 AI模型预测:")
            print(f"   预测方向: {prediction.get('action', prediction.get('direction', 'Unknown'))}")
            print(f"   置信度: {prediction['confidence']:.1%}")
            print(f"   模型输出: {prediction.get('prediction', 'N/A')}")
            print(f"   数据源: {prediction['data_source']}")
            if 'error' in prediction:
                print(f"   ⚠️ 错误: {prediction['error']}")

        # 账户状态
        total_return = (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        balance_color = "💚" if total_return >= 0 else "❤️"

        print(f"\n💰 模拟账户状态:")
        print(f"   可用余额: ${self.account['balance']:.2f}")
        print(f"   未实现盈亏: ${self.account['unrealized_pnl']:+.2f}")
        print(f"   总权益: {balance_color} ${self.account['equity']:.2f} ({total_return:+.2f}%)")
        print(f"   已用保证金: ${self.account['margin_used']:.2f}")

        # 持仓详情
        if self.position['side'] is not None:
            side_text = "🟢 做多" if self.position['side'] == 'LONG' else "🔴 做空"
            entry_price = self.position['entry_price']
            stop_loss = self.position['stop_loss_price']
            take_profit = self.position['take_profit_price']
            hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 3600

            print(f"\n📊 当前持仓:")
            print(f"   方向: {side_text}")
            print(f"   仓位大小: {self.position['size']:.6f} BTC")
            print(f"   📍 开仓价格: ${entry_price:,.2f}")
            print(f"   📍 当前价格: ${current_price:,.2f}")
            print(f"   🛑 止损价格: ${stop_loss:,.2f}")
            print(f"   🎯 止盈价格: ${take_profit:,.2f}")
            print(f"   ⏱️ 持仓时间: {hold_time:.1f}小时")
            print(f"   💵 未实现盈亏: ${self.position['unrealized_pnl']:+.2f}")
        else:
            print(f"\n📊 当前持仓: 空仓")

        # 当前动作
        action = result['action']
        if action == '已开仓':
            force_text = " (强制交易)" if result.get('force_trade') else ""
            print(f"\n🚀 交易动作: ✅ AI模型开仓{force_text}")
        elif action == '已平仓':
            last_trade = self.trade_history[-1] if self.trade_history else {}
            reason = last_trade.get('reason', '未知')
            pnl = last_trade.get('net_pnl', 0)
            print(f"\n🏁 交易动作: ✅ 已平仓 ({reason}) 盈亏: ${pnl:+.2f}")
        elif action == '等待中':
            print(f"\n⏳ 交易动作: AI等待交易机会 (第{self.consecutive_wait_cycles}轮)")
        elif action == '监控中':
            print(f"\n👁️ 交易动作: AI监控持仓中")

        # 交易统计
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']
        if closed_trades:
            total_pnl = sum(t['net_pnl'] for t in closed_trades)
            win_rate = len([t for t in closed_trades if t['net_pnl'] > 0]) / len(closed_trades)
            print(f"\n📈 AI交易统计:")
            print(f"   完成交易: {len(closed_trades)}笔")
            print(f"   胜率: {win_rate:.1%}")
            print(f"   总盈亏: ${total_pnl:+.2f}")
            print(f"   数据源: AI模型预测 + 真实市场数据")

        print("-" * 120)

    def get_statistics(self) -> Dict:
        """获取统计数据"""
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']

        if not closed_trades:
            return {
                'total_trades': 0,
                'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100,
                'current_balance': self.account['equity'],
                'model_info': self.model_info
            }

        total_pnl = sum(t['net_pnl'] for t in closed_trades)
        winning_trades = [t for t in closed_trades if t['net_pnl'] > 0]

        return {
            'total_trades': len(closed_trades),
            'winning_trades': len(winning_trades),
            'win_rate': len(winning_trades) / len(closed_trades) if closed_trades else 0,
            'total_pnl': total_pnl,
            'avg_pnl': total_pnl / len(closed_trades) if closed_trades else 0,
            'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100,
            'current_balance': self.account['equity'],
            'avg_hold_time': sum(t['hold_time'] for t in closed_trades) / len(closed_trades) if closed_trades else 0,
            'model_info': self.model_info
        }

def run_model_based_trading():
    """运行基于已训练模型的AI交易系统"""
    print("🤖 基于已训练AI模型的真实市场交易系统")
    print("使用之前训练的模型 + 币安API真实数据")
    print("=" * 120)

    try:
        # 初始化交易系统
        trader = RealModelBasedTrader(initial_balance=50.0, leverage=10.0)

        print(f"\n🎯 系统配置:")
        print(f"✅ AI模型: {trader.model_info.get('model_name', 'Unknown')}")
        print(f"✅ 数据源: 币安API真实市场数据")
        print(f"✅ 特征工程: 自动生成技术指标特征")
        print(f"✅ 预测方式: 已训练模型推理")
        print(f"✅ 置信度阈值: {trader.trading_params['confidence_threshold']:.0%}")

        # 获取运行参数
        try:
            duration = float(input("\n运行时长（小时，默认3）: ") or "3")
            interval = int(input("检查间隔（分钟，默认5）: ") or "5")
        except:
            duration = 3
            interval = 5

        print(f"\n🎯 开始{duration}小时AI模型交易测试...")
        print(f"⏰ 检查间隔: {interval}分钟")
        print(f"🤖 每{interval}分钟使用AI模型分析最新市场数据")

        start_time = datetime.now()
        end_time = start_time + timedelta(hours=duration)
        cycle_count = 0

        try:
            while datetime.now() < end_time:
                cycle_count += 1

                # 运行AI交易循环
                result = trader.run_trading_cycle()

                # 检查数据获取失败
                if result['action'] == 'DATA_FAILED':
                    print(f"\n⚠️ 数据获取失败，等待下一轮...")
                    time.sleep(30)
                    continue

                # 显示详细状态
                trader.print_detailed_status(cycle_count, result)

                # 等待下一轮
                remaining_time = (end_time - datetime.now()).total_seconds()
                interval_seconds = interval * 60

                if remaining_time > interval_seconds:
                    print(f"\n⏳ 等待{interval}分钟进行下一轮AI分析...")
                    time.sleep(min(interval_seconds, 60))  # 演示最多60秒
                else:
                    print(f"\n⏳ 即将完成...")
                    time.sleep(max(0, min(remaining_time, 10)))
                    break

        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断交易")
        except Exception as e:
            print(f"\n❌ 系统错误: {e}")
            import traceback
            traceback.print_exc()

        # 最终报告
        print(f"\n🏁 AI模型交易测试完成")
        print("=" * 120)

        stats = trader.get_statistics()
        actual_runtime = (datetime.now() - start_time).total_seconds() / 3600

        print(f"⏰ 实际运行: {actual_runtime:.1f} 小时")
        print(f"🔄 总轮数: {cycle_count}")
        print(f"💰 最终余额: ${stats['current_balance']:.2f}")
        print(f"📈 总收益率: {stats['total_return']:+.2f}%")
        print(f"📊 完成交易: {stats['total_trades']}笔")
        print(f"🤖 AI模型: {stats['model_info'].get('model_name', 'Unknown')}")

        if stats['total_trades'] > 0:
            print(f"🎯 交易胜率: {stats['win_rate']:.1%}")
            print(f"💵 平均盈亏: ${stats['avg_pnl']:+.2f}")
            print(f"⏱️ 平均持仓: {stats['avg_hold_time']:.1f}小时")

            # 评估AI模型在真实市场的表现
            if stats['total_return'] > 20:
                print(f"🎉 卓越: AI模型在真实市场表现卓越")
            elif stats['total_return'] > 15:
                print(f"✅ 优秀: AI模型在真实市场表现优秀")
            elif stats['total_return'] > 10:
                print(f"✅ 良好: AI模型在真实市场表现良好")
            elif stats['total_return'] > 5:
                print(f"✅ 及格: AI模型在真实市场表现及格")
            elif stats['total_return'] > 0:
                print(f"⚠️ 微利: AI模型在真实市场微盈利")
            else:
                print(f"📉 亏损: AI模型需要进一步优化")

            print(f"\n📊 AI模型验证价值:")
            print(f"✅ 验证了已训练AI模型在实际市场的有效性")
            print(f"✅ 基于真实价格数据和技术指标的AI决策")
            print(f"✅ 展示了机器学习在量化交易中的应用")
            print(f"✅ 为模型优化提供了实际表现数据")
        else:
            print(f"⚠️ 无交易: AI模型未找到符合条件的交易机会")
            print(f"💡 建议: 调整置信度阈值或重新训练模型")

        return trader

    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        print(f"💡 请确保:")
        print(f"   1. 已训练过AI模型（./models/目录中有模型文件）")
        print(f"   2. 网络连接正常（可访问币安API）")
        print(f"   3. 相关依赖包已安装")
        return None

if __name__ == "__main__":
    print("🤖 基于已训练AI模型的真实市场交易系统")
    print("正确的架构：已训练模型 + 真实市场数据 = AI交易决策")
    print("")

    try:
        trader = run_model_based_trading()
        if trader:
            print(f"\n🎉 AI模型交易测试完成！")
            print(f"✅ 使用了已训练的AI模型")
            print(f"✅ 基于真实市场数据进行预测")
            print(f"✅ 验证了AI策略的实际效果")
        else:
            print(f"\n❌ 测试失败：系统初始化错误")

    except KeyboardInterrupt:
        print(f"\n👋 用户退出")
    except Exception as e:
        print(f"\n❌ 程序错误: {str(e)}")
        import traceback
        traceback.print_exc()
