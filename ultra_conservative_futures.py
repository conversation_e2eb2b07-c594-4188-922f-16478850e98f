#!/usr/bin/env python3
"""
超保守永续合约策略 - 大幅提高交易门槛
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

def ultra_conservative_futures_test():
    """
    超保守永续合约测试
    """
    print("🛡️ 超保守永续合约策略测试")
    print("=" * 60)
    print("策略特点:")
    print("- 极高置信度要求 (0.85+)")
    print("- 更严格的止损 (2%)")
    print("- 更短的持仓时间 (8小时)")
    print("- 降低杠杆 (1.5x)")
    print("")
    
    # 使用最新模型
    import glob
    model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
    if not model_files:
        print("❌ 未找到BTCUSDT模型文件")
        return
    
    model_path = max(model_files, key=lambda x: x.split('_')[-1])
    
    # 超保守参数
    leverage = 1.5
    confidence_threshold = 0.85  # 极高置信度
    stop_loss_ratio = 0.02       # 2%止损
    take_profit_ratio = 0.04     # 4%止盈
    max_hold_hours = 8           # 最多8小时
    
    # 加载模型
    model_data = joblib.load(model_path)
    model = model_data['model']
    scaler = model_data['scaler']
    
    # 获取测试数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data('BTCUSDT', '1h', start_date.strftime('%Y-%m-%d'))
    
    # 特征工程
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    # 数据准备
    X = df_features.drop(columns=['target'], errors='ignore')
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    mask = ~X.isna().any(axis=1)
    X = X[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    # 预测
    X_scaled = scaler.transform(X)
    prediction_proba = model.predict_proba(X_scaled)
    up_probabilities = prediction_proba[:, 1]
    
    # 回测
    capital = 70.06  # 恢复之前的权益状态
    position = 0
    entry_price = 0
    entry_time = None
    trades = []
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    print(f"📈 开始超保守回测...")
    
    for i in range(len(prices)):
        price = prices[i]
        timestamp = timestamps[i]
        up_prob = up_probabilities[i]
        
        # 平仓检查
        if position != 0 and entry_time:
            # 计算持仓时间
            hold_hours = (timestamp - entry_time).total_seconds() / 3600
            
            # 计算盈亏
            if position == 1:  # 多头
                pnl_ratio = (price - entry_price) / entry_price
            else:  # 空头
                pnl_ratio = (entry_price - price) / entry_price
            
            should_close = False
            close_reason = ""
            
            # 止损
            if pnl_ratio < -stop_loss_ratio:
                should_close = True
                close_reason = "止损"
            
            # 止盈
            elif pnl_ratio > take_profit_ratio:
                should_close = True
                close_reason = "止盈"
            
            # 时间止损
            elif hold_hours > max_hold_hours:
                should_close = True
                close_reason = "时间止损"
            
            # 信号反转
            elif position == 1 and up_prob < 0.3:
                should_close = True
                close_reason = "多头信号反转"
            elif position == -1 and up_prob > 0.7:
                should_close = True
                close_reason = "空头信号反转"
            
            if should_close:
                # 平仓
                leveraged_pnl = pnl_ratio * leverage
                total_fees = 0.0006 * leverage  # 更高的手续费估算
                final_pnl = leveraged_pnl - total_fees
                
                capital = capital * (1 + final_pnl)
                
                trades.append({
                    'action': 'CLOSE',
                    'direction': 'LONG' if position == 1 else 'SHORT',
                    'price': price,
                    'pnl_ratio': pnl_ratio,
                    'final_pnl': final_pnl,
                    'reason': close_reason,
                    'hold_hours': hold_hours,
                    'capital': capital
                })
                
                direction_text = "平多" if position == 1 else "平空"
                print(f"✅ {direction_text} @ ${price:.2f} ({close_reason}, 盈亏: {final_pnl:+.2%}, 资金: ${capital:.2f})")
                
                position = 0
                entry_price = 0
                entry_time = None
        
        # 开仓检查
        if position == 0:
            # 极严格的开仓条件
            if up_prob > confidence_threshold:
                # 做多
                position = 1
                entry_price = price
                entry_time = timestamp
                
                trades.append({
                    'action': 'OPEN',
                    'direction': 'LONG',
                    'price': price,
                    'confidence': up_prob
                })
                
                print(f"✅ 做多 @ ${price:.2f} (置信度: {up_prob:.3f})")
                
            elif up_prob < (1 - confidence_threshold):
                # 做空
                position = -1
                entry_price = price
                entry_time = timestamp
                
                trades.append({
                    'action': 'OPEN',
                    'direction': 'SHORT',
                    'price': price,
                    'confidence': up_prob
                })
                
                print(f"✅ 做空 @ ${price:.2f} (置信度: {up_prob:.3f})")
    
    # 强制平仓
    if position != 0:
        if position == 1:
            pnl_ratio = (prices[-1] - entry_price) / entry_price
        else:
            pnl_ratio = (entry_price - prices[-1]) / entry_price
        
        final_pnl = pnl_ratio * leverage - 0.0006 * leverage
        capital = capital * (1 + final_pnl)
        print("🔄 强制平仓")
    
    # 计算结果
    total_return = (capital - 50) / 50
    buy_hold_return = (prices[-1] - prices[0]) / prices[0]
    
    close_trades = [t for t in trades if t['action'] == 'CLOSE']
    profitable_trades = [t for t in close_trades if t.get('final_pnl', 0) > 0]
    win_rate = len(profitable_trades) / len(close_trades) if close_trades else 0
    
    avg_hold_time = np.mean([t.get('hold_hours', 0) for t in close_trades]) if close_trades else 0
    
    print(f"\n📊 超保守永续合约结果:")
    print("=" * 50)
    print(f"💰 最终资金: ${capital:,.2f}")
    print(f"📈 总收益率: {total_return:+.2%}")
    print(f"📈 基准收益率: {buy_hold_return:+.2%}")
    print(f"🎯 超额收益: {total_return - buy_hold_return:+.2%}")
    print(f"📊 完成交易: {len(close_trades)}")
    print(f"📊 胜率: {win_rate:.2%}")
    print(f"⏱️  平均持仓: {avg_hold_time:.1f}小时")
    print(f"🔧 杠杆倍数: {leverage}x")
    print(f"🎯 置信度要求: {confidence_threshold}")
    
    # 评估
    if total_return > 0.1 and win_rate > 0.6 and len(close_trades) > 5:
        print(f"\n✅ 超保守策略表现良好")
        print(f"建议可以考虑小额实盘测试")
    elif total_return > 0 and win_rate > 0.5:
        print(f"\n⚠️  超保守策略表现一般")
        print(f"建议进一步提高置信度要求")
    else:
        print(f"\n❌ 超保守策略仍需优化")
        print(f"建议考虑只使用现货策略")
    
    return {
        'total_return': total_return,
        'win_rate': win_rate,
        'total_trades': len(close_trades),
        'avg_hold_time': avg_hold_time
    }

def compare_all_strategies():
    """
    对比所有策略
    """
    print("\n📊 所有策略对比")
    print("=" * 80)
    
    strategies = {
        "现货激进策略": {"return": 0.1980, "winrate": 0.5577, "trades": 52, "leverage": 1},
        "现货平衡策略": {"return": 0.1647, "winrate": 0.6591, "trades": 44, "leverage": 1},
        "永续合约2x": {"return": 0.1373, "winrate": 0.4800, "trades": 125, "leverage": 2},
    }
    
    # 测试超保守策略
    print("测试超保守永续合约策略...")
    ultra_result = ultra_conservative_futures_test()
    
    if ultra_result:
        strategies["超保守永续"] = {
            "return": ultra_result['total_return'],
            "winrate": ultra_result['win_rate'],
            "trades": ultra_result['total_trades'],
            "leverage": 1.5
        }
    
    print(f"\n📊 策略综合对比:")
    print("=" * 80)
    print(f"{'策略':<15} {'收益率':<10} {'胜率':<10} {'交易数':<8} {'杠杆':<6} {'推荐度':<8}")
    print("-" * 80)
    
    for name, data in strategies.items():
        # 计算推荐度评分
        score = data['return'] * 50 + data['winrate'] * 30 + min(data['trades']/50, 1) * 10
        if data['leverage'] > 1:
            score *= 0.8  # 杠杆策略降权
        
        if score > 25:
            recommendation = "⭐⭐⭐⭐⭐"
        elif score > 20:
            recommendation = "⭐⭐⭐⭐"
        elif score > 15:
            recommendation = "⭐⭐⭐"
        elif score > 10:
            recommendation = "⭐⭐"
        else:
            recommendation = "⭐"
        
        print(f"{name:<15} {data['return']:>8.2%} {data['winrate']:>8.2%} "
              f"{data['trades']:>6d} {data['leverage']:>4.1f}x {recommendation:<8}")
    
    print(f"\n🎯 最终建议:")
    print(f"1. 新手推荐: 现货平衡策略 (高胜率，低风险)")
    print(f"2. 进阶推荐: 现货激进策略 (高收益，中等风险)")
    print(f"3. 永续合约: 需要更多优化，暂不推荐")

if __name__ == "__main__":
    compare_all_strategies()
