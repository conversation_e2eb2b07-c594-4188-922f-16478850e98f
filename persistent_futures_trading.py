#!/usr/bin/env python3
"""
持久化永续合约交易系统 - 自动保存和恢复交易状态
"""

import pandas as pd
import numpy as np
import time
import json
import pickle
from datetime import datetime, timedelta
import joblib
import warnings
import os
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class PersistentFuturesTrader:
    """
    持久化永续合约交易器 - 自动保存和恢复状态
    """
    
    def __init__(self, initial_capital=50, leverage=2, state_file="trading_state.json", restore_previous=True):
        """
        初始化持久化交易器
        """
        self.state_file = state_file
        self.leverage = min(max(leverage, 1), 3)
        
        # 尝试恢复之前的状态
        if restore_previous and os.path.exists(state_file):
            self.load_state()
            print(f"🔄 恢复之前的交易状态")
        else:
            # 手动设置您的实际状态
            self.initial_capital = 50.00
            self.capital = 70.06  # 您的实际权益
            
            # 恢复您的持仓
            self.position = -0.000382  # 空头持仓
            self.entry_price = 104730.90
            self.entry_time = datetime.now()
            self.margin_used = 20.00
            
            # 初始化其他状态
            self.trades = []
            self.equity_history = [{
                'timestamp': datetime.now(),
                'equity': 70.06,
                'total_return': 0.4013
            }]
            
            print(f"🔄 手动恢复交易状态到实际水平")
        
        # 交易参数
        self.adaptive_params = {
            'base_thresholds': {
                'strong_long': 0.70, 'weak_long': 0.55,
                'weak_short': 0.45, 'strong_short': 0.30
            },
            'position_sizes': {
                'strong': 0.7, 'weak': 0.5
            },
            'risk_params': {
                'strong_signal': {'stop_loss': 0.03, 'take_profit': 0.08, 'max_hold_hours': 24},
                'weak_signal': {'stop_loss': 0.025, 'take_profit': 0.06, 'max_hold_hours': 16}
            }
        }
        
        # 利润保护参数
        self.profit_protection = {
            'enable': True,
            'protection_levels': {
                0.20: 0.30,  # 20%收益→保护30%
                0.30: 0.50,  # 30%收益→保护50%
                0.40: 0.70,  # 40%收益→保护70% ← 当前应该触发
                0.50: 0.80,  # 50%收益→保护80%
            }
        }
        
        self.commission_rate = 0.0004
        self.funding_rate = 0.0001
        
        # 加载模型
        import glob
        model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
        if model_files:
            model_path = max(model_files, key=lambda x: x.split('_')[-1])
            self.model_data = joblib.load(model_path)
            self.model = self.model_data['model']
            self.scaler = self.model_data['scaler']
        
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        
        # 立即保存当前状态
        self.save_state()
        
        print(f"💾 持久化交易器初始化完成")
        print(f"   当前权益: ${self.capital:.2f}")
        print(f"   总收益率: {self.get_total_return():.2%}")
        print(f"   当前持仓: {'空头' if self.position < 0 else '多头' if self.position > 0 else '空仓'} {abs(self.position):.6f} BTC")
        if self.position != 0:
            print(f"   入场价格: ${self.entry_price:,.2f}")
        print(f"   状态文件: {self.state_file}")
    
    def get_total_return(self):
        """计算总收益率"""
        if hasattr(self, 'equity_history') and self.equity_history:
            current_equity = self.equity_history[-1]['equity']
        else:
            current_equity = self.capital
        return (current_equity - self.initial_capital) / self.initial_capital
    
    def save_state(self):
        """保存交易状态"""
        state = {
            'initial_capital': self.initial_capital,
            'capital': self.capital,
            'position': self.position,
            'entry_price': self.entry_price,
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'margin_used': self.margin_used,
            'trades': self.trades,
            'equity_history': [
                {
                    'timestamp': eq['timestamp'].isoformat() if isinstance(eq['timestamp'], datetime) else eq['timestamp'],
                    'equity': eq['equity'],
                    'total_return': eq.get('total_return', 0)
                } for eq in self.equity_history
            ],
            'adaptive_params': self.adaptive_params,
            'last_saved': datetime.now().isoformat()
        }
        
        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
    
    def load_state(self):
        """加载交易状态"""
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            self.initial_capital = state.get('initial_capital', 50)
            self.capital = state.get('capital', 50)
            self.position = state.get('position', 0)
            self.entry_price = state.get('entry_price', 0)
            self.entry_time = datetime.fromisoformat(state['entry_time']) if state.get('entry_time') else None
            self.margin_used = state.get('margin_used', 0)
            self.trades = state.get('trades', [])
            
            # 恢复权益历史
            self.equity_history = []
            for eq in state.get('equity_history', []):
                self.equity_history.append({
                    'timestamp': datetime.fromisoformat(eq['timestamp']) if isinstance(eq['timestamp'], str) else eq['timestamp'],
                    'equity': eq['equity'],
                    'total_return': eq.get('total_return', 0)
                })
            
            self.adaptive_params = state.get('adaptive_params', {})
            
            print(f"✅ 成功加载交易状态 (保存时间: {state.get('last_saved', '未知')})")
            
        except Exception as e:
            print(f"❌ 加载状态失败: {str(e)}")
            # 使用默认值
            self.initial_capital = 50
            self.capital = 70.06  # 手动设置您的实际权益
            self.position = -0.000382
            self.entry_price = 104730.90
            self.entry_time = datetime.now()
            self.margin_used = 20.00
            self.trades = []
            self.equity_history = []
    
    def check_profit_protection(self, current_price):
        """检查利润保护触发"""
        current_return = self.get_total_return()
        
        # 计算包含未实现盈亏的总收益率
        if self.position != 0:
            if self.position > 0:
                unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
            else:
                unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
            
            unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
            total_equity = self.capital + self.margin_used + unrealized_pnl
            current_return = (total_equity - self.initial_capital) / self.initial_capital
        
        # 检查保护级别
        for return_threshold, protection_ratio in sorted(self.profit_protection['protection_levels'].items(), reverse=True):
            if current_return >= return_threshold:
                return True, protection_ratio, current_return
        
        return False, 0, current_return
    
    def execute_profit_protection(self, protection_ratio, current_price, current_return):
        """执行利润保护"""
        if self.position == 0:
            return False
        
        # 计算保护数量
        protection_amount = abs(self.position) * protection_ratio
        
        # 计算保护收益
        if self.position > 0:
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        leveraged_pnl = pnl_ratio * self.leverage * (self.margin_used * protection_ratio)
        closing_fee = protection_amount * current_price * self.commission_rate
        protected_profit = leveraged_pnl - closing_fee
        
        # 更新状态
        self.capital += (self.margin_used * protection_ratio) + protected_profit
        self.margin_used *= (1 - protection_ratio)
        
        if self.position > 0:
            self.position -= protection_amount
        else:
            self.position += protection_amount
        
        # 记录保护操作
        protection_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'PROFIT_PROTECTION',
            'protection_ratio': protection_ratio,
            'current_return': current_return,
            'protected_profit': protected_profit,
            'remaining_position': self.position,
            'price': current_price
        }
        
        self.trades.append(protection_record)
        
        print(f"\n🛡️ 【利润保护执行】")
        print(f"   ✅ 平仓比例: {protection_ratio:.0%}头寸")
        print(f"   📊 触发收益率: {current_return:.2%}")
        print(f"   💰 保护利润: ${protected_profit:+.2f}")
        print(f"   📈 剩余头寸: {abs(self.position):.6f} BTC")
        print(f"   💼 更新资金: ${self.capital:.2f}")
        print(f"   🎯 保护策略: 成功锁定大部分收益")
        
        # 立即保存状态
        self.save_state()
        
        return True
    
    def get_current_prediction(self, symbol='BTCUSDT'):
        """获取当前预测"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                return None, None, None
            
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            current_time = datetime.now()
            
            return up_probability, current_price, current_time
            
        except Exception as e:
            print(f"❌ 获取预测错误: {str(e)}")
            return None, None, None
    
    def clean_micro_position(self, current_price, threshold_value=1.0):
        """
        清理微小持仓
        """
        if self.position == 0:
            return False

        position_value = abs(self.position) * current_price

        # 如果持仓价值小于阈值，强制平仓
        if position_value < threshold_value:
            print(f"\n🧹 【微小持仓清理】")
            print(f"   持仓数量: {abs(self.position):.6f} BTC")
            print(f"   持仓价值: ${position_value:.4f}")
            print(f"   清理原因: 价值过小，无实际意义")

            # 强制平仓
            self.capital += self.margin_used  # 释放保证金
            self.position = 0
            self.entry_price = 0
            self.entry_time = None
            self.margin_used = 0

            # 记录清理操作
            clean_record = {
                'timestamp': datetime.now().isoformat(),
                'action': 'MICRO_POSITION_CLEAN',
                'position_value': position_value,
                'reason': f'Position value ${position_value:.4f} below threshold ${threshold_value}'
            }

            self.trades.append(clean_record)
            self.save_state()

            print(f"   ✅ 微小持仓已清理完成")
            return True

        return False

    def update_equity(self, current_price, timestamp):
        """更新权益"""
        # 首先检查并清理微小持仓
        self.clean_micro_position(current_price)

        if self.position != 0:
            if self.position > 0:
                unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
            else:
                unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price

            unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
            current_equity = self.capital + self.margin_used + unrealized_pnl
        else:
            current_equity = self.capital

        equity_record = {
            'timestamp': timestamp,
            'equity': current_equity,
            'total_return': (current_equity - self.initial_capital) / self.initial_capital
        }

        self.equity_history.append(equity_record)

        # 限制历史记录大小
        if len(self.equity_history) > 1000:
            self.equity_history = self.equity_history[-500:]

        # 定期保存状态
        if len(self.equity_history) % 10 == 0:
            self.save_state()
    
    def print_status(self, current_price=None, up_probability=None):
        """打印优化的状态显示"""
        current_return = self.get_total_return()

        if self.equity_history:
            latest_equity = self.equity_history[-1]['equity']
        else:
            latest_equity = self.capital

        # 计算绝对收益
        absolute_profit = latest_equity - self.initial_capital

        print(f"\n🎯 【智能永续合约交易系统】")
        print("=" * 60)

        # 💰 收益概览
        print(f"💰 收益概览:")
        print(f"   初始资金: ${self.initial_capital:.2f}")
        print(f"   当前权益: ${latest_equity:.2f}")
        print(f"   绝对收益: ${absolute_profit:+.2f}")

        # 收益率颜色显示
        if current_return >= 0.5:
            return_emoji = "🚀"
            return_status = "卓越表现"
        elif current_return >= 0.3:
            return_emoji = "🎉"
            return_status = "优秀表现"
        elif current_return >= 0.1:
            return_emoji = "✅"
            return_status = "良好表现"
        else:
            return_emoji = "📊"
            return_status = "正常表现"

        print(f"   总收益率: {return_emoji} {current_return:+.2%} ({return_status})")

        # 💼 资金状况
        print(f"\n💼 资金状况:")
        print(f"   可用资金: ${self.capital:.2f}")
        print(f"   占用保证金: ${self.margin_used:.2f}")

        # 计算资金使用率
        if latest_equity > 0:
            margin_usage = (self.margin_used / latest_equity) * 100
            print(f"   保证金使用率: {margin_usage:.1f}%")

        # 🛡️ 利润保护状态
        print(f"\n🛡️ 利润保护状态:")
        protection_actions = [t for t in self.trades if t.get('action') == 'PROFIT_PROTECTION']

        if current_price:
            should_protect, protection_ratio, total_return = self.check_profit_protection(current_price)
            if should_protect:
                print(f"   🚨 触发保护: {protection_ratio:.0%}头寸 (收益率: {total_return:.1%})")

                # 自动执行保护
                if self.profit_protection['enable']:
                    self.execute_profit_protection(protection_ratio, current_price, total_return)
            else:
                # 显示下一个保护级别
                next_level = None
                for level in sorted(self.profit_protection['protection_levels'].keys()):
                    if current_return < level:
                        next_level = level
                        break

                if next_level:
                    print(f"   📊 下一保护级别: {next_level:.0%}收益率")
                else:
                    print(f"   ✅ 已达最高保护级别")

        print(f"   保护操作历史: {len(protection_actions)}次")

        # 📈 持仓信息
        print(f"\n📈 持仓信息:")
        if self.position != 0:
            position_type = "🟢 多头" if self.position > 0 else "🔴 空头"
            position_size = abs(self.position)

            print(f"   当前持仓: {position_type}")
            print(f"   持仓数量: {position_size:.6f} BTC")
            print(f"   入场价格: ${self.entry_price:,.2f}")

            if current_price:
                # 计算未实现盈亏
                if self.position > 0:
                    unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
                    price_direction = "📈" if current_price > self.entry_price else "📉"
                else:
                    unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
                    price_direction = "📈" if current_price < self.entry_price else "📉"

                unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used

                print(f"   当前价格: ${current_price:,.2f} {price_direction}")
                print(f"   未实现盈亏: {unrealized_pnl:+.2f} ({unrealized_pnl_ratio * self.leverage:+.2%})")

                # 计算距离止损的距离
                if self.position > 0:
                    stop_loss_price = self.entry_price * 0.975  # 2.5%止损
                    distance_to_stop = (current_price - stop_loss_price) / current_price
                else:
                    stop_loss_price = self.entry_price * 1.025  # 2.5%止损
                    distance_to_stop = (stop_loss_price - current_price) / current_price

                if distance_to_stop < 0.01:
                    risk_level = "🚨 高风险"
                elif distance_to_stop < 0.02:
                    risk_level = "⚠️ 中风险"
                else:
                    risk_level = "✅ 安全"

                print(f"   风险评估: {risk_level} (距止损: {distance_to_stop:+.2%})")
        else:
            print(f"   当前持仓: 💤 空仓")

        # 📊 交易统计
        print(f"\n📊 交易统计:")
        completed_trades = [t for t in self.trades if t.get('action') == 'CLOSE']
        print(f"   完成交易: {len(completed_trades)}笔")

        if completed_trades:
            profitable_trades = [t for t in completed_trades if t.get('final_pnl', 0) > 0]
            win_rate = len(profitable_trades) / len(completed_trades)
            print(f"   胜率: {win_rate:.1%}")

            total_pnl = sum(t.get('final_pnl', 0) for t in completed_trades)
            print(f"   交易总盈亏: ${total_pnl:+.2f}")

        # 📈 市场分析
        if current_price and up_probability:
            print(f"\n📈 市场分析:")
            print(f"   BTC永续价格: ${current_price:,.2f}")
            print(f"   AI预测概率:")
            print(f"     📈 上涨概率: {up_probability:.1%}")
            print(f"     📉 下跌概率: {1-up_probability:.1%}")

            # 信号强度分析
            if up_probability > 0.7:
                signal = "🟢 强烈看涨"
                signal_strength = "强"
            elif up_probability > 0.55:
                signal = "🟡 轻微看涨"
                signal_strength = "弱"
            elif up_probability < 0.3:
                signal = "🔴 强烈看跌"
                signal_strength = "强"
            elif up_probability < 0.45:
                signal = "🟡 轻微看跌"
                signal_strength = "弱"
            else:
                signal = "⚪ 中性观望"
                signal_strength = "无"

            print(f"   市场信号: {signal} ({signal_strength}信号)")

        print("=" * 60)

def run_persistent_simulation(check_interval=300, leverage=2):
    """运行持久化模拟"""
    print("💾 启动持久化永续合约交易系统")
    print("=" * 60)
    print("特点: 自动保存状态、恢复数据、利润保护")
    print("")
    
    trader = PersistentFuturesTrader(initial_capital=50, leverage=leverage)
    
    try:
        while True:
            current_time = datetime.now()
            print(f"\n⏰ {current_time.strftime('%Y-%m-%d %H:%M:%S')} - 持久化交易分析...")
            
            up_prob, current_price, _ = trader.get_current_prediction()
            
            if up_prob is None:
                print("❌ 无法获取预测，跳过")
                time.sleep(check_interval)
                continue
            
            trader.update_equity(current_price, current_time)
            trader.print_status(current_price, up_prob)
            
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        print(f"\n🛑 停止持久化交易")
        trader.save_state()
        print(f"💾 交易状态已保存")

if __name__ == "__main__":
    import sys
    
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300
    leverage = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    
    print("💾 持久化交易系统说明:")
    print("- 自动保存和恢复交易状态")
    print("- 手动恢复您的40%收益状态")
    print("- 自动触发利润保护机制")
    print("- 数据永不丢失")
    print("")
    
    run_persistent_simulation(interval, leverage)
