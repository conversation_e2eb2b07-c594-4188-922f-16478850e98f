#!/usr/bin/env python3
"""
自适应永续合约模拟交易系统 - 智能阈值和多级信号
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class AdaptiveFuturesTrader:
    """
    自适应永续合约交易器 - 智能阈值系统
    """
    
    def __init__(self, initial_capital=50, leverage=2, model_path=None):
        """
        初始化自适应永续合约交易器
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = min(max(leverage, 1), 3)
        
        # 持仓状态
        self.position = 0
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0
        
        # 自适应阈值系统
        self.thresholds = {
            'strong_long': 0.70,    # 强做多: >70%
            'weak_long': 0.55,      # 弱做多: 55-70%
            'weak_short': 0.45,     # 弱做空: 30-45%
            'strong_short': 0.30    # 强做空: <30%
        }
        
        # 多级仓位管理
        self.position_sizes = {
            'strong': 0.8,  # 强信号: 80%仓位
            'weak': 0.5     # 弱信号: 50%仓位
        }
        
        # 动态风险参数
        self.risk_params = {
            'strong_signal': {
                'stop_loss': 0.03,      # 3%止损
                'take_profit': 0.08,    # 8%止盈
                'max_hold_hours': 24
            },
            'weak_signal': {
                'stop_loss': 0.02,      # 2%止损
                'take_profit': 0.05,    # 5%止盈
                'max_hold_hours': 12
            }
        }
        
        self.commission_rate = 0.0004
        self.funding_rate = 0.0001
        
        # 交易记录
        self.trades = []
        self.equity_history = []
        self.signal_history = []
        
        # 加载模型
        if model_path is None:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if not model_files:
                raise ValueError("未找到BTCUSDT模型文件")
            model_path = max(model_files, key=lambda x: x.split('_')[-1])
        
        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']
        
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        
        print(f"🚀 自适应永续合约交易器初始化完成")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   强做多: >{self.thresholds['strong_long']:.0%}")
        print(f"   弱做多: {self.thresholds['weak_long']:.0%}-{self.thresholds['strong_long']:.0%}")
        print(f"   弱做空: {self.thresholds['strong_short']:.0%}-{self.thresholds['weak_short']:.0%}")
        print(f"   强做空: <{self.thresholds['strong_short']:.0%}")
        print(f"   中性区间: 仅{self.thresholds['weak_short']:.0%}-{self.thresholds['weak_long']:.0%} (10%)")
    
    def analyze_signal(self, up_probability):
        """
        分析信号强度和方向
        """
        if up_probability > self.thresholds['strong_long']:
            return 'strong_long', self.position_sizes['strong'], self.risk_params['strong_signal']
        elif up_probability > self.thresholds['weak_long']:
            return 'weak_long', self.position_sizes['weak'], self.risk_params['weak_signal']
        elif up_probability < self.thresholds['strong_short']:
            return 'strong_short', self.position_sizes['strong'], self.risk_params['strong_signal']
        elif up_probability < self.thresholds['weak_short']:
            return 'weak_short', self.position_sizes['weak'], self.risk_params['weak_signal']
        else:
            return 'neutral', 0, None
    
    def get_current_prediction(self, symbol='BTCUSDT'):
        """
        获取当前预测
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                return None, None, None
            
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            current_time = datetime.now()
            
            return up_probability, current_price, current_time
            
        except Exception as e:
            print(f"❌ 获取预测错误: {str(e)}")
            return None, None, None
    
    def should_open_position(self, up_probability):
        """
        判断是否开仓
        """
        if self.position != 0:
            return False, 0, 0, None, "已有持仓"
        
        signal_type, position_size, risk_params = self.analyze_signal(up_probability)
        
        if signal_type == 'neutral':
            return False, 0, 0, None, f"中性信号 ({up_probability:.1%})"
        
        # 确定方向
        if 'long' in signal_type:
            direction = 1
        else:
            direction = -1
        
        return True, direction, position_size, risk_params, f"{signal_type} 信号 ({up_probability:.1%})"
    
    def should_close_position(self, up_probability, current_price, current_risk_params):
        """
        判断是否平仓
        """
        if self.position == 0:
            return False, "无持仓"
        
        # 计算盈亏
        if self.position > 0:
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        # 使用当前信号的风险参数
        stop_loss = current_risk_params['stop_loss']
        take_profit = current_risk_params['take_profit']
        max_hours = current_risk_params['max_hold_hours']
        
        # 止损
        if pnl_ratio < -stop_loss:
            return True, f"止损 ({pnl_ratio:.2%})"
        
        # 止盈
        if pnl_ratio > take_profit:
            return True, f"止盈 ({pnl_ratio:.2%})"
        
        # 时间止损
        if self.entry_time:
            hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
            if hold_hours > max_hours:
                return True, f"时间止损 ({hold_hours:.1f}h)"
        
        # 强信号反转
        if self.position > 0 and up_probability < 0.35:
            return True, f"多头强反转 ({up_probability:.1%})"
        elif self.position < 0 and up_probability > 0.65:
            return True, f"空头强反转 ({up_probability:.1%})"
        
        return False, "持有"
    
    def execute_trade(self, action, direction, position_size_ratio, price, timestamp, confidence=None, reason="", risk_params=None):
        """
        执行交易
        """
        if action == 'OPEN':
            available_margin = self.capital * 0.8
            position_value = available_margin * position_size_ratio * self.leverage
            position_size = position_value / price
            
            if direction == -1:
                position_size = -position_size
            
            self.position = position_size
            self.entry_price = price
            self.entry_time = timestamp
            self.margin_used = available_margin * position_size_ratio
            self.current_risk_params = risk_params
            
            opening_fee = abs(position_size) * price * self.commission_rate
            self.capital -= opening_fee
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'OPEN',
                'direction': 'LONG' if direction == 1 else 'SHORT',
                'signal_strength': 'STRONG' if position_size_ratio > 0.6 else 'WEAK',
                'price': price,
                'position_size': position_size,
                'position_ratio': position_size_ratio,
                'margin_used': self.margin_used,
                'leverage': self.leverage,
                'confidence': confidence,
                'reason': reason,
                'risk_params': risk_params,
                'opening_fee': opening_fee
            }
            
            strength = "强" if position_size_ratio > 0.6 else "弱"
            direction_text = "做多" if direction == 1 else "做空"
            print(f"✅ {strength}{direction_text} {abs(position_size):.6f} BTC @ ${price:,.2f}")
            print(f"   仓位: {position_size_ratio:.0%}, 杠杆: {self.leverage}x, 置信度: {confidence:.1%}")
            
        elif action == 'CLOSE':
            if self.position > 0:
                pnl_ratio = (price - self.entry_price) / self.entry_price
            else:
                pnl_ratio = (self.entry_price - price) / self.entry_price
            
            leveraged_pnl = pnl_ratio * self.leverage * self.margin_used
            closing_fee = abs(self.position) * price * self.commission_rate
            funding_fee = self.calculate_funding_fee()
            final_pnl = leveraged_pnl - closing_fee + funding_fee
            
            self.capital = self.capital + self.margin_used + final_pnl
            
            hold_time = (timestamp - self.entry_time).total_seconds() / 3600 if self.entry_time else 0
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'CLOSE',
                'direction': 'LONG' if self.position > 0 else 'SHORT',
                'price': price,
                'entry_price': self.entry_price,
                'position_size': self.position,
                'pnl_ratio': pnl_ratio,
                'leveraged_pnl': leveraged_pnl,
                'final_pnl': final_pnl,
                'hold_hours': hold_time,
                'confidence': confidence,
                'reason': reason,
                'capital_after': self.capital
            }
            
            direction_text = "平多" if self.position > 0 else "平空"
            print(f"✅ {direction_text} @ ${price:,.2f} (盈亏: {final_pnl:+.2f}, 资金: ${self.capital:.2f})")
            
            self.position = 0
            self.entry_price = 0
            self.entry_time = None
            self.margin_used = 0
            self.current_risk_params = None
        
        self.trades.append(trade_record)
    
    def calculate_funding_fee(self):
        """计算资金费率"""
        if self.position == 0 or not self.entry_time:
            return 0
        
        hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
        funding_periods = int(hold_hours / 8)
        
        if funding_periods > 0:
            position_value = abs(self.position) * self.entry_price
            funding_fee = position_value * self.funding_rate * funding_periods
            return -funding_fee if self.position > 0 else funding_fee
        return 0
    
    def update_equity(self, current_price, timestamp):
        """更新权益"""
        if self.position != 0:
            if self.position > 0:
                unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
            else:
                unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
            
            unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
            funding_fee = self.calculate_funding_fee()
            current_equity = self.capital + self.margin_used + unrealized_pnl + funding_fee
        else:
            current_equity = self.capital
        
        self.equity_history.append({
            'timestamp': timestamp,
            'price': current_price,
            'equity': current_equity,
            'total_return': (current_equity - self.initial_capital) / self.initial_capital
        })
    
    def print_status(self, current_price=None, up_probability=None):
        """打印状态"""
        if self.equity_history:
            latest_equity = self.equity_history[-1]['equity']
            total_return = self.equity_history[-1]['total_return']
        else:
            latest_equity = self.capital
            total_return = 0
        
        completed_trades = [t for t in self.trades if t['action'] == 'CLOSE']
        profitable_trades = [t for t in completed_trades if t.get('final_pnl', 0) > 0]
        win_rate = len(profitable_trades) / len(completed_trades) if completed_trades else 0
        
        print(f"\n📊 自适应永续合约交易状态")
        print("=" * 50)
        print(f"当前权益: ${latest_equity:.2f}")
        print(f"总收益率: {total_return:+.2%}")
        print(f"可用资金: ${self.capital:.2f}")
        print(f"占用保证金: ${self.margin_used:.2f}")
        
        if self.position != 0:
            position_type = "多头" if self.position > 0 else "空头"
            print(f"当前持仓: {position_type} {abs(self.position):.6f} BTC")
            print(f"入场价格: ${self.entry_price:,.2f}")
            
            if current_price:
                if self.position > 0:
                    unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
                else:
                    unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
                
                unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
                print(f"未实现盈亏: {unrealized_pnl:+.2f} ({unrealized_pnl_ratio * self.leverage:+.2%})")
        else:
            print(f"当前持仓: 空仓")
        
        print(f"完成交易: {len(completed_trades)}")
        print(f"胜率: {win_rate:.2%}")
        
        if current_price and up_probability:
            signal_type, _, _ = self.analyze_signal(up_probability)
            
            print(f"\n📈 当前市场信息:")
            print(f"BTC永续价格: ${current_price:,.2f}")
            print(f"上涨概率: {up_probability:.3f} ({up_probability:.1%})")
            
            signal_map = {
                'strong_long': "🟢 强烈看涨 (强做多信号)",
                'weak_long': "🟡 轻微看涨 (弱做多信号)",
                'weak_short': "🟡 轻微看跌 (弱做空信号)",
                'strong_short': "🔴 强烈看跌 (强做空信号)",
                'neutral': "⚪ 中性 (无交易信号)"
            }
            
            print(f"市场信号: {signal_map.get(signal_type, '未知')}")

def run_adaptive_futures_simulation(check_interval=300, leverage=2):
    """运行自适应永续合约模拟"""
    print("🚀 启动自适应永续合约模拟交易系统")
    print("=" * 60)
    print("特点: 智能阈值、多级信号、动态风险管理")
    print(f"杠杆倍数: {leverage}x")
    print(f"检查间隔: {check_interval}秒")
    print("")
    
    trader = AdaptiveFuturesTrader(initial_capital=50, leverage=leverage)
    
    try:
        while True:
            print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 检查市场...")
            
            up_prob, current_price, current_time = trader.get_current_prediction()
            
            if up_prob is None:
                print("❌ 无法获取预测，跳过")
                time.sleep(check_interval)
                continue
            
            trader.update_equity(current_price, current_time)
            
            # 检查平仓
            if trader.position != 0:
                should_close, close_reason = trader.should_close_position(
                    up_prob, current_price, trader.current_risk_params
                )
                if should_close:
                    trader.execute_trade('CLOSE', 0, 0, current_price, current_time, up_prob, close_reason)
            
            # 检查开仓
            should_open, direction, position_size, risk_params, open_reason = trader.should_open_position(up_prob)
            if should_open:
                trader.execute_trade('OPEN', direction, position_size, current_price, current_time, up_prob, open_reason, risk_params)
            
            trader.print_status(current_price, up_prob)
            
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        print(f"\n🛑 停止交易")
        trader.print_status(current_price, up_prob)

if __name__ == "__main__":
    import sys
    
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300
    leverage = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    leverage = min(max(leverage, 1), 3)
    
    print("🎯 自适应永续合约交易说明:")
    print("- 智能多级阈值系统")
    print("- 强信号: 70%+做多, 30%-做空")
    print("- 弱信号: 55-70%做多, 30-45%做空")
    print("- 中性区间仅10% (45-55%)")
    print("- 动态仓位和风险管理")
    print("")
    
    run_adaptive_futures_simulation(interval, leverage)
