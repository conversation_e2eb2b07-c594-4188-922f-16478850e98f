#!/usr/bin/env python3
"""
基于Sklearn的深度学习高频交易系统
目标: 70%+准确率的高频交易
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from sklearn.neural_network import MLPClassifier
from sklearn.ensemble import VotingClassifier, GradientBoostingClassifier, RandomForestClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedHFTFeatureEngineer:
    """高频交易高级特征工程"""
    
    def __init__(self):
        self.scalers = {}
        
    def create_price_microstructure_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建价格微观结构特征"""
        features = pd.DataFrame(index=data.index)
        
        # 多层次价格变化
        for lag in [1, 2, 3, 5, 10, 15, 20, 30]:
            features[f'return_{lag}'] = data['close'].pct_change(lag)
            features[f'log_return_{lag}'] = np.log(data['close'] / data['close'].shift(lag))
        
        # 价格加速度和动量
        features['price_acceleration'] = data['close'].pct_change().diff()
        features['price_momentum'] = data['close'].pct_change() * data['volume']
        
        # 高低价特征
        features['hl_ratio'] = (data['high'] - data['low']) / data['close']
        features['oc_ratio'] = (data['close'] - data['open']) / data['open']
        features['ho_ratio'] = (data['high'] - data['open']) / data['open']
        features['lo_ratio'] = (data['low'] - data['open']) / data['open']
        
        # 价格位置特征
        for window in [5, 10, 20, 30, 60]:
            rolling_min = data['low'].rolling(window).min()
            rolling_max = data['high'].rolling(window).max()
            features[f'price_position_{window}'] = (data['close'] - rolling_min) / (rolling_max - rolling_min)
            
            # 相对强度
            features[f'relative_strength_{window}'] = data['close'] / data['close'].rolling(window).mean()
        
        return features
    
    def create_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建成交量特征"""
        features = pd.DataFrame(index=data.index)
        
        # 成交量变化
        for lag in [1, 2, 3, 5, 10, 20]:
            features[f'volume_change_{lag}'] = data['volume'].pct_change(lag)
            features[f'volume_ratio_{lag}'] = data['volume'] / data['volume'].rolling(lag).mean()
        
        # 价量关系
        features['price_volume_trend'] = data['close'].pct_change() * data['volume']
        features['volume_weighted_price'] = (data['high'] + data['low'] + data['close']) / 3 * data['volume']
        
        # OBV和相关指标
        price_change = data['close'].diff()
        obv = (data['volume'] * np.sign(price_change)).cumsum()
        features['obv'] = obv
        features['obv_slope'] = obv.diff()
        
        for window in [10, 20, 50]:
            features[f'obv_ma_{window}'] = obv.rolling(window).mean()
            features[f'obv_ratio_{window}'] = obv / features[f'obv_ma_{window}']
        
        return features
    
    def create_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建波动率特征"""
        features = pd.DataFrame(index=data.index)
        
        returns = data['close'].pct_change()
        
        # 多时间框架波动率
        for window in [5, 10, 20, 30, 60]:
            features[f'volatility_{window}'] = returns.rolling(window).std()
            features[f'volatility_ratio_{window}'] = features[f'volatility_{window}'] / returns.rolling(window*2).std()
        
        # Parkinson波动率
        features['parkinson_vol'] = np.sqrt(0.25 * np.log(data['high'] / data['low'])**2)
        
        # Garman-Klass波动率
        features['gk_vol'] = np.sqrt(
            0.5 * (np.log(data['high'] / data['low'])**2) -
            (2 * np.log(2) - 1) * (np.log(data['close'] / data['open'])**2)
        )
        
        # 波动率突变检测
        features['vol_shock'] = features['volatility_5'] / features['volatility_20']
        features['vol_regime'] = (features['volatility_10'] > features['volatility_10'].rolling(50).quantile(0.8)).astype(int)
        
        return features
    
    def create_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建技术指标"""
        features = pd.DataFrame(index=data.index)
        
        # RSI多时间框架
        for period in [5, 10, 14, 20, 30]:
            rsi = self.calculate_rsi(data['close'], period)
            features[f'rsi_{period}'] = rsi
            features[f'rsi_slope_{period}'] = rsi.diff()
        
        # MACD
        exp1 = data['close'].ewm(span=12).mean()
        exp2 = data['close'].ewm(span=26).mean()
        macd = exp1 - exp2
        signal = macd.ewm(span=9).mean()
        features['macd'] = macd
        features['macd_signal'] = signal
        features['macd_histogram'] = macd - signal
        features['macd_slope'] = macd.diff()
        
        # 布林带
        for period in [10, 20, 30]:
            ma = data['close'].rolling(period).mean()
            std = data['close'].rolling(period).std()
            features[f'bb_upper_{period}'] = ma + 2 * std
            features[f'bb_lower_{period}'] = ma - 2 * std
            features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / ma
            features[f'bb_position_{period}'] = (data['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
        
        # 移动平均线
        for period in [5, 10, 20, 30, 50]:
            ma = data['close'].rolling(period).mean()
            features[f'ma_{period}'] = ma
            features[f'ma_slope_{period}'] = ma.diff()
            features[f'price_ma_ratio_{period}'] = data['close'] / ma
        
        return features
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def create_sequence_features(self, data: pd.DataFrame, lookback: int = 30) -> pd.DataFrame:
        """创建序列特征"""
        features = pd.DataFrame(index=data.index)
        
        # 价格序列统计
        for window in [5, 10, 20, 30]:
            price_series = data['close'].rolling(window)
            features[f'price_mean_{window}'] = price_series.mean()
            features[f'price_std_{window}'] = price_series.std()
            features[f'price_skew_{window}'] = price_series.skew()
            features[f'price_kurt_{window}'] = price_series.kurt()
            
            # 趋势强度
            features[f'trend_strength_{window}'] = (data['close'] - data['close'].shift(window)) / data['close'].shift(window)
        
        # 序列模式
        for lag in range(1, min(lookback, 10)):
            features[f'lag_{lag}_return'] = data['close'].pct_change(lag)
            features[f'lag_{lag}_volume'] = data['volume'].shift(lag)
        
        return features
    
    def create_all_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建所有特征"""
        logger.info("创建高频交易高级特征...")
        
        # 各类特征
        price_features = self.create_price_microstructure_features(data)
        volume_features = self.create_volume_features(data)
        volatility_features = self.create_volatility_features(data)
        technical_features = self.create_technical_indicators(data)
        sequence_features = self.create_sequence_features(data)
        
        # 合并所有特征
        all_features = pd.concat([
            price_features, volume_features, volatility_features,
            technical_features, sequence_features
        ], axis=1)
        
        # 处理缺失值和无穷值
        all_features = all_features.replace([np.inf, -np.inf], np.nan)
        all_features = all_features.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        logger.info(f"创建了 {len(all_features.columns)} 个高级特征")
        return all_features

class DeepMLPHFTModel:
    """深度MLP高频交易模型"""
    
    def __init__(self):
        self.models = {}
        self.best_model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        
    def create_target_labels(self, data: pd.DataFrame, 
                           future_periods: int = 2,
                           threshold: float = 0.0005) -> pd.Series:
        """创建高频交易标签"""
        logger.info(f"创建高频标签，预测{future_periods}分钟后的价格变动...")
        
        # 计算未来收益率
        future_returns = data['close'].pct_change(future_periods).shift(-future_periods)
        
        # 创建标签 (更严格的阈值用于高频)
        labels = pd.Series(index=data.index, dtype=int)
        labels[future_returns < -threshold] = 0  # 下跌
        labels[(future_returns >= -threshold) & (future_returns <= threshold)] = 1  # 横盘
        labels[future_returns > threshold] = 2  # 上涨
        
        # 统计分布
        label_counts = labels.value_counts().sort_index()
        logger.info(f"标签分布: 下跌={label_counts.get(0, 0)}, 横盘={label_counts.get(1, 0)}, 上涨={label_counts.get(2, 0)}")
        
        return labels
    
    def train_deep_models(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """训练深度模型"""
        logger.info("开始训练深度学习模型...")
        
        # 数据预处理
        X_scaled = self.scaler.fit_transform(X)
        
        # 时间序列分割
        tscv = TimeSeriesSplit(n_splits=5)
        
        # 深度神经网络配置
        deep_mlp_configs = {
            'deep_mlp_large': {
                'model': MLPClassifier(random_state=42, max_iter=500),
                'params': {
                    'hidden_layer_sizes': [(200, 100, 50), (300, 150, 75), (400, 200, 100)],
                    'activation': ['relu', 'tanh'],
                    'alpha': [0.0001, 0.001, 0.01],
                    'learning_rate': ['adaptive'],
                    'early_stopping': [True],
                    'validation_fraction': [0.1]
                }
            },
            'deep_mlp_wide': {
                'model': MLPClassifier(random_state=42, max_iter=500),
                'params': {
                    'hidden_layer_sizes': [(500, 250), (800, 400), (1000, 500)],
                    'activation': ['relu'],
                    'alpha': [0.0001, 0.001],
                    'learning_rate': ['adaptive'],
                    'early_stopping': [True]
                }
            },
            'gradient_boosting': {
                'model': GradientBoostingClassifier(random_state=42),
                'params': {
                    'n_estimators': [200, 300, 500],
                    'learning_rate': [0.05, 0.1, 0.15],
                    'max_depth': [5, 7, 9],
                    'subsample': [0.8, 0.9, 1.0]
                }
            },
            'random_forest': {
                'model': RandomForestClassifier(random_state=42),
                'params': {
                    'n_estimators': [200, 300, 500],
                    'max_depth': [15, 20, 25],
                    'min_samples_split': [2, 5],
                    'min_samples_leaf': [1, 2]
                }
            }
        }
        
        results = {}
        
        for model_name, config in deep_mlp_configs.items():
            logger.info(f"训练 {model_name}...")
            
            try:
                # 网格搜索
                grid_search = GridSearchCV(
                    config['model'],
                    config['params'],
                    cv=tscv,
                    scoring='accuracy',
                    n_jobs=-1,
                    verbose=0
                )
                
                grid_search.fit(X_scaled, y)
                
                best_model = grid_search.best_estimator_
                best_score = grid_search.best_score_
                
                self.models[model_name] = best_model
                results[model_name] = {
                    'model': best_model,
                    'score': best_score,
                    'params': grid_search.best_params_
                }
                
                logger.info(f"{model_name} 最佳得分: {best_score:.4f}")
                
                # 检查是否达到70%目标
                if best_score >= 0.70:
                    logger.info(f"🎉 {model_name} 达到70%+准确率: {best_score:.1%}")
                
            except Exception as e:
                logger.error(f"{model_name} 训练失败: {e}")
        
        # 创建集成模型
        if len(self.models) >= 2:
            logger.info("创建集成模型...")
            
            # 选择最佳的几个模型
            sorted_models = sorted(results.items(), key=lambda x: x[1]['score'], reverse=True)
            top_models = sorted_models[:3]  # 取前3个
            
            voting_estimators = [(name, results[name]['model']) for name, _ in top_models]
            
            voting_classifier = VotingClassifier(
                estimators=voting_estimators,
                voting='soft'
            )
            
            # 评估集成模型
            from sklearn.model_selection import cross_val_score
            ensemble_scores = cross_val_score(voting_classifier, X_scaled, y, cv=tscv, scoring='accuracy')
            ensemble_score = ensemble_scores.mean()
            
            self.models['ensemble'] = voting_classifier
            results['ensemble'] = {
                'model': voting_classifier,
                'score': ensemble_score,
                'params': 'ensemble'
            }
            
            logger.info(f"集成模型得分: {ensemble_score:.4f}")
            
            if ensemble_score >= 0.70:
                logger.info(f"🎉 集成模型达到70%+准确率: {ensemble_score:.1%}")
        
        # 选择最佳模型
        if results:
            best_model_name = max(results.keys(), key=lambda k: results[k]['score'])
            self.best_model = results[best_model_name]['model']
            best_score = results[best_model_name]['score']
            
            logger.info(f"最佳模型: {best_model_name} (得分: {best_score:.4f})")
            
            if best_score >= 0.70:
                logger.info("🎉 成功达到70%+准确率目标！")
            else:
                logger.warning(f"⚠️ 未达到70%目标，当前最佳: {best_score:.1%}")
            
            self.is_trained = True
        
        return results
    
    def predict(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """预测"""
        if not self.is_trained:
            raise ValueError("模型未训练")
        
        X_scaled = self.scaler.transform(X)
        predictions = self.best_model.predict(X_scaled)
        
        # 获取概率
        if hasattr(self.best_model, 'predict_proba'):
            probabilities = self.best_model.predict_proba(X_scaled)
            confidence = np.max(probabilities, axis=1)
        else:
            confidence = np.ones(len(predictions)) * 0.5
        
        return predictions, confidence

def create_realistic_hft_data(n_points: int = 5000) -> pd.DataFrame:
    """创建更真实的高频数据"""
    logger.info(f"创建 {n_points} 条真实高频数据...")
    
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=n_points, freq='1T')
    
    # 更复杂的价格模型
    price = 100.0
    prices = []
    volumes = []
    
    # 市场状态参数
    trend_strength = 0
    volatility_regime = 1.0
    
    for i in range(n_points):
        # 动态调整市场状态
        if i % 500 == 0:  # 每500分钟调整一次
            trend_strength = np.random.normal(0, 0.0001)
            volatility_regime = np.random.uniform(0.5, 2.0)
        
        # 多层次价格变动
        trend = trend_strength
        mean_reversion = -0.05 * (price - 100) / 100
        noise = np.random.normal(0, 0.0003 * volatility_regime)
        
        # 微观结构噪音
        microstructure_noise = np.random.normal(0, 0.0001)
        
        # 偶尔的流动性冲击
        if np.random.random() < 0.002:
            liquidity_shock = np.random.normal(0, 0.001)
        else:
            liquidity_shock = 0
        
        total_change = trend + mean_reversion + noise + microstructure_noise + liquidity_shock
        price *= (1 + total_change)
        prices.append(price)
        
        # 成交量模型
        base_volume = 1000
        volume_trend = base_volume * (1 + abs(total_change) * 100)
        volume = np.random.poisson(volume_trend)
        volumes.append(volume)
    
    # 创建OHLC
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = [prices[max(0, i-1)] for i in range(n_points)]
    
    # 更真实的高低价
    highs = []
    lows = []
    for i in range(n_points):
        if i == 0:
            high = low = prices[i]
        else:
            # 基于前一分钟的价格范围
            prev_price = prices[i-1]
            curr_price = prices[i]
            
            # 计算可能的价格范围
            price_range = abs(curr_price - prev_price) + abs(np.random.normal(0, curr_price * 0.0001))
            
            high = max(prev_price, curr_price) + price_range * np.random.random()
            low = min(prev_price, curr_price) - price_range * np.random.random()
        
        highs.append(high)
        lows.append(low)
    
    data['high'] = highs
    data['low'] = lows
    data['volume'] = volumes
    
    # 确保OHLC合理性
    data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
    data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))
    
    return data

if __name__ == "__main__":
    print("🚀 基于Sklearn的深度学习高频交易系统")
    print("🎯 目标: 70%+准确率")
    print("⚡ 模式: 高频交易优化")
    
    # 创建真实高频数据
    data = create_realistic_hft_data(8000)
    print(f"✅ 创建高频数据: {len(data)} 条1分钟K线")
    
    # 高级特征工程
    feature_engineer = AdvancedHFTFeatureEngineer()
    features = feature_engineer.create_all_features(data)
    print(f"✅ 创建特征: {len(features.columns)} 个高级特征")
    
    # 创建模型
    model = DeepMLPHFTModel()
    
    # 创建高频标签
    target = model.create_target_labels(data, future_periods=2, threshold=0.0008)
    
    # 准备数据
    common_index = features.index.intersection(target.index)
    features_aligned = features.loc[common_index]
    target_aligned = target.loc[common_index]
    
    # 去除缺失值
    valid_mask = ~(features_aligned.isnull().any(axis=1) | target_aligned.isnull())
    X = features_aligned[valid_mask]
    y = target_aligned[valid_mask]
    
    print(f"✅ 有效训练数据: {len(X)} 条记录")
    
    if len(X) > 1000:
        print("🤖 开始深度学习模型训练...")
        print("⏳ 训练中，请稍候...")
        
        # 训练模型
        results = model.train_deep_models(X, y)
        
        print("\n🎉 深度学习高频交易系统训练完成！")
        print("\n📊 模型性能:")
        for model_name, result in results.items():
            score = result['score']
            print(f"  {model_name}: {score:.4f} ({score:.1%})")
            if score >= 0.70:
                print(f"    🎉 达到70%+目标！")
        
        # 总结
        best_score = max([r['score'] for r in results.values()]) if results else 0
        if best_score >= 0.70:
            print(f"\n🎉 成功！最佳模型达到 {best_score:.1%} 准确率")
            print("✅ 已达到70%+准确率目标")
        else:
            print(f"\n⚠️ 当前最佳准确率: {best_score:.1%}")
            print("💡 建议进一步优化:")
            print("  1. 增加更多真实市场数据")
            print("  2. 优化特征工程")
            print("  3. 调整模型超参数")
            print("  4. 尝试更复杂的集成方法")
    
    else:
        print("⚠️ 训练数据不足")
    
    print("\n🔧 系统特点:")
    print("  ✅ 高级特征工程 (微观结构)")
    print("  ✅ 深度神经网络")
    print("  ✅ 模型集成")
    print("  ✅ 时间序列验证")
    print("  ✅ 超参数优化")
