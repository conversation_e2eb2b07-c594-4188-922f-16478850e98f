#!/usr/bin/env python3
"""
模型性能对比分析
比较传统机器学习模型与深度学习模型的实际表现
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer, FeatureConfig
from train_no_tensorflow import SimpleModelTrainer

def comprehensive_model_comparison(symbol='BTCUSDT', days_back=180):
    """
    全面的模型性能比较
    """
    print(f"🔍 开始 {symbol} 模型性能全面对比...")
    print(f"📅 使用最近 {days_back} 天的数据")
    
    # 1. 准备数据
    print("\n📊 准备数据...")
    start_date = (datetime.now() - pd.Timedelta(days=days_back)).strftime('%Y-%m-%d')
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date)
    
    # 特征工程
    config = FeatureConfig(
        ma_periods=[5, 10, 20, 50, 100],
        rsi_periods=[6, 14, 24],
        prediction_window=24,
        enable_variance_threshold_selection=True,
        enable_correlation_selection=True
    )
    engineer = FeatureEngineer(config=config)
    df_features = engineer.create_features(df)
    
    # 分离特征和目标
    X = df_features.drop(columns=['target'])
    y = df_features['target']
    
    # 移除原始列
    original_cols = ['open', 'high', 'low', 'close', 'volume']
    X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
    
    print(f"✅ 数据准备完成: {X.shape[1]} 特征, {len(y)} 样本, {len(y.unique())} 类别")
    
    # 2. 测试不同模型
    models_to_test = ['rf', 'gb', 'xgb', 'lgb']
    results = {}
    
    for model_type in models_to_test:
        print(f"\n🎯 测试 {model_type.upper()} 模型...")
        
        try:
            trainer = SimpleModelTrainer(
                model_type=model_type,
                use_optuna=True,
                n_trials=30
            )
            
            result = trainer.train(X, y)
            results[model_type] = {
                'accuracy': result['accuracy'],
                'precision': result['precision'],
                'recall': result['recall'],
                'f1': result['f1'],
                'accuracy_std': result['accuracy_std'],
                'trainer': trainer
            }
            
            print(f"✅ {model_type.upper()} 完成 - 准确率: {result['accuracy']:.4f}")
            
        except Exception as e:
            print(f"❌ {model_type.upper()} 失败: {str(e)}")
            results[model_type] = {'error': str(e)}
    
    # 3. 分析结果
    print(f"\n{'='*60}")
    print(f"📊 {symbol} 模型性能详细对比")
    print(f"{'='*60}")
    
    # 创建对比表格
    comparison_data = []
    for model_type, result in results.items():
        if 'error' not in result:
            comparison_data.append({
                'Model': model_type.upper(),
                'Accuracy': f"{result['accuracy']:.4f} ± {result['accuracy_std']:.4f}",
                'Precision': f"{result['precision']:.4f}",
                'Recall': f"{result['recall']:.4f}",
                'F1-Score': f"{result['f1']:.4f}",
                'Accuracy_Raw': result['accuracy']
            })
    
    if comparison_data:
        df_comparison = pd.DataFrame(comparison_data)
        print(df_comparison.to_string(index=False))
        
        # 找出最佳模型
        best_model = df_comparison.loc[df_comparison['Accuracy_Raw'].idxmax()]
        print(f"\n🏆 最佳模型: {best_model['Model']} (准确率: {best_model['Accuracy']})")
        
        # 4. 可视化对比
        create_performance_visualization(results, symbol)
        
        # 5. 详细分析
        analyze_model_characteristics(results)
        
        return results, df_comparison
    else:
        print("❌ 没有成功训练的模型")
        return None, None

def create_performance_visualization(results, symbol):
    """创建性能可视化图表"""
    
    # 准备数据
    models = []
    accuracies = []
    precisions = []
    recalls = []
    f1_scores = []
    
    for model_type, result in results.items():
        if 'error' not in result:
            models.append(model_type.upper())
            accuracies.append(result['accuracy'])
            precisions.append(result['precision'])
            recalls.append(result['recall'])
            f1_scores.append(result['f1'])
    
    if not models:
        return
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'{symbol} 模型性能对比', fontsize=16, fontweight='bold')
    
    # 准确率对比
    bars1 = ax1.bar(models, accuracies, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax1.set_title('准确率对比')
    ax1.set_ylabel('准确率')
    ax1.set_ylim(0, 1)
    for i, v in enumerate(accuracies):
        ax1.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    # 精确率对比
    bars2 = ax2.bar(models, precisions, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax2.set_title('精确率对比')
    ax2.set_ylabel('精确率')
    ax2.set_ylim(0, 1)
    for i, v in enumerate(precisions):
        ax2.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    # 召回率对比
    bars3 = ax3.bar(models, recalls, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax3.set_title('召回率对比')
    ax3.set_ylabel('召回率')
    ax3.set_ylim(0, 1)
    for i, v in enumerate(recalls):
        ax3.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    # F1分数对比
    bars4 = ax4.bar(models, f1_scores, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax4.set_title('F1分数对比')
    ax4.set_ylabel('F1分数')
    ax4.set_ylim(0, 1)
    for i, v in enumerate(f1_scores):
        ax4.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plt.savefig(f'model_comparison_{symbol}_{timestamp}.png', dpi=300, bbox_inches='tight')
    print(f"\n📈 性能对比图表已保存: model_comparison_{symbol}_{timestamp}.png")
    
    # 显示图表（如果在交互环境中）
    try:
        plt.show()
    except:
        pass

def analyze_model_characteristics(results):
    """分析模型特性"""
    
    print(f"\n🔬 模型特性分析:")
    print("-" * 50)
    
    model_characteristics = {
        'RF': {
            'name': '随机森林',
            'pros': ['训练快速', '不易过拟合', '特征重要性清晰', '对异常值鲁棒'],
            'cons': ['可能欠拟合', '内存占用大'],
            'best_for': '稳定性要求高的场景'
        },
        'GB': {
            'name': '梯度提升',
            'pros': ['性能优秀', '特征重要性准确', '处理非线性关系好'],
            'cons': ['训练时间长', '容易过拟合', '参数敏感'],
            'best_for': '追求高准确率的场景'
        },
        'XGB': {
            'name': 'XGBoost',
            'pros': ['性能卓越', '训练效率高', '内置正则化', '处理缺失值'],
            'cons': ['参数复杂', '内存占用较大'],
            'best_for': '竞赛和生产环境的首选'
        },
        'LGB': {
            'name': 'LightGBM',
            'pros': ['训练速度最快', '内存效率高', '准确率高', '支持类别特征'],
            'cons': ['小数据集容易过拟合', '对参数敏感'],
            'best_for': '大数据集和快速迭代'
        }
    }
    
    for model_type, result in results.items():
        if 'error' not in result and model_type.upper() in model_characteristics:
            char = model_characteristics[model_type.upper()]
            print(f"\n{char['name']} ({model_type.upper()}):")
            print(f"  准确率: {result['accuracy']:.4f}")
            print(f"  适用场景: {char['best_for']}")
            print(f"  主要优势: {', '.join(char['pros'][:2])}")

def lstm_vs_traditional_analysis():
    """LSTM vs 传统模型理论分析"""
    
    print(f"\n{'='*60}")
    print("🧠 LSTM vs 传统机器学习模型分析")
    print(f"{'='*60}")
    
    analysis = {
        "数据量需求": {
            "LSTM": "需要大量数据 (>10万样本)",
            "传统ML": "中等数据量即可 (>1万样本)",
            "当前项目": "约2-5千样本 → 传统ML更适合"
        },
        "特征工程": {
            "LSTM": "自动学习特征，但需要原始序列",
            "传统ML": "依赖手工特征，但技术指标很有效",
            "当前项目": "已有丰富技术指标 → 传统ML优势明显"
        },
        "训练稳定性": {
            "LSTM": "训练不稳定，结果难以重现",
            "传统ML": "训练稳定，结果可重现",
            "当前项目": "需要稳定预测 → 传统ML更可靠"
        },
        "解释性": {
            "LSTM": "黑盒模型，难以解释",
            "传统ML": "可以分析特征重要性",
            "当前项目": "需要理解预测依据 → 传统ML更好"
        },
        "计算资源": {
            "LSTM": "需要GPU，训练时间长",
            "传统ML": "CPU即可，训练快速",
            "当前项目": "资源有限 → 传统ML更实用"
        }
    }
    
    for aspect, details in analysis.items():
        print(f"\n📊 {aspect}:")
        for model, desc in details.items():
            print(f"  {model:12}: {desc}")

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    days = int(sys.argv[2]) if len(sys.argv) > 2 else 180
    
    # 执行全面对比
    results, comparison_df = comprehensive_model_comparison(symbol, days)
    
    # 理论分析
    lstm_vs_traditional_analysis()
    
    print(f"\n{'='*60}")
    print("📝 总结建议")
    print(f"{'='*60}")
    print("1. 对于加密货币预测，传统机器学习模型通常表现更好")
    print("2. XGBoost和LightGBM是最佳选择，准确率通常在70-80%")
    print("3. LSTM在当前数据规模下容易过拟合，准确率可能更低")
    print("4. 建议优先使用XGBoost，然后尝试LightGBM")
    print("5. 如果需要LSTM，建议先积累更多历史数据")
