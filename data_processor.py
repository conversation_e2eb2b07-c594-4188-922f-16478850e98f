import pandas as pd
import numpy as np
from binance.client import Client
# from binance.helpers import interval_to_ms # 移除错误的导入
import ta
import logging
from datetime import datetime, timedelta # 确保 timedelta 已导入
import os
from typing import Optional, Tuple, List, Dict, Any
from dotenv import load_dotenv
import time
import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局的黄金特征列表 (从 golden_feature_names.txt 获取)
# 注意：这个列表应该包含所有234个特征名
GOLDEN_FEATURE_NAMES = [
    'open_15m', 'high_15m', 'low_15m', 'close_15m', 'volume_15m', 
    'quote_asset_volume_15m', 'number_of_trades_15m', 
    'taker_buy_base_asset_volume_15m', 'taker_buy_quote_asset_volume_15m', 
    'rsi_15m', 'macd_15m', 'macd_signal_15m', 'macd_diff_15m', 
    'stoch_k_15m', 'stoch_d_15m', 'ao_15m', 'rsi_6_15m', 
    'stoch_slow_k_20_5_15m', 'stoch_slow_d_20_5_15m', 
    'bollinger_hband_15m', 'bollinger_lband_15m', 'bollinger_mavg_15m', 'bollinger_width_15m', 
    'atr_15m', 'atr_20_15m', 'cci_15m', 'adx_15m', 'trix_15m', 
    'sma_7_15m', 'ema_7_15m', 'sma_10_15m', 'ema_10_15m', 
    'sma_20_15m', 'ema_20_15m', 'sma_30_15m', 'ema_30_15m', 
    'sma_50_15m', 'ema_50_15m', 'sma_60_15m', 'ema_60_15m', 
    'sma_200_15m', 'ema_200_15m', 'sma_20_50_diff_15m', 'sma_20_gt_sma_50_15m', 
    'volume_adi_15m', 'volume_obv_15m', 'volume_cmf_15m', 'volume_fi_15m', 
    'volume_em_15m', 'volume_vwap_15m', 'price_change_15m', 'returns_15m', 
    'log_returns_15m', 'macd_gt_zero_15m', 'macd_signal_gt_zero_15m', 
    'close_relative_high_10_15m', 'close_relative_low_10_15m', 
    'rsi_relative_high_10_15m', 'rsi_relative_low_10_15m', 
    'close_relative_high_20_15m', 'close_relative_low_20_15m', 
    'rsi_relative_high_20_15m', 'rsi_relative_low_20_15m', 
    'close_lag_1_15m', 'volume_lag_1_15m', 'returns_lag_1_15m', 
    'close_lag_2_15m', 'volume_lag_2_15m', 'returns_lag_2_15m', 
    'close_lag_3_15m', 'volume_lag_3_15m', 'returns_lag_3_15m', 
    'close_lag_5_15m', 'volume_lag_5_15m', 'returns_lag_5_15m', 
    'close_lag_10_15m', 'volume_lag_10_15m', 'returns_lag_10_15m', 
    'open_1h', 'high_1h', 'low_1h', 'close_1h', 'volume_1h', 
    'quote_asset_volume_1h', 'number_of_trades_1h', 
    'taker_buy_base_asset_volume_1h', 'taker_buy_quote_asset_volume_1h', 
    'rsi_1h', 'macd_1h', 'macd_signal_1h', 'macd_diff_1h', 
    'stoch_k_1h', 'stoch_d_1h', 'ao_1h', 'rsi_6_1h', 
    'stoch_slow_k_20_5_1h', 'stoch_slow_d_20_5_1h', 
    'bollinger_hband_1h', 'bollinger_lband_1h', 'bollinger_mavg_1h', 'bollinger_width_1h', 
    'atr_1h', 'atr_20_1h', 'cci_1h', 'adx_1h', 'trix_1h', 
    'sma_7_1h', 'ema_7_1h', 'sma_10_1h', 'ema_10_1h', 
    'sma_20_1h', 'ema_20_1h', 'sma_30_1h', 'ema_30_1h', 
    'sma_50_1h', 'ema_50_1h', 'sma_60_1h', 'ema_60_1h', 
    'sma_200_1h', 'ema_200_1h', 'sma_20_50_diff_1h', 'sma_20_gt_sma_50_1h', 
    'volume_adi_1h', 'volume_obv_1h', 'volume_cmf_1h', 'volume_fi_1h', 
    'volume_em_1h', 'volume_vwap_1h', 'price_change_1h', 'returns_1h', 
    'log_returns_1h', 'macd_gt_zero_1h', 'macd_signal_gt_zero_1h', 
    'close_relative_high_10_1h', 'close_relative_low_10_1h', 
    'rsi_relative_high_10_1h', 'rsi_relative_low_10_1h', 
    'close_relative_high_20_1h', 'close_relative_low_20_1h', 
    'rsi_relative_high_20_1h', 'rsi_relative_low_20_1h', 
    'close_lag_1_1h', 'volume_lag_1_1h', 'returns_lag_1_1h', 
    'close_lag_2_1h', 'volume_lag_2_1h', 'returns_lag_2_1h', 
    'close_lag_3_1h', 'volume_lag_3_1h', 'returns_lag_3_1h', 
    'close_lag_5_1h', 'volume_lag_5_1h', 'returns_lag_5_1h', 
    'close_lag_10_1h', 'volume_lag_10_1h', 'returns_lag_10_1h', 
    'open_4h', 'high_4h', 'low_4h', 'close_4h', 'volume_4h', 
    'quote_asset_volume_4h', 'number_of_trades_4h', 
    'taker_buy_base_asset_volume_4h', 'taker_buy_quote_asset_volume_4h', 
    'rsi_4h', 'macd_4h', 'macd_signal_4h', 'macd_diff_4h', 
    'stoch_k_4h', 'stoch_d_4h', 'ao_4h', 'rsi_6_4h', 
    'stoch_slow_k_20_5_4h', 'stoch_slow_d_20_5_4h', 
    'bollinger_hband_4h', 'bollinger_lband_4h', 'bollinger_mavg_4h', 'bollinger_width_4h', 
    'atr_4h', 'atr_20_4h', 'cci_4h', 'adx_4h', 'trix_4h', 
    'sma_7_4h', 'ema_7_4h', 'sma_10_4h', 'ema_10_4h', 
    'sma_20_4h', 'ema_20_4h', 'sma_30_4h', 'ema_30_4h', 
    'sma_50_4h', 'ema_50_4h', 'sma_60_4h', 'ema_60_4h', 
    'sma_200_4h', 'ema_200_4h', 'sma_20_50_diff_4h', 'sma_20_gt_sma_50_4h', 
    'volume_adi_4h', 'volume_obv_4h', 'volume_cmf_4h', 'volume_fi_4h', 
    'volume_em_4h', 'volume_vwap_4h', 'price_change_4h', 'returns_4h', 
    'log_returns_4h', 'macd_gt_zero_4h', 'macd_signal_gt_zero_4h', 
    'close_relative_high_10_4h', 'close_relative_low_10_4h', 
    'rsi_relative_high_10_4h', 'rsi_relative_low_10_4h', 
    'close_relative_high_20_4h', 'close_relative_low_20_4h', 
    'rsi_relative_high_20_4h', 'rsi_relative_low_20_4h', 
    'close_lag_1_4h', 'volume_lag_1_4h', 'returns_lag_1_4h', 
    'close_lag_2_4h', 'volume_lag_2_4h', 'returns_lag_2_4h', 
    'close_lag_3_4h', 'volume_lag_3_4h', 'returns_lag_3_4h', 
    'close_lag_5_4h', 'volume_lag_5_4h', 'returns_lag_5_4h', 
    'close_lag_10_4h', 'volume_lag_10_4h', 'returns_lag_10_4h'
]

# Define the 78 unique feature roots
FEATURE_ROOTS = [
    'open', 'high', 'low', 'close', 'volume', 'quote_asset_volume', 'number_of_trades',
    'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'rsi', 'macd', 
    'macd_signal', 'macd_diff', 'stoch_k', 'stoch_d', 'ao', 'rsi_6', 
    'stoch_slow_k_20_5', 'stoch_slow_d_20_5', 'bollinger_hband', 'bollinger_lband',
    'bollinger_mavg', 'bollinger_width', 'atr', 'atr_20', 'cci', 'adx', 'trix', 
    'sma_7', 'ema_7', 'sma_10', 'ema_10', 'sma_20', 'ema_20', 'sma_30', 'ema_30',
    'sma_50', 'ema_50', 'sma_60', 'ema_60', 'sma_200', 'ema_200', 
    'sma_20_50_diff', 'sma_20_gt_sma_50', 'volume_adi', 'volume_obv', 'volume_cmf', 
    'volume_fi', 'volume_em', 'volume_vwap', 'price_change', 'returns', 'log_returns',
    'macd_gt_zero', 'macd_signal_gt_zero', 'close_relative_high_10', 
    'close_relative_low_10', 'rsi_relative_high_10', 'rsi_relative_low_10',
    'close_relative_high_20', 'close_relative_low_20', 'rsi_relative_high_20',
    'rsi_relative_low_20', 'close_lag_1', 'volume_lag_1', 'returns_lag_1',
    'close_lag_2', 'volume_lag_2', 'returns_lag_2', 'close_lag_3', 'volume_lag_3',
    'returns_lag_3', 'close_lag_5', 'volume_lag_5', 'returns_lag_5', 'close_lag_10',
    'volume_lag_10', 'returns_lag_10'
]
if len(FEATURE_ROOTS) != 78:
    logger.critical(f"代码错误: FEATURE_ROOTS 列表应包含78个元素, 当前为 {len(FEATURE_ROOTS)}")

def _interval_to_milliseconds(interval: str) -> int:
    """Converts K-line interval string to milliseconds."""
    seconds_per_unit = {
        "m": 60,
        "h": 60 * 60,
        "d": 24 * 60 * 60,
        "w": 7 * 24 * 60 * 60
    }
    try:
        unit = interval[-1]
        if unit.isdigit(): # e.g. '1', '5', '15' for minutes if no char, assume minutes
            # This case is less common with Binance strings like '1m', '15m'
            # but if only a number is passed, let's assume it's minutes by default or handle error.
            # For now, let's stick to expecting a unit character.
            if interval.isdigit(): # '1' could mean 1 minute
                 return int(interval) * seconds_per_unit["m"] * 1000
            raise ValueError(f"无法解析的间隔单位 (末尾应为 m, h, d, w): {interval}")
            
        if unit not in seconds_per_unit:
            raise ValueError(f"无效的间隔单位: {unit} in {interval}")
            
        value = int(interval[:-1])
        return value * seconds_per_unit[unit] * 1000
    except ValueError as e:
        logger.error(f"转换K线间隔 '{interval}' 为毫秒时出错: {e}")
        # Fallback or re-raise. For Binance, common intervals are fixed, so error is unlikely if used correctly.
        # Fallback to a default (e.g., 1 minute) or raise error might be safer.
        # Let's raise to make it explicit if an unsupported interval is passed.
        raise ValueError(f"不支持的K线间隔格式: {interval}. 期望格式如 '1m', '15m', '1h', '4h', '1d'.") from e
    except Exception as e:
        logger.error(f"转换K线间隔 '{interval}' 为毫秒时发生意外错误: {e}")
        raise

class DataProcessor:
    def __init__(self, 
                 intervals: List[str] = [Client.KLINE_INTERVAL_15MINUTE, Client.KLINE_INTERVAL_1HOUR, Client.KLINE_INTERVAL_4HOUR],
                 future_periods: int = 2, 
                 atr_multiplier: float = 1.0): # ATR乘数因子，用于定义涨跌阈值
        """
        Initialize data processor with proper proxy handling
        """
        load_dotenv()
        
        self.api_key = os.getenv('BINANCE_API_KEY')
        self.api_secret = os.getenv('BINANCE_API_SECRET')

        # 新增：加载代理配置
        self.proxy_http = os.getenv('PROXY_HTTP')
        self.proxy_https = os.getenv('PROXY_HTTPS')
        
        # Proper proxy configuration
        requests_params = {'timeout': 30}
        if self.proxy_http and self.proxy_https:
            requests_params['proxies'] = {
                'http': self.proxy_http,
                'https': self.proxy_https
            }
            logger.info(f"使用代理: HTTP -> {self.proxy_http}, HTTPS -> {self.proxy_https}")
        elif self.proxy_http or self.proxy_https:
            logger.warning("检测到部分代理配置 (PROXY_HTTP 或 PROXY_HTTPS)，但两者都需要才能正确设置代理。将不使用代理。")

        if not self.api_key or not self.api_secret:
            logger.warning("未找到Binance API密钥，将使用公共API (可能受代理影响，如果已配置)")
            self.client = Client(requests_params=requests_params)
        else:
            try:
                self.client = Client(
                    self.api_key, 
                    self.api_secret, 
                    requests_params=requests_params
                )
                logger.info("Successfully connected to Binance API (可能通过代理).")
            except Exception as e:
                logger.error(f"连接到Binance API时发生错误: {str(e)}")
                raise
            
        self.intervals = intervals # 存储需要处理的时间周期列表
        self.target_interval = intervals[0] if intervals else Client.KLINE_INTERVAL_15MINUTE # 默认第一个为目标周期
        self.future_periods = future_periods
        self.atr_multiplier = atr_multiplier
        
    def _fetch_historical_data_in_chunks(self,
                                   symbol: str,
                                   interval: str,
                                   start_str: str,
                                   end_str: Optional[str] = None,
                                   chunk_size: str = '1 year') -> pd.DataFrame: # chunk_size is not directly used in the modified logic
        """
        Fetches historical klines in chunks from start_str to end_str.
        Relies on the API to return data from the earliest available if start_str is too early.
        """
        all_klines_list: List[List[Any]] = []
        
        current_request_start_dt = pd.to_datetime(start_str)
        # Ensure end_date is timezone-aware if current_request_start_dt is, or make them both naive
        # For simplicity, assuming naive or consistent timezone handling from pd.to_datetime
        
        # 使用新的辅助函数
        final_end_dt = pd.to_datetime(end_str) if end_str else pd.Timestamp.now().tz_localize(None) - pd.Timedelta(milliseconds=_interval_to_milliseconds(interval))

        logger.info(f"开始分段获取 {symbol} ({interval}) 从 {current_request_start_dt.strftime('%Y-%m-%d %H:%M:%S')} 到 {final_end_dt.strftime('%Y-%m-%d %H:%M:%S')} 的历史数据...")
        
        MAX_RETRIES_PER_CHUNK = 3
        RETRY_DELAY_SECONDS = 5

        while current_request_start_dt < final_end_dt:
            retries = 0
            # klines_chunk_for_period = [] # 这行似乎不需要了，因为在try块内重新赋值
            current_chunk_end_dt = min(current_request_start_dt + pd.Timedelta(days=60), final_end_dt) # 假设60天为一个合理的请求大小
            start_ms = int(current_request_start_dt.timestamp() * 1000)
            end_ms = int(current_chunk_end_dt.timestamp() * 1000)
            logger.info(f"  -> 正在获取分段: {symbol} ({interval}) 从 {current_request_start_dt.strftime('%Y-%m-%d %H:%M:%S')} 到 {current_chunk_end_dt.strftime('%Y-%m-%d %H:%M:%S')}...")
            
            while retries < MAX_RETRIES_PER_CHUNK:
                klines_chunk_for_period = [] # 在每次重试前重置
                try:
                    if symbol.endswith('USDT'): # Assuming futures market
                        klines_chunk_for_period = self.client.futures_historical_klines(
                            symbol=symbol, 
                            interval=interval, 
                            start_str=start_ms,
                            end_str=end_ms,
                            limit=1500
                        )
                    else: # Spot market
                        klines_chunk_for_period = self.client.get_historical_klines(
                            symbol=symbol, 
                            interval=interval, 
                            start_str=start_ms,
                            end_str=end_ms,
                            limit=1000
                        )
                    
                    # Handle data fetching result within the try block
                    if klines_chunk_for_period:
                        logger.info(f"    成功获取 {len(klines_chunk_for_period)} 条数据 for {symbol} ({interval}). 最后K线时间: {pd.to_datetime(klines_chunk_for_period[-1][0], unit='ms')}")
                        all_klines_list.extend(klines_chunk_for_period)
                        # Update start time for the next chunk based on the last kline received
                        # 确保使用 _interval_to_milliseconds 获得正确的毫秒数
                        current_request_start_dt = pd.to_datetime(klines_chunk_for_period[-1][0], unit='ms') + pd.Timedelta(milliseconds=_interval_to_milliseconds(interval))
                    else: # Correctly placed else for "if klines_chunk_for_period"
                        logger.info(f"    在 {current_request_start_dt.strftime('%Y-%m-%d %H:%M:%S')} 到 {current_chunk_end_dt.strftime('%Y-%m-%d %H:%M:%S')} 期间未获取到 {symbol} ({interval}) 的数据。")
                        # If no data, advance current_request_start_dt to current_chunk_end_dt to avoid getting stuck in the outer loop
                        current_request_start_dt = current_chunk_end_dt 
                    
                    time.sleep(0.3) # Small delay to be respectful to the API, still within try
                    break # Success or no data for this period, exit retry loop (inner while), still within try

                except requests.exceptions.Timeout as e:
                    retries += 1
                    logger.warning(f"    获取 {symbol} ({interval}) 数据超时 (尝试 {retries}/{MAX_RETRIES_PER_CHUNK}): {e}. {RETRY_DELAY_SECONDS}秒后重试...")
                    time.sleep(RETRY_DELAY_SECONDS)
                except requests.exceptions.RequestException as e: # Broader network errors
                    retries += 1
                    logger.warning(f"    获取 {symbol} ({interval}) 数据时网络错误 (尝试 {retries}/{MAX_RETRIES_PER_CHUNK}): {e}. {RETRY_DELAY_SECONDS}秒后重试...")
                    time.sleep(RETRY_DELAY_SECONDS)
                except Exception as e: # Other Binance API errors or unexpected errors
                    logger.error(f"    获取 {symbol} ({interval}) 数据时发生错误: {e}", exc_info=False) # exc_info=True might be better for debugging
                    # In case of other errors, advance to next chunk to prevent infinite loop on a problematic chunk
                    current_request_start_dt = current_chunk_end_dt 
                    break # Exit retry loop (inner while) to move to the next chunk in the outer while

            # This 'if' block is outside the inner 'while retries < MAX_RETRIES_PER_CHUNK:' loop
            # It checks if all retries for the current chunk were exhausted
            if retries >= MAX_RETRIES_PER_CHUNK:
                logger.error(f"    获取 {symbol} ({interval}) 在 {current_request_start_dt.strftime('%Y-%m-%d %H:%M:%S')} 到 {current_chunk_end_dt.strftime('%Y-%m-%d %H:%M:%S')} 的数据失败，已达到最大重试次数。")
                # Advance to the next chunk period to avoid getting stuck
                current_request_start_dt = current_chunk_end_dt
            
            # Small delay before fetching the next major chunk (if any)
            # time.sleep(0.1) # Optional: if the outer loop fetches many chunks rapidly

        logger.info(f"分段数据获取完成 for {symbol} ({interval})。总共获取 {len(all_klines_list)} 条原始K线数据。")

        if not all_klines_list:
            logger.warning(f"未获取到任何历史K线数据 for {symbol} ({interval})。")
            return pd.DataFrame()
            
        df = pd.DataFrame(all_klines_list, columns=[\
            'open_time', 'open', 'high', 'low', 'close', 'volume',\
            'close_time', 'quote_asset_volume', 'number_of_trades',\
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'\
        ])
        
        # 转换时间戳和数据类型
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 
                         'quote_asset_volume', 'taker_buy_base_asset_volume', 
                         'taker_buy_quote_asset_volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['number_of_trades'] = pd.to_numeric(df['number_of_trades'], errors='coerce').astype('Int64') # 使用 Int64 支持 NaN

        df.set_index('open_time', inplace=True)
        df.drop(columns=['close_time', 'ignore'], inplace=True, errors='ignore')
        
        # 去重并排序 (如果因为current_start的更新方式导致了重叠)
        df = df[~df.index.duplicated(keep='first')]
        df.sort_index(inplace=True)
        
        logger.info(f"处理分段数据后，总共获得 {len(df)} 条不重复且排序后的历史数据 for {symbol} ({interval})。")
        return df

    def add_technical_indicators(self, df: pd.DataFrame, interval: str) -> pd.DataFrame:
        logger.info(f"正在为 {interval} 数据显式计算和添加所有 {len(FEATURE_ROOTS)} 个黄金技术指标词根...")
        df_input = df.copy()

        rename_map = {'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}
        df_input.rename(columns={k: v for k, v in rename_map.items() if k in df_input.columns}, inplace=True)

        # Ensure essential columns are present
        essential_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in essential_cols:
            if col not in df_input.columns:
                logger.error(f"输入到 add_technical_indicators 的DataFrame (周期 {interval}) 缺少核心列: {col}。将返回填充了NaN的特征集。")
                # Create an empty DataFrame with suffixed root columns
                empty_features_df = pd.DataFrame(index=df_input.index)
                for root in FEATURE_ROOTS:
                    empty_features_df[f"{root}_{interval}"] = np.nan
                return empty_features_df

        processed_features_df = pd.DataFrame(index=df_input.index)

        # Helper for safe division in relative strength/position indicators
        def safe_division(numerator, denominator, fill_value=0.5):
            return np.where(denominator != 0, numerator / denominator, fill_value)

        # --- 1. Base OHLCV and other direct data (9 features) ---
        direct_copy_roots = ['open', 'high', 'low', 'close', 'volume', 
                             'quote_asset_volume', 'number_of_trades',
                             'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume']
        for root in direct_copy_roots:
            if root in FEATURE_ROOTS:
                if root in df_input.columns:
                    processed_features_df[root] = df_input[root]
                else:
                    logger.warning(f"基础列 {root} 在 df_input (周期 {interval}) 中未找到，将填充NaN。")
                    processed_features_df[root] = np.nan
        
        # --- Pre-calculate 'returns' for lagged features, handle potential NaNs from pct_change early ---
        # fillna(0) for returns so that lags don't propagate initial NaNs too much if data is short.
        # Or ffill/bfill later. Let's compute returns and handle NaNs at the end of all calculations.
        temp_returns = df_input['close'].pct_change()


        # --- Technical Indicators Calculation (grouped by type) ---
        # Wrap TA calculations in try-except blocks to catch errors from insufficient data
        try:
            # Momentum Indicators
            if 'rsi' in FEATURE_ROOTS: processed_features_df['rsi'] = ta.momentum.RSIIndicator(close=df_input['close'], window=14, fillna=False).rsi()
            if 'rsi_6' in FEATURE_ROOTS: processed_features_df['rsi_6'] = ta.momentum.RSIIndicator(close=df_input['close'], window=6, fillna=False).rsi()
            
            if 'macd' in FEATURE_ROOTS or 'macd_signal' in FEATURE_ROOTS or 'macd_diff' in FEATURE_ROOTS or 'macd_gt_zero' in FEATURE_ROOTS or 'macd_signal_gt_zero' in FEATURE_ROOTS:
                # Corrected: MACD is in ta.trend
                macd_indicator = ta.trend.MACD(close=df_input['close'], fillna=False)
                if 'macd' in FEATURE_ROOTS: processed_features_df['macd'] = macd_indicator.macd()
                if 'macd_signal' in FEATURE_ROOTS: processed_features_df['macd_signal'] = macd_indicator.macd_signal()
                if 'macd_diff' in FEATURE_ROOTS: processed_features_df['macd_diff'] = macd_indicator.macd_diff()

            if 'stoch_k' in FEATURE_ROOTS or 'stoch_d' in FEATURE_ROOTS: # Default: window=14, smooth_window=3
                stoch_default = ta.momentum.StochasticOscillator(high=df_input['high'], low=df_input['low'], close=df_input['close'], window=14, smooth_window=3, fillna=False)
                if 'stoch_k' in FEATURE_ROOTS: processed_features_df['stoch_k'] = stoch_default.stoch()
                if 'stoch_d' in FEATURE_ROOTS: processed_features_df['stoch_d'] = stoch_default.stoch_signal()

            if 'stoch_slow_k_20_5' in FEATURE_ROOTS or 'stoch_slow_d_20_5' in FEATURE_ROOTS:
                stoch_slow = ta.momentum.StochasticOscillator(high=df_input['high'], low=df_input['low'], close=df_input['close'], window=20, smooth_window=5, fillna=False)
                if 'stoch_slow_k_20_5' in FEATURE_ROOTS: processed_features_df['stoch_slow_k_20_5'] = stoch_slow.stoch()
                if 'stoch_slow_d_20_5' in FEATURE_ROOTS: processed_features_df['stoch_slow_d_20_5'] = stoch_slow.stoch_signal()
            
            if 'ao' in FEATURE_ROOTS: processed_features_df['ao'] = ta.momentum.AwesomeOscillatorIndicator(high=df_input['high'], low=df_input['low'], fillna=False).awesome_oscillator()

            # Volatility Indicators
            if 'bollinger_hband' in FEATURE_ROOTS or 'bollinger_lband' in FEATURE_ROOTS or 'bollinger_mavg' in FEATURE_ROOTS or 'bollinger_width' in FEATURE_ROOTS:
                bb_indicator = ta.volatility.BollingerBands(close=df_input['close'], window=20, fillna=False) # Default window 20
                if 'bollinger_hband' in FEATURE_ROOTS: processed_features_df['bollinger_hband'] = bb_indicator.bollinger_hband()
                if 'bollinger_lband' in FEATURE_ROOTS: processed_features_df['bollinger_lband'] = bb_indicator.bollinger_lband()
                if 'bollinger_mavg' in FEATURE_ROOTS: processed_features_df['bollinger_mavg'] = bb_indicator.bollinger_mavg()
                if 'bollinger_width' in FEATURE_ROOTS: processed_features_df['bollinger_width'] = bb_indicator.bollinger_wband()

            if 'atr' in FEATURE_ROOTS: processed_features_df['atr'] = ta.volatility.AverageTrueRange(high=df_input['high'], low=df_input['low'], close=df_input['close'], window=14, fillna=False).average_true_range()
            if 'atr_20' in FEATURE_ROOTS: processed_features_df['atr_20'] = ta.volatility.AverageTrueRange(high=df_input['high'], low=df_input['low'], close=df_input['close'], window=20, fillna=False).average_true_range()

            # Trend Indicators
            if 'adx' in FEATURE_ROOTS: processed_features_df['adx'] = ta.trend.ADXIndicator(high=df_input['high'], low=df_input['low'], close=df_input['close'], window=14, fillna=False).adx()
            if 'cci' in FEATURE_ROOTS: processed_features_df['cci'] = ta.trend.CCIIndicator(high=df_input['high'], low=df_input['low'], close=df_input['close'], window=20, fillna=False).cci()
            if 'trix' in FEATURE_ROOTS: processed_features_df['trix'] = ta.trend.TRIXIndicator(close=df_input['close'], window=15, fillna=False).trix() # Default window 15

            sma_windows = [7, 10, 20, 30, 50, 60, 200]
            ema_windows = [7, 10, 20, 30, 50, 60, 200]
            for w in sma_windows:
                root_name = f'sma_{w}'
                if root_name in FEATURE_ROOTS: processed_features_df[root_name] = ta.trend.SMAIndicator(close=df_input['close'], window=w, fillna=False).sma_indicator()
            for w in ema_windows:
                root_name = f'ema_{w}'
                if root_name in FEATURE_ROOTS: processed_features_df[root_name] = ta.trend.EMAIndicator(close=df_input['close'], window=w, fillna=False).ema_indicator()

            # Volume Indicators
            if 'volume_adi' in FEATURE_ROOTS: processed_features_df['volume_adi'] = ta.volume.AccDistIndexIndicator(high=df_input['high'], low=df_input['low'], close=df_input['close'], volume=df_input['volume'], fillna=False).acc_dist_index()
            if 'volume_obv' in FEATURE_ROOTS: processed_features_df['volume_obv'] = ta.volume.OnBalanceVolumeIndicator(close=df_input['close'], volume=df_input['volume'], fillna=False).on_balance_volume()
            if 'volume_cmf' in FEATURE_ROOTS: processed_features_df['volume_cmf'] = ta.volume.ChaikinMoneyFlowIndicator(high=df_input['high'], low=df_input['low'], close=df_input['close'], volume=df_input['volume'], window=20, fillna=False).chaikin_money_flow() # Default window 20
            if 'volume_fi' in FEATURE_ROOTS: processed_features_df['volume_fi'] = ta.volume.ForceIndexIndicator(close=df_input['close'], volume=df_input['volume'], window=13, fillna=False).force_index() # Default window 13
            if 'volume_em' in FEATURE_ROOTS: processed_features_df['volume_em'] = ta.volume.EaseOfMovementIndicator(high=df_input['high'], low=df_input['low'], volume=df_input['volume'], window=14, fillna=False).ease_of_movement() 
            if 'volume_vwap' in FEATURE_ROOTS: processed_features_df['volume_vwap'] = ta.volume.VolumeWeightedAveragePrice(high=df_input['high'], low=df_input['low'], close=df_input['close'], volume=df_input['volume'], window=14, fillna=False).volume_weighted_average_price() # Default window 14
        
        except Exception as e:
            logger.error(f"在为周期 {interval} 计算核心TA指标时发生错误: {e}. 部分指标可能为NaN.", exc_info=True)
            # Allow to continue, NaNs will be handled later or missing columns added

        # Derived Price/Return/Indicator Features
        if 'price_change' in FEATURE_ROOTS: processed_features_df['price_change'] = df_input['close'].diff()
        if 'returns' in FEATURE_ROOTS: processed_features_df['returns'] = temp_returns # Use pre-calculated returns
        if 'log_returns' in FEATURE_ROOTS: processed_features_df['log_returns'] = np.log(df_input['close'] / df_input['close'].shift(1))
        
        if 'sma_20' in processed_features_df and 'sma_50' in processed_features_df:
            if 'sma_20_50_diff' in FEATURE_ROOTS: processed_features_df['sma_20_50_diff'] = processed_features_df['sma_20'] - processed_features_df['sma_50']
            if 'sma_20_gt_sma_50' in FEATURE_ROOTS: processed_features_df['sma_20_gt_sma_50'] = (processed_features_df['sma_20'] > processed_features_df['sma_50']).astype(int)
        
        if 'macd' in processed_features_df:
            if 'macd_gt_zero' in FEATURE_ROOTS: processed_features_df['macd_gt_zero'] = (processed_features_df['macd'] > 0).astype(int)
        if 'macd_signal' in processed_features_df:
            if 'macd_signal_gt_zero' in FEATURE_ROOTS: processed_features_df['macd_signal_gt_zero'] = (processed_features_df['macd_signal'] > 0).astype(int)

        # Relative High/Low Features
        for W in [10, 20]:
            min_low_W = df_input['low'].rolling(window=W, min_periods=1).min()
            max_high_W = df_input['high'].rolling(window=W, min_periods=1).max()
            range_W = max_high_W - min_low_W
            
            if f'close_relative_high_{W}' in FEATURE_ROOTS:
                processed_features_df[f'close_relative_high_{W}'] = safe_division(df_input['close'] - min_low_W, range_W)
            if f'close_relative_low_{W}' in FEATURE_ROOTS: # This is 1 - relative_high if definition is symmetric
                processed_features_df[f'close_relative_low_{W}'] = safe_division(max_high_W - df_input['close'], range_W)

            if f'rsi_relative_high_{W}' in FEATURE_ROOTS or f'rsi_relative_low_{W}' in FEATURE_ROOTS:
                # Use default RSI (14) if 'rsi' exists, otherwise calculate it
                rsi_series = processed_features_df.get('rsi', ta.momentum.RSIIndicator(close=df_input['close'], window=14, fillna=False).rsi())
                if not rsi_series.empty:
                    min_rsi_W = rsi_series.rolling(window=W, min_periods=1).min()
                    max_rsi_W = rsi_series.rolling(window=W, min_periods=1).max()
                    range_rsi_W = max_rsi_W - min_rsi_W
                    if f'rsi_relative_high_{W}' in FEATURE_ROOTS:
                        processed_features_df[f'rsi_relative_high_{W}'] = safe_division(rsi_series - min_rsi_W, range_rsi_W)
                    if f'rsi_relative_low_{W}' in FEATURE_ROOTS:
                        processed_features_df[f'rsi_relative_low_{W}'] = safe_division(max_rsi_W - rsi_series, range_rsi_W)
                else: # Should not happen if RSI is calculated above
                    if f'rsi_relative_high_{W}' in FEATURE_ROOTS: processed_features_df[f'rsi_relative_high_{W}'] = np.nan
                    if f'rsi_relative_low_{W}' in FEATURE_ROOTS: processed_features_df[f'rsi_relative_low_{W}'] = np.nan
        
        # Lagged Features
        lag_periods = [1, 2, 3, 5, 10]
        for L in lag_periods:
            if f'close_lag_{L}' in FEATURE_ROOTS: processed_features_df[f'close_lag_{L}'] = df_input['close'].shift(L)
            if f'volume_lag_{L}' in FEATURE_ROOTS: processed_features_df[f'volume_lag_{L}'] = df_input['volume'].shift(L)
            if f'returns_lag_{L}' in FEATURE_ROOTS: processed_features_df[f'returns_lag_{L}'] = temp_returns.shift(L)

        # --- Final Steps ---
        # Ensure all FEATURE_ROOTS are present, adding NaNs if any were missed (e.g., due to error or unmet condition)
        final_col_order_with_roots = []
        for root in FEATURE_ROOTS:
            if root not in processed_features_df.columns:
                logger.warning(f"特征词根 '{root}' 在显式计算后仍缺失 for interval {interval}。将添加为NaN列。")
                processed_features_df[root] = np.nan
            final_col_order_with_roots.append(root)
        
        # Select only the defined FEATURE_ROOTS in the correct order
        processed_features_df = processed_features_df[final_col_order_with_roots]

        # Add interval suffix to all columns
        processed_features_df.columns = [f"{col_name}_{interval}" for col_name in processed_features_df.columns]
        
        # Fill NaN values robustly
        processed_features_df.ffill(inplace=True)
        processed_features_df.bfill(inplace=True)
        
        # Final check for any remaining NaNs, especially if data was too short for all indicators
        if processed_features_df.isnull().any().any():
             logger.warning(f"为周期 {interval} 计算技术指标后，即使经过ffill/bfill，仍存在NaN值。可能数据量不足。首行NaN情况: {processed_features_df.iloc[0].isnull().sum()} / {len(processed_features_df.columns)}")


        logger.info(f"为 {interval} 数据显式计算技术指标完成。生成特征数量: {len(processed_features_df.columns)}")
        if len(processed_features_df.columns) != len(FEATURE_ROOTS):
             logger.error(f"代码逻辑错误: 为周期 {interval} 生成的特征列数 ({len(processed_features_df.columns)}) 与期望的词根数 ({len(FEATURE_ROOTS)}) 不匹配。")

        return processed_features_df
            
    def add_target_variable(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        添加目标变量 (只基于目标时间周期的数据)
        
        参数:
            df (pd.DataFrame): 包含目标时间周期特征的DataFrame
            
        返回:
            pd.DataFrame: 添加了目标变量的DataFrame
        """
        try:
            # 这个方法只应用于目标时间周期的数据
            logger.info(f"正在为目标时间周期 ({self.target_interval}) 添加目标变量...")
            df = df.copy()
            
            # 使用带目标时间间隔后缀的列名
            close_col_name = f'close_{self.target_interval}'
            atr_col_name = f'atr_20_{self.target_interval}'

            # 确保所需的列存在
            if close_col_name not in df.columns:
                 logger.error(f"DataFrame中缺少 '' 列，无法添加目标变量。")
                 raise ValueError(f"DataFrame中缺少 {close_col_name} 列，无法定义目标变量")

            if atr_col_name not in df.columns:
                # Fallback to generic 'atr' if interval not in col name (应该不会发生，因为add_technical_indicators会加后缀)
                logger.error(f"DataFrame中缺少 '{atr_col_name}' 列，无法使用基于ATR的动态阈值。请确保先计算ATR并正确命名列。")
                raise ValueError(f"DataFrame中缺少 {atr_col_name} 列，无法定义目标变量")

            # 计算未来价格
            df['future_close'] = df[close_col_name].shift(-self.future_periods)
            
            # 计算价格变化绝对值 和 ATR阈值
            # df['price_change_pct'] = (df['future_close'] - df['close']) / df['close'] # 旧的百分比变化
            
            # 定义动态阈值
            atr_threshold = df[atr_col_name] * self.atr_multiplier
            price_diff = df['future_close'] - df[close_col_name]
            
            # 定义目标变量
            df['target'] = 0  # 默认为横盘
            df.loc[price_diff > atr_threshold, 'target'] = 1  # 上涨
            df.loc[price_diff < -atr_threshold, 'target'] = -1 # 下跌
            
            # 删除中间计算列和包含NaN的行
            df.drop(columns=['future_close'], inplace=True)
            # df.dropna(subset=['target', atr_col_name], inplace=True) # 确保target和atr列没有NaN
            # 目标变量只在目标周期的DataFrame上计算和定义，最后再合并
            
            # 输出目标变量分布
            distribution = df['target'].value_counts()
            total = len(df)
            logger.info("目标变量分布:")
            for label, count in distribution.items():
                percentage = count / total * 100
                logger.info(f"类别 {label}: {count} 样本 ({percentage:.2f}%)")
            
            return df
            
        except Exception as e:
            logger.error(f"添加目标变量时发生错误: {str(e)}")
            raise
            
    def prepare_data(self, 
                    symbol: str = 'BTCUSDT',
                    start_str: str = '1 Jan, 2020',
                    end_str: Optional[str] = None,
                    mode: str = 'train') -> Tuple[pd.DataFrame, Optional[pd.Series], Optional[pd.DataFrame]]:
        """
        准备完整的训练数据 (包含多时间周期特征) 或预测/回测所需的特征数据。
        
        参数:
            symbol (str): 交易对符号
            start_str (str): 开始时间
            end_str (str): 结束时间（可选）
            mode (str): 'train', 'predict', 或 'backtest'. 
                        - 'train': 计算并返回 X 和 y.
                        - 'predict'/'backtest': 只计算并返回 X (最新的特征行)，y 为 None.
                                                还会返回带有指标的目标周期DataFrame。
            
        返回:
            - 如果 mode == 'train': Tuple[pd.DataFrame, pd.Series, None] (特征DataFrame, 目标变量Series, None)
            - 如果 mode == 'predict' or mode == 'backtest': Tuple[pd.DataFrame, None, pd.DataFrame] (特征DataFrame, None, 原始目标周期数据含指标)
        """
        try:
            all_data_frames_processed_ta: Dict[str, pd.DataFrame] = {}
            all_data_frames_raw: Dict[str, pd.DataFrame] = {}
            for interval in self.intervals:
                df_raw = self._fetch_historical_data_in_chunks(
                    symbol=symbol,
                    interval=interval,
                    start_str=start_str,
                    end_str=end_str,
                )
                if df_raw.empty:
                    logger.warning(f"未能获取 {interval} 的历史数据，跳过此周期。")
                    continue
                all_data_frames_raw[interval] = df_raw.copy()
                df_indicators_with_suffix = self.add_technical_indicators(df_raw, interval)
                all_data_frames_processed_ta[interval] = df_indicators_with_suffix
            if self.target_interval not in all_data_frames_processed_ta:
                logger.error(f"未能获取或处理目标时间周期 ({self.target_interval}) 的技术指标数据。")
                if mode == 'train':
                    return pd.DataFrame(), pd.Series(), None
                else:
                    return pd.DataFrame(), None, pd.DataFrame()
            df_target_indicators_processed = all_data_frames_processed_ta[self.target_interval].copy()
            # df_target_raw_data = all_data_frames_raw.get(self.target_interval) # Still needed for predict/backtest return
            
            y: Optional[pd.Series] = None
            df_with_target: Optional[pd.DataFrame] = None # Initialize df_with_target

            if mode == 'train':
                # df_target_indicators_processed should now contain 'close_TARGET_INTERVAL' and 'atr_20_TARGET_INTERVAL'
                # from the modified add_technical_indicators
                
                # Check if necessary columns for target calculation exist in df_target_indicators_processed
                required_cols_for_target = [f'close_{self.target_interval}', f'atr_20_{self.target_interval}']
                if not all(col in df_target_indicators_processed.columns for col in required_cols_for_target):
                    missing_cols = [col for col in required_cols_for_target if col not in df_target_indicators_processed.columns]
                    logger.error(f"目标周期 ({self.target_interval}) 的指标数据中缺少计算目标所需的列: {missing_cols}。")
                    return pd.DataFrame(), pd.Series(dtype='float64'), None

                # No need to join df_target_raw_data['close'] as it should be in df_target_indicators_processed
                # temp_df_for_target = df_target_indicators_processed.join(
                #     df_target_raw_data[['close']].rename(columns={'close': f'close_{self.target_interval}'}),
                #     how='inner'
                # )
                # if temp_df_for_target.empty:
                #     logger.error("为计算目标变量而合并指标和收盘价后数据为空。")
                #     return pd.DataFrame(), pd.Series(dtype='float64'), None
                # df_with_target = self.add_target_variable(temp_df_for_target)
                
                df_with_target = self.add_target_variable(df_target_indicators_processed.copy()) # Pass a copy

                if 'target' not in df_with_target.columns:
                     logger.error(f"调用 add_target_variable 后，目标列 'target' 未找到。")
                     return pd.DataFrame(), pd.Series(dtype='float64'), None

                y = df_with_target['target']
                base_features = df_target_indicators_processed.loc[y.index] # Ensure index alignment
            else: # mode == 'predict' or 'backtest'
                base_features = df_target_indicators_processed.copy()
            
            final_features = base_features
            for interval_val in self.intervals:
                if interval_val == self.target_interval or interval_val not in all_data_frames_processed_ta:
                    continue
                df_other_interval_processed = all_data_frames_processed_ta[interval_val]
                final_features = pd.merge_asof(
                    left=final_features.sort_index(),
                    right=df_other_interval_processed.sort_index(),
                    left_index=True,
                    right_index=True,
                    direction='backward'
                )
                logger.info(f"成功合并来自 {interval_val} 的特征。final_features 当前形状: {final_features.shape}")
            if mode == 'train':
                final_features.ffill(inplace=True)
                final_features.dropna(inplace=True)
                if y is not None:
                    y = y.loc[final_features.index]
            elif mode in ['predict', 'backtest']:
                final_features.ffill(inplace=True)
                final_features.bfill(inplace=True) # Added bfill for predict/backtest after reindex and ffill
            if final_features.empty:
                logger.error(f"模式 {mode}: 合并特征并清理NaN后，特征数据为空。")
                if mode == 'train':
                    return pd.DataFrame(), pd.Series(), None
                else:
                    # For predict/backtest, still return the raw target interval data as before
                    df_target_raw_data_for_return = all_data_frames_raw.get(self.target_interval)
                    return pd.DataFrame(), None, df_target_raw_data_for_return
            X = final_features
            if mode == 'train' and y is not None:
                 y = y.loc[X.index]
                 if X.empty or y.empty: # Corrected indentation for this nested if
                    logger.error("训练模式下，X或y在最终对齐后为空。")
                    return pd.DataFrame(), pd.Series(), None
            if mode in ['predict', 'backtest'] and X.empty:
                logger.warning(f"模式 {mode}: X为空，即使经过了ffill处理。可能是输入数据不足。")
                return pd.DataFrame(), None, pd.DataFrame()
            logger.info(f"模式 '{mode}' 数据准备完成。特征数量: {X.shape[1]}, 样本数量: {X.shape[0]}.")
            
            # Add a check for NaNs in X before returning, especially for predict mode
            if mode in ['predict', 'backtest'] and X.isnull().any().any():
                nan_cols_in_X = X.columns[X.isnull().any()].tolist()
                logger.warning(f"模式 '{mode}': 最终特征X中仍包含NaN值。列: {nan_cols_in_X[:10]}. 总NaN数量: {X.isnull().sum().sum()}. 首行NaN数量: {X.iloc[0].isnull().sum() if not X.empty else 'N/A'}")

            if mode == 'train':
                return X, y, None 
            else: # mode == 'predict' or 'backtest'
                # For predict/backtest, still return the raw target interval data as before
                df_target_raw_data_for_return = all_data_frames_raw.get(self.target_interval)
                return X, None, df_target_raw_data_for_return
        except Exception as e:
            logger.error(f"准备多时间周期数据时发生错误 (模式: {mode}): {str(e)}", exc_info=True)
            if mode == 'train':
                return pd.DataFrame(), pd.Series(), None
            else:
                return pd.DataFrame(), None, pd.DataFrame()
            
if __name__ == "__main__":
    # 示例用法或测试代码
    try:
        # 确保环境变量已加载或直接提供API密钥
        # load_dotenv() # 已经在 DataProcessor 的 __init__ 中调用

        processor = DataProcessor(
            intervals=[Client.KLINE_INTERVAL_15MINUTE, Client.KLINE_INTERVAL_1HOUR, Client.KLINE_INTERVAL_4HOUR],
            future_periods=2, # 预测未来2个15分钟K线
            atr_multiplier=1.5
        )

        # 测试数据获取和准备
        # 注意：这里的起始和结束时间仅为示例，真实使用时应根据需求调整
        # '1 Nov, 2024' 是未来的日期，如果用于获取历史数据会返回空，请使用过去的日期
        # 例如: start_str='1 Jan, 2023', end_str='1 Feb, 2023'
        
        # 获取 ETHUSDT 的数据进行测试
        symbol_to_test = 'ETHUSDT'
        start_date_test = '1 Oct, 2023'
        end_date_test = '1 Nov, 2023'

        logger.info(f"开始为符号 {symbol_to_test} 从 {start_date_test} 到 {end_date_test} 准备数据...")
        
        X, y, df_target_raw_data = processor.prepare_data(
            symbol=symbol_to_test,
            start_str=start_date_test,
            end_str=end_date_test
        )
        
        if X is not None and not X.empty and y is not None and not y.empty:
            logger.info(f"数据准备完成。特征 (X) 的形状: {X.shape}, 目标 (y) 的形状: {y.shape}")
            logger.info(f"特征 (X) 的前几行:\\n{X.head()}")
            logger.info(f"目标 (y) 的前几行:\\n{y.head()}")
            
            # 检查NaN值
            logger.info(f"X中NaN值的数量: {X.isnull().sum().sum()}")
            logger.info(f"y中NaN值的数量: {y.isnull().sum().sum()}")

            # 检查是否有无限值
            if np.isinf(X.select_dtypes(include=np.number)).any().any():
                logger.warning("特征X中包含无限值！")
            if pd.api.types.is_numeric_dtype(y) and np.isinf(y).any(): # NEW - Using pandas API for type check
                 logger.warning("目标y中包含无限值！")

        else:
            logger.error("数据准备失败，返回的X或y为空。")

    except Exception as e:
        logger.error(f"在主执行块中发生错误: {str(e)}", exc_info=True) 