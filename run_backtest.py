import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime, date
import os

from data_processor import DataProcessor
from model_trainer import ModelTrainer # 用于加载模型和scaler
from backtester import Backtester # 导入我们刚刚修改的Backtester
from binance.client import Client # 用于KLINE_INTERVAL常量

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- 回测配置参数 (需要根据您的实际情况修改) ---
SYMBOL_TO_BACKTEST = 'BTCUSDT'  # 您希望回测的交易对
INTERVAL_TO_BACKTEST = Client.KLINE_INTERVAL_15MINUTE # 回测使用的K线周期 (与模型训练时一致)
START_DATE_BACKTEST = '2020-01-01' # 回测开始日期
END_DATE_BACKTEST = date.today().strftime('%Y-%m-%d') # 回测结束日期 (动态获取今天)

# 模型相关配置 (需要与模型训练时的配置一致)
MODEL_DIR_CONFIG = 'trained_models' # 模型保存的目录
MODEL_TYPE_FOR_LATEST_CONFIG = 'ensemble' # 加载模型的类型 (例如 'ensemble', 'rf', 'xgb' 等)

# DataProcessor 相关配置 (需要与模型训练时的配置一致)
DP_INTERVALS_CONFIG = [
    Client.KLINE_INTERVAL_1MINUTE, Client.KLINE_INTERVAL_5MINUTE,
    Client.KLINE_INTERVAL_15MINUTE, Client.KLINE_INTERVAL_30MINUTE,
    Client.KLINE_INTERVAL_1HOUR, Client.KLINE_INTERVAL_4HOUR
]
DP_TARGET_INTERVAL_CONFIG = Client.KLINE_INTERVAL_15MINUTE
DP_FUTURE_PERIODS_CONFIG = 10
DP_ATR_MULTIPLIER_CONFIG = 2.0

# Backtester 配置
INITIAL_CAPITAL_CONFIG = 10000.0
COMMISSION_CONFIG = 0.00075 # 币安手续费 (Maker: 0.00075, Taker: 0.00075, VIP 0级, 无BNB抵扣)
SLIPPAGE_CONFIG = 0.0005 # 滑点估算 (可以根据市场情况调整)
BACKTEST_OUTPUT_DIR = 'backtest_results_model' # 模型回测结果的输出目录
# --- 配置结束 ---

def find_latest_model_path(model_base_dir: Path, model_type_prefix: str) -> Path:
    """查找最新模型文件路径 (仅模型文件，不含scaler或info)"""
    model_files = list(model_base_dir.glob(f'model_{model_type_prefix}_*.joblib'))
    if not model_files:
        raise FileNotFoundError(f"在 {model_base_dir} 中没有找到 {model_type_prefix} 类型的模型文件")
    latest_model = max(model_files, key=lambda p: p.stat().st_mtime)
    logger.info(f"找到最新的模型文件: {latest_model}")
    return latest_model

def generate_signals_from_model(
    model_trainer: ModelTrainer,
    data_processor: DataProcessor,
    historical_data: pd.DataFrame, # This is the raw historical data for the target interval
    future_periods: int, # This seems to be a leftover, DataProcessor uses its own config
    atr_multiplier: float, # This also seems to be a leftover
    extended_start_date_param: str # New parameter for the extended start date
) -> pd.Series:
    """
    使用加载的模型为历史数据生成交易信号。
    信号: 1 (买入), -1 (卖出), 0 (持有/无明确信号)
    """
    logger.info("开始使用模型为历史数据生成交易信号...")
    signals = pd.Series(index=historical_data.index, dtype=int, data=0) # 默认信号为0 (持有)

    # 为了模拟逐根K线预测，我们需要迭代历史数据
    # 注意：这里的实现方式是为了回测的准确性，效率上可能不是最优的
    # 真实场景中，模型预测通常基于当前最新的完整数据点

    # 我们需要足够的历史数据来计算初始特征
    # DataProcessor.prepare_data 会自动处理所需的最小数据量
    # 但为了逐根K线生成特征，我们从一个能产生有效特征的最小点开始
    # 假设至少需要 N 根K线才能生成第一个有效特征集 (N取决于DataProcessor的实现)
    # 这里为了简化，我们假设 DataProcessor.MIN_DATA_POINTS_FOR_FEATURES 存在
    # 或者我们从一个合理的点开始，例如第 (k-1) 个点开始准备数据，以预测第 k 个点的信号
    
    min_lookback_needed = getattr(data_processor, 'MIN_DATA_POINTS_FOR_FEATURES', 100) # 假设值，应从DP获取

    for i in range(min_lookback_needed, len(historical_data)):
        # 获取到当前K线为止的数据，用于特征工程
        # 注意：模型预测的是下一根K线的行为，所以我们用 X_current 去预测 y_next
        current_data_segment = historical_data.iloc[:i+1] # 包含当前K线
        
        try:
            # 获取当前K线的时间作为 end_str
            # start_str 需要足够早，以包含所有 self.intervals 所需的初始数据
            # 为了简化，我们这里使用固定的 extended_start_date，但理想情况下，
            # prepare_data 内部应能处理好只取到 end_str 所需的部分。
            current_kline_time = historical_data.index[i]
            
            # 注意：当调用 processor.prepare_data 时，它会获取所有 self.intervals 的数据直到 end_str
            # 这在循环中可能会很慢。更优化的方法是 DataProcessor 有一个方法
            # 可以基于已提供的多周期数据字典来生成特征。
            # 目前的临时修正是让它每次都获取。
            X_features_current_segment, _, _ = data_processor.prepare_data(
                symbol=SYMBOL_TO_BACKTEST,
                start_str=extended_start_date_param, # 使用参数
                end_str=current_kline_time.strftime('%Y-%m-%d %H:%M:%S'), # 当前K线时间作为结束时间
                mode='backtest' 
            )

            if X_features_current_segment is None or X_features_current_segment.empty:
                logger.warning(f"日期 {historical_data.index[i]}: 特征工程后数据为空，跳过预测。")
                continue

            # 获取最后一行的特征用于预测 (这一行对应的是 current_data_segment 的最后一行)
            X_to_predict = X_features_current_segment.iloc[[-1]]

            # 进行预测
            # predict 方法返回一个numpy数组，其中包含预测值 (1, -1, 0)
            prediction_values = model_trainer.predict(X_to_predict)
            
            if prediction_values is not None and len(prediction_values) > 0:
                predicted_signal = int(prediction_values[0])
                # 将信号记录在当前K线的索引上，表示基于当前K线的信息，对下一根K线开盘时的操作
                signals.iloc[i] = predicted_signal 
            else:
                logger.warning(f"日期 {historical_data.index[i]}: 模型未返回有效预测，信号设为0。")
                signals.iloc[i] = 0
                
        except Exception as e:
            logger.error(f"在日期 {historical_data.index[i]} 生成信号时出错: {e}", exc_info=False)
            signals.iloc[i] = 0 # 出错时保持持有

    logger.info("模型信号生成完毕。")
    return signals


def main():
    logger.info("开始执行模型回测脚本...")

    # 1. 初始化 DataProcessor
    # API Key 和 Secret 现在由 DataProcessor 内部从环境变量加载
    # 我们需要传递 DataProcessor __init__ 定义的参数
    
    # 保留日志警告，以防用户忘记设置环境变量，即使DataProcessor内部会处理
    api_key_env = os.getenv('BINANCE_API_KEY_FUTURES') # 检查环境变量（仅用于日志）
    api_secret_env = os.getenv('BINANCE_API_SECRET_FUTURES') # 检查环境变量（仅用于日志）
    if not api_key_env or not api_secret_env:
        logger.warning("脚本检测到 BINANCE_API_KEY_FUTURES 或 BINANCE_API_SECRET_FUTURES 环境变量未设置。")
        logger.warning("DataProcessor 将尝试从 BINANCE_API_KEY 和 BINANCE_API_SECRET 加载。请确保这些已设置。")

    processor = DataProcessor(
        intervals=DP_INTERVALS_CONFIG,
        future_periods=DP_FUTURE_PERIODS_CONFIG,
        atr_multiplier=DP_ATR_MULTIPLIER_CONFIG
    )
    logger.info(f"主 DataProcessor 初始化完毕。将使用以下配置: intervals={DP_INTERVALS_CONFIG}, future_periods={DP_FUTURE_PERIODS_CONFIG}, atr_multiplier={DP_ATR_MULTIPLIER_CONFIG}")


    # 2. 加载模型和Scaler
    model_path = find_latest_model_path(Path(MODEL_DIR_CONFIG), MODEL_TYPE_FOR_LATEST_CONFIG)
    scaler_path = Path(MODEL_DIR_CONFIG) / model_path.name.replace('model_', 'scaler_').replace('.joblib', '.pkl')
    
    if not scaler_path.exists():
        logger.error(f"Scaler文件 {scaler_path} 未找到。请确保与模型一起保存了对应的scaler。")
        return

    model_trainer_instance = ModelTrainer(
        output_dir=MODEL_DIR_CONFIG,
        model_type=MODEL_TYPE_FOR_LATEST_CONFIG,
        random_state=42
    )
    model_trainer_instance.load_model(model_path=model_path.name)
    model_trainer_instance.load_scaler(scaler_filename=scaler_path.name)
    logger.info(f"模型 {model_path.name} 和 Scaler {scaler_path.name} 加载成功。")

    # 3. 获取回测历史数据
    logger.info(f"正在获取 {SYMBOL_TO_BACKTEST} 在 {INTERVAL_TO_BACKTEST} 周期的历史数据...")
    extended_start_date = (pd.to_datetime(START_DATE_BACKTEST) - pd.Timedelta(days=90)).strftime('%Y-%m-%d')
    
    # DataProcessor._fetch_historical_data_in_chunks 是一个内部方法，我们应该调用公共的 prepare_data 或类似方法
    # 或者，如果 DataProcessor 有一个专门的公共方法来获取数据，我们应该用那个。
    # 假设 DataProcessor 内部的 client 会被用于获取数据，其配置（如API密钥）已在 __init__ 中完成。
    # processor.fetch_historical_data 现在需要与 DataProcessor 中该方法的实际签名匹配。
    # 根据 data_processor.py，没有一个名为 fetch_historical_data 的公共方法。
    # _fetch_historical_data_in_chunks 是内部的。
    # prepare_data 是主要的公共方法，它内部会调用 _fetch_historical_data_in_chunks。
    
    # 因此，我们不直接调用 processor.fetch_historical_data。
    # generate_signals_from_model 函数内部会迭代调用 backtest_processor.prepare_data，
    # 而 prepare_data 内部会获取所需数据。
    # 我们需要一个初始的完整历史数据集，从中截取每一段进行特征工程。
    # 这个初始数据集应该由一个 DataProcessor 实例获取。

    logger.info(f"正在为回测范围准备初始的完整K线数据集 (从 {extended_start_date} 到 {END_DATE_BACKTEST})...")
    # 使用主 processor 实例来获取一次性的原始数据。
    # prepare_data 本身会返回 X, y。我们只需要它的数据获取部分。
    # 这是一个需要改进的地方：DataProcessor 应该有一个更纯粹的数据获取方法。
    # 暂时，我们将调用 prepare_data 并只取其获取的数据部分，或者需要修改 DataProcessor。

    # --- 临时的获取数据方式 ---
    # 我们需要一个方法能返回某个symbol、某个interval的原始OHLCV数据。
    # _fetch_historical_data_in_chunks 是最接近的，但它是内部的。
    # 为了继续，我假设我们可以直接调用它，或者您会修改 DataProcessor 提供一个公共接口。
    # **注意：直接调用内部方法 (_method) 通常不推荐。**
    try:
        historical_data_raw = processor._fetch_historical_data_in_chunks( # 调用内部方法
            symbol=SYMBOL_TO_BACKTEST,
            interval=INTERVAL_TO_BACKTEST, 
            start_str=extended_start_date,
            end_str=END_DATE_BACKTEST
        )
    except AttributeError:
        logger.error("DataProcessor 类似乎没有 _fetch_historical_data_in_chunks 方法，或者它不可访问。")
        logger.error("请确保 DataProcessor 有一个可用于获取指定交易对和周期的原始K线数据的方法。")
        return
    # --- 结束临时的获取数据方式 ---


    if historical_data_raw.empty:
        logger.error("未能获取到回测所需历史数据。请检查交易对、日期范围和网络连接。")
        return
        
    historical_data_for_backtest = historical_data_raw[
        (historical_data_raw.index >= pd.to_datetime(START_DATE_BACKTEST)) &
        (historical_data_raw.index <= pd.to_datetime(END_DATE_BACKTEST))
    ].copy()

    if historical_data_for_backtest.empty:
        logger.error(f"在指定的日期范围 {START_DATE_BACKTEST} 到 {END_DATE_BACKTEST} 内没有数据。")
        return

    logger.info(f"获取到 {len(historical_data_for_backtest)} 条数据用于回测（{START_DATE_BACKTEST} 至 {END_DATE_BACKTEST}）。")
    logger.info(f"将使用 {len(historical_data_raw)} 条数据（从 {extended_start_date} 开始）用于特征工程。")
    

    # 4. 使用模型为历史数据生成交易信号
    # 创建一个新的 DataProcessor 实例，专门用于回测中的特征生成
    # 它应该使用与训练模型时完全相同的参数配置
    backtest_processor = DataProcessor(
        intervals=DP_INTERVALS_CONFIG, # 这些是训练时DataProcessor使用的参数
        future_periods=DP_FUTURE_PERIODS_CONFIG,
        atr_multiplier=DP_ATR_MULTIPLIER_CONFIG
    )
    logger.info(f"回测特征专用 DataProcessor 初始化完毕。将使用以下配置: intervals={DP_INTERVALS_CONFIG}, future_periods={DP_FUTURE_PERIODS_CONFIG}, atr_multiplier={DP_ATR_MULTIPLIER_CONFIG}")


    # generate_signals_from_model 函数将使用 backtest_processor
    # 并且需要 backtest_processor.prepare_data 支持 'backtest' 模式
    # (即不计算target，不删除最后一行特征)
    model_signals = generate_signals_from_model(
        model_trainer=model_trainer_instance,
        data_processor=backtest_processor,
        historical_data=historical_data_raw.copy(), # Pass the full raw data with lookback
        future_periods=DP_FUTURE_PERIODS_CONFIG, 
        atr_multiplier=DP_ATR_MULTIPLIER_CONFIG, 
        extended_start_date_param=extended_start_date # Pass the extended_start_date from main
    )
    
    aligned_model_signals = model_signals.reindex(historical_data_for_backtest.index).fillna(0).astype(int)

    # 5. 初始化并运行 Backtester
    logger.info("初始化 Backtester...")
    backtester = Backtester(
        data=historical_data_for_backtest, 
        initial_capital=INITIAL_CAPITAL_CONFIG,
        commission=COMMISSION_CONFIG,
        slippage=SLIPPAGE_CONFIG,
        output_dir=Path(BACKTEST_OUTPUT_DIR) / f"{SYMBOL_TO_BACKTEST}_{INTERVAL_TO_BACKTEST}_{MODEL_TYPE_FOR_LATEST_CONFIG}"
    )

    logger.info("开始应用策略进行回测...")
    backtester.apply_strategy(signals=aligned_model_signals)

    # 6. 计算并打印指标
    logger.info("计算回测性能指标...")
    metrics = backtester.calculate_metrics()
    
    print("\n=== 模型回测性能指标 ===")
    for metric, value in metrics.items():
        if isinstance(value, float):
            print(f"{metric}: {value:,.4f}")
        else:
            print(f"{metric}: {value}")

    # 7. 生成图表和报告
    logger.info("生成回测图表...")
    backtester.plot_results(save=True)
    logger.info("生成回测报告...")
    backtester.generate_report()

    logger.info(f"模型回测完成。结果保存在: {backtester.output_dir}")


if __name__ == "__main__":
    try:
        main()
    except FileNotFoundError as e:
        logger.error(f"文件未找到错误: {e}")
    except ValueError as e:
        logger.error(f"值错误: {e}")
    except Exception as e:
        logger.error(f"执行回测脚本时发生未预料的错误: {e}", exc_info=True) 