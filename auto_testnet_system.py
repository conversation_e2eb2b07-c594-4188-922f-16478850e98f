#!/usr/bin/env python3
"""
自动运行的修复版测试网剥头皮交易系统
解决时间戳同步问题，无需交互输入
"""

import numpy as np
import logging
import time
import requests
import hmac
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Tuple
import threading
import queue
from urllib.parse import urlencode

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoBinanceTestnetAPI:
    """自动运行的币安测试网API客户端"""
    
    def __init__(self):
        # 币安测试网端点
        self.base_url = "https://testnet.binance.vision"
        
        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        
        # 时间同步
        self.server_time_offset = 0
        self.last_sync_time = 0
        self.testnet_connected = False
        
        # 初始化时间同步
        self._sync_server_time()
        
    def _sync_server_time(self):
        """同步服务器时间"""
        try:
            response = self.session.get(f"{self.base_url}/api/v3/time", timeout=10)
            response.raise_for_status()
            server_time = response.json()['serverTime']
            local_time = int(time.time() * 1000)
            self.server_time_offset = server_time - local_time
            self.last_sync_time = time.time()
            logger.info(f"✅ 时间同步成功，偏移: {self.server_time_offset}ms")
        except Exception as e:
            logger.warning(f"⚠️ 时间同步失败: {e}")
            self.server_time_offset = 0
    
    def _get_timestamp(self) -> int:
        """获取同步后的时间戳"""
        # 每5分钟重新同步一次
        if time.time() - self.last_sync_time > 300:
            self._sync_server_time()
        
        return int(time.time() * 1000) + self.server_time_offset
    
    def _make_request(self, method: str, endpoint: str, params: dict = None) -> Optional[dict]:
        """发送API请求"""
        if params is None:
            params = {}
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == "GET":
                response = self.session.get(url, params=params, timeout=10)
            else:
                return None
            
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"API请求失败: {e}")
            return None
    
    def test_connectivity(self) -> bool:
        """测试连接"""
        try:
            result = self._make_request("GET", "/api/v3/ping")
            if result == {}:
                logger.info("✅ 测试网连接成功")
                self.testnet_connected = True
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 测试网连接失败: {e}")
            return False
    
    def get_price(self, symbol: str = "ADAUSDT") -> Optional[float]:
        """获取当前价格"""
        result = self._make_request("GET", "/api/v3/ticker/price", {"symbol": symbol})
        if result and 'price' in result:
            return float(result['price'])
        return None
    
    def get_klines(self, symbol: str = "ADAUSDT", interval: str = "1m", limit: int = 100) -> Optional[list]:
        """获取K线数据"""
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        return self._make_request("GET", "/api/v3/klines", params)

class AutoTestnetScalping:
    """自动运行的测试网剥头皮交易系统"""
    
    def __init__(self, initial_balance: float = 1000.0):
        self.symbol = "ADAUSDT"
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        
        # 成功验证的剥头皮参数
        self.position_risk = 0.025  # 2.5%风险
        self.stop_loss_ratio = 0.0004  # 0.04%止损
        self.take_profit_ratio = 0.0005  # 0.05%止盈
        self.min_confidence = 0.65  # 65%置信度
        
        # 自动API
        self.api = AutoBinanceTestnetAPI()
        
        # 交易状态
        self.current_position = None
        self.last_trade_time = None
        self.total_trades = 0
        self.winning_trades = 0
        self.trade_history = []
        
        # 实时监控
        self.is_running = False
        self.price_monitor_thread = None
        self.price_queue = queue.Queue()
        
    def initialize_testnet(self) -> bool:
        """初始化测试网连接"""
        logger.info("🔧 初始化自动测试网连接...")
        
        # 测试连接
        if not self.api.test_connectivity():
            logger.error("❌ 测试网连接失败")
            return False
        
        logger.info("ℹ️ 使用模拟交易模式（无需API密钥）")
        return True
    
    def start_price_monitor(self):
        """启动价格监控"""
        def price_monitor():
            while self.is_running:
                try:
                    price = self.api.get_price(self.symbol)
                    if price:
                        self.price_queue.put({
                            'timestamp': datetime.now(),
                            'price': price
                        })
                    
                    time.sleep(2 if self.current_position else 5)
                        
                except Exception as e:
                    logger.error(f"价格监控错误: {e}")
                    time.sleep(3)
        
        self.price_monitor_thread = threading.Thread(target=price_monitor, daemon=True)
        self.price_monitor_thread.start()
        logger.info("✅ 自动价格监控已启动")
    
    def get_latest_price(self) -> Optional[float]:
        """获取最新价格"""
        try:
            latest_price = None
            while not self.price_queue.empty():
                price_data = self.price_queue.get_nowait()
                latest_price = price_data['price']
            
            return latest_price if latest_price else self.api.get_price(self.symbol)
            
        except Exception:
            return self.api.get_price(self.symbol)
    
    def calculate_signal(self) -> Tuple[str, float]:
        """计算交易信号（保持成功的策略）"""
        try:
            klines = self.api.get_klines(self.symbol, "1m", 20)
            if not klines or len(klines) < 10:
                return "HOLD", 0.0
            
            # 转换价格数据
            closes = [float(k[4]) for k in klines]
            
            current_price = closes[-1]
            prev_price_1 = closes[-2]
            prev_price_3 = closes[-4] if len(closes) >= 4 else closes[-2]
            
            # 计算变化率
            change_1 = (current_price - prev_price_1) / prev_price_1
            change_3 = (current_price - prev_price_3) / prev_price_3
            
            # 简单移动平均
            ma_5 = np.mean(closes[-5:])
            
            # 评分系统（保持成功的逻辑）
            score = 0
            confidence_factors = []
            
            # 1分钟动量
            if change_1 > 0.0002:  # 0.02%
                score += 3
                confidence_factors.append(0.25)
            elif change_1 < -0.0002:
                score -= 3
                confidence_factors.append(0.25)
            
            # 3分钟趋势
            if change_3 > 0.0003:  # 0.03%
                score += 2
                confidence_factors.append(0.15)
            elif change_3 < -0.0003:
                score -= 2
                confidence_factors.append(0.15)
            
            # 移动平均
            if current_price > ma_5 * 1.0001:  # 0.01%
                score += 1
                confidence_factors.append(0.10)
            elif current_price < ma_5 * 0.9999:
                score -= 1
                confidence_factors.append(0.10)
            
            # 决策
            if score >= 3:
                direction = "LONG"
                base_confidence = 0.70
            elif score <= -3:
                direction = "SHORT"
                base_confidence = 0.70
            else:
                direction = "HOLD"
                base_confidence = 0.5
            
            # 计算最终置信度
            if direction != "HOLD":
                confidence_boost = sum(confidence_factors)
                final_confidence = min(0.90, base_confidence + confidence_boost)
            else:
                final_confidence = base_confidence
            
            return direction, final_confidence
            
        except Exception as e:
            logger.error(f"信号计算失败: {e}")
            return "HOLD", 0.0
    
    def can_trade(self) -> bool:
        """检查是否可以交易"""
        if self.current_position:
            return False
        
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < 30:
                return False
        
        if self.current_balance < self.initial_balance * 0.3:
            return False
        
        return True
    
    def execute_trade(self, direction: str, confidence: float, entry_price: float):
        """执行交易（模拟）"""
        # 计算仓位大小
        risk_amount = self.current_balance * self.position_risk
        position_value = risk_amount * 125  # 125x杠杆
        position_size = position_value / entry_price
        
        # 计算止损止盈
        if direction == "LONG":
            stop_loss = entry_price * (1 - self.stop_loss_ratio)
            take_profit = entry_price * (1 + self.take_profit_ratio)
        else:
            stop_loss = entry_price * (1 + self.stop_loss_ratio)
            take_profit = entry_price * (1 - self.take_profit_ratio)
        
        # 创建持仓记录
        self.current_position = {
            'direction': direction,
            'entry_price': entry_price,
            'entry_time': datetime.now(),
            'position_size': position_size,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'confidence': confidence
        }
        
        self.total_trades += 1
        self.last_trade_time = datetime.now()
        
        logger.info(f"🚀 模拟开仓: {direction} @ {entry_price:.4f}")
        logger.info(f"   置信度: {confidence:.1%}")
        logger.info(f"   止损: {stop_loss:.4f} ({self.stop_loss_ratio:.2%})")
        logger.info(f"   止盈: {take_profit:.4f} ({self.take_profit_ratio:.2%})")
    
    def check_exit_conditions(self, current_price: float) -> bool:
        """检查退出条件"""
        if not self.current_position:
            return False
        
        pos = self.current_position
        should_exit = False
        exit_reason = ""
        
        # 检查止损止盈
        if pos['direction'] == "LONG":
            if current_price <= pos['stop_loss']:
                should_exit = True
                exit_reason = "止损"
            elif current_price >= pos['take_profit']:
                should_exit = True
                exit_reason = "止盈"
        else:
            if current_price >= pos['stop_loss']:
                should_exit = True
                exit_reason = "止损"
            elif current_price <= pos['take_profit']:
                should_exit = True
                exit_reason = "止盈"
        
        # 检查时间退出
        duration = (datetime.now() - pos['entry_time']).total_seconds()
        if duration > 120:  # 2分钟
            should_exit = True
            exit_reason = "时间退出"
        
        if should_exit:
            self.close_position(current_price, exit_reason)
            return True
        
        return False
    
    def close_position(self, exit_price: float, exit_reason: str):
        """平仓"""
        if not self.current_position:
            return
        
        pos = self.current_position
        
        # 计算盈亏
        if pos['direction'] == "LONG":
            pnl_pct = (exit_price - pos['entry_price']) / pos['entry_price']
        else:
            pnl_pct = (pos['entry_price'] - exit_price) / pos['entry_price']
        
        leveraged_pnl = pnl_pct * 125
        position_value = pos['position_size'] * pos['entry_price']
        margin_used = position_value / 125
        pnl_amount = margin_used * leveraged_pnl
        
        # 更新余额
        self.current_balance += pnl_amount
        
        # 更新统计
        is_winner = pnl_amount > 0
        if is_winner:
            self.winning_trades += 1
        
        # 记录交易
        holding_time = (datetime.now() - pos['entry_time']).total_seconds()
        
        trade_record = {
            'trade_id': self.total_trades,
            'entry_time': pos['entry_time'],
            'exit_time': datetime.now(),
            'direction': pos['direction'],
            'entry_price': pos['entry_price'],
            'exit_price': exit_price,
            'pnl_amount': pnl_amount,
            'holding_time': holding_time,
            'is_winner': is_winner,
            'exit_reason': exit_reason,
            'confidence': pos['confidence']
        }
        
        self.trade_history.append(trade_record)
        self.current_position = None
        
        # 显示结果
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        
        status = "✅" if is_winner else "❌"
        logger.info(f"📈 平仓: {pos['direction']} @ {exit_price:.4f} ({exit_reason})")
        logger.info(f"   {status} 盈亏: ${pnl_amount:+.2f} ({leveraged_pnl:+.2%})")
        logger.info(f"   持仓时间: {holding_time:.0f}秒")
        logger.info(f"   余额: ${self.current_balance:.2f}")
        logger.info(f"   胜率: {win_rate:.1%}, 收益: {total_return:+.1%}")
    
    def run_trading(self, duration_minutes: int = 10):
        """运行交易"""
        logger.info("🚀 启动自动测试网剥头皮交易系统")
        logger.info(f"💰 初始资金: ${self.initial_balance}")
        logger.info(f"📊 参数: 止损{self.stop_loss_ratio:.2%}, 止盈{self.take_profit_ratio:.2%}")
        logger.info(f"🔧 修复: 时间戳同步 + 自动运行")
        logger.info(f"⏰ 运行时间: {duration_minutes} 分钟")
        
        # 初始化测试网
        if not self.initialize_testnet():
            logger.error("❌ 测试网初始化失败")
            return
        
        self.is_running = True
        self.start_price_monitor()
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        try:
            while datetime.now() < end_time and self.is_running:
                current_price = self.get_latest_price()
                if current_price is None:
                    time.sleep(3)
                    continue
                
                # 检查退出条件
                if self.current_position:
                    if self.check_exit_conditions(current_price):
                        continue
                    
                    # 显示持仓状态
                    pos = self.current_position
                    duration = (datetime.now() - pos['entry_time']).total_seconds()
                    
                    if pos['direction'] == "LONG":
                        unrealized_pnl = (current_price - pos['entry_price']) / pos['entry_price'] * 125
                    else:
                        unrealized_pnl = (pos['entry_price'] - current_price) / pos['entry_price'] * 125
                    
                    logger.info(f"📊 持仓: {pos['direction']} @ {current_price:.4f} "
                               f"({duration:.0f}秒) 浮盈: {unrealized_pnl:+.2%}")
                    
                    time.sleep(2)
                
                else:
                    # 寻找交易机会
                    if self.can_trade():
                        direction, confidence = self.calculate_signal()
                        
                        if direction in ["LONG", "SHORT"] and confidence >= self.min_confidence:
                            self.execute_trade(direction, confidence, current_price)
                    
                    time.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断交易")
        except Exception as e:
            logger.error(f"❌ 交易异常: {e}")
        finally:
            self.is_running = False
            
            # 强制平仓
            if self.current_position:
                final_price = self.get_latest_price()
                if final_price:
                    self.close_position(final_price, "系统停止")
            
            self.show_results()
    
    def show_results(self):
        """显示结果"""
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        
        print("\n" + "="*70)
        print("🎉 自动测试网剥头皮交易完成")
        print("="*70)
        print(f"📊 交易统计:")
        print(f"  总交易数: {self.total_trades}")
        print(f"  胜率: {win_rate:.1%}")
        print(f"  收益率: {total_return:+.1%}")
        print(f"💰 财务表现:")
        print(f"  初始余额: ${self.initial_balance:.2f}")
        print(f"  最终余额: ${self.current_balance:.2f}")
        print(f"  净盈亏: ${self.current_balance - self.initial_balance:+.2f}")
        print(f"🔧 系统验证:")
        print(f"  时间同步: {'✅ 成功' if self.api.server_time_offset != 0 else '⚠️ 未同步'}")
        print(f"  API连接: {'✅ 正常' if self.api.testnet_connected else '❌ 失败'}")

if __name__ == "__main__":
    print("🔧 自动运行的修复版测试网剥头皮交易系统")
    print("📊 解决时间戳同步问题")
    print("🛡️ 无需交互输入，自动运行")
    print("⚡ 使用模拟交易模式")
    
    trader = AutoTestnetScalping(initial_balance=1000.0)
    
    print("\n🚀 开始10分钟自动交易测试...")
    trader.run_trading(duration_minutes=10)
