#!/usr/bin/env python3
"""
Test script to verify ROI and P&L consistency
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_roi_pnl_consistency():
    """Test ROI and P&L consistency"""
    print("🧪 Testing ROI and P&L Consistency")
    print("=" * 60)
    
    print("\n📊 User's Reported Data:")
    print("📈 ROI: +396.3%")
    print("💰 P&L: $+32.67")
    print("🎯 Expected margin: $2.5 (50u × 5%)")
    
    # Calculate what the P&L should be based on ROI
    reported_roi = 396.3  # %
    reported_pnl = 32.67  # $
    expected_margin = 2.5  # $
    
    # If ROI is correct, what should P&L be?
    expected_pnl_from_roi = expected_margin * (reported_roi / 100)
    
    # If P&L is correct, what should ROI be?
    expected_roi_from_pnl = (reported_pnl / expected_margin) * 100
    
    print(f"\n🔍 Consistency Check:")
    print(f"   📊 If ROI {reported_roi:.1f}% is correct:")
    print(f"      💰 Expected P&L: ${expected_margin:.2f} × {reported_roi:.1f}% = ${expected_pnl_from_roi:.2f}")
    print(f"      📊 Actual P&L: ${reported_pnl:.2f}")
    print(f"      📈 Difference: ${reported_pnl - expected_pnl_from_roi:+.2f}")
    
    print(f"\n   💰 If P&L ${reported_pnl:.2f} is correct:")
    print(f"      📊 Expected ROI: (${reported_pnl:.2f} ÷ ${expected_margin:.2f}) × 100 = {expected_roi_from_pnl:.1f}%")
    print(f"      📊 Actual ROI: {reported_roi:.1f}%")
    print(f"      📈 Difference: {reported_roi - expected_roi_from_pnl:+.1f}%")
    
    # Check if margin calculation is wrong
    print(f"\n🔍 Margin Calculation Check:")
    
    # If P&L and ROI are both correct, what margin was actually used?
    actual_margin_used = reported_pnl / (reported_roi / 100)
    
    print(f"   💰 Reported margin: ${expected_margin:.2f}")
    print(f"   💰 Calculated margin: ${actual_margin_used:.2f}")
    print(f"   📈 Difference: ${actual_margin_used - expected_margin:+.2f}")
    
    if abs(actual_margin_used - expected_margin) > 1:
        print(f"   🚨 PROBLEM: Margin calculation is wrong!")
        print(f"   💡 System may be using ${actual_margin_used:.2f} instead of ${expected_margin:.2f}")
    else:
        print(f"   ✅ Margin calculation seems correct")
    
    # Analyze possible causes
    print(f"\n🔍 Possible Causes:")
    
    # Cause 1: Wrong margin base
    if actual_margin_used > expected_margin * 2:
        print(f"   1. 🚨 Using wrong margin base:")
        print(f"      - Expected: 50u × 5% = $2.5")
        print(f"      - Actual: ~${actual_margin_used:.2f}")
        print(f"      - Possible: Using larger percentage or wrong balance")
    
    # Cause 2: Position size calculation error
    position_size_from_margin = expected_margin / 102513.10  # Using entry price from log
    position_size_from_pnl = actual_margin_used / 102513.10
    
    print(f"\n   2. 📊 Position size analysis:")
    print(f"      - Expected position: ${expected_margin:.2f} ÷ $102,513 = {position_size_from_margin:.6f} BTC")
    print(f"      - Actual position: ${actual_margin_used:.2f} ÷ $102,513 = {position_size_from_pnl:.6f} BTC")
    print(f"      - Reported position: 0.010179 BTC")
    
    reported_position = 0.010179
    if abs(reported_position - position_size_from_margin) > 0.005:
        print(f"      🚨 Position size doesn't match expected margin!")
        
        # Calculate what margin this position should require
        required_margin_for_position = reported_position * 102513.10 / 125  # Divide by leverage
        print(f"      💰 Required margin for 0.010179 BTC: ${required_margin_for_position:.2f}")
        
        if abs(required_margin_for_position - actual_margin_used) < 1:
            print(f"      💡 FOUND ISSUE: Position size is too large for 5% margin!")
            print(f"      📊 0.010179 BTC requires ${required_margin_for_position:.2f} margin")
            print(f"      📊 But 5% of $50 is only ${expected_margin:.2f}")
            print(f"      🔧 Position should be: ${expected_margin:.2f} × 125 ÷ $102,513 = {position_size_from_margin:.6f} BTC")
    
    # Cause 3: ROI calculation base error
    print(f"\n   3. 📈 ROI calculation base:")
    print(f"      - If using correct margin (${expected_margin:.2f}): ROI = {expected_roi_from_pnl:.1f}%")
    print(f"      - If using wrong margin (${actual_margin_used:.2f}): ROI = {reported_roi:.1f}%")
    
    # Summary and recommendations
    print(f"\n" + "="*60)
    print("🎯 Analysis Summary:")
    
    if abs(actual_margin_used - expected_margin) > 5:
        print("🚨 MAJOR ISSUE: Margin calculation is completely wrong")
        print("🔧 Fix needed: Position size calculation logic")
        print("💡 Root cause: Not using 5% of $50 as intended")
    elif abs(reported_roi - expected_roi_from_pnl) > 50:
        print("🚨 MAJOR ISSUE: ROI calculation is wrong")
        print("🔧 Fix needed: ROI calculation base")
        print("💡 Root cause: Using wrong denominator for ROI")
    else:
        print("✅ Calculations seem consistent")
    
    print(f"\n💡 Recommended Fixes:")
    print(f"   1. 🔧 Ensure position size = (50 × 5% × 125) ÷ price")
    print(f"   2. 🔧 Ensure margin used = position_size × price ÷ 125")
    print(f"   3. 🔧 Ensure ROI = P&L ÷ margin_used × 100")
    print(f"   4. 🔧 Verify all calculations use consistent values")
    
    print(f"\n🎯 Expected Correct Values:")
    print(f"   💰 Margin: ${expected_margin:.2f}")
    print(f"   📊 Position: {position_size_from_margin:.6f} BTC")
    print(f"   📈 For +396.3% ROI: P&L should be ${expected_pnl_from_roi:.2f}")
    print(f"   📈 For ${reported_pnl:.2f} P&L: ROI should be {expected_roi_from_pnl:.1f}%")

if __name__ == "__main__":
    test_roi_pnl_consistency()
