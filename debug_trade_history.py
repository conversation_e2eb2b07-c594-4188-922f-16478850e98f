#!/usr/bin/env python3
"""
Debug script to investigate trade history inconsistencies
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader
from datetime import datetime

def debug_trade_history_issue():
    """Debug the trade history inconsistency issue"""
    print("🔍 Debugging Trade History Inconsistency")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 Simulating Your Exact Scenario:")
    print("Initial Balance: $50.00")
    print("Final Balance: $65.20")
    print("Trade Records: Only 1 trade with +$1.52 profit")
    print("Missing: $13.68 in unrecorded profits")
    
    # Simulate the exact scenario you encountered
    # 1. Set final balance to $65.20
    trader.account['balance'] = 65.20
    trader.account['equity'] = 65.20
    trader.account['unrealized_pnl'] = 0.00
    trader.account['margin_used'] = 0.00
    trader.account['available_margin'] = 65.20
    
    # 2. Add only one trade record (as shown in your output)
    trader.trade_history.append({
        'action': 'OPEN',
        'timestamp': '2025-06-22 11:00:00',
        'side': 'LONG',
        'price': 102000,
        'size': 0.000141,
        'leverage': 125.0
    })
    
    trader.trade_history.append({
        'action': 'CLOSE',
        'timestamp': '2025-06-22 11:02:00',
        'side': 'LONG',
        'price': 102200,
        'net_pnl': 1.52,
        'roi_percent': 10.6,
        'hold_time': 0.2,
        'reason': '止盈'
    })
    
    print("\n🔍 Running Enhanced Display with Debug Info:")
    trader._print_enhanced_account_status()
    trader._print_enhanced_trading_statistics()
    
    print("\n" + "="*60)
    print("🎯 Analysis of the Problem:")
    print("✅ The debug info should now show:")
    print("   - Complete trade history details")
    print("   - Balance change tracking")
    print("   - Missing trade detection")
    print("   - Possible causes of the inconsistency")
    
    print("\n💡 Likely Causes:")
    print("   1. Multiple trading sessions with incomplete record keeping")
    print("   2. System restarts that preserved balance but lost trade history")
    print("   3. Manual balance adjustments not recorded as trades")
    print("   4. Previous profitable trades that weren't properly saved")
    print("   5. Cache or persistence issues with trade history")

if __name__ == "__main__":
    debug_trade_history_issue()
