#!/usr/bin/env python3
"""
🚀 智能交易系统运行器 - 自动保存日志
运行交易系统并将所有输出保存到日志文件
"""

import subprocess
import sys
import os
from datetime import datetime

def run_trading_with_log():
    """运行交易系统并保存日志"""
    
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 生成日志文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_filename = f'logs/console_output_{timestamp}.log'
    
    print(f"🚀 启动智能交易系统...")
    print(f"📝 控制台输出将保存到: {log_filename}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 运行交易系统并实时保存输出
        with open(log_filename, 'w', encoding='utf-8') as log_file:
            # 写入日志头部信息
            log_file.write(f"智能交易系统运行日志\n")
            log_file.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            log_file.write("=" * 60 + "\n\n")
            log_file.flush()
            
            # 启动交易系统进程
            process = subprocess.Popen(
                [sys.executable, 'smart_auto_trading.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时读取并保存输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # 同时显示在控制台和保存到文件
                    print(output.strip())
                    log_file.write(output)
                    log_file.flush()
            
            # 等待进程结束
            return_code = process.poll()
            
            # 写入结束信息
            end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            end_info = f"\n\n" + "=" * 60 + f"\n结束时间: {end_time}\n返回代码: {return_code}\n"
            print(end_info)
            log_file.write(end_info)
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断程序")
        with open(log_filename, 'a', encoding='utf-8') as log_file:
            log_file.write(f"\n\n⚠️ 用户中断程序 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        with open(log_filename, 'a', encoding='utf-8') as log_file:
            log_file.write(f"\n\n❌ 运行出错: {e} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    finally:
        print(f"\n📝 完整日志已保存到: {log_filename}")

if __name__ == "__main__":
    run_trading_with_log()
