#!/usr/bin/env python3
"""
完整的加密货币预测模型训练代码
支持多种模型类型和配置选项
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
import argparse
import sys
import warnings
warnings.filterwarnings('ignore')

# 导入项目模块
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer, FeatureConfig
from model_trainer import ModelTrainer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def train_crypto_model(
    symbol: str = 'BTCUSDT',
    interval: str = '1h', 
    start_date: str = '2022-01-01',
    model_type: str = 'xgb',
    use_optuna: bool = True,
    optuna_trials: int = 50,
    lstm_timesteps: int = 10,
    lstm_epochs: int = 50,
    save_plots: bool = True
):
    """
    训练加密货币预测模型
    
    参数:
        symbol: 交易对符号 (如 'BTCUSDT')
        interval: K线时间间隔 ('1m', '5m', '15m', '1h', '4h', '1d')
        start_date: 训练数据开始日期 (YYYY-MM-DD)
        model_type: 模型类型 ('rf', 'gb', 'xgb', 'lgb', 'ensemble', 'lstm')
        use_optuna: 是否使用Optuna进行超参数优化
        optuna_trials: Optuna优化试验次数
        lstm_timesteps: LSTM时间步长
        lstm_epochs: LSTM训练轮数
        save_plots: 是否保存训练图表
    """
    
    logger.info(f"开始训练模型: {symbol} {interval} {model_type}")
    logger.info(f"训练参数: start_date={start_date}, optuna={use_optuna}, trials={optuna_trials}")
    
    try:
        # 1. 数据获取
        logger.info("步骤 1/5: 获取历史数据...")
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(
            symbol=symbol,
            interval=interval, 
            start_date=start_date,
            is_futures=False
        )
        
        if df.empty:
            raise ValueError(f"无法获取 {symbol} 的历史数据")
        
        logger.info(f"获取到 {len(df)} 条历史数据")
        
        # 2. 特征工程
        logger.info("步骤 2/5: 特征工程...")
        
        # 配置特征工程参数
        feature_config = FeatureConfig(
            prediction_window=24,  # 预测未来24小时
            ma_periods=[5, 10, 20, 50, 120, 200],
            rsi_periods=[6, 12, 14, 24],
            enable_variance_threshold_selection=True,
            enable_correlation_selection=True,
            correlation_threshold=0.95,
            max_features_after_importance=100
        )
        
        engineer = FeatureEngineer(config=feature_config)
        df_features = engineer.create_features(df, force_refresh=True)
        
        # 检查目标变量
        if 'target' not in df_features.columns:
            raise ValueError("特征工程未生成目标变量 'target'")
        
        # 分离特征和目标
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始OHLCV列（可选）
        original_cols = ['open', 'high', 'low', 'close', 'volume']
        X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
        
        logger.info(f"特征工程完成: {X.shape[1]} 个特征, {len(y.unique())} 个类别")
        
        # 3. 模型配置
        logger.info("步骤 3/5: 配置模型...")
        
        # 创建模型保存目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path(f"models/{symbol}/{interval}/{model_type}_{timestamp}")
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置模型训练器
        trainer_config = {
            'model_type': model_type,
            'output_dir': model_dir,
            'use_optuna': use_optuna,
            'optuna_n_trials': optuna_trials,
            'n_splits': 5,  # 5折交叉验证
            'save_plots': save_plots,
            'scaler_type': 'robust',  # 使用稳健缩放器
            'num_classes': len(y.unique())
        }
        
        # LSTM特定配置
        if model_type == 'lstm':
            trainer_config.update({
                'lstm_timesteps': lstm_timesteps,
                'lstm_epochs': lstm_epochs,
                'lstm_units': 64,
                'lstm_dropout': 0.2,
                'lstm_learning_rate': 0.001,
                'lstm_batch_size': 32
            })
        
        trainer = ModelTrainer(**trainer_config)
        
        # 4. 模型训练
        logger.info("步骤 4/5: 开始训练模型...")
        
        results = trainer.train(X, y)
        
        # 5. 保存模型和结果
        logger.info("步骤 5/5: 保存模型和结果...")
        
        model_path, scaler_path = trainer.save_model()
        
        # 保存训练结果
        save_training_results(results, symbol, interval, model_type, timestamp, model_dir)
        
        # 打印训练结果摘要
        print_training_summary(results, model_type)
        
        logger.info(f"模型训练完成! 模型保存在: {model_dir}")
        
        return {
            'trainer': trainer,
            'results': results,
            'model_path': model_path,
            'scaler_path': scaler_path,
            'model_dir': model_dir
        }
        
    except Exception as e:
        logger.error(f"训练过程中发生错误: {str(e)}")
        raise

def save_training_results(results, symbol, interval, model_type, timestamp, model_dir):
    """保存训练结果到文件"""
    
    results_file = model_dir / f"training_results_{timestamp}.txt"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        f.write("=" * 60 + "\n")
        f.write(f"加密货币预测模型训练结果\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"交易对: {symbol}\n")
        f.write(f"时间间隔: {interval}\n") 
        f.write(f"模型类型: {model_type}\n")
        f.write(f"训练时间: {timestamp}\n\n")
        
        f.write("交叉验证结果:\n")
        f.write("-" * 30 + "\n")
        mean_scores = results.get('mean_scores', {})
        std_scores = results.get('std_scores', {})
        
        for metric in ['accuracy', 'precision', 'recall', 'f1']:
            if metric in mean_scores:
                mean_val = mean_scores[metric]
                std_val = std_scores.get(metric, 0)
                f.write(f"{metric.capitalize():12}: {mean_val:.4f} (+/- {std_val:.4f})\n")
        
        # 最佳参数
        if results.get('best_params'):
            f.write(f"\n最佳超参数:\n")
            f.write("-" * 30 + "\n")
            for param, value in results['best_params'].items():
                f.write(f"{param}: {value}\n")
        
        # 特征重要性
        if results.get('feature_importance') is not None:
            f.write(f"\n特征重要性 (Top 15):\n")
            f.write("-" * 30 + "\n")
            top_features = results['feature_importance'].head(15)
            for feature, importance in top_features.items():
                f.write(f"{feature:30}: {importance:.6f}\n")
    
    logger.info(f"训练结果已保存到: {results_file}")

def print_training_summary(results, model_type):
    """打印训练结果摘要"""
    
    print("\n" + "=" * 60)
    print(f"🎯 {model_type.upper()} 模型训练完成!")
    print("=" * 60)
    
    mean_scores = results.get('mean_scores', {})
    std_scores = results.get('std_scores', {})
    
    print("\n📊 性能指标:")
    print("-" * 30)
    for metric in ['accuracy', 'precision', 'recall', 'f1']:
        if metric in mean_scores:
            mean_val = mean_scores[metric]
            std_val = std_scores.get(metric, 0)
            print(f"{metric.capitalize():12}: {mean_val:.4f} ± {std_val:.4f}")
    
    # 特征重要性前5
    if results.get('feature_importance') is not None:
        print("\n🔍 Top 5 重要特征:")
        print("-" * 30)
        top_features = results['feature_importance'].head(5)
        for i, (feature, importance) in enumerate(top_features.items(), 1):
            print(f"{i}. {feature}: {importance:.4f}")
    
    print("\n✅ 训练完成!")

def main():
    """主函数 - 命令行接口"""
    
    parser = argparse.ArgumentParser(description="加密货币预测模型训练")
    
    # 基础参数
    parser.add_argument('--symbol', type=str, default='BTCUSDT', 
                       help='交易对符号 (默认: BTCUSDT)')
    parser.add_argument('--interval', type=str, default='1h',
                       choices=['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'],
                       help='K线时间间隔 (默认: 1h)')
    parser.add_argument('--start-date', type=str, default='2022-01-01',
                       help='训练数据开始日期 YYYY-MM-DD (默认: 2022-01-01)')
    parser.add_argument('--model-type', type=str, default='xgb',
                       choices=['rf', 'gb', 'xgb', 'lgb', 'ensemble', 'lstm'],
                       help='模型类型 (默认: xgb)')
    
    # 优化参数
    parser.add_argument('--no-optuna', action='store_true',
                       help='禁用Optuna超参数优化')
    parser.add_argument('--optuna-trials', type=int, default=50,
                       help='Optuna优化试验次数 (默认: 50)')
    
    # LSTM参数
    parser.add_argument('--lstm-timesteps', type=int, default=10,
                       help='LSTM时间步长 (默认: 10)')
    parser.add_argument('--lstm-epochs', type=int, default=50,
                       help='LSTM训练轮数 (默认: 50)')
    
    # 其他参数
    parser.add_argument('--no-plots', action='store_true',
                       help='不保存训练图表')
    
    args = parser.parse_args()
    
    # 执行训练
    try:
        result = train_crypto_model(
            symbol=args.symbol,
            interval=args.interval,
            start_date=args.start_date,
            model_type=args.model_type,
            use_optuna=not args.no_optuna,
            optuna_trials=args.optuna_trials,
            lstm_timesteps=args.lstm_timesteps,
            lstm_epochs=args.lstm_epochs,
            save_plots=not args.no_plots
        )
        
        print(f"\n🎉 训练成功完成!")
        print(f"📁 模型保存位置: {result['model_dir']}")
        
    except KeyboardInterrupt:
        print("\n❌ 训练被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 训练失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
