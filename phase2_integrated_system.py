#!/usr/bin/env python3
"""
第二阶段集成交易系统 - 策略扩展与智能化
整合多策略库、情绪分析、AI模型监控
"""

import pandas as pd
import numpy as np
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 导入第一阶段模块
from integrated_trading_system import IntegratedTradingSystem
from enhanced_risk_management import EnhancedRiskManager
from multi_timeframe_analysis import MultiTimeFrameAnalyzer

# 导入第二阶段新模块
from multi_strategy_library import StrategyManager
from sentiment_analysis import SentimentAnalyzer
from ai_model_monitor import AIModelMonitor

class Phase2TradingSystem(IntegratedTradingSystem):
    """
    第二阶段集成交易系统
    在第一阶段基础上增加多策略、情绪分析、AI监控
    """
    
    def __init__(self, initial_capital: float = 50.0, leverage: int = 2):
        # 继承第一阶段系统
        super().__init__(initial_capital, leverage)
        
        # 第二阶段新增模块
        self.strategy_manager = StrategyManager()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.ai_monitor = AIModelMonitor()
        
        # 第二阶段配置
        self.phase2_config = {
            'enable_multi_strategy': True,
            'enable_sentiment_analysis': True,
            'enable_ai_monitoring': True,
            'sentiment_weight': 0.15,           # 情绪分析权重
            'multi_strategy_weight': 0.4,       # 多策略权重
            'original_signal_weight': 0.45,     # 原始信号权重
            'min_strategy_agreement': 0.6,      # 最小策略一致性
            'sentiment_confirmation_threshold': 0.7  # 情绪确认阈值
        }
        
        # 状态文件更新
        self.state_file = "phase2_trading_state.json"
        
        # 性能跟踪
        self.phase2_performance = {
            'strategy_contributions': {},
            'sentiment_accuracy': [],
            'ai_monitoring_alerts': [],
            'decision_breakdown': []
        }
        
        print(f"🚀 第二阶段AI增强交易系统启动")
        print(f"   ✅ 多策略库: {len(self.strategy_manager.strategies)}个策略")
        print(f"   ✅ 情绪分析: {len(self.sentiment_analyzer.sentiment_sources)}个数据源")
        print(f"   ✅ AI监控: 实时性能监控")
        print(f"   📊 集成权重: 原始{self.phase2_config['original_signal_weight']:.0%} + 多策略{self.phase2_config['multi_strategy_weight']:.0%} + 情绪{self.phase2_config['sentiment_weight']:.0%}")
    
    def generate_phase2_signal(self, ai_probability: float, indicators: Dict) -> Dict:
        """
        生成第二阶段增强信号
        整合原始信号、多策略、情绪分析
        """
        # 1. 获取第一阶段原始信号
        original_signal = self.generate_integrated_signal(ai_probability, indicators)
        
        # 2. 获取多策略信号
        multi_strategy_signal = None
        if self.phase2_config['enable_multi_strategy']:
            try:
                # 准备策略数据
                strategy_data = self._prepare_strategy_data(indicators)
                multi_strategy_signal = self.strategy_manager.generate_combined_signal(strategy_data)
            except Exception as e:
                print(f"⚠️ 多策略信号生成失败: {str(e)}")
                multi_strategy_signal = self._get_default_strategy_signal()
        
        # 3. 获取情绪分析信号
        sentiment_signal = None
        if self.phase2_config['enable_sentiment_analysis']:
            try:
                sentiment_analysis = self.sentiment_analyzer.get_comprehensive_sentiment()
                sentiment_signal = sentiment_analysis['trading_signal']
            except Exception as e:
                print(f"⚠️ 情绪分析失败: {str(e)}")
                sentiment_signal = self._get_default_sentiment_signal()
        
        # 4. AI模型监控
        ai_monitoring_status = None
        if self.phase2_config['enable_ai_monitoring']:
            try:
                # 记录AI预测用于监控
                features = self._extract_features_for_monitoring(indicators)
                monitoring_result = self.ai_monitor.record_prediction(
                    features, ai_probability, confidence=original_signal.get('confidence', 0.5)
                )
                ai_monitoring_status = monitoring_result
            except Exception as e:
                print(f"⚠️ AI监控记录失败: {str(e)}")
                ai_monitoring_status = {'status': 'error'}
        
        # 5. 综合信号融合
        final_signal = self._fuse_all_signals(
            original_signal, 
            multi_strategy_signal, 
            sentiment_signal,
            ai_monitoring_status
        )
        
        # 6. 记录决策过程
        self._record_decision_breakdown(
            original_signal, multi_strategy_signal, sentiment_signal, final_signal
        )
        
        return final_signal
    
    def _prepare_strategy_data(self, indicators: Dict) -> Dict:
        """为策略准备数据"""
        # 获取价格历史用于统计套利
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            
            df = self.fetcher.get_historical_data(
                'BTCUSDT', '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            price_history = df['close'].tolist() if len(df) > 0 else []
        except:
            price_history = []
        
        return {
            'price': indicators['price'],
            'rsi': indicators['rsi'],
            'bb_position': indicators['bb_position'],
            'volume_ratio': indicators['volume_ratio'],
            'atr_percentage': indicators['atr_percentage'],
            'macd_trend': indicators['macd_trend'],
            'price_history': price_history
        }
    
    def _extract_features_for_monitoring(self, indicators: Dict) -> np.ndarray:
        """提取特征用于AI监控"""
        features = [
            indicators.get('rsi', 50),
            indicators.get('bb_position', 0.5),
            indicators.get('volume_ratio', 1.0),
            indicators.get('atr_percentage', 0.02),
            1.0 if indicators.get('macd_trend') == 'bullish' else 0.0,
            indicators.get('price', 104000) / 100000  # 标准化价格
        ]
        
        return np.array(features)
    
    def _fuse_all_signals(self, original_signal: Dict, multi_strategy_signal: Dict, 
                         sentiment_signal: Dict, ai_monitoring: Dict) -> Dict:
        """融合所有信号"""
        
        # 权重配置
        original_weight = self.phase2_config['original_signal_weight']
        strategy_weight = self.phase2_config['multi_strategy_weight']
        sentiment_weight = self.phase2_config['sentiment_weight']
        
        # 信号强度和方向收集
        signals = []
        
        # 1. 原始信号
        if original_signal['direction'] != 'WAIT':
            signals.append({
                'source': 'original',
                'direction': original_signal['direction'],
                'strength': original_signal['strength'],
                'confidence': original_signal['confidence'],
                'weight': original_weight
            })
        
        # 2. 多策略信号
        if multi_strategy_signal and multi_strategy_signal['direction'] != 'WAIT':
            # 检查策略一致性
            agreement_score = multi_strategy_signal.get('agreement_score', 0.5)
            if agreement_score >= self.phase2_config['min_strategy_agreement']:
                signals.append({
                    'source': 'multi_strategy',
                    'direction': multi_strategy_signal['direction'],
                    'strength': multi_strategy_signal['strength'],
                    'confidence': multi_strategy_signal['confidence'],
                    'weight': strategy_weight,
                    'agreement': agreement_score
                })
        
        # 3. 情绪信号
        if sentiment_signal and sentiment_signal['direction'] != 'WAIT':
            # 情绪信号作为确认或反向指标
            sentiment_confidence = sentiment_signal.get('confidence', 0.5)
            if sentiment_confidence >= self.phase2_config['sentiment_confirmation_threshold']:
                signals.append({
                    'source': 'sentiment',
                    'direction': sentiment_signal['direction'],
                    'strength': sentiment_signal['strength'],
                    'confidence': sentiment_confidence,
                    'weight': sentiment_weight,
                    'signal_type': sentiment_signal.get('signal_type', 'unknown')
                })
        
        # 4. 如果没有有效信号
        if not signals:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'reason': '所有信号源建议等待',
                'signal_breakdown': {
                    'original': original_signal,
                    'multi_strategy': multi_strategy_signal,
                    'sentiment': sentiment_signal
                },
                'ai_monitoring': ai_monitoring,
                'fusion_method': 'no_signals'
            }
        
        # 5. 加权融合计算
        weighted_long = 0
        weighted_short = 0
        total_weight = 0
        confidence_sum = 0
        
        signal_details = []
        
        for signal in signals:
            weight = signal['weight']
            strength = signal['strength']
            confidence = signal['confidence']
            
            total_weight += weight
            confidence_sum += confidence * weight
            
            if signal['direction'] == 'LONG':
                weighted_long += weight * strength
                signal_details.append(f"{signal['source']}看涨({strength:.1%})")
            elif signal['direction'] == 'SHORT':
                weighted_short += weight * strength
                signal_details.append(f"{signal['source']}看跌({strength:.1%})")
        
        # 6. 计算最终信号
        if total_weight == 0:
            final_direction = 'WAIT'
            final_strength = 0
            final_confidence = 0.3
        else:
            avg_confidence = confidence_sum / total_weight
            
            if weighted_long > weighted_short:
                final_direction = 'LONG'
                final_strength = weighted_long / total_weight
                signal_agreement = weighted_long / (weighted_long + weighted_short)
            elif weighted_short > weighted_long:
                final_direction = 'SHORT'
                final_strength = weighted_short / total_weight
                signal_agreement = weighted_short / (weighted_long + weighted_short)
            else:
                final_direction = 'WAIT'
                final_strength = 0
                signal_agreement = 0.5
            
            # 调整最终置信度
            final_confidence = avg_confidence * signal_agreement
        
        # 7. AI监控状态检查
        ai_warning = ""
        if ai_monitoring and ai_monitoring.get('drift_score', 0) > 0.1:
            final_confidence *= 0.8  # 降低置信度
            ai_warning = " (AI漂移警告)"
        
        return {
            'direction': final_direction,
            'strength': final_strength,
            'confidence': final_confidence,
            'reason': f"第二阶段融合: {', '.join(signal_details)}{ai_warning}",
            'signal_breakdown': {
                'original': original_signal,
                'multi_strategy': multi_strategy_signal,
                'sentiment': sentiment_signal
            },
            'fusion_details': {
                'active_signals': len(signals),
                'total_weight': total_weight,
                'signal_agreement': signal_agreement if 'signal_agreement' in locals() else 0.5,
                'confidence_sources': [s['source'] for s in signals]
            },
            'ai_monitoring': ai_monitoring,
            'fusion_method': 'weighted_average'
        }
    
    def _record_decision_breakdown(self, original: Dict, strategy: Dict, 
                                 sentiment: Dict, final: Dict):
        """记录决策分解过程"""
        breakdown = {
            'timestamp': datetime.now().isoformat(),
            'original_signal': {
                'direction': original['direction'],
                'strength': original['strength'],
                'confidence': original['confidence']
            },
            'strategy_signal': {
                'direction': strategy['direction'] if strategy else 'WAIT',
                'strength': strategy.get('strength', 0) if strategy else 0,
                'confidence': strategy.get('confidence', 0) if strategy else 0,
                'active_strategies': strategy.get('active_strategies', 0) if strategy else 0
            },
            'sentiment_signal': {
                'direction': sentiment['direction'] if sentiment else 'WAIT',
                'strength': sentiment.get('strength', 0) if sentiment else 0,
                'confidence': sentiment.get('confidence', 0) if sentiment else 0,
                'signal_type': sentiment.get('signal_type', 'none') if sentiment else 'none'
            },
            'final_signal': {
                'direction': final['direction'],
                'strength': final['strength'],
                'confidence': final['confidence'],
                'fusion_method': final.get('fusion_method', 'unknown')
            }
        }
        
        self.phase2_performance['decision_breakdown'].append(breakdown)
        
        # 保持记录在合理范围
        if len(self.phase2_performance['decision_breakdown']) > 100:
            self.phase2_performance['decision_breakdown'] = self.phase2_performance['decision_breakdown'][-100:]
    
    def run_phase2_cycle(self) -> bool:
        """运行第二阶段交易循环"""
        try:
            # 1. 获取AI预测
            ai_probability, current_price = self.get_ai_prediction()
            
            # 2. 计算技术指标
            indicators = self.calculate_technical_indicators()
            indicators['price'] = current_price
            
            # 3. 生成第二阶段增强信号
            signal = self.generate_phase2_signal(ai_probability, indicators)
            
            # 4. 检查止损止盈
            if self.position['size'] != 0:
                if self.check_stop_loss_take_profit(current_price, signal):
                    # 重新生成信号
                    signal = self.generate_phase2_signal(ai_probability, indicators)
            
            # 5. 执行交易决策
            if signal['direction'] in ['LONG', 'SHORT'] and self.position['size'] == 0:
                if self.risk_manager.should_trade(signal['confidence'], signal.get('risk_params', {})):
                    size_info = self.calculate_position_size(current_price, signal)
                    if size_info:
                        self.open_position(signal['direction'], current_price, size_info, signal)
                else:
                    print(f"🛡️ 风险管理阻止交易")
            
            # 6. 打印第二阶段状态
            self.print_phase2_status(ai_probability, indicators, signal)
            
            # 7. 记录权益历史
            unrealized_pnl = self.calculate_unrealized_pnl(current_price)
            total_equity = self.capital + unrealized_pnl
            
            equity_record = {
                'timestamp': datetime.now().isoformat(),
                'capital': self.capital,
                'unrealized_pnl': unrealized_pnl,
                'total_equity': total_equity,
                'btc_price': current_price,
                'ai_probability': ai_probability,
                'final_signal': signal['direction'],
                'signal_confidence': signal['confidence'],
                'active_strategies': signal.get('fusion_details', {}).get('active_signals', 0),
                'phase': 2
            }
            
            self.equity_history.append(equity_record)
            
            # 8. 保存状态
            self._save_phase2_state()
            
            return True
            
        except Exception as e:
            print(f"❌ 第二阶段交易循环失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def print_phase2_status(self, ai_probability: float, indicators: Dict, signal: Dict):
        """打印第二阶段状态"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_price = indicators['price']
        
        print(f"\n⏰ {current_time} - 第二阶段增强系统")
        print("=" * 130)
        
        # 账户状态
        unrealized_pnl = self.calculate_unrealized_pnl(current_price)
        total_equity = self.capital + unrealized_pnl
        total_return = (total_equity - self.initial_capital) / self.initial_capital * 100
        
        print(f"💰 账户: ${self.capital:.2f} + ${unrealized_pnl:+.2f} = ${total_equity:.2f} ({total_return:+.2f}%)")
        
        # 持仓状态
        if self.position['size'] != 0:
            hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            print(f"📊 持仓: 🔥 {self.position['side']} {abs(self.position['size']):.6f} BTC @ ${self.position['entry_price']:,.0f} | {hold_hours:.1f}h")
        else:
            print(f"📊 持仓: 💤 空仓 | BTC: ${current_price:,.0f}")
        
        # 信号分解显示
        breakdown = signal.get('signal_breakdown', {})
        
        print(f"\n🧠 信号分解分析:")
        
        # 原始信号
        original = breakdown.get('original', {})
        print(f"   🎯 原始信号: {original.get('direction', 'WAIT')} (强度:{original.get('strength', 0):.1%}, 置信度:{original.get('confidence', 0):.1%})")
        
        # 多策略信号
        strategy = breakdown.get('multi_strategy', {})
        if strategy:
            print(f"   📊 多策略: {strategy.get('direction', 'WAIT')} (强度:{strategy.get('strength', 0):.1%}, 一致性:{strategy.get('agreement_score', 0):.1%})")
            print(f"      活跃策略: {strategy.get('active_strategies', 0)}/{strategy.get('total_strategies', 0)}")
        
        # 情绪信号
        sentiment = breakdown.get('sentiment', {})
        if sentiment:
            print(f"   😊 情绪分析: {sentiment.get('direction', 'WAIT')} (强度:{sentiment.get('strength', 0):.1%}, 类型:{sentiment.get('signal_type', 'unknown')})")
        
        # 融合详情
        fusion = signal.get('fusion_details', {})
        print(f"   🔗 信号融合: {fusion.get('active_signals', 0)}个信号源, 一致性:{fusion.get('signal_agreement', 0):.1%}")
        
        # AI监控状态
        ai_monitoring = signal.get('ai_monitoring', {})
        if ai_monitoring:
            drift_score = ai_monitoring.get('drift_score', 0)
            print(f"   🔍 AI监控: 漂移分数:{drift_score:.3f} {'⚠️' if drift_score > 0.1 else '✅'}")
        
        # 最终决策
        print(f"\n🚀 第二阶段最终决策:")
        signal_emoji = {"LONG": "🟢", "SHORT": "🔴", "WAIT": "⏸️"}
        decision_color = signal_emoji.get(signal['direction'], '❓')
        
        print(f"   {decision_color} 决策: {signal['direction']}")
        print(f"   📊 置信度: {signal['confidence']:.1%} | 信号强度: {signal['strength']:.1%}")
        print(f"   💭 决策理由: {signal['reason']}")
        print(f"   🔧 融合方法: {signal.get('fusion_method', 'unknown')}")
        
        print("=" * 130)
    
    def _save_phase2_state(self):
        """保存第二阶段状态"""
        # 继承第一阶段保存方法，添加第二阶段数据
        self._save_state()  # 调用父类方法
        
        # 保存第二阶段特有数据
        phase2_data = {
            'phase2_config': self.phase2_config,
            'phase2_performance': self.phase2_performance,
            'strategy_manager_report': self.strategy_manager.get_strategy_report(),
            'ai_monitoring_report': self.ai_monitor.get_monitoring_report()
        }
        
        with open("phase2_additional_state.json", 'w', encoding='utf-8') as f:
            json.dump(phase2_data, f, indent=2, ensure_ascii=False, default=str)
    
    def _get_default_strategy_signal(self) -> Dict:
        """默认策略信号"""
        return {
            'direction': 'WAIT',
            'strength': 0,
            'confidence': 0.3,
            'reason': '策略信号生成失败',
            'active_strategies': 0,
            'total_strategies': 0
        }
    
    def _get_default_sentiment_signal(self) -> Dict:
        """默认情绪信号"""
        return {
            'direction': 'WAIT',
            'strength': 0,
            'confidence': 0.3,
            'reason': '情绪分析失败',
            'signal_type': 'error'
        }

def run_phase2_trading_system(check_interval: int = 300):
    """
    运行第二阶段AI增强交易系统
    """
    print("🚀 第二阶段AI增强交易系统 - 策略扩展与智能化")
    print("=" * 120)
    print("第二阶段升级特点:")
    print("• 🎯 多策略库: 网格交易 + 统计套利 + 动量反转 + 突破跟踪")
    print("• 😊 情绪分析: 恐慌贪婪指数 + 新闻情绪 + 社交媒体 + 链上指标")
    print("• 🔍 AI监控: 性能监控 + 漂移检测 + 自动重训练建议")
    print("• 🔗 信号融合: 多维度加权融合决策")
    print("• 📊 智能权重: 根据市场状态动态调整策略权重")
    print("")
    print("⚠️ 这是模拟交易，不涉及真实资金")
    print("🎯 第二阶段：策略扩展与智能化测试")
    print("")

    # 初始化第二阶段交易系统
    trader = Phase2TradingSystem(initial_capital=50.0, leverage=2)

    print(f"⏰ 交易循环间隔: {check_interval/60:.1f}分钟")
    print(f"🔄 按 Ctrl+C 停止系统")
    print("")

    cycle_count = 0
    start_time = datetime.now()

    try:
        while True:
            cycle_count += 1
            print(f"\n🔄 第 {cycle_count} 次第二阶段交易循环")

            # 执行第二阶段交易循环
            success = trader.run_phase2_cycle()

            if not success:
                print(f"❌ 第二阶段交易循环失败，等待下次重试...")

            # 检查是否爆仓
            if trader.capital < 5:
                print(f"\n💥 资金不足，停止交易")
                print(f"最终资金: ${trader.capital:.2f}")
                break

            # 生成阶段性报告
            if cycle_count % 12 == 0:  # 每12个循环(1小时)
                generate_phase2_report(trader, start_time, cycle_count)

            # 检查AI模型状态
            if cycle_count % 6 == 0:  # 每6个循环检查一次
                check_ai_model_status(trader)

            # 等待下次循环
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟后继续...")
            time.sleep(check_interval)

    except KeyboardInterrupt:
        print(f"\n🛑 用户停止第二阶段交易系统")

    except Exception as e:
        print(f"\n❌ 系统异常: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # 生成最终报告
        generate_phase2_final_report(trader, start_time, cycle_count)

def generate_phase2_report(trader: Phase2TradingSystem, start_time: datetime, cycle_count: int):
    """生成第二阶段阶段性报告"""
    current_time = datetime.now()
    runtime_hours = (current_time - start_time).total_seconds() / 3600

    print(f"\n📊 第二阶段阶段性报告 (运行{runtime_hours:.1f}小时, {cycle_count}个循环)")
    print("=" * 100)

    # 账户状态
    try:
        current_price = trader.fetcher.get_current_price('BTCUSDT', is_futures=True)
        unrealized_pnl = trader.calculate_unrealized_pnl(current_price)
        total_equity = trader.capital + unrealized_pnl
        total_return = (total_equity - trader.initial_capital) / trader.initial_capital * 100

        print(f"💰 账户表现:")
        print(f"   当前权益: ${total_equity:.2f} ({total_return:+.2f}%)")
        print(f"   年化收益率: {(total_return / runtime_hours * 24 * 365):+.1f}%")
    except:
        print(f"💰 账户状态获取失败")

    # 策略表现
    strategy_report = trader.strategy_manager.get_strategy_report()
    print(f"\n📈 策略表现:")
    print(f"   启用策略: {strategy_report['enabled_strategies']}/{strategy_report['total_strategies']}")

    for name, perf in strategy_report['strategy_performance'].items():
        if perf['signals_generated'] > 0:
            print(f"   {name}: {perf['signals_generated']}次信号, 成功率{perf['success_rate']:.1%}")

    # AI监控状态
    ai_report = trader.ai_monitor.get_monitoring_report()
    print(f"\n🔍 AI监控状态:")
    print(f"   总预测数: {ai_report['total_predictions']}")
    print(f"   活跃警报: {ai_report['active_alerts']}")

    if ai_report['last_evaluation']:
        perf = ai_report['last_evaluation']
        print(f"   最新准确率: {perf['accuracy']:.1%}")

    retrain_rec = ai_report['retrain_recommendation']
    print(f"   重训练建议: {retrain_rec['recommendation']}")

    # 情绪分析趋势
    try:
        sentiment_trend = trader.sentiment_analyzer.get_sentiment_trend()
        print(f"\n😊 情绪分析:")
        print(f"   情绪趋势: {sentiment_trend['trend']}")
        print(f"   趋势强度: {sentiment_trend['trend_strength']:.2f}")
    except:
        print(f"\n😊 情绪分析: 数据获取失败")

    print("=" * 100)

def check_ai_model_status(trader: Phase2TradingSystem):
    """检查AI模型状态"""
    try:
        retrain_rec = trader.ai_monitor.should_retrain_model()

        if retrain_rec['should_retrain']:
            print(f"\n⚠️ AI模型重训练建议:")
            print(f"   建议: {retrain_rec['recommendation']}")
            print(f"   理由: {', '.join(retrain_rec['reasons'])}")
            print(f"   置信度: {retrain_rec['confidence']:.1%}")

            # 可以在这里添加自动重训练逻辑
            # auto_retrain_model(trader)

    except Exception as e:
        print(f"⚠️ AI模型状态检查失败: {str(e)}")

def generate_phase2_final_report(trader: Phase2TradingSystem, start_time: datetime, cycle_count: int):
    """生成第二阶段最终报告"""
    end_time = datetime.now()
    runtime_hours = (end_time - start_time).total_seconds() / 3600

    print(f"\n📊 第二阶段最终报告")
    print("=" * 120)
    print(f"运行时间: {runtime_hours:.1f}小时 ({cycle_count}个循环)")

    # 最终账户状态
    try:
        current_price = trader.fetcher.get_current_price('BTCUSDT', is_futures=True)
        unrealized_pnl = trader.calculate_unrealized_pnl(current_price)
        final_equity = trader.capital + unrealized_pnl
        total_return = (final_equity - trader.initial_capital) / trader.initial_capital * 100

        print(f"\n💰 最终账户状态:")
        print(f"   最终权益: ${final_equity:.2f} ({total_return:+.2f}%)")
        print(f"   年化收益率: {(total_return / runtime_hours * 24 * 365):+.1f}%")
    except:
        print(f"💰 最终状态计算失败")

    # 详细策略分析
    strategy_report = trader.strategy_manager.get_strategy_report()
    print(f"\n📈 策略详细分析:")

    for name, perf in strategy_report['strategy_performance'].items():
        if perf['signals_generated'] > 0:
            print(f"   {name}:")
            print(f"      信号数: {perf['signals_generated']}, 成功率: {perf['success_rate']:.1%}")
            print(f"      平均收益: {perf['avg_return']:+.4f}, 总收益: {perf['total_return']:+.2f}")
            print(f"      近期表现: {perf['recent_success_rate']:.1%}")

    # AI监控总结
    ai_report = trader.ai_monitor.get_monitoring_report()
    print(f"\n🔍 AI监控总结:")
    print(f"   监控状态: {ai_report['monitoring_status']}")
    print(f"   总预测数: {ai_report['total_predictions']}")
    print(f"   性能评估: {ai_report['performance_evaluations']}次")

    if 'performance_trend' in ai_report:
        trend = ai_report['performance_trend']
        print(f"   性能趋势: {trend['trend']} (斜率: {trend['slope']:.4f})")

    # 决策分解统计
    decision_breakdown = trader.phase2_performance['decision_breakdown']
    if decision_breakdown:
        print(f"\n🧠 决策分解统计:")

        # 统计各信号源的使用频率
        original_signals = sum(1 for d in decision_breakdown if d['original_signal']['direction'] != 'WAIT')
        strategy_signals = sum(1 for d in decision_breakdown if d['strategy_signal']['direction'] != 'WAIT')
        sentiment_signals = sum(1 for d in decision_breakdown if d['sentiment_signal']['direction'] != 'WAIT')

        total_decisions = len(decision_breakdown)

        print(f"   总决策数: {total_decisions}")
        print(f"   原始信号激活: {original_signals}次 ({original_signals/total_decisions:.1%})")
        print(f"   策略信号激活: {strategy_signals}次 ({strategy_signals/total_decisions:.1%})")
        print(f"   情绪信号激活: {sentiment_signals}次 ({sentiment_signals/total_decisions:.1%})")

    # 第二阶段总结
    print(f"\n🎯 第二阶段总结:")
    print(f"✅ 多策略集成: {strategy_report['total_strategies']}个策略协同工作")
    print(f"✅ 情绪分析集成: 多维度市场情绪监控")
    print(f"✅ AI性能监控: 实时模型健康检查")
    print(f"✅ 智能信号融合: 多源信号加权决策")

    if total_return > 5:
        print(f"🎉 第二阶段测试优秀！系统显著提升")
    elif total_return > 0:
        print(f"📈 第二阶段测试良好，系统稳定盈利")
    elif total_return > -5:
        print(f"📊 第二阶段测试基本达标，小幅亏损可接受")
    else:
        print(f"🔧 第二阶段需要进一步优化")

    print(f"\n💾 详细数据已保存到:")
    print(f"   主状态: {trader.state_file}")
    print(f"   第二阶段数据: phase2_additional_state.json")
    print(f"🔄 准备进入第三阶段开发或继续优化测试")
    print("=" * 120)

if __name__ == "__main__":
    import sys

    # 解析命令行参数
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300  # 默认5分钟

    print("🎯 第二阶段AI增强交易系统测试")
    print("=" * 80)
    print("第二阶段目标:")
    print("1. ✅ 多策略库构建")
    print("2. ✅ 市场情绪分析集成")
    print("3. ✅ AI模型性能监控")
    print("4. 📊 智能信号融合")
    print("")

    # 启动第二阶段系统
    run_phase2_trading_system(interval)
