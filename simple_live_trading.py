#!/usr/bin/env python3
"""
简化版实时模拟交易系统 - 展示AI增强效果
初始资金: $50, 基于币安永续合约规则
"""

import pandas as pd
import numpy as np
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class SimpleLiveTrader:
    """
    简化版实时模拟交易器
    """
    
    def __init__(self, initial_capital: float = 50.0, leverage: int = 2):
        """
        初始化交易器
        """
        # 账户设置
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = leverage
        
        # 币安永续合约规则
        self.contract_rules = {
            'min_notional': 5.0,      # 最小名义价值 5 USDT
            'taker_fee': 0.0004,      # Taker手续费 0.04%
        }
        
        # 持仓状态
        self.position = {
            'size': 0.0,              # 持仓数量 (BTC)
            'side': None,             # 'LONG' 或 'SHORT'
            'entry_price': 0.0,       # 入场价格
            'entry_time': None,       # 入场时间
            'margin_used': 0.0,       # 占用保证金
        }
        
        # 交易记录
        self.trades = []
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0
        }
        
        # 模拟价格数据
        self.current_price = 104226.80
        self.price_history = []
        
        print(f"🚀 简化版实时模拟交易系统启动")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   合约规则: 币安永续合约标准")
    
    def simulate_price_movement(self) -> float:
        """模拟价格变动"""
        # 添加随机价格变动
        change_pct = np.random.normal(0, 0.015)  # 1.5%标准差
        self.current_price *= (1 + change_pct)
        
        # 记录价格历史
        self.price_history.append({
            'timestamp': datetime.now(),
            'price': self.current_price
        })
        
        # 只保留最近100个价格点
        if len(self.price_history) > 100:
            self.price_history = self.price_history[-100:]
        
        return self.current_price
    
    def get_ai_prediction(self) -> float:
        """获取AI预测 (模拟)"""
        # 基础概率 + 随机变化
        base_prob = 0.372
        noise = np.random.normal(0, 0.08)
        probability = np.clip(base_prob + noise, 0.15, 0.85)
        return probability
    
    def calculate_technical_indicators(self) -> Dict:
        """计算技术指标 (简化版)"""
        if len(self.price_history) < 20:
            return {
                'rsi': 50.0,
                'macd_trend': 'neutral',
                'bb_position': 0.5,
                'volume_ratio': 1.0
            }
        
        # 提取价格
        prices = [p['price'] for p in self.price_history[-20:]]
        
        # 简化RSI计算
        changes = np.diff(prices)
        gains = np.where(changes > 0, changes, 0)
        losses = np.where(changes < 0, -changes, 0)
        
        avg_gain = np.mean(gains[-14:]) if len(gains) >= 14 else np.mean(gains)
        avg_loss = np.mean(losses[-14:]) if len(losses) >= 14 else np.mean(losses)
        
        if avg_loss == 0:
            rsi = 100
        else:
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
        
        # 简化MACD
        if len(prices) >= 12:
            ema_12 = np.mean(prices[-12:])
            ema_26 = np.mean(prices[-20:]) if len(prices) >= 20 else np.mean(prices)
            macd_trend = 'bullish' if ema_12 > ema_26 else 'bearish'
        else:
            macd_trend = 'neutral'
        
        # 简化布林带位置
        sma_20 = np.mean(prices)
        std_20 = np.std(prices)
        upper_band = sma_20 + (std_20 * 2)
        lower_band = sma_20 - (std_20 * 2)
        
        if upper_band != lower_band:
            bb_position = (self.current_price - lower_band) / (upper_band - lower_band)
        else:
            bb_position = 0.5
        
        return {
            'rsi': rsi,
            'macd_trend': macd_trend,
            'bb_position': bb_position,
            'volume_ratio': np.random.uniform(0.8, 1.5)  # 模拟成交量比率
        }
    
    def generate_trading_signal(self, ai_probability: float, indicators: Dict) -> Dict:
        """生成交易信号"""
        signals = []
        
        # AI增强信号
        if ai_probability < 0.35:  # 强看跌
            confirmations = 0
            if indicators['rsi'] > 50:
                confirmations += 1
            if indicators['macd_trend'] == 'bearish':
                confirmations += 1
            if indicators['bb_position'] > 0.6:
                confirmations += 1
            
            if confirmations >= 2:
                signals.append({
                    'direction': 'SHORT',
                    'strength': 0.8,
                    'reason': f'AI强看跌({ai_probability:.1%})+{confirmations}个指标确认',
                    'confidence': 0.75
                })
        
        elif ai_probability > 0.65:  # 强看涨
            confirmations = 0
            if indicators['rsi'] < 50:
                confirmations += 1
            if indicators['macd_trend'] == 'bullish':
                confirmations += 1
            if indicators['bb_position'] < 0.4:
                confirmations += 1
            
            if confirmations >= 2:
                signals.append({
                    'direction': 'LONG',
                    'strength': 0.8,
                    'reason': f'AI强看涨({ai_probability:.1%})+{confirmations}个指标确认',
                    'confidence': 0.75
                })
        
        elif ai_probability < 0.45:  # 弱看跌
            confirmations = 0
            if indicators['rsi'] > 55:
                confirmations += 1
            if indicators['macd_trend'] == 'bearish':
                confirmations += 1
            if indicators['bb_position'] > 0.7:
                confirmations += 1
            if indicators['volume_ratio'] > 1.2:
                confirmations += 1
            
            if confirmations >= 3:
                signals.append({
                    'direction': 'SHORT',
                    'strength': 0.6,
                    'reason': f'AI弱看跌({ai_probability:.1%})+{confirmations}个指标强确认',
                    'confidence': 0.65
                })
        
        elif ai_probability > 0.55:  # 弱看涨
            confirmations = 0
            if indicators['rsi'] < 45:
                confirmations += 1
            if indicators['macd_trend'] == 'bullish':
                confirmations += 1
            if indicators['bb_position'] < 0.3:
                confirmations += 1
            if indicators['volume_ratio'] > 1.2:
                confirmations += 1
            
            if confirmations >= 3:
                signals.append({
                    'direction': 'LONG',
                    'strength': 0.6,
                    'reason': f'AI弱看涨({ai_probability:.1%})+{confirmations}个指标强确认',
                    'confidence': 0.65
                })
        
        # 返回最强信号
        if signals:
            best_signal = max(signals, key=lambda x: x['strength'] * x['confidence'])
            return best_signal
        else:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'reason': f'AI中性({ai_probability:.1%})，技术指标确认不足',
                'confidence': 0.5
            }
    
    def calculate_position_size(self, entry_price: float, confidence: float) -> Optional[Dict]:
        """计算仓位大小"""
        # 可用资金 (保留20%作为缓冲)
        available_capital = self.capital * 0.8
        
        # 基础风险金额 (最大2%资金风险)
        max_risk = self.capital * 0.02
        
        # 根据置信度调整风险
        confidence_adj = min(confidence * 1.5, 1.0)
        risk_amount = max_risk * confidence_adj
        
        # 计算仓位价值 (假设2.5%止损)
        stop_loss_pct = 0.025
        position_value = risk_amount / stop_loss_pct
        
        # 应用杠杆
        margin_required = position_value / self.leverage
        
        # 检查限制
        if margin_required > available_capital:
            margin_required = available_capital
            position_value = margin_required * self.leverage
        
        # 计算BTC数量
        btc_size = position_value / entry_price
        
        # 检查最小订单限制
        if position_value < self.contract_rules['min_notional']:
            return None
        
        return {
            'btc_size': btc_size,
            'position_value': position_value,
            'margin_required': margin_required,
            'risk_amount': risk_amount
        }
    
    def calculate_unrealized_pnl(self) -> float:
        """计算未实现盈亏"""
        if self.position['size'] == 0:
            return 0.0
        
        entry_price = self.position['entry_price']
        
        if self.position['side'] == 'LONG':
            price_diff = self.current_price - entry_price
        else:  # SHORT
            price_diff = entry_price - self.current_price
        
        # 计算盈亏 (考虑杠杆)
        pnl = (price_diff / entry_price) * self.position['margin_used'] * self.leverage
        
        return pnl
    
    def open_position(self, side: str, entry_price: float, size_info: Dict, reason: str, confidence: float) -> bool:
        """开仓"""
        if self.position['size'] != 0:
            return False
        
        btc_size = size_info['btc_size']
        margin_required = size_info['margin_required']
        
        # 计算手续费
        position_value = btc_size * entry_price
        fee = position_value * self.contract_rules['taker_fee']
        
        # 检查资金充足
        if margin_required + fee > self.capital:
            return False
        
        # 扣除手续费
        self.capital -= fee
        
        # 设置持仓
        self.position = {
            'size': btc_size if side == 'LONG' else -btc_size,
            'side': side,
            'entry_price': entry_price,
            'entry_time': datetime.now(),
            'margin_used': margin_required
        }
        
        # 记录交易
        self.trades.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'OPEN',
            'side': side,
            'size': btc_size,
            'price': entry_price,
            'margin': margin_required,
            'fee': fee,
            'reason': reason,
            'confidence': confidence
        })
        
        self.performance_stats['total_trades'] += 1
        
        print(f"✅ 开仓: {side} {btc_size:.6f} BTC @ ${entry_price:,.2f}")
        print(f"   理由: {reason}")
        print(f"   剩余资金: ${self.capital:.2f}")
        
        return True
    
    def close_position(self, current_price: float, reason: str) -> bool:
        """平仓"""
        if self.position['size'] == 0:
            return False
        
        # 计算盈亏
        unrealized_pnl = self.calculate_unrealized_pnl()
        
        # 计算手续费
        position_value = abs(self.position['size']) * current_price
        closing_fee = position_value * self.contract_rules['taker_fee']
        
        # 最终盈亏
        final_pnl = unrealized_pnl - closing_fee
        
        # 释放保证金并结算盈亏
        self.capital += self.position['margin_used'] + final_pnl
        
        # 更新统计
        if final_pnl > 0:
            self.performance_stats['winning_trades'] += 1
        else:
            self.performance_stats['losing_trades'] += 1
        
        self.performance_stats['total_pnl'] += final_pnl
        
        # 计算胜率
        if self.performance_stats['total_trades'] > 0:
            self.performance_stats['win_rate'] = self.performance_stats['winning_trades'] / self.performance_stats['total_trades']
        
        # 记录交易
        hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
        
        self.trades.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'CLOSE',
            'side': self.position['side'],
            'entry_price': self.position['entry_price'],
            'exit_price': current_price,
            'final_pnl': final_pnl,
            'hold_hours': hold_hours,
            'reason': reason
        })
        
        print(f"✅ 平仓: {self.position['side']} @ ${current_price:,.2f}")
        print(f"   持仓时间: {hold_hours:.1f}小时")
        print(f"   最终盈亏: ${final_pnl:+.2f}")
        print(f"   当前资金: ${self.capital:.2f}")
        
        # 重置持仓
        self.position = {
            'size': 0.0,
            'side': None,
            'entry_price': 0.0,
            'entry_time': None,
            'margin_used': 0.0
        }
        
        return True

    def check_stop_loss_take_profit(self) -> bool:
        """检查止损止盈"""
        if self.position['size'] == 0:
            return False

        entry_price = self.position['entry_price']
        side = self.position['side']

        # 计算止损止盈价格
        stop_loss_pct = 0.025  # 2.5%止损
        take_profit_pct = 0.05  # 5%止盈

        if side == 'LONG':
            stop_loss_price = entry_price * (1 - stop_loss_pct)
            take_profit_price = entry_price * (1 + take_profit_pct)

            if self.current_price <= stop_loss_price:
                self.close_position(self.current_price, f"止损触发")
                return True
            elif self.current_price >= take_profit_price:
                self.close_position(self.current_price, f"止盈触发")
                return True

        else:  # SHORT
            stop_loss_price = entry_price * (1 + stop_loss_pct)
            take_profit_price = entry_price * (1 - take_profit_pct)

            if self.current_price >= stop_loss_price:
                self.close_position(self.current_price, f"止损触发")
                return True
            elif self.current_price <= take_profit_price:
                self.close_position(self.current_price, f"止盈触发")
                return True

        return False

    def print_status(self, ai_probability: float, indicators: Dict, signal: Dict):
        """打印当前状态"""
        current_time = datetime.now().strftime('%H:%M:%S')

        print(f"\n⏰ {current_time}")
        print("=" * 60)

        # 账户状态
        unrealized_pnl = self.calculate_unrealized_pnl()
        total_equity = self.capital + unrealized_pnl
        total_return = (total_equity - self.initial_capital) / self.initial_capital * 100

        print(f"💰 账户: ${self.capital:.2f} + ${unrealized_pnl:+.2f} = ${total_equity:.2f} ({total_return:+.1f}%)")

        # 持仓状态
        if self.position['size'] != 0:
            hold_hours = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            print(f"📊 持仓: {self.position['side']} {abs(self.position['size']):.6f} BTC @ ${self.position['entry_price']:,.0f} ({hold_hours:.1f}h)")
        else:
            print(f"📊 持仓: 空仓")

        print(f"💎 BTC: ${self.current_price:,.0f}")

        # AI分析
        print(f"🤖 AI: {ai_probability:.1%}↑ {1-ai_probability:.1%}↓")

        # 技术指标
        print(f"📈 指标: RSI={indicators['rsi']:.0f} MACD={indicators['macd_trend']} BB={indicators['bb_position']:.1%}")

        # 交易信号
        signal_emoji = {"LONG": "🟢", "SHORT": "🔴", "WAIT": "⏸️"}
        print(f"🎯 信号: {signal_emoji.get(signal['direction'], '❓')} {signal['direction']} ({signal['confidence']:.0%})")
        print(f"   理由: {signal['reason']}")

        # 绩效统计
        stats = self.performance_stats
        if stats['total_trades'] > 0:
            print(f"📊 绩效: {stats['total_trades']}笔 胜率{stats['win_rate']:.0%} 总盈亏${stats['total_pnl']:+.2f}")

        print("=" * 60)

    def run_single_cycle(self) -> bool:
        """运行单次交易循环"""
        try:
            # 1. 模拟价格变动
            self.simulate_price_movement()

            # 2. 获取AI预测
            ai_probability = self.get_ai_prediction()

            # 3. 计算技术指标
            indicators = self.calculate_technical_indicators()

            # 4. 生成交易信号
            signal = self.generate_trading_signal(ai_probability, indicators)

            # 5. 检查止损止盈
            if self.position['size'] != 0:
                if self.check_stop_loss_take_profit():
                    # 如果触发止损止盈，重新生成信号
                    signal = self.generate_trading_signal(ai_probability, indicators)

            # 6. 执行交易决策
            if signal['direction'] in ['LONG', 'SHORT'] and self.position['size'] == 0:
                # 开新仓
                size_info = self.calculate_position_size(self.current_price, signal['confidence'])
                if size_info:
                    self.open_position(
                        signal['direction'],
                        self.current_price,
                        size_info,
                        signal['reason'],
                        signal['confidence']
                    )

            # 7. 打印状态
            self.print_status(ai_probability, indicators, signal)

            return True

        except Exception as e:
            print(f"❌ 交易循环执行失败: {str(e)}")
            return False

def run_simple_live_trading(check_interval: int = 60):
    """
    运行简化版实时模拟交易系统
    """
    print("🚀 简化版实时模拟交易系统")
    print("=" * 60)
    print("🎯 让我们看看AI增强系统的真实表现！")
    print("")
    print("💰 初始资金: $50")
    print("🔧 杠杆: 2x")
    print("📊 止损/止盈: 2.5% / 5%")
    print("⏰ 循环间隔: 1分钟")
    print("⚠️ 这是模拟交易，不涉及真实资金")
    print("")
    print("🔄 按 Ctrl+C 停止系统")
    print("")

    # 初始化交易器
    trader = SimpleLiveTrader(initial_capital=50.0, leverage=2)

    cycle_count = 0

    try:
        while True:
            cycle_count += 1
            print(f"\n🔄 第 {cycle_count} 次循环")

            # 执行交易循环
            success = trader.run_single_cycle()

            if not success:
                print(f"❌ 交易循环失败")

            # 检查是否爆仓
            if trader.capital < 5:  # 资金低于5美元
                print(f"\n💥 资金不足，停止交易")
                break

            # 等待下次循环
            time.sleep(check_interval)

    except KeyboardInterrupt:
        print(f"\n🛑 用户停止交易系统")

    except Exception as e:
        print(f"\n❌ 系统异常: {str(e)}")

    finally:
        # 最终统计
        print(f"\n📊 最终交易统计:")
        print("=" * 40)

        final_equity = trader.capital + trader.calculate_unrealized_pnl()
        total_return = (final_equity - trader.initial_capital) / trader.initial_capital * 100

        print(f"初始资金: ${trader.initial_capital}")
        print(f"最终权益: ${final_equity:.2f}")
        print(f"总收益率: {total_return:+.2f}%")
        print(f"总交易数: {trader.performance_stats['total_trades']}")

        if trader.performance_stats['total_trades'] > 0:
            print(f"胜率: {trader.performance_stats['win_rate']:.1%}")
            print(f"总盈亏: ${trader.performance_stats['total_pnl']:+.2f}")

            if total_return > 0:
                print(f"🎉 恭喜！AI增强系统盈利了！")
            elif total_return > -10:
                print(f"📈 表现不错，小幅亏损在可接受范围")
            else:
                print(f"📉 需要继续优化策略")

        print(f"\n💡 这展示了AI从单一概率到完整交易系统的威力！")

if __name__ == "__main__":
    import sys

    # 解析命令行参数
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 60  # 默认1分钟

    print("🎯 AI增强交易系统实战测试")
    print("=" * 50)
    print("从37.2%概率到完整交易决策的华丽转身！")
    print("")

    # 启动系统
    run_simple_live_trading(interval)
