#!/usr/bin/env python3
"""
平衡生产策略 - 在交易频率和胜率之间找到平衡
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import joblib
import json
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

def test_multiple_thresholds():
    """
    测试多个置信度阈值找到最佳平衡点
    """
    print("🔧 寻找最佳平衡点")
    print("=" * 50)
    
    # 使用最新模型
    import glob
    model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
    if not model_files:
        print("❌ 未找到BTCUSDT模型文件")
        return
    
    model_path = max(model_files, key=lambda x: x.split('_')[-1])
    
    # 测试不同的置信度阈值
    thresholds = [0.55, 0.58, 0.60, 0.62, 0.65, 0.68, 0.70]
    stop_loss = 0.04
    
    results = []
    
    for threshold in thresholds:
        print(f"\n📊 测试置信度阈值: {threshold}")
        
        try:
            result = run_balanced_backtest(model_path, threshold, stop_loss)
            results.append({
                'threshold': threshold,
                'total_return': result['total_return'],
                'win_rate': result['win_rate'],
                'total_trades': result['total_trades'],
                'sharpe_ratio': result.get('sharpe_ratio', 0),
                'score': calculate_balance_score(result)
            })
            
            print(f"   收益: {result['total_return']:.2%}, 胜率: {result['win_rate']:.2%}, 交易: {result['total_trades']}")
            
        except Exception as e:
            print(f"   ❌ 失败: {str(e)}")
    
    # 分析结果
    if results:
        print(f"\n📊 平衡点分析:")
        print("=" * 70)
        print(f"{'阈值':<8} {'收益率':<10} {'胜率':<10} {'交易数':<8} {'夏普':<8} {'综合分':<8}")
        print("-" * 70)
        
        for r in results:
            print(f"{r['threshold']:<8} {r['total_return']:>8.2%} {r['win_rate']:>8.2%} "
                  f"{r['total_trades']:>6d} {r['sharpe_ratio']:>6.2f} {r['score']:>6.2f}")
        
        # 找到最佳平衡点
        best_balance = max(results, key=lambda x: x['score'])
        best_return = max(results, key=lambda x: x['total_return'])
        
        print(f"\n🏆 最佳平衡点:")
        print(f"   阈值: {best_balance['threshold']}")
        print(f"   收益率: {best_balance['total_return']:.2%}")
        print(f"   胜率: {best_balance['win_rate']:.2%}")
        print(f"   交易数: {best_balance['total_trades']}")
        print(f"   综合分: {best_balance['score']:.2f}")
        
        print(f"\n💰 最高收益点:")
        print(f"   阈值: {best_return['threshold']}")
        print(f"   收益率: {best_return['total_return']:.2%}")
        print(f"   胜率: {best_return['win_rate']:.2%}")
        print(f"   交易数: {best_return['total_trades']}")
        
        return best_balance['threshold'], best_return['threshold']
    
    return None, None

def calculate_balance_score(result):
    """
    计算平衡分数 - 综合考虑收益率、胜率和交易频率
    """
    return_score = result['total_return'] * 100  # 收益率权重
    winrate_score = result['win_rate'] * 50      # 胜率权重
    trade_score = min(result['total_trades'] / 20, 2) * 25  # 交易频率权重(最多50分)
    
    return return_score + winrate_score + trade_score

def run_balanced_backtest(model_path, confidence_threshold, stop_loss_ratio):
    """
    运行平衡回测
    """
    # 加载模型
    model_data = joblib.load(model_path)
    model = model_data['model']
    scaler = model_data['scaler']
    
    # 获取测试数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*30)
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data('BTCUSDT', '1h', start_date.strftime('%Y-%m-%d'))
    
    # 特征工程
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    # 清理数据
    X = df_features.drop(columns=['target'], errors='ignore')
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    mask = ~X.isna().any(axis=1)
    X = X[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    # 预测
    X_scaled = scaler.transform(X)
    prediction_proba = model.predict_proba(X_scaled)
    up_proba = prediction_proba[:, 1]
    
    # 回测
    capital = 50
    position = 0
    trades = []
    equity_curve = []
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    for i in range(len(up_proba)):
        price = prices[i]
        up_prob = up_proba[i]
        
        # 买入信号
        if up_prob > confidence_threshold and position == 0:
            position = 1
            entry_price = price
            trades.append({
                'type': '买入',
                'price': price,
                'time': timestamps[i],
                'confidence': up_prob
            })
        
        # 卖出信号
        elif position == 1 and (up_prob < (1 - confidence_threshold) or 
                               (price / entry_price - 1) < -stop_loss_ratio):
            position = 0
            pnl_ratio = (price - entry_price) / entry_price
            capital = capital * (1 + pnl_ratio - 0.002)
            
            trades.append({
                'type': '卖出',
                'price': price,
                'entry_price': entry_price,
                'pnl_ratio': pnl_ratio,
                'time': timestamps[i],
                'confidence': up_prob
            })
        
        # 记录权益
        if position == 1:
            current_value = capital * (price / entry_price)
        else:
            current_value = capital
        
        equity_curve.append(current_value)
    
    # 最后平仓
    if position == 1:
        final_pnl = (prices[-1] - entry_price) / entry_price
        capital = capital * (1 + final_pnl - 0.002)
    
    # 计算结果
    total_return = (capital - 50) / 50
    buy_hold_return = (prices[-1] - prices[0]) / prices[0]
    
    profitable_trades = len([t for t in trades if t.get('pnl_ratio', 0) > 0])
    total_completed_trades = len([t for t in trades if 'pnl_ratio' in t])
    win_rate = profitable_trades / total_completed_trades if total_completed_trades > 0 else 0
    
    # 最大回撤
    peak = np.maximum.accumulate(equity_curve)
    drawdown = (np.array(equity_curve) - peak) / peak
    max_drawdown = np.min(drawdown)
    
    # 夏普比率
    returns = np.diff(equity_curve) / equity_curve[:-1]
    sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(24*365) if np.std(returns) > 0 else 0
    
    return {
        'total_return': total_return,
        'benchmark_return': buy_hold_return,
        'win_rate': win_rate,
        'max_drawdown': max_drawdown,
        'total_trades': total_completed_trades,
        'final_capital': capital,
        'sharpe_ratio': sharpe_ratio
    }

def create_final_recommendation():
    """
    创建最终推荐策略
    """
    print("\n🎯 创建最终推荐策略")
    print("=" * 50)
    
    # 测试多个阈值
    best_balance, best_return = test_multiple_thresholds()
    
    if best_balance and best_return:
        print(f"\n📋 最终推荐:")
        print(f"🎯 平衡策略: 置信度阈值 {best_balance}, 止损 4%")
        print(f"💰 激进策略: 置信度阈值 {best_return}, 止损 4%")
        
        # 创建最终配置
        final_config = {
            "strategy_recommendations": {
                "balanced_strategy": {
                    "confidence_threshold": best_balance,
                    "stop_loss_ratio": 0.04,
                    "description": "平衡收益率和交易频率的策略"
                },
                "aggressive_strategy": {
                    "confidence_threshold": best_return,
                    "stop_loss_ratio": 0.04,
                    "description": "追求最高收益率的策略"
                }
            },
            "usage_recommendation": {
                "conservative_trader": "使用平衡策略",
                "aggressive_trader": "使用激进策略",
                "beginner": "建议从平衡策略开始"
            },
            "next_steps": [
                "选择适合的策略参数",
                "进行小额实盘测试",
                "监控实际表现",
                "根据结果调整参数"
            ]
        }
        
        config_path = "final_strategy_recommendation.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(final_config, f, indent=2, ensure_ascii=False)
        
        print(f"📝 最终推荐已保存: {config_path}")
        
        return final_config
    
    return None

if __name__ == "__main__":
    print("🔧 寻找最佳平衡策略")
    print("=" * 60)
    print("目标: 在收益率、胜率和交易频率之间找到最佳平衡点")
    
    config = create_final_recommendation()
    
    if config:
        print(f"\n🎉 策略优化完成!")
        print(f"现在您有了科学验证的最佳参数组合")
        print(f"建议根据您的风险偏好选择合适的策略")
    else:
        print(f"\n❌ 策略优化失败，请检查数据和模型")
