#!/usr/bin/env python3
"""
左侧交易增强版系统
支持逆向交易策略，在趋势反转前提前进场
"""

import pandas as pd
import numpy as np
import time
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings

# 禁用所有警告和详细日志
warnings.filterwarnings('ignore')
logging.getLogger().setLevel(logging.ERROR)

# 导入组件
from enhanced_real_sentiment import EnhancedRealSentimentAnalyzer
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class LeftSideTrader:
    """
    左侧交易系统
    
    特点：
    - 🎯 逆向交易策略
    - 📊 在趋势反转前进场
    - 💰 50美元模拟账户
    - 🔄 支持分批建仓
    """
    
    def __init__(self, initial_balance: float = 50.0):
        self.initial_balance = initial_balance
        
        # 账户状态
        self.account = {
            'balance': initial_balance,
            'equity': initial_balance,
            'unrealized_pnl': 0.0
        }
        
        # 持仓信息
        self.position = {
            'side': None,
            'size': 0.0,
            'entry_price': 0.0,
            'entry_time': None,
            'leverage': 2,
            'batches': []  # 分批建仓记录
        }
        
        # 左侧交易配置
        self.config = {
            'leverage': 2,
            'trading_mode': 'left_side',    # 左侧交易模式
            'stop_loss_pct': 0.025,         # 2.5%止损（更紧）
            'take_profit_pct': 0.05,        # 5%止盈（更小）
            'min_confidence': 0.45,         # 45%置信度（更低）
            'trading_fee': 0.0004,
            'batch_size_ratio': 0.3,        # 每批30%资金
            'max_batches': 3,               # 最多3批
            'rsi_oversold': 35,             # RSI超卖线（放宽）
            'rsi_overbought': 65,           # RSI超买线（放宽）
            'contrarian_strength': 0.7      # 逆向信号强度
        }
        
        # 初始化组件
        print("🎯 初始化左侧交易系统...")
        
        self.data_fetcher = BinanceDataFetcher()
        self.feature_engineer = FeatureEngineer()
        self.sentiment_analyzer = EnhancedRealSentimentAnalyzer()
        
        # 交易记录
        self.trade_history = []
        self.last_trade_time = None
        
        # 状态文件
        self.state_file = "left_side_trader_state.json"
        self.load_state()
        
        print("✅ 左侧交易系统初始化完成")
        print(f"   交易模式: 逆向交易（左侧）")
        print(f"   分批建仓: 最多{self.config['max_batches']}批")
        print(f"   风险控制: {self.config['stop_loss_pct']:.1%}止损")
    
    def get_market_data(self) -> Dict:
        """获取市场数据"""
        try:
            # 获取当前价格
            current_price = self.data_fetcher.get_current_price('BTCUSDT', is_futures=True)
            
            # 获取历史数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            
            df = self.data_fetcher.get_historical_data(
                'BTCUSDT', '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            # 计算技术指标
            features_df = self.feature_engineer.create_features(df)
            latest_features = features_df.iloc[-1]
            
            # 计算额外的左侧交易指标
            recent_prices = df['close'].tail(24)  # 24小时价格
            
            # 价格偏离度
            sma_20 = recent_prices.rolling(20).mean().iloc[-1]
            price_deviation = (current_price - sma_20) / sma_20
            
            # 波动率
            volatility = recent_prices.pct_change().std()
            
            # 价格位置（相对于近期高低点）
            recent_high = recent_prices.max()
            recent_low = recent_prices.min()
            price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5
            
            return {
                'current_price': current_price,
                'rsi': latest_features.get('RSI_14', 50),
                'bb_position': latest_features.get('BB_position', 0.5),
                'macd_signal': latest_features.get('MACD_signal', 0),
                'price_deviation': price_deviation,
                'volatility': volatility,
                'price_position': price_position,
                'recent_high': recent_high,
                'recent_low': recent_low,
                'sma_20': sma_20,
                'price_change_24h': ((current_price - df['close'].iloc[-24]) / df['close'].iloc[-24]) if len(df) >= 24 else 0
            }
            
        except Exception as e:
            print(f"❌ 市场数据获取失败: {str(e)}")
            return None
    
    def generate_left_side_signal(self, market_data: Dict) -> Dict:
        """生成左侧交易信号"""
        try:
            # 提取指标
            rsi = market_data['rsi']
            bb_position = market_data['bb_position']
            price_deviation = market_data['price_deviation']
            price_position = market_data['price_position']
            volatility = market_data['volatility']
            price_change_24h = market_data['price_change_24h']
            
            # 左侧交易信号计算
            signals = []
            signal_weights = []
            
            # 1. RSI逆向信号（权重40%）
            rsi_signal = 0.5
            if rsi <= self.config['rsi_oversold']:  # 超卖买入
                rsi_signal = 0.8 + (35 - rsi) / 35 * 0.2  # RSI越低信号越强
            elif rsi >= self.config['rsi_overbought']:  # 超买卖出
                rsi_signal = 0.2 - (rsi - 65) / 35 * 0.2  # RSI越高信号越弱
            
            signals.append(rsi_signal)
            signal_weights.append(0.40)
            
            # 2. 价格偏离逆向信号（权重25%）
            deviation_signal = 0.5
            if price_deviation < -0.03:  # 价格低于均线3%以上，看涨
                deviation_signal = 0.75 + abs(price_deviation) * 5
            elif price_deviation > 0.03:  # 价格高于均线3%以上，看跌
                deviation_signal = 0.25 - price_deviation * 5
            
            signals.append(max(0, min(1, deviation_signal)))
            signal_weights.append(0.25)
            
            # 3. 价格位置逆向信号（权重20%）
            position_signal = 0.5
            if price_position < 0.2:  # 接近低点，看涨
                position_signal = 0.8
            elif price_position > 0.8:  # 接近高点，看跌
                position_signal = 0.2
            
            signals.append(position_signal)
            signal_weights.append(0.20)
            
            # 4. 24小时变化逆向信号（权重15%）
            change_signal = 0.5
            if price_change_24h < -0.05:  # 24小时跌超5%，反弹机会
                change_signal = 0.75
            elif price_change_24h > 0.05:  # 24小时涨超5%，回调风险
                change_signal = 0.25
            
            signals.append(change_signal)
            signal_weights.append(0.15)
            
            # 加权计算最终信号
            final_signal = sum(s * w for s, w in zip(signals, signal_weights))
            
            # 计算置信度（基于信号一致性）
            signal_std = np.std(signals)
            base_confidence = max(0.3, min(0.9, 1 - signal_std))
            
            # 波动率调整置信度
            if volatility > 0.03:  # 高波动降低置信度
                base_confidence *= 0.8
            elif volatility < 0.01:  # 低波动提高置信度
                base_confidence *= 1.1
            
            confidence = min(0.9, base_confidence)
            
            # 确定交易方向
            if final_signal > 0.6:
                direction = 'LONG'
                strength = (final_signal - 0.5) * 2
            elif final_signal < 0.4:
                direction = 'SHORT'
                strength = (0.5 - final_signal) * 2
            else:
                direction = 'WAIT'
                strength = 0
            
            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'signal_value': final_signal,
                'signals_breakdown': {
                    'rsi': signals[0],
                    'deviation': signals[1],
                    'position': signals[2],
                    'change_24h': signals[3]
                },
                'market_conditions': {
                    'rsi': rsi,
                    'price_deviation': price_deviation,
                    'price_position': price_position,
                    'volatility': volatility
                }
            }
            
        except Exception as e:
            print(f"❌ 左侧信号生成失败: {str(e)}")
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'signal_value': 0.5,
                'error': str(e)
            }
    
    def get_sentiment_signal(self) -> Dict:
        """获取情绪信号（左侧交易视角）"""
        try:
            sentiment_data = self.sentiment_analyzer.get_comprehensive_sentiment()
            
            # 左侧交易：情绪极端时反向操作
            sentiment_score = sentiment_data['overall_sentiment_score']
            
            # 逆向情绪信号
            if sentiment_score < 0.3:  # 极度悲观时看涨
                contrarian_direction = 'LONG'
                contrarian_strength = (0.3 - sentiment_score) / 0.3
            elif sentiment_score > 0.7:  # 极度乐观时看跌
                contrarian_direction = 'SHORT'
                contrarian_strength = (sentiment_score - 0.7) / 0.3
            else:
                contrarian_direction = 'WAIT'
                contrarian_strength = 0
            
            return {
                'direction': contrarian_direction,
                'strength': contrarian_strength,
                'confidence': abs(sentiment_score - 0.5) * 2,  # 越极端置信度越高
                'sentiment_score': sentiment_score,
                'classification': sentiment_data['sentiment_classification'],
                'data_quality': sentiment_data['data_quality']
            }
            
        except Exception as e:
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'error': str(e)
            }
    
    def fuse_left_side_signals(self, technical_signal: Dict, sentiment_signal: Dict) -> Dict:
        """融合左侧交易信号"""
        
        # 左侧交易权重配置
        technical_weight = 0.8  # 技术分析权重更高
        sentiment_weight = 0.2  # 情绪分析作为确认
        
        # 转换信号为数值
        def signal_to_value(signal):
            if signal['direction'] == 'LONG':
                return 0.5 + signal['strength'] * 0.5
            elif signal['direction'] == 'SHORT':
                return 0.5 - signal['strength'] * 0.5
            else:
                return 0.5
        
        technical_value = technical_signal['signal_value']
        sentiment_value = signal_to_value(sentiment_signal)
        
        # 加权融合
        final_value = technical_value * technical_weight + sentiment_value * sentiment_weight
        final_confidence = technical_signal['confidence'] * technical_weight + sentiment_signal['confidence'] * sentiment_weight
        
        # 确定最终方向
        if final_value > 0.55:  # 左侧交易阈值稍微放宽
            direction = 'LONG'
            strength = (final_value - 0.5) * 2
        elif final_value < 0.45:
            direction = 'SHORT'
            strength = (0.5 - final_value) * 2
        else:
            direction = 'WAIT'
            strength = 0
        
        return {
            'direction': direction,
            'strength': strength,
            'confidence': final_confidence,
            'final_value': final_value,
            'signal_breakdown': {
                'technical': {'value': technical_value, 'weight': technical_weight},
                'sentiment': {'value': sentiment_value, 'weight': sentiment_weight}
            },
            'trading_mode': 'left_side'
        }

    def calculate_batch_size(self, current_price: float, batch_number: int) -> float:
        """计算分批建仓大小"""
        # 可用资金
        available_balance = self.account['balance']

        # 分批策略：第一批30%，第二批40%，第三批30%
        batch_ratios = [0.3, 0.4, 0.3]
        batch_ratio = batch_ratios[min(batch_number, len(batch_ratios) - 1)]

        # 计算本批次资金
        batch_amount = available_balance * batch_ratio

        # 计算BTC数量
        btc_size = batch_amount / current_price / self.config['leverage']

        return btc_size

    def should_add_batch(self, current_price: float, signal: Dict) -> bool:
        """判断是否应该加仓"""
        if len(self.position['batches']) >= self.config['max_batches']:
            return False

        if self.position['size'] == 0:
            return True  # 首次开仓

        # 检查价格是否进一步偏离
        last_batch = self.position['batches'][-1]
        last_price = last_batch['price']

        if self.position['side'] == 'LONG':
            # 做多时，价格进一步下跌才加仓
            price_drop = (last_price - current_price) / last_price
            return price_drop > 0.02 and signal['confidence'] > 0.5
        else:
            # 做空时，价格进一步上涨才加仓
            price_rise = (current_price - last_price) / last_price
            return price_rise > 0.02 and signal['confidence'] > 0.5

    def open_left_side_position(self, signal: Dict, current_price: float) -> bool:
        """开启左侧交易仓位"""
        try:
            # 检查是否应该开仓或加仓
            if not self.should_add_batch(current_price, signal):
                return False

            # 检查交易间隔
            if self.last_trade_time:
                time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
                if time_since_last < 300:  # 5分钟间隔
                    return False

            # 计算本批次大小
            batch_number = len(self.position['batches'])
            btc_size = self.calculate_batch_size(current_price, batch_number)

            # 计算交易费用
            position_value = btc_size * current_price * self.config['leverage']
            trading_fee = position_value * self.config['trading_fee']

            # 检查资金是否足够
            if trading_fee > self.account['balance']:
                return False

            # 记录批次信息
            batch_info = {
                'batch_number': batch_number + 1,
                'size': btc_size,
                'price': current_price,
                'time': datetime.now(),
                'trading_fee': trading_fee
            }

            # 更新持仓信息
            if self.position['size'] == 0:
                # 首次开仓
                self.position.update({
                    'side': signal['direction'],
                    'size': btc_size,
                    'entry_price': current_price,
                    'entry_time': datetime.now(),
                    'batches': [batch_info]
                })
            else:
                # 加仓
                total_size = self.position['size'] + btc_size
                # 计算平均成本
                total_cost = self.position['entry_price'] * self.position['size'] + current_price * btc_size
                avg_price = total_cost / total_size

                self.position['size'] = total_size
                self.position['entry_price'] = avg_price
                self.position['batches'].append(batch_info)

            # 扣除手续费
            self.account['balance'] -= trading_fee

            # 记录交易
            self.trade_history.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'OPEN_BATCH',
                'side': signal['direction'],
                'batch_number': batch_number + 1,
                'size': btc_size,
                'price': current_price,
                'trading_fee': trading_fee,
                'signal': signal,
                'avg_price': self.position['entry_price'],
                'total_size': self.position['size']
            })

            self.last_trade_time = datetime.now()

            return True

        except Exception as e:
            print(f"❌ 左侧开仓失败: {str(e)}")
            return False

    def close_left_side_position(self, current_price: float, reason: str) -> bool:
        """平仓左侧交易"""
        try:
            if self.position['size'] == 0:
                return False

            # 计算总盈亏
            if self.position['side'] == 'LONG':
                price_diff = current_price - self.position['entry_price']
            else:
                price_diff = self.position['entry_price'] - current_price

            # 计算实际盈亏（考虑杠杆）
            pnl = self.position['size'] * price_diff * self.config['leverage']

            # 计算交易费用
            position_value = self.position['size'] * current_price * self.config['leverage']
            trading_fee = position_value * self.config['trading_fee']

            # 净盈亏
            net_pnl = pnl - trading_fee

            # 更新账户
            self.account['balance'] += net_pnl
            self.account['equity'] = self.account['balance']
            self.account['unrealized_pnl'] = 0

            # 记录交易
            self.trade_history.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'CLOSE_ALL',
                'side': self.position['side'],
                'total_size': self.position['size'],
                'avg_entry_price': self.position['entry_price'],
                'exit_price': current_price,
                'pnl': pnl,
                'trading_fee': trading_fee,
                'net_pnl': net_pnl,
                'reason': reason,
                'batches_count': len(self.position['batches']),
                'hold_time': (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            })

            self.last_trade_time = datetime.now()

            # 清空持仓
            self.position.update({
                'side': None,
                'size': 0.0,
                'entry_price': 0.0,
                'entry_time': None,
                'batches': []
            })

            return True

        except Exception as e:
            print(f"❌ 左侧平仓失败: {str(e)}")
            return False

    def check_left_side_exit(self, current_price: float) -> bool:
        """检查左侧交易退出条件"""
        if self.position['size'] == 0:
            return False

        # 计算当前盈亏百分比
        if self.position['side'] == 'LONG':
            pnl_pct = (current_price - self.position['entry_price']) / self.position['entry_price']
        else:
            pnl_pct = (self.position['entry_price'] - current_price) / self.position['entry_price']

        # 止损检查
        if pnl_pct <= -self.config['stop_loss_pct']:
            self.close_left_side_position(current_price, 'STOP_LOSS')
            return True

        # 止盈检查
        if pnl_pct >= self.config['take_profit_pct']:
            self.close_left_side_position(current_price, 'TAKE_PROFIT')
            return True

        # 左侧交易特有：趋势确认反转时退出
        # 这里可以添加更复杂的退出逻辑

        return False

    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.position['size'] == 0:
            self.account['unrealized_pnl'] = 0
            self.account['equity'] = self.account['balance']
            return

        # 计算未实现盈亏
        if self.position['side'] == 'LONG':
            price_diff = current_price - self.position['entry_price']
        else:
            price_diff = self.position['entry_price'] - current_price

        unrealized_pnl = self.position['size'] * price_diff * self.config['leverage']

        self.account['unrealized_pnl'] = unrealized_pnl
        self.account['equity'] = self.account['balance'] + unrealized_pnl

    def print_left_side_status(self, market_data: Dict, technical_signal: Dict,
                              sentiment_signal: Dict, final_signal: Dict):
        """打印左侧交易状态"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_price = market_data['current_price']

        print(f"\n🎯 {current_time} | BTC: ${current_price:,.0f} | 左侧交易模式")
        print("=" * 100)

        # 账户状态
        total_return = (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        print(f"💰 账户: ${self.account['balance']:.2f} + ${self.account['unrealized_pnl']:+.2f} = ${self.account['equity']:.2f} ({total_return:+.2f}%)")

        # 持仓状态
        if self.position['size'] != 0:
            hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            pnl_pct = self.account['unrealized_pnl'] / (self.position['size'] * self.position['entry_price'] * self.config['leverage']) * 100

            print(f"📊 持仓: 🔥 {self.position['side']} {self.position['size']:.6f} BTC")
            print(f"   平均成本: ${self.position['entry_price']:,.0f} | 当前价: ${current_price:,.0f}")
            print(f"   持仓时间: {hold_time:.1f}小时 | 盈亏: {pnl_pct:+.1f}% | 批次: {len(self.position['batches'])}")

            # 显示各批次详情
            print(f"   📋 分批详情:")
            for i, batch in enumerate(self.position['batches']):
                batch_pnl = (current_price - batch['price']) / batch['price'] * 100
                if self.position['side'] == 'SHORT':
                    batch_pnl = -batch_pnl
                print(f"      批次{batch['batch_number']}: {batch['size']:.6f} BTC @ ${batch['price']:,.0f} ({batch_pnl:+.1f}%)")
        else:
            print(f"📊 持仓: 💤 空仓")

        # 左侧交易信号分析
        print(f"\n🎯 左侧交易信号:")
        print(f"   最终决策: {final_signal['direction']} (强度{final_signal['strength']:.1%}, 置信度{final_signal['confidence']:.1%})")
        print(f"   信号值: {final_signal['final_value']:.2f}")

        # 技术信号分解
        tech_signals = technical_signal['signals_breakdown']
        print(f"   📊 技术信号: RSI{tech_signals['rsi']:.2f} | 偏离{tech_signals['deviation']:.2f} | 位置{tech_signals['position']:.2f} | 变化{tech_signals['change_24h']:.2f}")

        # 情绪逆向信号
        print(f"   😊 情绪逆向: {sentiment_signal['direction']} (情绪分数{sentiment_signal['sentiment_score']:.2f})")

        # 市场条件
        conditions = technical_signal['market_conditions']
        print(f"📈 市场条件: RSI{conditions['rsi']:.0f} | 偏离{conditions['price_deviation']:+.1%} | 位置{conditions['price_position']:.2f} | 波动{conditions['volatility']:.3f}")

    def save_state(self):
        """保存状态"""
        state_data = {
            'account': self.account,
            'position': {k: v.isoformat() if isinstance(v, datetime) else v
                        for k, v in self.position.items() if k != 'batches'},
            'batches': [{k: v.isoformat() if isinstance(v, datetime) else v
                        for k, v in batch.items()} for batch in self.position['batches']],
            'trade_history': self.trade_history,
            'last_trade_time': self.last_trade_time.isoformat() if self.last_trade_time else None,
            'config': self.config
        }

        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, default=str)

    def load_state(self):
        """加载状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                self.account = state_data.get('account', self.account)

                position_data = state_data.get('position', {})
                if position_data.get('entry_time'):
                    position_data['entry_time'] = datetime.fromisoformat(position_data['entry_time'])
                self.position.update(position_data)

                # 加载批次信息
                batches_data = state_data.get('batches', [])
                self.position['batches'] = []
                for batch in batches_data:
                    if batch.get('time'):
                        batch['time'] = datetime.fromisoformat(batch['time'])
                    self.position['batches'].append(batch)

                self.trade_history = state_data.get('trade_history', [])

                if state_data.get('last_trade_time'):
                    self.last_trade_time = datetime.fromisoformat(state_data['last_trade_time'])

                if self.account['balance'] != self.initial_balance or self.trade_history:
                    print(f"📂 加载历史状态: 余额${self.account['balance']:.2f}, {len(self.trade_history)}笔交易")

            except Exception as e:
                print(f"⚠️ 状态加载失败: {str(e)}")

    def run_left_side_cycle(self) -> bool:
        """运行一个左侧交易周期"""
        try:
            # 1. 获取市场数据
            market_data = self.get_market_data()
            if not market_data:
                print("❌ 市场数据获取失败")
                return False

            current_price = market_data['current_price']

            # 2. 更新未实现盈亏
            self.update_unrealized_pnl(current_price)

            # 3. 检查退出条件
            if self.check_left_side_exit(current_price):
                print(f"🔔 触发退出条件，已平仓")
                self.save_state()
                return True

            # 4. 生成左侧交易信号
            technical_signal = self.generate_left_side_signal(market_data)
            sentiment_signal = self.get_sentiment_signal()
            final_signal = self.fuse_left_side_signals(technical_signal, sentiment_signal)

            # 5. 执行左侧交易决策
            if final_signal['direction'] in ['LONG', 'SHORT']:
                if final_signal['confidence'] >= self.config['min_confidence']:
                    if self.open_left_side_position(final_signal, current_price):
                        batch_num = len(self.position['batches'])
                        print(f"🔔 左侧交易: {final_signal['direction']} 第{batch_num}批 @ ${current_price:,.0f}")
                    else:
                        print("⚠️ 左侧开仓条件不满足")
                else:
                    print(f"⚠️ 置信度不足: {final_signal['confidence']:.1%} < {self.config['min_confidence']:.1%}")

            # 6. 打印状态
            self.print_left_side_status(market_data, technical_signal, sentiment_signal, final_signal)

            # 7. 保存状态
            self.save_state()

            return True

        except Exception as e:
            print(f"❌ 左侧交易周期失败: {str(e)}")
            return False

    def get_statistics(self) -> Dict:
        """获取交易统计"""
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE_ALL']

        if not closed_trades:
            return {
                'total_trades': 0,
                'total_batches': len([t for t in self.trade_history if t['action'] == 'OPEN_BATCH']),
                'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
            }

        total_trades = len(closed_trades)
        winning_trades = [t for t in closed_trades if t['net_pnl'] > 0]
        total_batches = sum(t['batches_count'] for t in closed_trades)

        return {
            'total_trades': total_trades,
            'total_batches': total_batches,
            'winning_trades': len(winning_trades),
            'win_rate': len(winning_trades) / total_trades,
            'total_pnl': sum(t['net_pnl'] for t in closed_trades),
            'avg_pnl': sum(t['net_pnl'] for t in closed_trades) / total_trades,
            'avg_batches': total_batches / total_trades,
            'avg_hold_time': sum(t['hold_time'] for t in closed_trades) / total_trades,
            'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        }

def run_left_side_trading():
    """运行左侧交易系统"""
    print("🎯 左侧交易系统")
    print("=" * 80)
    print("📊 逆向交易策略 - 在趋势反转前进场")
    print("🔄 支持分批建仓，降低风险")
    print("💰 50美元模拟账户")
    print("")

    # 获取参数
    try:
        duration = float(input("运行时长（小时，默认6）: ") or "6")
        interval = int(input("检测间隔（分钟，默认5）: ") or "5")

        # 询问是否从当前市场状况开始左侧交易
        print(f"\n📊 当前市场分析:")
        print(f"   BTC价格: 约$103,000")
        print(f"   24小时跌幅: -1.4%")
        print(f"   RSI: 约40 (从超买回落)")
        print(f"   情绪: 机构偏悲观，散户偏乐观")

        start_trading = input("\n🎯 当前是左侧交易的好时机，是否立即开始？(y/n，默认y): ").strip().lower()
        if start_trading in ['n', 'no']:
            print("⏸️ 用户选择暂不开始")
            return None

    except:
        duration = 6
        interval = 5

    print(f"\n🎯 启动左侧交易系统...")
    print(f"⏰ 运行时长: {duration}小时")
    print(f"🔄 检测间隔: {interval}分钟")
    print(f"📊 交易策略: 逆向交易 + 分批建仓")

    # 创建左侧交易器
    trader = LeftSideTrader(50.0)

    start_time = datetime.now()
    end_time = start_time + timedelta(hours=duration)
    cycle_count = 0

    try:
        while datetime.now() < end_time:
            cycle_count += 1

            # 计算进度
            elapsed_time = (datetime.now() - start_time).total_seconds() / 3600
            progress = elapsed_time / duration * 100
            remaining_hours = duration - elapsed_time

            print(f"\n🎯 第 {cycle_count} 个左侧交易周期 | 进度: {progress:.1f}% | 剩余: {remaining_hours:.1f}小时")

            # 运行左侧交易周期
            trader.run_left_side_cycle()

            # 显示简要统计
            stats = trader.get_statistics()
            print(f"📈 当前统计: 权益${trader.account['equity']:.2f} | 收益{stats['total_return']:+.2f}% | 交易{stats['total_trades']}笔 | 批次{stats['total_batches']}个")

            if stats['total_trades'] > 0:
                print(f"   胜率{stats['win_rate']:.1%} | 平均批次{stats['avg_batches']:.1f} | 平均持仓{stats['avg_hold_time']:.1f}小时")

            # 等待下一个周期
            remaining_time = (end_time - datetime.now()).total_seconds()
            interval_seconds = interval * 60

            if remaining_time > interval_seconds:
                print(f"⏳ 等待 {interval} 分钟...")
                time.sleep(interval_seconds)
            else:
                print(f"⏳ 剩余时间不足，等待 {remaining_time:.0f} 秒...")
                time.sleep(max(0, remaining_time))
                break

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断")

    # 显示最终结果
    print(f"\n🏁 左侧交易结束")
    print("=" * 80)

    final_stats = trader.get_statistics()
    actual_runtime = (datetime.now() - start_time).total_seconds() / 3600

    print(f"📊 最终统计:")
    print(f"   运行时长: {actual_runtime:.1f}小时")
    print(f"   交易周期: {cycle_count}个")
    print(f"   最终权益: ${trader.account['equity']:.2f}")
    print(f"   总收益率: {final_stats['total_return']:+.2f}%")
    print(f"   总交易次数: {final_stats['total_trades']}")
    print(f"   总批次数: {final_stats['total_batches']}")

    if final_stats['total_trades'] > 0:
        print(f"   胜率: {final_stats['win_rate']:.1%}")
        print(f"   平均盈亏: ${final_stats['avg_pnl']:+.2f}")
        print(f"   平均批次: {final_stats['avg_batches']:.1f}")
        print(f"   平均持仓时间: {final_stats['avg_hold_time']:.1f}小时")

    print(f"\n💾 数据已保存到: {trader.state_file}")

    # 左侧交易总结
    print(f"\n🎯 左侧交易总结:")
    if final_stats['total_return'] > 0:
        print(f"✅ 左侧交易策略成功！")
        print(f"✅ 在市场下跌中获得正收益")
        print(f"✅ 分批建仓策略有效降低风险")
    else:
        print(f"📊 左侧交易经验积累")
        print(f"📊 为下次更好的时机做准备")
        print(f"📊 风险控制保护了大部分资金")

    return trader

if __name__ == "__main__":
    print("🎯 左侧交易系统")
    print("基于第三阶段完全真实化系统 - 逆向交易版")
    print("")

    try:
        trader = run_left_side_trading()
        if trader:
            print(f"\n🎉 左侧交易完成！")

    except KeyboardInterrupt:
        print(f"\n👋 用户退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
