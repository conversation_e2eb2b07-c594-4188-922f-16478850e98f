#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复smart_ai_trader.py中的模型加载逻辑
"""

import re

def fix_model_loading():
    """修复模型加载逻辑"""
    
    # 读取文件
    with open('smart_ai_trader.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 要替换的旧代码
    old_pattern = r'''            # 根据交易频率选择合适的模型
            print\(f"[^"]*临时修复：使用兼容的1小时数据模型"\)
            preferred_patterns = \[
                "balanced_cost_sensitive",
                "improved_model",
                "realistic",
                "binary_trend"  # 作为备选
            \]'''
    
    # 新代码
    new_code = '''            # 根据交易频率选择合适的模型
            if self.trading_frequency == 'high_frequency':
                print(f"🚀 高频交易模式：优先选择高频专用模型")
                # 优先选择高频交易专用模型
                preferred_patterns = [
                    "hft_xgb",  # 高频交易专用模型
                    "binary_trend",  # 二元趋势模型（适合短期）
                    "balanced_cost_sensitive",  # 备选
                ]
            else:
                print(f"📊 普通交易模式：选择平衡模型")
                # 普通模式：优先选择balanced模型
                preferred_patterns = [
                    "balanced_cost_sensitive",
                    "improved_model",
                    "realistic"
                ]'''
    
    # 执行替换
    new_content = re.sub(old_pattern, new_code, content, flags=re.MULTILINE)
    
    # 如果正则表达式替换失败，尝试简单的字符串替换
    if new_content == content:
        print("正则表达式替换失败，尝试简单替换...")
        
        # 查找并替换
        lines = content.split('\n')
        new_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 找到目标行
            if '# 根据交易频率选择合适的模型' in line:
                new_lines.append(line)
                i += 1
                
                # 跳过旧的print和preferred_patterns
                while i < len(lines) and (
                    '临时修复' in lines[i] or 
                    'preferred_patterns = [' in lines[i] or
                    '"balanced_cost_sensitive",' in lines[i] or
                    '"improved_model",' in lines[i] or
                    '"realistic",' in lines[i] or
                    '"binary_trend"' in lines[i] or
                    ']' in lines[i].strip()
                ):
                    i += 1
                
                # 添加新代码
                new_lines.extend([
                    '            if self.trading_frequency == \'high_frequency\':',
                    '                print(f"🚀 高频交易模式：优先选择高频专用模型")',
                    '                # 优先选择高频交易专用模型',
                    '                preferred_patterns = [',
                    '                    "hft_xgb",  # 高频交易专用模型',
                    '                    "binary_trend",  # 二元趋势模型（适合短期）',
                    '                    "balanced_cost_sensitive",  # 备选',
                    '                ]',
                    '            else:',
                    '                print(f"📊 普通交易模式：选择平衡模型")',
                    '                # 普通模式：优先选择balanced模型',
                    '                preferred_patterns = [',
                    '                    "balanced_cost_sensitive",',
                    '                    "improved_model",',
                    '                    "realistic"',
                    '                ]'
                ])
                continue
            
            new_lines.append(line)
            i += 1
        
        new_content = '\n'.join(new_lines)
    
    # 写回文件
    with open('smart_ai_trader.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ 模型加载逻辑修复完成")
    print("🚀 高频模式将优先使用hft_xgb模型")
    print("📊 普通模式将使用balanced_cost_sensitive模型")

if __name__ == "__main__":
    fix_model_loading()
