#!/usr/bin/env python3
"""
50美元模拟账户实时交易系统
基于第三阶段完全真实化系统，使用100%真实数据进行实时预测交易
"""

import pandas as pd
import numpy as np
import time
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置日志级别，减少输出
logging.getLogger('data_fetcher').setLevel(logging.WARNING)
logging.getLogger('feature_engineering').setLevel(logging.WARNING)

# 导入第三阶段真实化组件
from enhanced_real_sentiment import EnhancedRealSentimentAnalyzer
from transparent_data_system import DataAuthenticityTracker
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer
from multi_strategy_library import StrategyManager

class LiveTradingSimulator:
    """
    50美元模拟账户实时交易系统
    
    特点：
    - ✅ $50初始资金模拟交易
    - ✅ 100%真实市场数据
    - ✅ 实时AI预测
    - ✅ 真实情绪分析
    - ✅ 完全透明记录
    """
    
    def __init__(self, initial_balance: float = 50.0):
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        
        # 账户状态
        self.account = {
            'balance': initial_balance,
            'equity': initial_balance,
            'margin_used': 0.0,
            'free_margin': initial_balance,
            'unrealized_pnl': 0.0
        }
        
        # 持仓信息
        self.position = {
            'symbol': 'BTCUSDT',
            'side': None,  # 'LONG' or 'SHORT'
            'size': 0.0,   # BTC数量
            'entry_price': 0.0,
            'entry_time': None,
            'margin_required': 0.0,
            'leverage': 2  # 2倍杠杆
        }
        
        # 交易配置
        self.trading_config = {
            'leverage': 2,
            'max_position_ratio': 0.9,  # 最大仓位比例
            'stop_loss_pct': 0.03,      # 3%止损
            'take_profit_pct': 0.06,    # 6%止盈
            'min_confidence': 0.65,     # 最低交易置信度
            'trading_fee': 0.0004,      # 0.04%交易手续费
            'min_trade_interval': 300   # 最小交易间隔5分钟
        }
        
        # 初始化组件
        print(f"🚀 50美元模拟账户实时交易系统启动")
        print("=" * 80)
        
        self.data_fetcher = BinanceDataFetcher()
        self.feature_engineer = FeatureEngineer()
        self.sentiment_analyzer = EnhancedRealSentimentAnalyzer()
        self.strategy_manager = StrategyManager()
        self.authenticity_tracker = DataAuthenticityTracker()
        
        # 注册交易系统
        self.system_source_id = self.authenticity_tracker.register_data_source(
            "Live Trading Simulator $50",
            {
                'type': 'live_trading_simulation',
                'initial_balance': initial_balance,
                'data_authenticity': '100% Real Market Data'
            }
        )
        
        # 交易记录
        self.trade_history = []
        self.equity_history = []
        self.prediction_history = []
        self.last_trade_time = None
        
        # 状态文件
        self.state_file = "live_trading_state.json"
        self.load_state()
        
        print(f"✅ 系统初始化完成")
        print(f"   初始资金: ${self.account['balance']:.2f}")
        print(f"   杠杆倍数: {self.position['leverage']}x")
        print(f"   数据来源: 100%真实市场数据")
        print(f"   交易模式: 模拟交易（无真实资金风险）")
    
    def get_real_market_data(self) -> Dict:
        """获取真实市场数据"""
        try:
            # 获取当前价格
            current_price = self.data_fetcher.get_current_price('BTCUSDT', is_futures=True)
            
            # 获取历史数据用于技术分析
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)
            
            df = self.data_fetcher.get_historical_data(
                'BTCUSDT', '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) == 0:
                raise Exception("无法获取历史数据")
            
            # 计算技术指标
            features_df = self.feature_engineer.create_features(df)
            
            if features_df is None or len(features_df) == 0:
                raise Exception("技术指标计算失败")
            
            latest_features = features_df.iloc[-1]
            
            market_data = {
                'current_price': current_price,
                'timestamp': datetime.now().isoformat(),
                'rsi': latest_features.get('RSI_14', 50),
                'bb_position': latest_features.get('BB_position', 0.5),
                'macd_signal': latest_features.get('MACD_signal', 0),
                'volume_ratio': latest_features.get('volume_ratio', 1.0),
                'atr_percentage': latest_features.get('ATR_percentage', 0.02),
                'price_change_24h': ((current_price - df['close'].iloc[-24]) / df['close'].iloc[-24]) if len(df) >= 24 else 0
            }
            
            # 验证数据真实性
            data_label = self.authenticity_tracker.label_data_authenticity(
                market_data,
                self.system_source_id,
                'real_market_data'
            )
            
            market_data['authenticity_verified'] = data_label['authenticity_verified']
            
            return market_data
            
        except Exception as e:
            print(f"❌ 市场数据获取失败: {str(e)}")
            return None
    
    def generate_ai_prediction(self, market_data: Dict) -> Dict:
        """生成AI预测"""
        try:
            # 基于技术指标的AI预测逻辑
            rsi = market_data['rsi']
            bb_position = market_data['bb_position']
            macd_signal = market_data['macd_signal']
            price_change_24h = market_data['price_change_24h']
            
            # 计算预测概率
            prediction_factors = []
            
            # RSI因子
            if rsi < 30:
                prediction_factors.append(0.8)  # 超卖，看涨
            elif rsi > 70:
                prediction_factors.append(0.2)  # 超买，看跌
            else:
                prediction_factors.append(0.5)  # 中性
            
            # 布林带因子
            if bb_position < 0.2:
                prediction_factors.append(0.75)  # 接近下轨，看涨
            elif bb_position > 0.8:
                prediction_factors.append(0.25)  # 接近上轨，看跌
            else:
                prediction_factors.append(0.5)   # 中性
            
            # MACD因子
            if macd_signal > 0:
                prediction_factors.append(0.7)   # 金叉，看涨
            elif macd_signal < 0:
                prediction_factors.append(0.3)   # 死叉，看跌
            else:
                prediction_factors.append(0.5)   # 中性
            
            # 24小时价格变化因子
            if price_change_24h > 0.05:
                prediction_factors.append(0.3)   # 大涨后可能回调
            elif price_change_24h < -0.05:
                prediction_factors.append(0.7)   # 大跌后可能反弹
            else:
                prediction_factors.append(0.5)   # 中性
            
            # 计算最终预测概率
            prediction_probability = np.mean(prediction_factors)
            
            # 计算置信度
            factor_std = np.std(prediction_factors)
            confidence = max(0.4, min(0.9, 1 - factor_std))
            
            # 确定预测方向
            if prediction_probability > 0.6:
                direction = 'LONG'
                strength = (prediction_probability - 0.5) * 2
            elif prediction_probability < 0.4:
                direction = 'SHORT'
                strength = (0.5 - prediction_probability) * 2
            else:
                direction = 'WAIT'
                strength = 0
            
            prediction = {
                'timestamp': datetime.now().isoformat(),
                'direction': direction,
                'probability': prediction_probability,
                'confidence': confidence,
                'strength': strength,
                'factors': {
                    'rsi_factor': prediction_factors[0],
                    'bb_factor': prediction_factors[1],
                    'macd_factor': prediction_factors[2],
                    'price_change_factor': prediction_factors[3]
                },
                'technical_summary': {
                    'rsi': rsi,
                    'bb_position': bb_position,
                    'macd_signal': macd_signal,
                    'price_change_24h': price_change_24h
                }
            }
            
            # 记录预测历史
            self.prediction_history.append(prediction)
            
            return prediction
            
        except Exception as e:
            print(f"❌ AI预测生成失败: {str(e)}")
            return {
                'direction': 'WAIT',
                'probability': 0.5,
                'confidence': 0.3,
                'strength': 0,
                'error': str(e)
            }
    
    def get_sentiment_signal(self) -> Dict:
        """获取情绪信号"""
        try:
            sentiment_data = self.sentiment_analyzer.get_comprehensive_sentiment()
            
            # 转换为交易信号
            sentiment_score = sentiment_data['overall_sentiment_score']
            trading_signal = sentiment_data['trading_signal']
            
            return {
                'sentiment_score': sentiment_score,
                'classification': sentiment_data['sentiment_classification'],
                'direction': trading_signal['direction'],
                'strength': trading_signal['strength'],
                'confidence': trading_signal['confidence'],
                'signal_type': trading_signal['signal_type'],
                'data_quality': sentiment_data['data_quality']
            }
            
        except Exception as e:
            print(f"❌ 情绪分析失败: {str(e)}")
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'error': str(e)
            }
    
    def get_strategy_signal(self, market_data: Dict) -> Dict:
        """获取策略信号"""
        try:
            strategy_data = {
                'price': market_data['current_price'],
                'rsi': market_data['rsi'],
                'bb_position': market_data['bb_position'],
                'volume_ratio': market_data['volume_ratio'],
                'atr_percentage': market_data['atr_percentage'],
                'macd_trend': 'bullish' if market_data['macd_signal'] > 0 else 'bearish'
            }
            
            strategy_signal = self.strategy_manager.generate_combined_signal(strategy_data)
            
            return strategy_signal
            
        except Exception as e:
            print(f"❌ 策略信号生成失败: {str(e)}")
            return {
                'direction': 'WAIT',
                'strength': 0,
                'confidence': 0.3,
                'error': str(e)
            }
    
    def fuse_signals(self, ai_prediction: Dict, sentiment_signal: Dict, strategy_signal: Dict) -> Dict:
        """融合所有信号"""
        
        # 权重配置
        weights = {
            'ai': 0.5,
            'sentiment': 0.2,
            'strategy': 0.3
        }
        
        # 转换信号为数值
        def signal_to_value(signal):
            if signal['direction'] == 'LONG':
                return 0.5 + signal['strength'] * 0.5
            elif signal['direction'] == 'SHORT':
                return 0.5 - signal['strength'] * 0.5
            else:
                return 0.5
        
        ai_value = ai_prediction['probability']
        sentiment_value = signal_to_value(sentiment_signal)
        strategy_value = signal_to_value(strategy_signal)
        
        # 加权融合
        final_value = (
            ai_value * weights['ai'] +
            sentiment_value * weights['sentiment'] +
            strategy_value * weights['strategy']
        )
        
        # 计算置信度
        final_confidence = (
            ai_prediction['confidence'] * weights['ai'] +
            sentiment_signal['confidence'] * weights['sentiment'] +
            strategy_signal['confidence'] * weights['strategy']
        )
        
        # 确定最终方向
        if final_value > 0.6:
            direction = 'LONG'
            strength = (final_value - 0.5) * 2
        elif final_value < 0.4:
            direction = 'SHORT'
            strength = (0.5 - final_value) * 2
        else:
            direction = 'WAIT'
            strength = 0
        
        return {
            'direction': direction,
            'strength': strength,
            'confidence': final_confidence,
            'final_value': final_value,
            'signal_breakdown': {
                'ai': {'value': ai_value, 'weight': weights['ai']},
                'sentiment': {'value': sentiment_value, 'weight': weights['sentiment']},
                'strategy': {'value': strategy_value, 'weight': weights['strategy']}
            }
        }
    
    def calculate_position_size(self, current_price: float, signal: Dict) -> Optional[Dict]:
        """计算仓位大小"""
        try:
            # 可用保证金
            available_margin = self.account['free_margin']
            
            # 风险金额（账户的2%）
            risk_amount = self.account['equity'] * 0.02
            
            # 止损距离
            stop_distance = current_price * self.trading_config['stop_loss_pct']
            
            # 基于风险计算仓位价值
            position_value = risk_amount / self.trading_config['stop_loss_pct']
            
            # 限制最大仓位
            max_position_value = available_margin * self.trading_config['max_position_ratio']
            position_value = min(position_value, max_position_value)
            
            # 计算BTC数量
            btc_size = position_value / current_price / self.position['leverage']
            
            # 计算所需保证金
            required_margin = position_value / self.position['leverage']
            
            if required_margin > available_margin:
                return None
            
            return {
                'btc_size': btc_size,
                'position_value': position_value,
                'required_margin': required_margin,
                'stop_loss_price': current_price * (1 - self.trading_config['stop_loss_pct']) if signal['direction'] == 'LONG' else current_price * (1 + self.trading_config['stop_loss_pct']),
                'take_profit_price': current_price * (1 + self.trading_config['take_profit_pct']) if signal['direction'] == 'LONG' else current_price * (1 - self.trading_config['take_profit_pct'])
            }
            
        except Exception as e:
            print(f"❌ 仓位计算失败: {str(e)}")
            return None

    def open_position(self, signal: Dict, current_price: float, position_info: Dict) -> bool:
        """开仓"""
        try:
            # 检查是否已有持仓
            if self.position['size'] != 0:
                print("⚠️ 已有持仓，无法开新仓")
                return False

            # 检查交易间隔
            if self.last_trade_time:
                time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
                if time_since_last < self.trading_config['min_trade_interval']:
                    print(f"⚠️ 交易间隔不足，需等待 {self.trading_config['min_trade_interval'] - time_since_last:.0f} 秒")
                    return False

            # 计算交易费用
            trading_fee = position_info['position_value'] * self.trading_config['trading_fee']

            # 更新持仓信息
            self.position.update({
                'side': signal['direction'],
                'size': position_info['btc_size'],
                'entry_price': current_price,
                'entry_time': datetime.now(),
                'margin_required': position_info['required_margin']
            })

            # 更新账户信息
            self.account['margin_used'] += position_info['required_margin']
            self.account['free_margin'] -= position_info['required_margin']
            self.account['balance'] -= trading_fee

            # 记录交易
            trade_record = {
                'timestamp': datetime.now().isoformat(),
                'action': 'OPEN',
                'side': signal['direction'],
                'size': position_info['btc_size'],
                'price': current_price,
                'margin_used': position_info['required_margin'],
                'trading_fee': trading_fee,
                'signal': signal,
                'stop_loss': position_info['stop_loss_price'],
                'take_profit': position_info['take_profit_price']
            }

            self.trade_history.append(trade_record)
            self.last_trade_time = datetime.now()

            print(f"📈 开仓成功: {signal['direction']} {position_info['btc_size']:.6f} BTC @ ${current_price:,.0f}")
            print(f"   保证金: ${position_info['required_margin']:.2f}")
            print(f"   止损价: ${position_info['stop_loss_price']:,.0f}")
            print(f"   止盈价: ${position_info['take_profit_price']:,.0f}")

            return True

        except Exception as e:
            print(f"❌ 开仓失败: {str(e)}")
            return False

    def close_position(self, current_price: float, reason: str) -> bool:
        """平仓"""
        try:
            if self.position['size'] == 0:
                return False

            # 计算盈亏
            if self.position['side'] == 'LONG':
                price_diff = current_price - self.position['entry_price']
            else:  # SHORT
                price_diff = self.position['entry_price'] - current_price

            # 计算实际盈亏（考虑杠杆）
            pnl = self.position['size'] * price_diff * self.position['leverage']

            # 计算交易费用
            position_value = self.position['size'] * current_price * self.position['leverage']
            trading_fee = position_value * self.trading_config['trading_fee']

            # 净盈亏
            net_pnl = pnl - trading_fee

            # 更新账户
            self.account['balance'] += net_pnl
            self.account['free_margin'] += self.position['margin_required']
            self.account['margin_used'] -= self.position['margin_required']
            self.account['equity'] = self.account['balance']
            self.account['unrealized_pnl'] = 0

            # 记录交易
            trade_record = {
                'timestamp': datetime.now().isoformat(),
                'action': 'CLOSE',
                'side': self.position['side'],
                'size': self.position['size'],
                'entry_price': self.position['entry_price'],
                'exit_price': current_price,
                'pnl': pnl,
                'trading_fee': trading_fee,
                'net_pnl': net_pnl,
                'reason': reason,
                'hold_time': (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            }

            self.trade_history.append(trade_record)
            self.last_trade_time = datetime.now()

            print(f"📉 平仓: {self.position['side']} @ ${current_price:,.0f}")
            print(f"   盈亏: ${net_pnl:+.2f} ({reason})")
            print(f"   持仓时间: {trade_record['hold_time']:.1f}小时")

            # 清空持仓
            self.position.update({
                'side': None,
                'size': 0.0,
                'entry_price': 0.0,
                'entry_time': None,
                'margin_required': 0.0
            })

            return True

        except Exception as e:
            print(f"❌ 平仓失败: {str(e)}")
            return False

    def check_stop_loss_take_profit(self, current_price: float) -> bool:
        """检查止损止盈"""
        if self.position['size'] == 0:
            return False

        # 计算当前盈亏百分比
        if self.position['side'] == 'LONG':
            pnl_pct = (current_price - self.position['entry_price']) / self.position['entry_price']
        else:  # SHORT
            pnl_pct = (self.position['entry_price'] - current_price) / self.position['entry_price']

        # 检查止损
        if pnl_pct <= -self.trading_config['stop_loss_pct']:
            self.close_position(current_price, 'STOP_LOSS')
            return True

        # 检查止盈
        if pnl_pct >= self.trading_config['take_profit_pct']:
            self.close_position(current_price, 'TAKE_PROFIT')
            return True

        return False

    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.position['size'] == 0:
            self.account['unrealized_pnl'] = 0
            self.account['equity'] = self.account['balance']
            return

        # 计算未实现盈亏
        if self.position['side'] == 'LONG':
            price_diff = current_price - self.position['entry_price']
        else:  # SHORT
            price_diff = self.position['entry_price'] - current_price

        unrealized_pnl = self.position['size'] * price_diff * self.position['leverage']

        self.account['unrealized_pnl'] = unrealized_pnl
        self.account['equity'] = self.account['balance'] + unrealized_pnl

    def print_trading_status(self, market_data: Dict, ai_prediction: Dict,
                           sentiment_signal: Dict, final_signal: Dict):
        """打印交易状态"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_price = market_data['current_price']

        print(f"\n💰 账户状态:")
        print(f"   余额: ${self.account['balance']:.2f}")
        print(f"   权益: ${self.account['equity']:.2f}")
        print(f"   未实现盈亏: ${self.account['unrealized_pnl']:+.2f}")
        print(f"   可用保证金: ${self.account['free_margin']:.2f}")

        total_return = (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        print(f"   总收益率: {total_return:+.2f}%")

        print(f"\n📊 持仓状态:")
        if self.position['size'] != 0:
            hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            pnl_pct = self.account['unrealized_pnl'] / (self.position['size'] * self.position['entry_price'] * self.position['leverage']) * 100
            print(f"   🔥 {self.position['side']} {self.position['size']:.6f} BTC")
            print(f"   开仓价: ${self.position['entry_price']:,.0f}")
            print(f"   当前价: ${current_price:,.0f}")
            print(f"   持仓时间: {hold_time:.1f}小时")
            print(f"   盈亏: ${self.account['unrealized_pnl']:+.2f} ({pnl_pct:+.1f}%)")
        else:
            print(f"   💤 空仓 | BTC: ${current_price:,.0f}")

        print(f"\n🤖 AI预测:")
        print(f"   方向: {ai_prediction['direction']}")
        print(f"   概率: {ai_prediction['probability']:.1%}")
        print(f"   置信度: {ai_prediction['confidence']:.1%}")

        print(f"\n😊 情绪分析:")
        print(f"   方向: {sentiment_signal['direction']}")
        print(f"   分类: {sentiment_signal.get('classification', 'Unknown')}")
        print(f"   数据质量: {sentiment_signal.get('data_quality', 'Unknown')}")

        print(f"\n🎯 最终决策:")
        signal_emoji = {"LONG": "🟢", "SHORT": "🔴", "WAIT": "⏸️"}
        decision_color = signal_emoji.get(final_signal['direction'], '❓')
        print(f"   {decision_color} 决策: {final_signal['direction']}")
        print(f"   置信度: {final_signal['confidence']:.1%}")
        print(f"   强度: {final_signal['strength']:.1%}")

        print(f"\n📈 技术指标:")
        print(f"   RSI: {market_data['rsi']:.1f}")
        print(f"   布林带位置: {market_data['bb_position']:.2f}")
        print(f"   24h变化: {market_data['price_change_24h']:+.1%}")

    def save_state(self):
        """保存交易状态"""
        state_data = {
            'account': self.account,
            'position': {k: v.isoformat() if isinstance(v, datetime) else v
                        for k, v in self.position.items()},
            'trade_history': self.trade_history,
            'equity_history': self.equity_history[-100:],  # 只保存最近100条
            'last_trade_time': self.last_trade_time.isoformat() if self.last_trade_time else None,
            'save_timestamp': datetime.now().isoformat()
        }

        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, ensure_ascii=False, default=str)

    def load_state(self):
        """加载交易状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                self.account = state_data.get('account', self.account)

                position_data = state_data.get('position', {})
                if position_data.get('entry_time'):
                    position_data['entry_time'] = datetime.fromisoformat(position_data['entry_time'])
                self.position.update(position_data)

                self.trade_history = state_data.get('trade_history', [])
                self.equity_history = state_data.get('equity_history', [])

                if state_data.get('last_trade_time'):
                    self.last_trade_time = datetime.fromisoformat(state_data['last_trade_time'])

                print(f"✅ 交易状态已加载")
                print(f"   账户余额: ${self.account['balance']:.2f}")
                print(f"   历史交易: {len(self.trade_history)}笔")

            except Exception as e:
                print(f"⚠️ 状态加载失败: {str(e)}")

    def get_trading_statistics(self) -> Dict:
        """获取交易统计"""
        if not self.trade_history:
            return {'total_trades': 0}

        # 只统计平仓交易
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']

        if not closed_trades:
            return {'total_trades': 0}

        total_trades = len(closed_trades)
        winning_trades = [t for t in closed_trades if t['net_pnl'] > 0]
        losing_trades = [t for t in closed_trades if t['net_pnl'] <= 0]

        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0

        total_pnl = sum(t['net_pnl'] for t in closed_trades)
        avg_pnl = total_pnl / total_trades if total_trades > 0 else 0

        max_win = max((t['net_pnl'] for t in winning_trades), default=0)
        max_loss = min((t['net_pnl'] for t in losing_trades), default=0)

        avg_hold_time = np.mean([t['hold_time'] for t in closed_trades]) if closed_trades else 0

        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_pnl': avg_pnl,
            'max_win': max_win,
            'max_loss': max_loss,
            'avg_hold_time': avg_hold_time,
            'current_balance': self.account['balance'],
            'current_equity': self.account['equity'],
            'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        }

    def run_trading_cycle(self) -> bool:
        """运行一个交易周期"""
        try:
            print(f"\n🔄 交易周期开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 80)

            # 1. 获取真实市场数据
            market_data = self.get_real_market_data()
            if not market_data:
                print("❌ 市场数据获取失败")
                return False

            current_price = market_data['current_price']

            # 2. 更新未实现盈亏
            self.update_unrealized_pnl(current_price)

            # 3. 检查止损止盈
            if self.check_stop_loss_take_profit(current_price):
                # 如果触发止损止盈，本周期结束
                self.save_state()
                return True

            # 4. 生成AI预测
            ai_prediction = self.generate_ai_prediction(market_data)

            # 5. 获取情绪信号
            sentiment_signal = self.get_sentiment_signal()

            # 6. 获取策略信号
            strategy_signal = self.get_strategy_signal(market_data)

            # 7. 融合信号
            final_signal = self.fuse_signals(ai_prediction, sentiment_signal, strategy_signal)

            # 8. 执行交易决策
            if self.position['size'] == 0:  # 无持仓时考虑开仓
                if final_signal['direction'] in ['LONG', 'SHORT']:
                    if final_signal['confidence'] >= self.trading_config['min_confidence']:
                        position_info = self.calculate_position_size(current_price, final_signal)
                        if position_info:
                            self.open_position(final_signal, current_price, position_info)
                        else:
                            print("⚠️ 资金不足，无法开仓")
                    else:
                        print(f"⚠️ 信号置信度不足: {final_signal['confidence']:.1%} < {self.trading_config['min_confidence']:.1%}")

            # 9. 记录权益历史
            equity_record = {
                'timestamp': datetime.now().isoformat(),
                'balance': self.account['balance'],
                'equity': self.account['equity'],
                'unrealized_pnl': self.account['unrealized_pnl'],
                'btc_price': current_price,
                'position_side': self.position['side'],
                'position_size': self.position['size'],
                'ai_prediction': ai_prediction,
                'sentiment_signal': sentiment_signal,
                'final_signal': final_signal
            }

            self.equity_history.append(equity_record)

            # 10. 打印状态
            self.print_trading_status(market_data, ai_prediction, sentiment_signal, final_signal)

            # 11. 保存状态
            self.save_state()

            return True

        except Exception as e:
            print(f"❌ 交易周期执行失败: {str(e)}")
            return False

def run_live_trading(duration_minutes: int = 60, cycle_interval: int = 300):
    """运行实时交易"""
    print("🚀 50美元模拟账户实时交易系统")
    print("=" * 100)
    print("🎯 基于第三阶段完全真实化系统")
    print("✅ 100%真实市场数据")
    print("✅ 100%真实情绪分析")
    print("✅ 完全透明记录")
    print(f"⏰ 运行时长: {duration_minutes}分钟")
    print(f"🔄 周期间隔: {cycle_interval}秒")
    print("")

    # 创建交易模拟器
    trader = LiveTradingSimulator(initial_balance=50.0)

    # 显示初始状态
    print("📊 初始账户状态:")
    stats = trader.get_trading_statistics()
    print(f"   账户余额: ${trader.account['balance']:.2f}")
    print(f"   历史交易: {stats['total_trades']}笔")

    if stats['total_trades'] > 0:
        print(f"   胜率: {stats['win_rate']:.1%}")
        print(f"   总收益率: {stats['total_return']:+.2f}%")

    print(f"\n🔄 开始实时交易循环...")

    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=duration_minutes)
    cycle_count = 0

    try:
        while datetime.now() < end_time:
            cycle_count += 1
            print(f"\n{'='*20} 第 {cycle_count} 个交易周期 {'='*20}")

            # 运行交易周期
            success = trader.run_trading_cycle()

            if not success:
                print("⚠️ 交易周期执行失败，等待下一个周期")

            # 显示简要统计
            current_stats = trader.get_trading_statistics()
            print(f"\n📈 当前统计:")
            print(f"   账户权益: ${trader.account['equity']:.2f}")
            if 'total_return' in current_stats:
                print(f"   总收益率: {current_stats['total_return']:+.2f}%")
            print(f"   交易次数: {current_stats['total_trades']}")
            if current_stats['total_trades'] > 0:
                print(f"   胜率: {current_stats['win_rate']:.1%}")

            # 等待下一个周期
            remaining_time = (end_time - datetime.now()).total_seconds()
            if remaining_time > cycle_interval:
                print(f"\n⏳ 等待 {cycle_interval} 秒后进行下一个周期...")
                time.sleep(cycle_interval)
            else:
                print(f"\n⏳ 剩余时间不足，等待 {remaining_time:.0f} 秒...")
                time.sleep(max(0, remaining_time))
                break

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断交易")

    except Exception as e:
        print(f"\n❌ 交易过程出错: {str(e)}")

    finally:
        # 显示最终结果
        print(f"\n🏁 实时交易结束")
        print("=" * 100)

        final_stats = trader.get_trading_statistics()

        print(f"📊 最终交易统计:")
        print(f"   运行时长: {(datetime.now() - start_time).total_seconds() / 60:.1f}分钟")
        print(f"   交易周期: {cycle_count}个")
        print(f"   初始资金: ${trader.initial_balance:.2f}")
        print(f"   最终权益: ${trader.account['equity']:.2f}")
        if 'total_return' in final_stats:
            print(f"   总收益率: {final_stats['total_return']:+.2f}%")
        print(f"   总交易次数: {final_stats['total_trades']}")

        if final_stats['total_trades'] > 0:
            print(f"   胜率: {final_stats['win_rate']:.1%}")
            print(f"   平均盈亏: ${final_stats['avg_pnl']:+.2f}")
            print(f"   最大盈利: ${final_stats['max_win']:+.2f}")
            print(f"   最大亏损: ${final_stats['max_loss']:+.2f}")
            print(f"   平均持仓时间: {final_stats['avg_hold_time']:.1f}小时")

        print(f"\n💾 交易数据已保存到: {trader.state_file}")

        # 生成透明度报告
        transparency_report = trader.authenticity_tracker.generate_transparency_report()
        trader.authenticity_tracker.save_transparency_report("live_trading_transparency.json")

        print(f"🔍 数据透明度:")
        print(f"   验证通过率: {transparency_report['summary']['verification_rate']:.1%}")
        print(f"   透明度等级: {transparency_report['transparency_level']}")

        return trader

if __name__ == "__main__":
    print("🎯 50美元模拟账户实时交易系统")
    print("基于第三阶段完全真实化AI交易系统")
    print("")

    # 询问运行参数
    try:
        duration = input("请输入运行时长（分钟，默认60）: ").strip()
        duration = int(duration) if duration else 60

        interval = input("请输入交易周期间隔（秒，默认300）: ").strip()
        interval = int(interval) if interval else 300

        print(f"\n🚀 启动实时交易...")
        print(f"   运行时长: {duration}分钟")
        print(f"   周期间隔: {interval}秒")

        # 运行实时交易
        trader = run_live_trading(duration_minutes=duration, cycle_interval=interval)

        print(f"\n🎉 实时交易完成！")

    except KeyboardInterrupt:
        print(f"\n👋 用户退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
