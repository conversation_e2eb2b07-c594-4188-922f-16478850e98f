#!/usr/bin/env python3
"""
高级特征工程模块
基于金融理论和机器学习最佳实践重新设计特征
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from sklearn.feature_selection import mutual_info_regression, SelectKBest, f_regression
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

# 自定义技术指标函数(替代talib)
def rsi(prices, period=14):
    """计算RSI"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def macd(prices, fast=12, slow=26, signal=9):
    """计算MACD"""
    exp1 = prices.ewm(span=fast).mean()
    exp2 = prices.ewm(span=slow).mean()
    macd_line = exp1 - exp2
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

def bollinger_bands(prices, period=20, std_dev=2):
    """计算布林带"""
    middle = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    return upper, middle, lower

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedFeatureEngineer:
    """高级特征工程器"""
    
    def __init__(self):
        self.feature_importance = {}
        self.selected_features = []
        self.scaler = StandardScaler()
        self.feature_categories = {
            'price': [],
            'volume': [],
            'volatility': [],
            'momentum': [],
            'trend': [],
            'pattern': [],
            'market_structure': []
        }
        
    def create_price_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建价格相关特征"""
        logger.info("创建价格特征...")
        features = pd.DataFrame(index=data.index)
        
        # 基础收益率特征
        for period in [1, 3, 5, 10, 15, 20]:
            features[f'returns_{period}'] = data['close'].pct_change(period)
            self.feature_categories['price'].append(f'returns_{period}')
        
        # 对数收益率
        features['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        self.feature_categories['price'].append('log_returns')
        
        # 价格位置特征
        for window in [10, 20, 50]:
            rolling_min = data['close'].rolling(window).min()
            rolling_max = data['close'].rolling(window).max()
            features[f'price_position_{window}'] = (data['close'] - rolling_min) / (rolling_max - rolling_min)
            self.feature_categories['price'].append(f'price_position_{window}')
        
        # 价格距离移动平均线
        for ma_period in [5, 10, 20, 50]:
            ma = data['close'].rolling(ma_period).mean()
            features[f'price_ma_ratio_{ma_period}'] = data['close'] / ma - 1
            self.feature_categories['price'].append(f'price_ma_ratio_{ma_period}')
        
        # 高低价特征
        features['hl_ratio'] = (data['high'] - data['low']) / data['close']
        features['oc_ratio'] = (data['close'] - data['open']) / data['open']
        features['ho_ratio'] = (data['high'] - data['open']) / data['open']
        features['lo_ratio'] = (data['low'] - data['open']) / data['open']
        
        for feat in ['hl_ratio', 'oc_ratio', 'ho_ratio', 'lo_ratio']:
            self.feature_categories['price'].append(feat)
        
        return features
    
    def create_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建成交量特征"""
        logger.info("创建成交量特征...")
        features = pd.DataFrame(index=data.index)
        
        # 成交量移动平均和比率
        for period in [5, 10, 20, 50]:
            vol_ma = data['volume'].rolling(period).mean()
            features[f'volume_ma_{period}'] = vol_ma
            features[f'volume_ratio_{period}'] = data['volume'] / vol_ma
            
            self.feature_categories['volume'].extend([f'volume_ma_{period}', f'volume_ratio_{period}'])
        
        # 成交量变化率
        for period in [1, 3, 5]:
            features[f'volume_change_{period}'] = data['volume'].pct_change(period)
            self.feature_categories['volume'].append(f'volume_change_{period}')
        
        # 价量关系
        features['price_volume_trend'] = data['close'].pct_change() * data['volume']
        features['volume_price_ratio'] = data['volume'] / data['close']
        
        # OBV (On Balance Volume)
        obv = (data['volume'] * np.sign(data['close'].diff())).cumsum()
        features['obv'] = obv
        features['obv_ma_ratio'] = obv / obv.rolling(20).mean()
        
        for feat in ['price_volume_trend', 'volume_price_ratio', 'obv', 'obv_ma_ratio']:
            self.feature_categories['volume'].append(feat)
        
        return features
    
    def create_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建波动率特征"""
        logger.info("创建波动率特征...")
        features = pd.DataFrame(index=data.index)
        
        # 实现波动率
        for window in [5, 10, 20, 50]:
            returns = data['close'].pct_change()
            vol = returns.rolling(window).std() * np.sqrt(288)  # 年化波动率
            features[f'realized_vol_{window}'] = vol
            self.feature_categories['volatility'].append(f'realized_vol_{window}')
        
        # 波动率比率
        features['vol_ratio_short_long'] = features['realized_vol_5'] / features['realized_vol_20']
        self.feature_categories['volatility'].append('vol_ratio_short_long')
        
        # Parkinson波动率 (基于高低价)
        features['parkinson_vol'] = np.sqrt(
            (1 / (4 * np.log(2))) * 
            np.log(data['high'] / data['low']).rolling(20).mean()
        ) * np.sqrt(288)
        self.feature_categories['volatility'].append('parkinson_vol')
        
        # Garman-Klass波动率
        features['gk_vol'] = np.sqrt(
            0.5 * (np.log(data['high'] / data['low'])**2).rolling(20).mean() -
            (2 * np.log(2) - 1) * (np.log(data['close'] / data['open'])**2).rolling(20).mean()
        ) * np.sqrt(288)
        self.feature_categories['volatility'].append('gk_vol')
        
        # ATR (Average True Range)
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        features['atr'] = true_range.rolling(14).mean()
        features['atr_ratio'] = features['atr'] / data['close']
        
        for feat in ['atr', 'atr_ratio']:
            self.feature_categories['volatility'].append(feat)
        
        return features
    
    def create_momentum_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建动量特征"""
        logger.info("创建动量特征...")
        features = pd.DataFrame(index=data.index)
        
        # RSI
        for period in [7, 14, 21]:
            rsi_values = rsi(data['close'], period)
            features[f'rsi_{period}'] = rsi_values
            self.feature_categories['momentum'].append(f'rsi_{period}')

        # Stochastic
        def stochastic(high, low, close, k_period=14, d_period=3):
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_period).mean()
            return k_percent, d_percent

        stoch_k, stoch_d = stochastic(data['high'], data['low'], data['close'])
        features['stoch_k'] = stoch_k
        features['stoch_d'] = stoch_d
        features['stoch_diff'] = stoch_k - stoch_d

        for feat in ['stoch_k', 'stoch_d', 'stoch_diff']:
            self.feature_categories['momentum'].append(feat)

        # Williams %R
        def williams_r(high, low, close, period=14):
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            return -100 * ((highest_high - close) / (highest_high - lowest_low))

        features['williams_r'] = williams_r(data['high'], data['low'], data['close'])
        self.feature_categories['momentum'].append('williams_r')

        # ROC (Rate of Change)
        for period in [5, 10, 20]:
            roc_values = ((data['close'] - data['close'].shift(period)) / data['close'].shift(period)) * 100
            features[f'roc_{period}'] = roc_values
            self.feature_categories['momentum'].append(f'roc_{period}')

        # MFI (Money Flow Index) - 简化版本
        if 'volume' in data.columns:
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            money_flow = typical_price * data['volume']
            positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(14).sum()
            negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(14).sum()
            features['mfi'] = 100 - (100 / (1 + positive_flow / negative_flow))
            self.feature_categories['momentum'].append('mfi')
        
        return features
    
    def create_trend_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建趋势特征"""
        logger.info("创建趋势特征...")
        features = pd.DataFrame(index=data.index)
        
        # MACD
        macd_line, macd_signal, macd_hist = macd(data['close'])
        features['macd'] = macd_line
        features['macd_signal'] = macd_signal
        features['macd_histogram'] = macd_hist
        features['macd_ratio'] = macd_line / macd_signal

        for feat in ['macd', 'macd_signal', 'macd_histogram', 'macd_ratio']:
            self.feature_categories['trend'].append(feat)

        # ADX (Average Directional Index) - 简化版本
        def calculate_adx(high, low, close, period=14):
            # 计算真实范围
            tr1 = high - low
            tr2 = abs(high - close.shift())
            tr3 = abs(low - close.shift())
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

            # 计算方向移动
            dm_plus = high.diff()
            dm_minus = -low.diff()
            dm_plus[dm_plus < 0] = 0
            dm_minus[dm_minus < 0] = 0

            # 平滑处理
            tr_smooth = tr.rolling(period).mean()
            dm_plus_smooth = dm_plus.rolling(period).mean()
            dm_minus_smooth = dm_minus.rolling(period).mean()

            # 计算DI
            di_plus = 100 * dm_plus_smooth / tr_smooth
            di_minus = 100 * dm_minus_smooth / tr_smooth

            # 计算ADX
            dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
            adx = dx.rolling(period).mean()

            return adx, di_plus, di_minus

        adx_val, di_plus_val, di_minus_val = calculate_adx(data['high'], data['low'], data['close'])
        features['adx'] = adx_val
        features['di_plus'] = di_plus_val
        features['di_minus'] = di_minus_val
        features['di_diff'] = di_plus_val - di_minus_val

        for feat in ['adx', 'di_plus', 'di_minus', 'di_diff']:
            self.feature_categories['trend'].append(feat)
        
        # 移动平均线系统
        ma_periods = [5, 10, 20, 50, 100]
        for i, period in enumerate(ma_periods):
            ma = data['close'].rolling(period).mean()
            features[f'ma_{period}'] = ma
            features[f'ma_slope_{period}'] = (ma - ma.shift(5)) / ma.shift(5)
            
            # 移动平均线交叉
            if i > 0:
                prev_period = ma_periods[i-1]
                ma_short = features[f'ma_{prev_period}']
                features[f'ma_cross_{prev_period}_{period}'] = (ma_short > ma).astype(int)
        
        # 布林带
        bb_upper, bb_middle, bb_lower = bollinger_bands(data['close'])
        features['bb_upper'] = bb_upper
        features['bb_middle'] = bb_middle
        features['bb_lower'] = bb_lower
        features['bb_width'] = (bb_upper - bb_lower) / bb_middle
        features['bb_position'] = (data['close'] - bb_lower) / (bb_upper - bb_lower)
        
        for feat in ['bb_width', 'bb_position']:
            self.feature_categories['trend'].append(feat)
        
        return features
    
    def create_pattern_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建模式识别特征"""
        logger.info("创建模式特征...")
        features = pd.DataFrame(index=data.index)
        

        
        # 简化的蜡烛图模式识别
        # Doji模式
        body_size = abs(data['close'] - data['open'])
        total_range = data['high'] - data['low']
        features['cdldoji'] = (body_size / total_range < 0.1).astype(int)

        # 锤子模式
        lower_shadow = data['open'].combine(data['close'], min) - data['low']
        upper_shadow = data['high'] - data['open'].combine(data['close'], max)
        features['cdlhammer'] = ((lower_shadow > 2 * body_size) & (upper_shadow < body_size)).astype(int)

        # 吞没模式
        bullish_engulf = ((data['close'] > data['open']) &
                         (data['close'].shift() < data['open'].shift()) &
                         (data['open'] < data['close'].shift()) &
                         (data['close'] > data['open'].shift())).astype(int)
        features['cdlengulfing'] = bullish_engulf

        for feat in ['cdldoji', 'cdlhammer', 'cdlengulfing']:
            self.feature_categories['pattern'].append(feat)
        
        return features
    
    def create_market_structure_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建市场结构特征"""
        logger.info("创建市场结构特征...")
        features = pd.DataFrame(index=data.index)
        
        # 支撑阻力水平
        for window in [20, 50]:
            rolling_max = data['high'].rolling(window).max()
            rolling_min = data['low'].rolling(window).min()
            
            features[f'resistance_distance_{window}'] = (rolling_max - data['close']) / data['close']
            features[f'support_distance_{window}'] = (data['close'] - rolling_min) / data['close']
            
            self.feature_categories['market_structure'].extend([
                f'resistance_distance_{window}', f'support_distance_{window}'
            ])
        
        # 价格突破
        for window in [10, 20]:
            high_breakout = data['close'] > data['high'].rolling(window).max().shift(1)
            low_breakout = data['close'] < data['low'].rolling(window).min().shift(1)
            
            features[f'high_breakout_{window}'] = high_breakout.astype(int)
            features[f'low_breakout_{window}'] = low_breakout.astype(int)
            
            self.feature_categories['market_structure'].extend([
                f'high_breakout_{window}', f'low_breakout_{window}'
            ])
        
        # 成交量确认
        vol_ma = data['volume'].rolling(20).mean()
        features['volume_confirmation'] = (data['volume'] > vol_ma * 1.5).astype(int)
        self.feature_categories['market_structure'].append('volume_confirmation')
        
        return features
    
    def create_all_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """创建所有特征"""
        logger.info("开始创建所有特征...")
        

        
        # 创建各类特征
        price_features = self.create_price_features(data)
        volume_features = self.create_volume_features(data)
        volatility_features = self.create_volatility_features(data)
        momentum_features = self.create_momentum_features(data)
        trend_features = self.create_trend_features(data)
        pattern_features = self.create_pattern_features(data)
        market_structure_features = self.create_market_structure_features(data)
        
        # 合并所有特征
        feature_dfs = [
            price_features, volume_features, volatility_features,
            momentum_features, trend_features, pattern_features,
            market_structure_features
        ]
        
        combined_features = pd.concat(feature_dfs, axis=1)
        
        # 处理无穷值和缺失值
        combined_features = combined_features.replace([np.inf, -np.inf], np.nan)
        combined_features = combined_features.fillna(method='ffill').fillna(method='bfill')
        
        logger.info(f"创建了 {len(combined_features.columns)} 个特征")
        return combined_features
    
    def select_best_features(self, features: pd.DataFrame, target: pd.Series, 
                           n_features: int = 30) -> List[str]:
        """选择最佳特征"""
        logger.info(f"开始特征选择，目标选择 {n_features} 个特征...")
        
        # 确保数据对齐
        common_index = features.index.intersection(target.index)
        features_aligned = features.loc[common_index]
        target_aligned = target.loc[common_index]
        
        # 去除缺失值
        valid_mask = ~(features_aligned.isnull().any(axis=1) | target_aligned.isnull())
        features_clean = features_aligned[valid_mask]
        target_clean = target_aligned[valid_mask]
        
        if len(features_clean) == 0:
            logger.error("没有有效的特征数据")
            return []
        
        logger.info(f"有效数据点: {len(features_clean)}")
        
        # 方法1: 互信息
        mi_selector = SelectKBest(mutual_info_regression, k=min(n_features, len(features_clean.columns)))
        mi_selector.fit(features_clean, target_clean)
        mi_features = features_clean.columns[mi_selector.get_support()].tolist()
        
        # 方法2: F统计量
        f_selector = SelectKBest(f_regression, k=min(n_features, len(features_clean.columns)))
        f_selector.fit(features_clean, target_clean)
        f_features = features_clean.columns[f_selector.get_support()].tolist()
        
        # 方法3: 随机森林特征重要性
        rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(features_clean, target_clean)
        rf_importance = pd.Series(rf.feature_importances_, index=features_clean.columns)
        rf_features = rf_importance.nlargest(n_features).index.tolist()
        
        # 综合选择
        feature_votes = {}
        for feature in features_clean.columns:
            votes = 0
            if feature in mi_features:
                votes += 1
            if feature in f_features:
                votes += 1
            if feature in rf_features:
                votes += 1
            feature_votes[feature] = votes
        
        # 选择得票最多的特征
        sorted_features = sorted(feature_votes.items(), key=lambda x: x[1], reverse=True)
        selected_features = [feat for feat, votes in sorted_features[:n_features] if votes > 0]
        
        # 记录特征重要性
        self.feature_importance = {
            'mutual_info': dict(zip(mi_features, mi_selector.scores_[mi_selector.get_support()])),
            'f_statistic': dict(zip(f_features, f_selector.scores_[f_selector.get_support()])),
            'random_forest': rf_importance.to_dict(),
            'combined_votes': feature_votes
        }
        
        self.selected_features = selected_features
        
        logger.info(f"选择了 {len(selected_features)} 个特征")
        logger.info(f"特征分布: {self._analyze_feature_distribution(selected_features)}")
        
        return selected_features
    
    def _analyze_feature_distribution(self, selected_features: List[str]) -> Dict[str, int]:
        """分析选择特征的分布"""
        distribution = {category: 0 for category in self.feature_categories.keys()}
        
        for feature in selected_features:
            for category, features_in_category in self.feature_categories.items():
                if feature in features_in_category:
                    distribution[category] += 1
                    break
        
        return distribution
    
    def get_feature_engineering_report(self) -> Dict:
        """获取特征工程报告"""
        return {
            'total_features_created': sum(len(features) for features in self.feature_categories.values()),
            'selected_features_count': len(self.selected_features),
            'feature_distribution': self._analyze_feature_distribution(self.selected_features),
            'feature_categories': {k: len(v) for k, v in self.feature_categories.items()},
            'selected_features': self.selected_features,
            'feature_importance': self.feature_importance
        }

if __name__ == "__main__":
    # 测试特征工程
    logger.info("测试高级特征工程...")
    
    # 创建模拟数据
    dates = pd.date_range('2024-01-01', periods=1000, freq='5T')
    np.random.seed(42)
    
    # 模拟价格数据
    price = 100
    prices = []
    volumes = []

    for i in range(1000):
        change = np.random.normal(0, 0.001)
        price *= (1 + change)
        prices.append(price)
        volumes.append(np.random.lognormal(10, 1))

    data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
        'close': prices,
        'volume': volumes
    }, index=dates)
    
    # 确保OHLC数据的合理性
    data['high'] = np.maximum(data['high'], np.maximum(data['open'], data['close']))
    data['low'] = np.minimum(data['low'], np.minimum(data['open'], data['close']))
    
    # 创建特征工程器
    feature_engineer = AdvancedFeatureEngineer()
    
    # 创建所有特征
    features = feature_engineer.create_all_features(data)
    
    # 创建目标变量(未来5期收益率)
    target = data['close'].pct_change(5).shift(-5)
    
    # 特征选择
    selected_features = feature_engineer.select_best_features(features, target, n_features=20)
    
    # 获取报告
    report = feature_engineer.get_feature_engineering_report()
    
    print("🎉 高级特征工程测试完成！")
    print(f"📊 创建特征总数: {report['total_features_created']}")
    print(f"🎯 选择特征数量: {report['selected_features_count']}")
    print(f"📈 特征分布: {report['feature_distribution']}")
    print(f"✅ 选择的特征: {selected_features[:10]}...")  # 显示前10个
