#!/usr/bin/env python3
"""
多策略集成框架 - 动态策略切换系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class MultiStrategyFramework:
    """
    多策略集成框架 - 基于市场条件动态切换策略
    """
    
    def __init__(self, ai_model_probability: float = 0.372):
        """
        初始化多策略框架
        """
        self.ai_probability = ai_model_probability
        self.strategy_weights = {}
        self.market_regime = None
        self.active_strategies = []
        
        # 策略配置
        self.strategy_configs = {
            'momentum': {
                'weight_base': 0.3,
                'best_conditions': ['trending', 'high_volatility'],
                'min_confidence': 0.6,
                'max_positions': 2
            },
            'mean_reversion': {
                'weight_base': 0.25,
                'best_conditions': ['sideways', 'low_volatility'],
                'min_confidence': 0.7,
                'max_positions': 3
            },
            'trend_following': {
                'weight_base': 0.2,
                'best_conditions': ['strong_trend', 'medium_volatility'],
                'min_confidence': 0.65,
                'max_positions': 1
            },
            'ai_enhanced': {
                'weight_base': 0.25,
                'best_conditions': ['any'],
                'min_confidence': 0.55,
                'max_positions': 2
            }
        }
        
        print(f"🎯 多策略框架初始化完成")
        print(f"   AI概率输入: {self.ai_probability:.1%}")
        print(f"   策略数量: {len(self.strategy_configs)}")
    
    def analyze_market_regime(self, data: pd.DataFrame) -> Dict:
        """
        分析市场状态
        """
        current_price = data['close'].iloc[-1]
        
        # 趋势分析
        ma_20 = data['close'].rolling(20).mean()
        ma_50 = data['close'].rolling(50).mean()
        
        trend_direction = 'up' if ma_20.iloc[-1] > ma_50.iloc[-1] else 'down'
        trend_strength = abs(ma_20.iloc[-1] - ma_50.iloc[-1]) / ma_50.iloc[-1]
        
        # 波动率分析
        returns = data['close'].pct_change().dropna()
        volatility = returns.tail(20).std() * np.sqrt(24)
        
        # 价格位置分析
        high_20 = data['high'].rolling(20).max().iloc[-1]
        low_20 = data['low'].rolling(20).min().iloc[-1]
        price_position = (current_price - low_20) / (high_20 - low_20)
        
        # 成交量分析
        volume_avg = data['volume'].rolling(20).mean().iloc[-1]
        volume_current = data['volume'].iloc[-1]
        volume_ratio = volume_current / volume_avg
        
        # 市场状态分类
        if trend_strength > 0.03 and volatility > 0.04:
            regime = 'strong_trending'
        elif trend_strength > 0.015 and volatility > 0.025:
            regime = 'trending'
        elif volatility < 0.02:
            regime = 'low_volatility'
        elif 0.3 < price_position < 0.7:
            regime = 'sideways'
        else:
            regime = 'transitional'
        
        self.market_regime = {
            'regime': regime,
            'trend_direction': trend_direction,
            'trend_strength': trend_strength,
            'volatility': volatility,
            'price_position': price_position,
            'volume_ratio': volume_ratio
        }
        
        return self.market_regime
    
    def calculate_strategy_weights(self, market_regime: Dict) -> Dict:
        """
        根据市场状态计算策略权重
        """
        weights = {}
        regime = market_regime['regime']
        volatility = market_regime['volatility']
        trend_strength = market_regime['trend_strength']
        
        # 基础权重
        for strategy, config in self.strategy_configs.items():
            weights[strategy] = config['weight_base']
        
        # 根据市场状态调整权重
        if regime == 'strong_trending':
            weights['momentum'] *= 1.5
            weights['trend_following'] *= 1.3
            weights['mean_reversion'] *= 0.5
        
        elif regime == 'trending':
            weights['momentum'] *= 1.2
            weights['trend_following'] *= 1.1
            weights['mean_reversion'] *= 0.8
        
        elif regime == 'sideways':
            weights['mean_reversion'] *= 1.4
            weights['momentum'] *= 0.6
            weights['trend_following'] *= 0.7
        
        elif regime == 'low_volatility':
            weights['mean_reversion'] *= 1.3
            weights['ai_enhanced'] *= 1.2
            weights['momentum'] *= 0.7
        
        # AI概率调整
        ai_strength = abs(self.ai_probability - 0.5) * 2  # 转换为0-1
        weights['ai_enhanced'] *= (1 + ai_strength)
        
        # 归一化权重
        total_weight = sum(weights.values())
        weights = {k: v/total_weight for k, v in weights.items()}
        
        self.strategy_weights = weights
        return weights
    
    def generate_momentum_signals(self, data: pd.DataFrame) -> List[Dict]:
        """
        生成动量策略信号
        """
        signals = []
        current_price = data['close'].iloc[-1]
        
        # 突破信号
        high_20 = data['high'].rolling(20).max()
        low_20 = data['low'].rolling(20).min()
        volume_avg = data['volume'].rolling(20).mean()
        
        # 向上突破
        if (current_price > high_20.iloc[-2] and 
            data['volume'].iloc[-1] > volume_avg.iloc[-1] * 1.2):
            signals.append({
                'strategy': 'momentum',
                'direction': 'LONG',
                'entry_price': current_price,
                'stop_loss': low_20.iloc[-1],
                'take_profit': current_price + (current_price - low_20.iloc[-1]) * 1.5,
                'confidence': 0.75,
                'reason': '向上突破确认'
            })
        
        # 向下突破
        if (current_price < low_20.iloc[-2] and 
            data['volume'].iloc[-1] > volume_avg.iloc[-1] * 1.2):
            signals.append({
                'strategy': 'momentum',
                'direction': 'SHORT',
                'entry_price': current_price,
                'stop_loss': high_20.iloc[-1],
                'take_profit': current_price - (high_20.iloc[-1] - current_price) * 1.5,
                'confidence': 0.75,
                'reason': '向下突破确认'
            })
        
        return signals
    
    def generate_mean_reversion_signals(self, data: pd.DataFrame) -> List[Dict]:
        """
        生成均值回归策略信号
        """
        signals = []
        current_price = data['close'].iloc[-1]
        
        # 布林带
        ma_20 = data['close'].rolling(20).mean()
        std_20 = data['close'].rolling(20).std()
        upper_band = ma_20 + (std_20 * 2)
        lower_band = ma_20 - (std_20 * 2)
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # 超卖反弹
        if (current_price < lower_band.iloc[-1] and rsi.iloc[-1] < 35):
            signals.append({
                'strategy': 'mean_reversion',
                'direction': 'LONG',
                'entry_price': current_price,
                'stop_loss': current_price * 0.975,
                'take_profit': ma_20.iloc[-1],
                'confidence': 0.8,
                'reason': '超卖反弹机会'
            })
        
        # 超买回调
        if (current_price > upper_band.iloc[-1] and rsi.iloc[-1] > 65):
            signals.append({
                'strategy': 'mean_reversion',
                'direction': 'SHORT',
                'entry_price': current_price,
                'stop_loss': current_price * 1.025,
                'take_profit': ma_20.iloc[-1],
                'confidence': 0.8,
                'reason': '超买回调机会'
            })
        
        return signals
    
    def generate_trend_following_signals(self, data: pd.DataFrame) -> List[Dict]:
        """
        生成趋势跟踪策略信号
        """
        signals = []
        current_price = data['close'].iloc[-1]
        
        # 多重均线
        ma_10 = data['close'].rolling(10).mean()
        ma_20 = data['close'].rolling(20).mean()
        ma_50 = data['close'].rolling(50).mean()
        
        # 上升趋势
        if (ma_10.iloc[-1] > ma_20.iloc[-1] > ma_50.iloc[-1] and
            current_price > ma_10.iloc[-1]):
            signals.append({
                'strategy': 'trend_following',
                'direction': 'LONG',
                'entry_price': current_price,
                'stop_loss': ma_20.iloc[-1],
                'take_profit': current_price * 1.06,
                'confidence': 0.7,
                'reason': '多重均线确认上升趋势'
            })
        
        # 下降趋势
        if (ma_10.iloc[-1] < ma_20.iloc[-1] < ma_50.iloc[-1] and
            current_price < ma_10.iloc[-1]):
            signals.append({
                'strategy': 'trend_following',
                'direction': 'SHORT',
                'entry_price': current_price,
                'stop_loss': ma_20.iloc[-1],
                'take_profit': current_price * 0.94,
                'confidence': 0.7,
                'reason': '多重均线确认下降趋势'
            })
        
        return signals
    
    def generate_ai_enhanced_signals(self, data: pd.DataFrame) -> List[Dict]:
        """
        生成AI增强策略信号
        """
        signals = []
        current_price = data['close'].iloc[-1]
        
        # 基于AI概率的信号强度
        if self.ai_probability < 0.35:  # 强看跌
            signals.append({
                'strategy': 'ai_enhanced',
                'direction': 'SHORT',
                'entry_price': current_price,
                'stop_loss': current_price * 1.025,
                'take_profit': current_price * 0.94,
                'confidence': 1 - self.ai_probability,
                'reason': f'AI强看跌信号 ({self.ai_probability:.1%})'
            })
        
        elif self.ai_probability > 0.65:  # 强看涨
            signals.append({
                'strategy': 'ai_enhanced',
                'direction': 'LONG',
                'entry_price': current_price,
                'stop_loss': current_price * 0.975,
                'take_profit': current_price * 1.06,
                'confidence': self.ai_probability,
                'reason': f'AI强看涨信号 ({self.ai_probability:.1%})'
            })
        
        elif self.ai_probability < 0.45:  # 弱看跌
            # 结合技术指标确认
            ma_20 = data['close'].rolling(20).mean().iloc[-1]
            if current_price < ma_20:
                signals.append({
                    'strategy': 'ai_enhanced',
                    'direction': 'SHORT',
                    'entry_price': current_price,
                    'stop_loss': current_price * 1.02,
                    'take_profit': current_price * 0.97,
                    'confidence': 0.6,
                    'reason': f'AI弱看跌+技术确认 ({self.ai_probability:.1%})'
                })
        
        return signals
    
    def integrate_all_signals(self, data: pd.DataFrame) -> Dict:
        """
        整合所有策略信号
        """
        # 分析市场状态
        market_regime = self.analyze_market_regime(data)
        
        # 计算策略权重
        weights = self.calculate_strategy_weights(market_regime)
        
        # 生成各策略信号
        all_signals = {
            'momentum': self.generate_momentum_signals(data),
            'mean_reversion': self.generate_mean_reversion_signals(data),
            'trend_following': self.generate_trend_following_signals(data),
            'ai_enhanced': self.generate_ai_enhanced_signals(data)
        }
        
        # 加权整合信号
        integrated_signals = []
        
        for strategy, signals in all_signals.items():
            weight = weights.get(strategy, 0)
            for signal in signals:
                # 调整信号置信度
                signal['weighted_confidence'] = signal['confidence'] * weight
                signal['strategy_weight'] = weight
                integrated_signals.append(signal)
        
        # 按加权置信度排序
        integrated_signals.sort(key=lambda x: x['weighted_confidence'], reverse=True)
        
        return {
            'market_regime': market_regime,
            'strategy_weights': weights,
            'all_signals': all_signals,
            'integrated_signals': integrated_signals[:5],  # 取前5个最强信号
            'recommendation': self._generate_final_recommendation(integrated_signals)
        }
    
    def _generate_final_recommendation(self, signals: List[Dict]) -> Dict:
        """
        生成最终推荐
        """
        if not signals:
            return {
                'action': 'WAIT',
                'reason': '无明确信号',
                'confidence': 0
            }
        
        # 取最强信号
        top_signal = signals[0]
        
        # 检查信号一致性
        top_3_signals = signals[:3]
        long_count = sum(1 for s in top_3_signals if s['direction'] == 'LONG')
        short_count = sum(1 for s in top_3_signals if s['direction'] == 'SHORT')
        
        if long_count > short_count:
            direction = 'LONG'
            consensus = long_count / len(top_3_signals)
        elif short_count > long_count:
            direction = 'SHORT'
            consensus = short_count / len(top_3_signals)
        else:
            direction = 'NEUTRAL'
            consensus = 0.5
        
        return {
            'action': direction,
            'primary_strategy': top_signal['strategy'],
            'entry_price': top_signal['entry_price'],
            'stop_loss': top_signal['stop_loss'],
            'take_profit': top_signal['take_profit'],
            'confidence': top_signal['weighted_confidence'],
            'consensus': consensus,
            'reason': top_signal['reason'],
            'supporting_strategies': len([s for s in top_3_signals if s['direction'] == direction])
        }
    
    def print_multi_strategy_analysis(self, analysis: Dict):
        """
        打印多策略分析结果
        """
        print(f"\n🎯 【多策略集成分析】")
        print("=" * 70)
        
        # 市场状态
        regime = analysis['market_regime']
        print(f"📊 市场状态:")
        print(f"   状态: {regime['regime']}")
        print(f"   趋势方向: {regime['trend_direction']}")
        print(f"   趋势强度: {regime['trend_strength']:.2%}")
        print(f"   波动率: {regime['volatility']:.2%}")
        
        # 策略权重
        weights = analysis['strategy_weights']
        print(f"\n⚖️ 策略权重分配:")
        for strategy, weight in weights.items():
            print(f"   {strategy}: {weight:.1%}")
        
        # 信号汇总
        print(f"\n🚀 策略信号汇总:")
        for strategy, signals in analysis['all_signals'].items():
            if signals:
                print(f"\n   📈 {strategy.upper()}策略:")
                for signal in signals:
                    direction_emoji = "🟢" if signal['direction'] == 'LONG' else "🔴"
                    print(f"      {direction_emoji} {signal['direction']} "
                          f"(置信度: {signal['confidence']:.1%})")
                    print(f"         理由: {signal['reason']}")
        
        # 最终推荐
        rec = analysis['recommendation']
        print(f"\n🎯 最终推荐:")
        action_emoji = {"LONG": "🟢", "SHORT": "🔴", "NEUTRAL": "🟡", "WAIT": "⏸️"}
        print(f"   {action_emoji.get(rec['action'], '❓')} 动作: {rec['action']}")
        print(f"   主导策略: {rec.get('primary_strategy', 'N/A')}")
        print(f"   置信度: {rec['confidence']:.1%}")
        print(f"   一致性: {rec.get('consensus', 0):.1%}")
        print(f"   理由: {rec['reason']}")
        
        if rec['action'] in ['LONG', 'SHORT']:
            print(f"   入场价: ${rec['entry_price']:,.2f}")
            print(f"   止损价: ${rec['stop_loss']:,.2f}")
            print(f"   止盈价: ${rec['take_profit']:,.2f}")

def demonstrate_multi_strategy():
    """
    演示多策略框架
    """
    print("🚀 多策略集成框架演示")
    print("=" * 50)
    
    # 创建模拟数据
    dates = pd.date_range(start='2024-01-01', periods=100, freq='H')
    np.random.seed(42)
    
    # 模拟价格数据
    price_changes = np.random.normal(0, 0.02, 100)
    prices = [104426.90]  # 当前BTC价格
    for change in price_changes:
        prices.append(prices[-1] * (1 + change))
    
    data = pd.DataFrame({
        'timestamp': dates,
        'open': prices[:-1],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices[:-1]],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices[:-1]],
        'close': prices[1:],
        'volume': np.random.normal(1000, 200, 100)
    })
    
    # 初始化多策略框架
    framework = MultiStrategyFramework(ai_model_probability=0.372)
    
    # 执行分析
    analysis = framework.integrate_all_signals(data)
    
    # 打印结果
    framework.print_multi_strategy_analysis(analysis)

if __name__ == "__main__":
    demonstrate_multi_strategy()
