#!/usr/bin/env python3
"""
激进平衡训练脚本 - 处理极度不平衡的数据
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import RobustScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, balanced_accuracy_score
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
import joblib

try:
    import xgboost as xgb
    HAS_XGB = True
except ImportError:
    HAS_XGB = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def aggressive_balance_train(symbol='BTCUSDT', model_type='xgb', months_back=35):
    """
    激进平衡训练 - 专门处理极度不平衡数据
    """
    print(f"🚀 开始激进平衡训练 {symbol} {model_type.upper()} 模型...")
    print(f"📊 数据: {months_back}个月")
    print("⚠️  注意：将使用激进的平衡策略")
    
    try:
        # 1. 获取数据
        print("📊 获取历史数据...")
        start_date = (datetime.now() - timedelta(days=months_back*30)).strftime('%Y-%m-%d')
        
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, '1h', start_date, force_refresh=False)
        
        print(f"✅ 获取到 {len(df)} 条数据")
        
        # 2. 特征工程
        print("🔧 特征工程...")
        engineer = FeatureEngineer()
        df_features = engineer.create_features(df)
        
        # 3. 数据准备
        print("📋 数据准备...")
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始列
        original_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
        
        # 数据清理
        before_clean = len(X)
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]
        
        X.replace([np.inf, -np.inf], np.nan, inplace=True)
        X.fillna(X.median(), inplace=True)
        
        print(f"数据清理: {before_clean} -> {len(X)} 样本")
        
        # 编码标签
        label_encoder = LabelEncoder()
        y_encoded = label_encoder.fit_transform(y)
        
        # 分析原始类别分布
        unique, counts = np.unique(y_encoded, return_counts=True)
        total_samples = len(y_encoded)
        
        print(f"\n📊 原始类别分布:")
        for class_id, count in zip(unique, counts):
            percentage = count / total_samples * 100
            print(f"   类别 {class_id}: {count:,} 样本 ({percentage:.1f}%)")
        
        # 4. 激进的类别重新定义
        print(f"\n🔄 激进类别重新定义...")
        
        # 策略1: 只保留有足够样本的类别，其他合并
        min_samples_threshold = 500  # 每个类别至少500个样本
        major_classes = unique[counts >= min_samples_threshold]
        
        print(f"   保留主要类别: {major_classes} (样本数 >= {min_samples_threshold})")
        
        # 重新映射类别
        y_simplified = np.full_like(y_encoded, -1)  # 初始化为-1
        
        # 保留主要类别
        for i, class_id in enumerate(major_classes):
            mask = y_encoded == class_id
            y_simplified[mask] = i
        
        # 将所有其他类别合并为"其他"类别
        other_mask = y_simplified == -1
        if other_mask.sum() > 0:
            y_simplified[other_mask] = len(major_classes)  # 最后一个类别为"其他"
        
        # 更新类别信息
        unique_simplified, counts_simplified = np.unique(y_simplified, return_counts=True)
        
        print(f"\n📊 简化后类别分布:")
        for class_id, count in zip(unique_simplified, counts_simplified):
            percentage = count / len(y_simplified) * 100
            if class_id < len(major_classes):
                original_class = major_classes[class_id]
                print(f"   类别 {class_id} (原类别{original_class}): {count:,} 样本 ({percentage:.1f}%)")
            else:
                print(f"   类别 {class_id} (其他类别): {count:,} 样本 ({percentage:.1f}%)")
        
        # 5. 激进的下采样 + 适度过采样
        print(f"\n⚖️ 激进平衡策略...")
        
        # 第一步：激进下采样主要类别
        max_samples_per_class = min(2000, max(counts_simplified) // 3)  # 最多2000样本或主要类别的1/3
        
        print(f"   第一步：下采样到每类最多 {max_samples_per_class} 样本")
        
        undersampler = RandomUnderSampler(
            sampling_strategy={
                class_id: min(count, max_samples_per_class)
                for class_id, count in zip(unique_simplified, counts_simplified)
            },
            random_state=42
        )
        
        X_undersampled, y_undersampled = undersampler.fit_resample(X, y_simplified)
        
        # 显示下采样后的分布
        unique_under, counts_under = np.unique(y_undersampled, return_counts=True)
        print(f"   下采样后分布: {dict(zip(unique_under, counts_under))}")
        
        # 第二步：适度过采样少数类别
        min_samples_target = max(200, min(counts_under) * 2)  # 目标最少样本数
        
        print(f"   第二步：过采样少数类别到至少 {min_samples_target} 样本")
        
        # 检查是否需要过采样
        need_oversample = any(count < min_samples_target for count in counts_under)
        
        if need_oversample:
            # 计算过采样策略
            oversample_strategy = {}
            for class_id, count in zip(unique_under, counts_under):
                if count < min_samples_target:
                    oversample_strategy[class_id] = min_samples_target
            
            print(f"   过采样策略: {oversample_strategy}")
            
            # 应用SMOTE
            smote = SMOTE(
                sampling_strategy=oversample_strategy,
                random_state=42,
                k_neighbors=min(5, min(counts_under)-1)
            )
            
            X_balanced, y_balanced = smote.fit_resample(X_undersampled, y_undersampled)
        else:
            X_balanced, y_balanced = X_undersampled, y_undersampled
        
        # 显示最终平衡结果
        unique_final, counts_final = np.unique(y_balanced, return_counts=True)
        print(f"\n📊 最终平衡后分布:")
        for class_id, count in zip(unique_final, counts_final):
            percentage = count / len(y_balanced) * 100
            print(f"   类别 {class_id}: {count:,} 样本 ({percentage:.1f}%)")
        
        final_imbalance_ratio = max(counts_final) / min(counts_final)
        print(f"   最终不平衡比例: {final_imbalance_ratio:.1f}:1")
        
        # 6. 时间序列分割
        print("\n✂️  时间序列分割...")
        split_point = int(len(X_balanced) * 0.8)
        
        X_train = X_balanced[:split_point]
        X_test = X_balanced[split_point:]
        y_train = y_balanced[:split_point]
        y_test = y_balanced[split_point:]
        
        print(f"   训练集: {len(X_train):,} 样本")
        print(f"   测试集: {len(X_test):,} 样本")
        
        # 检查测试集类别分布
        test_unique, test_counts = np.unique(y_test, return_counts=True)
        print(f"   测试集类别分布: {dict(zip(test_unique, test_counts))}")
        
        # 7. 特征缩放
        print("📏 特征缩放...")
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 8. 创建保守的模型
        print(f"🎯 创建保守的 {model_type.upper()} 模型...")
        
        if model_type == 'xgb' and HAS_XGB:
            model = xgb.XGBClassifier(
                n_estimators=80,        # 减少树的数量
                max_depth=4,            # 减少深度
                learning_rate=0.08,     # 降低学习率
                subsample=0.8,          # 子采样
                colsample_bytree=0.8,   # 特征子采样
                reg_alpha=1.0,          # L1正则化
                reg_lambda=1.0,         # L2正则化
                min_child_weight=5,     # 增加最小子权重
                random_state=42,
                n_jobs=-1,
                eval_metric='mlogloss'
            )
        else:
            model = RandomForestClassifier(
                n_estimators=80,
                max_depth=6,
                min_samples_split=10,
                min_samples_leaf=5,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1
            )
        
        # 9. 训练和评估
        print("🏃 训练模型...")
        model.fit(X_train_scaled, y_train)
        
        print("📊 评估模型...")
        y_train_pred = model.predict(X_train_scaled)
        y_test_pred = model.predict(X_test_scaled)
        
        train_accuracy = accuracy_score(y_train, y_train_pred)
        test_accuracy = accuracy_score(y_test, y_test_pred)
        train_balanced = balanced_accuracy_score(y_train, y_train_pred)
        test_balanced = balanced_accuracy_score(y_test, y_test_pred)
        
        # 10. 显示结果
        print("\n" + "="*60)
        print("🎉 激进平衡训练完成!")
        print("="*60)
        print(f"原始样本: {total_samples:,}")
        print(f"原始类别数: {len(unique)} -> 简化后: {len(unique_final)}")
        print(f"最终样本: {len(y_balanced):,}")
        print(f"不平衡比例: {final_imbalance_ratio:.1f}:1")
        print(f"")
        print(f"训练集准确率:     {train_accuracy:.4f}")
        print(f"测试集准确率:     {test_accuracy:.4f}")
        print(f"训练集平衡准确率: {train_balanced:.4f}")
        print(f"测试集平衡准确率: {test_balanced:.4f}")
        
        overfitting = train_accuracy - test_accuracy
        print(f"\n📊 模型健康度:")
        print(f"过拟合程度: {overfitting:.4f}")
        if overfitting > 0.15:
            print("⚠️  严重过拟合")
        elif overfitting > 0.1:
            print("⚠️  存在过拟合")
        else:
            print("✅ 泛化能力良好")
        
        # 详细分类报告
        print("\n📋 详细分类报告:")
        print(classification_report(y_test, y_test_pred, zero_division=0))
        
        # 11. 特征重要性
        if hasattr(model, 'feature_importances_'):
            print("\n🔍 Top 10 重要特征:")
            feature_importance = pd.Series(
                model.feature_importances_, 
                index=X.columns
            ).sort_values(ascending=False)
            
            for i, (feature, importance) in enumerate(feature_importance.head(10).items(), 1):
                print(f"{i:2d}. {feature:25}: {importance:.6f}")
        
        # 12. 保存模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path("models")
        model_dir.mkdir(exist_ok=True)
        
        model_path = model_dir / f"aggressive_balanced_{model_type}_{symbol}_{timestamp}.joblib"
        scaler_path = model_dir / f"aggressive_scaler_{model_type}_{symbol}_{timestamp}.joblib"
        encoder_path = model_dir / f"aggressive_encoder_{model_type}_{symbol}_{timestamp}.joblib"
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        joblib.dump(label_encoder, encoder_path)
        
        print(f"\n💾 模型已保存: {model_path}")
        
        return {
            'model': model,
            'scaler': scaler,
            'label_encoder': label_encoder,
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'balanced_accuracy': test_balanced,
            'final_imbalance_ratio': final_imbalance_ratio,
            'num_classes': len(unique_final),
            'total_samples': len(y_balanced),
            'model_path': model_path
        }
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    import sys
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    model_type = sys.argv[2] if len(sys.argv) > 2 else 'xgb'
    months = int(sys.argv[3]) if len(sys.argv) > 3 else 35
    
    if model_type == 'xgb' and not HAS_XGB:
        print("XGBoost不可用，使用随机森林")
        model_type = 'rf'
    
    aggressive_balance_train(symbol, model_type, months)
