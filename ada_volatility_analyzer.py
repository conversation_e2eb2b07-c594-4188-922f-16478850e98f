#!/usr/bin/env python3
"""
ADAUSDT波动率分析器
分析1分钟涨幅，为剥头皮交易设置合理的止盈止损
"""

import pandas as pd
import numpy as np
import requests
import logging
from datetime import datetime
import matplotlib.pyplot as plt

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_ada_recent_data(limit: int = 500) -> pd.DataFrame:
    """获取ADA最近的K线数据"""
    try:
        url = "https://fapi.binance.com/fapi/v1/klines"
        params = {
            'symbol': 'ADAUSDT',
            'interval': '1m',
            'limit': limit
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        # 转换为DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'trades_count',
            'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
        ])
        
        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        return df[['open', 'high', 'low', 'close', 'volume']]
        
    except Exception as e:
        logger.error(f"获取数据失败: {e}")
        return pd.DataFrame()

def analyze_ada_volatility(df: pd.DataFrame):
    """分析ADA波动率"""
    if len(df) < 100:
        logger.error("数据不足")
        return
    
    # 计算1分钟涨跌幅
    df['price_change_pct'] = df['close'].pct_change() * 100
    df['price_change_abs'] = abs(df['price_change_pct'])
    
    # 计算高低价差
    df['hl_range_pct'] = ((df['high'] - df['low']) / df['close']) * 100
    
    # 计算开收价差
    df['oc_range_pct'] = abs((df['close'] - df['open']) / df['open']) * 100
    
    # 移除异常值
    valid_data = df.dropna()
    
    print("🔍 ADAUSDT 1分钟波动率分析")
    print("="*60)
    
    # 基础统计
    print(f"📊 数据样本: {len(valid_data)} 分钟")
    print(f"📈 当前价格: {df['close'].iloc[-1]:.4f} USDT")
    print(f"⏰ 数据时间: {df.index[-1].strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n💹 1分钟价格变动统计:")
    price_changes = valid_data['price_change_pct']
    print(f"   平均变动: {price_changes.mean():.3f}%")
    print(f"   标准差: {price_changes.std():.3f}%")
    print(f"   最大涨幅: {price_changes.max():.3f}%")
    print(f"   最大跌幅: {price_changes.min():.3f}%")
    print(f"   平均绝对变动: {valid_data['price_change_abs'].mean():.3f}%")
    
    # 分位数分析
    print(f"\n📊 1分钟变动分位数:")
    abs_changes = valid_data['price_change_abs']
    percentiles = [50, 70, 80, 90, 95, 99]
    for p in percentiles:
        value = np.percentile(abs_changes, p)
        print(f"   {p}%分位数: {value:.3f}%")
    
    # 高低价差分析
    print(f"\n🎯 1分钟高低价差统计:")
    hl_ranges = valid_data['hl_range_pct']
    print(f"   平均高低价差: {hl_ranges.mean():.3f}%")
    print(f"   标准差: {hl_ranges.std():.3f}%")
    print(f"   50%分位数: {np.percentile(hl_ranges, 50):.3f}%")
    print(f"   80%分位数: {np.percentile(hl_ranges, 80):.3f}%")
    print(f"   90%分位数: {np.percentile(hl_ranges, 90):.3f}%")
    
    # 开收价差分析
    print(f"\n📈 1分钟开收价差统计:")
    oc_ranges = valid_data['oc_range_pct']
    print(f"   平均开收价差: {oc_ranges.mean():.3f}%")
    print(f"   50%分位数: {np.percentile(oc_ranges, 50):.3f}%")
    print(f"   80%分位数: {np.percentile(oc_ranges, 80):.3f}%")
    
    # 剥头皮建议
    print(f"\n🎯 剥头皮参数建议:")
    
    # 基于80%分位数设置参数
    target_profit = np.percentile(abs_changes, 70)  # 70%的时间能达到的涨幅
    stop_loss = np.percentile(abs_changes, 60)      # 60%的时间能承受的跌幅
    
    print(f"   建议止盈: {target_profit:.3f}% (基于70%分位数)")
    print(f"   建议止损: {stop_loss:.3f}% (基于60%分位数)")
    
    # 更保守的建议
    conservative_profit = np.percentile(abs_changes, 60)
    conservative_stop = np.percentile(abs_changes, 50)
    
    print(f"   保守止盈: {conservative_profit:.3f}% (基于60%分位数)")
    print(f"   保守止损: {conservative_stop:.3f}% (基于50%分位数)")
    
    # 激进的建议
    aggressive_profit = np.percentile(abs_changes, 80)
    aggressive_stop = np.percentile(abs_changes, 70)
    
    print(f"   激进止盈: {aggressive_profit:.3f}% (基于80%分位数)")
    print(f"   激进止损: {aggressive_stop:.3f}% (基于70%分位数)")
    
    # 分析有效交易机会
    print(f"\n⚡ 剥头皮机会分析:")
    
    # 计算不同阈值下的交易机会
    thresholds = [0.05, 0.08, 0.10, 0.15, 0.20]
    for threshold in thresholds:
        opportunities = (abs_changes >= threshold).sum()
        frequency = opportunities / len(valid_data) * 100
        print(f"   {threshold:.2f}%以上变动: {opportunities}次 ({frequency:.1f}%)")
    
    # 最近1小时的波动
    recent_60 = valid_data.tail(60)
    if len(recent_60) >= 60:
        print(f"\n🕐 最近1小时波动:")
        recent_changes = recent_60['price_change_abs']
        print(f"   平均变动: {recent_changes.mean():.3f}%")
        print(f"   最大变动: {recent_changes.max():.3f}%")
        print(f"   有效机会(>0.1%): {(recent_changes >= 0.1).sum()}次")
    
    # 最近10分钟的波动
    recent_10 = valid_data.tail(10)
    if len(recent_10) >= 10:
        print(f"\n🕐 最近10分钟波动:")
        recent_changes = recent_10['price_change_abs']
        print(f"   平均变动: {recent_changes.mean():.3f}%")
        print(f"   最大变动: {recent_changes.max():.3f}%")
        print(f"   当前趋势: {'上涨' if recent_10['price_change_pct'].mean() > 0 else '下跌'}")
    
    return {
        'target_profit': target_profit,
        'stop_loss': stop_loss,
        'conservative_profit': conservative_profit,
        'conservative_stop': conservative_stop,
        'aggressive_profit': aggressive_profit,
        'aggressive_stop': aggressive_stop,
        'avg_change': valid_data['price_change_abs'].mean(),
        'current_price': df['close'].iloc[-1]
    }

def generate_scalping_config(analysis_result: dict):
    """生成剥头皮配置"""
    if not analysis_result:
        return
    
    print(f"\n🔧 生成剥头皮交易配置:")
    print("="*60)
    
    # 您要求的参数
    your_profit = 0.20  # 0.2%
    your_stop = 0.15    # 0.15%
    
    print(f"📋 您要求的参数:")
    print(f"   止盈: {your_profit:.2f}%")
    print(f"   止损: {your_stop:.2f}%")
    
    # 对比分析结果
    recommended_profit = analysis_result['target_profit']
    recommended_stop = analysis_result['stop_loss']
    
    print(f"\n📊 数据分析建议:")
    print(f"   建议止盈: {recommended_profit:.3f}%")
    print(f"   建议止损: {recommended_stop:.3f}%")
    
    # 可行性分析
    avg_change = analysis_result['avg_change']
    
    print(f"\n✅ 可行性分析:")
    if your_profit <= recommended_profit:
        print(f"   ✅ 止盈{your_profit:.2f}%是合理的 (≤建议{recommended_profit:.3f}%)")
    else:
        print(f"   ⚠️ 止盈{your_profit:.2f}%可能过高 (>建议{recommended_profit:.3f}%)")
    
    if your_stop <= recommended_stop:
        print(f"   ✅ 止损{your_stop:.2f}%是合理的 (≤建议{recommended_stop:.3f}%)")
    else:
        print(f"   ⚠️ 止损{your_stop:.2f}%可能过高 (>建议{recommended_stop:.3f}%)")
    
    # 成功率预估
    print(f"\n📈 预估成功率:")
    print(f"   平均1分钟变动: {avg_change:.3f}%")
    
    if your_profit <= avg_change:
        print(f"   ✅ 止盈目标容易达到")
    else:
        print(f"   ⚠️ 止盈目标较难达到")
    
    # 生成代码配置
    print(f"\n💻 代码配置:")
    print(f"self.stop_loss_ratio = {your_stop/100:.5f}  # {your_stop:.2f}%")
    print(f"self.take_profit_ratio = {your_profit/100:.5f}  # {your_profit:.2f}%")

if __name__ == "__main__":
    print("🔍 ADAUSDT剥头皮波动率分析")
    print("分析1分钟涨幅，优化止盈止损参数")
    
    # 获取数据
    logger.info("获取ADAUSDT最近数据...")
    df = get_ada_recent_data(500)  # 获取500分钟数据
    
    if not df.empty:
        # 分析波动率
        analysis_result = analyze_ada_volatility(df)
        
        # 生成配置
        generate_scalping_config(analysis_result)
        
        print(f"\n🎉 分析完成！")
        print(f"💡 建议根据实际数据调整参数")
    else:
        print("❌ 无法获取数据")
