#!/usr/bin/env python3
"""
自定义杠杆多风险偏好自适应交易系统
支持1x-10x自定义杠杆，特别优化50美元小资金账户
"""

import pandas as pd
import numpy as np
import time
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings

# 禁用所有警告和详细日志
warnings.filterwarnings('ignore')
logging.getLogger().setLevel(logging.ERROR)

# 导入组件
from enhanced_real_sentiment import EnhancedRealSentimentAnalyzer
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class LeverageAdaptiveTrader:
    """
    自定义杠杆多风险偏好自适应交易系统
    
    特点：
    - 🎯 1x-10x自定义杠杆选择
    - 📊 智能左侧/右侧交易选择
    - 💰 小资金账户特别优化
    - 🔄 动态风险调整
    - ⚡ 高杠杆AI能力测试
    """
    
    def __init__(self, initial_balance: float = 50.0, risk_preference: str = 'auto', leverage: float = None):
        self.initial_balance = initial_balance
        
        # 账户状态
        self.account = {
            'balance': initial_balance,
            'equity': initial_balance,
            'unrealized_pnl': 0.0
        }
        
        # 杠杆配置
        self.leverage_configs = {
            1: {'name': '保守杠杆', 'risk_level': '极低', 'expected_return': '5-15%/月', 'stop_loss_adjust': 1.0},
            2: {'name': '平衡杠杆', 'risk_level': '低', 'expected_return': '10-30%/月', 'stop_loss_adjust': 1.0},
            3: {'name': '积极杠杆', 'risk_level': '中', 'expected_return': '15-45%/月', 'stop_loss_adjust': 0.9},
            4: {'name': '激进杠杆', 'risk_level': '中高', 'expected_return': '20-60%/月', 'stop_loss_adjust': 0.8},
            5: {'name': '高风险杠杆', 'risk_level': '高', 'expected_return': '25-75%/月', 'stop_loss_adjust': 0.7},
            6: {'name': '极高风险杠杆', 'risk_level': '极高', 'expected_return': '30-90%/月', 'stop_loss_adjust': 0.6},
            7: {'name': '专业级杠杆', 'risk_level': '专业', 'expected_return': '35-105%/月', 'stop_loss_adjust': 0.5},
            8: {'name': '专家级杠杆', 'risk_level': '专家', 'expected_return': '40-120%/月', 'stop_loss_adjust': 0.4},
            9: {'name': '大师级杠杆', 'risk_level': '大师', 'expected_return': '45-135%/月', 'stop_loss_adjust': 0.35},
            10: {'name': '极限杠杆', 'risk_level': '极限', 'expected_return': '50-150%/月', 'stop_loss_adjust': 0.3}
        }
        
        # 持仓信息
        self.position = {
            'side': None,
            'size': 0.0,
            'entry_price': 0.0,
            'entry_time': None,
            'leverage': leverage or self.get_recommended_leverage(),
            'trading_mode': None,  # 'left_side' or 'right_side'
            'risk_mode': None,     # 'conservative', 'balanced', 'aggressive'
            'batches': []
        }
        
        # 风险偏好配置
        self.risk_configs = {
            'conservative': {
                'name': '保守模式',
                'description': '低风险、小仓位、严格止损',
                'position_size_ratio': 0.15,    # 15%资金
                'base_stop_loss_pct': 0.02,     # 2%基础止损
                'take_profit_pct': 0.04,        # 4%止盈
                'min_confidence': 0.75,         # 75%置信度
                'max_trades_per_day': 3,        # 每天最多3笔
                'min_trade_interval': 1800,     # 30分钟间隔
            },
            'balanced': {
                'name': '平衡模式',
                'description': '中等风险、适中仓位、标准止损止盈',
                'position_size_ratio': 0.25,    # 25%资金
                'base_stop_loss_pct': 0.03,     # 3%基础止损
                'take_profit_pct': 0.06,        # 6%止盈
                'min_confidence': 0.60,         # 60%置信度
                'max_trades_per_day': 6,        # 每天最多6笔
                'min_trade_interval': 900,      # 15分钟间隔
            },
            'aggressive': {
                'name': '激进模式',
                'description': '高风险、大仓位、宽松止损、追求高收益',
                'position_size_ratio': 0.40,    # 40%资金
                'base_stop_loss_pct': 0.05,     # 5%基础止损
                'take_profit_pct': 0.10,        # 10%止盈
                'min_confidence': 0.45,         # 45%置信度
                'max_trades_per_day': 12,       # 每天最多12笔
                'min_trade_interval': 300,      # 5分钟间隔
            }
        }
        
        # 小资金账户特殊配置
        self.small_account_configs = {
            'threshold': 100,  # 小于100美元视为小资金
            'leverage_boost': {
                'recommended_min': 6,           # 推荐最低6x杠杆
                'recommended_max': 10,          # 推荐最高10x杠杆
                'position_size_ratio': 0.60,   # 提升到60%
                'min_confidence': 0.35,        # 降低到35%
                'max_trades_per_day': 25,      # 增加到25笔
                'min_trade_interval': 120,     # 降低到2分钟
                'description': '小资金高杠杆增强模式'
            }
        }
        
        # 设置风险偏好和杠杆
        self.risk_preference = self.determine_risk_preference(risk_preference)
        if leverage is None:
            self.position['leverage'] = self.get_recommended_leverage()
        else:
            self.position['leverage'] = leverage
        
        self.current_config = self.get_current_config()
        
        # 市场状态和交易模式
        self.current_trading_mode = None
        self.market_state_history = []
        
        # 杠杆历史记录
        self.leverage_history = []
        self.leverage_performance = {}
        
        # 初始化组件
        print("🎯 初始化自定义杠杆多风险偏好自适应交易系统...")
        
        self.data_fetcher = BinanceDataFetcher()
        self.feature_engineer = FeatureEngineer()
        self.sentiment_analyzer = EnhancedRealSentimentAnalyzer()
        
        # 交易记录
        self.trade_history = []
        self.mode_switch_history = []
        self.risk_switch_history = []
        self.last_trade_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        # 状态文件
        self.state_file = "leverage_adaptive_trader_state.json"
        self.load_state()
        
        print("✅ 自定义杠杆多风险偏好自适应交易系统初始化完成")
        self.print_system_info()
    
    def get_recommended_leverage(self) -> float:
        """获取推荐杠杆倍数"""
        if self.initial_balance < self.small_account_configs['threshold']:
            # 小资金账户推荐高杠杆
            return self.small_account_configs['leverage_boost']['recommended_min']
        else:
            # 大资金账户推荐保守杠杆
            return 2
    
    def determine_risk_preference(self, preference: str) -> str:
        """确定风险偏好"""
        if preference == 'auto':
            # 小资金账户自动推荐激进模式
            if self.initial_balance < self.small_account_configs['threshold']:
                return 'aggressive'
            else:
                return 'balanced'
        return preference
    
    def get_current_config(self) -> Dict:
        """获取当前配置"""
        base_config = self.risk_configs[self.risk_preference].copy()
        
        # 根据杠杆调整止损
        leverage = self.position['leverage']
        leverage_config = self.leverage_configs.get(int(leverage), self.leverage_configs[10])
        
        # 杠杆调整止损
        adjusted_stop_loss = base_config['base_stop_loss_pct'] * leverage_config['stop_loss_adjust']
        base_config['stop_loss_pct'] = max(0.01, adjusted_stop_loss)  # 最低1%止损
        
        # 小资金账户增强
        if (self.initial_balance < self.small_account_configs['threshold'] and 
            self.risk_preference == 'aggressive'):
            
            boost_config = self.small_account_configs['leverage_boost']
            base_config.update({
                'position_size_ratio': boost_config['position_size_ratio'],
                'min_confidence': boost_config['min_confidence'],
                'max_trades_per_day': boost_config['max_trades_per_day'],
                'min_trade_interval': boost_config['min_trade_interval']
            })
            base_config['name'] += ' (小资金高杠杆增强)'
        
        # 添加杠杆信息
        base_config['leverage'] = leverage
        base_config['leverage_name'] = leverage_config['name']
        base_config['risk_level'] = leverage_config['risk_level']
        base_config['expected_return'] = leverage_config['expected_return']
        
        return base_config
    
    def calculate_leverage_metrics(self, leverage: float, balance: float) -> Dict:
        """计算杠杆相关指标"""
        # 理论最大收益计算
        position_ratio = 0.6 if balance < 100 else 0.4  # 小资金用更高仓位
        position_value = balance * position_ratio * leverage
        
        # 假设10%价格变动的收益
        theoretical_profit_10pct = position_value * 0.1
        theoretical_return_10pct = theoretical_profit_10pct / balance * 100
        
        # 假设5%价格变动的收益
        theoretical_profit_5pct = position_value * 0.05
        theoretical_return_5pct = theoretical_profit_5pct / balance * 100
        
        # 止损风险计算
        leverage_config = self.leverage_configs.get(int(leverage), self.leverage_configs[10])
        stop_loss_pct = 0.05 * leverage_config['stop_loss_adjust']
        max_loss = position_value * stop_loss_pct
        max_loss_ratio = max_loss / balance * 100
        
        return {
            'position_value': position_value,
            'theoretical_profit_10pct': theoretical_profit_10pct,
            'theoretical_return_10pct': theoretical_return_10pct,
            'theoretical_profit_5pct': theoretical_profit_5pct,
            'theoretical_return_5pct': theoretical_return_5pct,
            'stop_loss_pct': stop_loss_pct,
            'max_loss': max_loss,
            'max_loss_ratio': max_loss_ratio
        }
    
    def print_system_info(self):
        """打印系统信息"""
        config = self.current_config
        leverage = self.position['leverage']
        
        print(f"\n📊 当前交易配置:")
        print(f"   风险模式: {config['name']}")
        print(f"   账户资金: ${self.initial_balance:.2f}")
        print(f"   杠杆倍数: {leverage}x ({config['leverage_name']})")
        print(f"   风险等级: {config['risk_level']}")
        print(f"   仓位比例: {config['position_size_ratio']:.0%}")
        print(f"   止损/止盈: {config['stop_loss_pct']:.1%}/{config['take_profit_pct']:.1%}")
        print(f"   置信度要求: {config['min_confidence']:.0%}")
        print(f"   预期收益: {config['expected_return']}")
        
        # 计算杠杆指标
        metrics = self.calculate_leverage_metrics(leverage, self.initial_balance)
        
        print(f"\n💎 杠杆效应分析:")
        print(f"   有效仓位: ${metrics['position_value']:.2f} ({leverage}x放大)")
        print(f"   5%涨幅收益: ${metrics['theoretical_profit_5pct']:.2f} ({metrics['theoretical_return_5pct']:.1f}%)")
        print(f"   10%涨幅收益: ${metrics['theoretical_profit_10pct']:.2f} ({metrics['theoretical_return_10pct']:.1f}%)")
        print(f"   最大止损: ${metrics['max_loss']:.2f} ({metrics['max_loss_ratio']:.1f}%)")
        
        if self.initial_balance < self.small_account_configs['threshold']:
            print(f"\n💡 小资金账户优化:")
            print(f"   ✅ 已启用小资金高杠杆增强模式")
            print(f"   ✅ 推荐杠杆: {self.small_account_configs['leverage_boost']['recommended_min']}x-{self.small_account_configs['leverage_boost']['recommended_max']}x")
            print(f"   ✅ 提高交易频率和仓位比例")
            print(f"   ⚠️ 高杠杆=高收益+高风险，请注意资金管理")
        
        # 风险警告
        if leverage >= 6:
            print(f"\n⚠️ 高杠杆风险警告:")
            print(f"   🔥 当前{leverage}x杠杆属于高风险级别")
            print(f"   🔥 价格波动将被{leverage}倍放大")
            print(f"   🔥 建议密切监控持仓风险")
            print(f"   🔥 系统已自动调整止损至{config['stop_loss_pct']:.1%}")
    
    def switch_leverage(self, new_leverage: float, reason: str = "用户手动调整"):
        """切换杠杆倍数"""
        if new_leverage < 1 or new_leverage > 10:
            print(f"❌ 杠杆倍数必须在1-10之间")
            return False
        
        if self.position['size'] != 0:
            print(f"⚠️ 当前有持仓，无法调整杠杆")
            return False
        
        old_leverage = self.position['leverage']
        self.position['leverage'] = new_leverage
        self.current_config = self.get_current_config()
        
        # 记录杠杆切换
        leverage_switch_record = {
            'timestamp': datetime.now().isoformat(),
            'from_leverage': old_leverage,
            'to_leverage': new_leverage,
            'reason': reason
        }
        
        self.leverage_history.append(leverage_switch_record)
        
        print(f"🔄 杠杆调整: {old_leverage}x → {new_leverage}x")
        print(f"   调整原因: {reason}")
        self.print_system_info()
        
        return True

    def get_market_data(self) -> Dict:
        """获取市场数据"""
        try:
            # 获取当前价格
            current_price = self.data_fetcher.get_current_price('BTCUSDT', is_futures=True)

            # 获取历史数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)

            df = self.data_fetcher.get_historical_data(
                'BTCUSDT', '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )

            # 计算技术指标
            features_df = self.feature_engineer.create_features(df)
            latest_features = features_df.iloc[-1]

            # 计算额外指标
            recent_prices = df['close'].tail(24)
            recent_volumes = df['volume'].tail(24)

            # 波动率
            volatility = recent_prices.pct_change().std()

            # 成交量比率
            volume_ratio = recent_volumes.iloc[-1] / recent_volumes.mean() if recent_volumes.mean() > 0 else 1

            # 价格位置
            recent_high = recent_prices.max()
            recent_low = recent_prices.min()
            price_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5

            return {
                'current_price': current_price,
                'rsi': latest_features.get('RSI_14', 50),
                'bb_position': latest_features.get('BB_position', 0.5),
                'macd_signal': latest_features.get('MACD_signal', 0),
                'volatility': volatility,
                'volume_ratio': volume_ratio,
                'price_position': price_position,
                'recent_high': recent_high,
                'recent_low': recent_low,
                'price_change_24h': ((current_price - df['close'].iloc[-24]) / df['close'].iloc[-24]) if len(df) >= 24 else 0
            }

        except Exception as e:
            print(f"❌ 市场数据获取失败: {str(e)}")
            return None

    def analyze_market_state(self, market_data: Dict) -> Dict:
        """分析市场状态，决定使用哪种交易模式"""
        try:
            # 提取关键指标
            rsi = market_data['rsi']
            bb_position = market_data['bb_position']
            volatility = market_data['volatility']
            price_change_24h = market_data['price_change_24h']
            volume_ratio = market_data.get('volume_ratio', 1.0)

            # 市场状态评分因子
            factors = {}

            # 1. 趋势强度分析
            trend_strength = min(1.0, abs(price_change_24h) * 10)
            factors['trend_strength'] = trend_strength

            # 2. 波动率分析
            volatility_score = min(1.0, volatility * 50)
            factors['volatility'] = volatility_score

            # 3. RSI极值分析
            rsi_extreme = 0
            if rsi < 30 or rsi > 70:
                rsi_extreme = abs(rsi - 50) / 50
            factors['rsi_extreme'] = rsi_extreme

            # 4. 成交量确认
            volume_confirmation = min(1.0, volume_ratio / 2)
            factors['volume_confirmation'] = volume_confirmation

            # 5. 布林带位置
            bb_extreme = 0
            if bb_position < 0.2 or bb_position > 0.8:
                bb_extreme = abs(bb_position - 0.5) * 2
            factors['bb_extreme'] = bb_extreme

            # 计算市场状态分数
            market_score = (
                trend_strength * 0.3 +
                volume_confirmation * 0.25 +
                (1 - rsi_extreme) * 0.2 +
                (1 - bb_extreme) * 0.15 +
                volatility_score * 0.1
            )

            # 根据杠杆调整阈值（高杠杆更保守）
            leverage = self.position['leverage']
            if leverage >= 8:
                threshold = 0.8  # 高杠杆更保守
            elif leverage >= 6:
                threshold = 0.75
            elif leverage >= 4:
                threshold = 0.7
            else:
                threshold = 0.65  # 低杠杆更激进

            # 确定推荐交易模式
            if market_score > threshold:
                recommended_mode = 'right_side'
                mode_confidence = market_score
                mode_reason = f"趋势明确(评分{market_score:.2f}>{threshold:.1f})，适合右侧交易"
            else:
                recommended_mode = 'left_side'
                mode_confidence = 1 - market_score
                mode_reason = f"震荡或反转(评分{market_score:.2f}<={threshold:.1f})，适合左侧交易"

            # 高杠杆特殊逻辑
            special_conditions = []
            if leverage >= 6:
                # 高杠杆时更谨慎
                if volatility > 0.03:
                    mode_confidence *= 0.8
                    special_conditions.append("HIGH_LEVERAGE_HIGH_VOL")

                # 高杠杆时偏向左侧交易（更安全）
                if market_score < 0.6:
                    recommended_mode = 'left_side'
                    mode_confidence = max(0.7, mode_confidence)
                    special_conditions.append("HIGH_LEVERAGE_CONSERVATIVE")

            market_state = {
                'timestamp': datetime.now().isoformat(),
                'market_score': market_score,
                'threshold': threshold,
                'recommended_mode': recommended_mode,
                'mode_confidence': mode_confidence,
                'mode_reason': mode_reason,
                'factors': factors,
                'special_conditions': special_conditions,
                'leverage': leverage,
                'risk_preference': self.risk_preference,
                'market_data': {
                    'rsi': rsi,
                    'bb_position': bb_position,
                    'volatility': volatility,
                    'price_change_24h': price_change_24h,
                    'volume_ratio': volume_ratio
                }
            }

            # 记录市场状态历史
            self.market_state_history.append(market_state)
            if len(self.market_state_history) > 100:
                self.market_state_history.pop(0)

            return market_state

        except Exception as e:
            print(f"❌ 市场状态分析失败: {str(e)}")
            return {
                'recommended_mode': 'left_side',  # 高杠杆默认保守
                'mode_confidence': 0.5,
                'mode_reason': '分析失败，默认左侧',
                'error': str(e)
            }

    def generate_leverage_adjusted_signal(self, market_data: Dict, trading_mode: str) -> Dict:
        """生成杠杆调整后的交易信号"""
        try:
            config = self.current_config
            leverage = self.position['leverage']

            rsi = market_data['rsi']
            bb_position = market_data['bb_position']
            price_position = market_data['price_position']
            price_change_24h = market_data['price_change_24h']
            volatility = market_data['volatility']
            volume_ratio = market_data['volume_ratio']

            signals = []

            if trading_mode == 'left_side':
                # 左侧交易信号

                # RSI逆向信号
                if rsi <= 25:
                    rsi_signal = 0.95
                elif rsi <= 30:
                    rsi_signal = 0.85
                elif rsi <= 35:
                    rsi_signal = 0.75
                elif rsi >= 75:
                    rsi_signal = 0.05
                elif rsi >= 70:
                    rsi_signal = 0.15
                elif rsi >= 65:
                    rsi_signal = 0.25
                else:
                    rsi_signal = 0.5
                signals.append(rsi_signal)

                # 价格位置逆向信号
                if price_position < 0.1:
                    position_signal = 0.9
                elif price_position < 0.2:
                    position_signal = 0.8
                elif price_position < 0.3:
                    position_signal = 0.7
                elif price_position > 0.9:
                    position_signal = 0.1
                elif price_position > 0.8:
                    position_signal = 0.2
                elif price_position > 0.7:
                    position_signal = 0.3
                else:
                    position_signal = 0.5
                signals.append(position_signal)

                # 24小时变化逆向信号
                if price_change_24h < -0.1:
                    change_signal = 0.95
                elif price_change_24h < -0.08:
                    change_signal = 0.9
                elif price_change_24h < -0.05:
                    change_signal = 0.8
                elif price_change_24h > 0.1:
                    change_signal = 0.05
                elif price_change_24h > 0.08:
                    change_signal = 0.1
                elif price_change_24h > 0.05:
                    change_signal = 0.2
                else:
                    change_signal = 0.5
                signals.append(change_signal)

                # 布林带逆向信号
                if bb_position < 0.1:
                    bb_signal = 0.9
                elif bb_position < 0.2:
                    bb_signal = 0.8
                elif bb_position > 0.9:
                    bb_signal = 0.1
                elif bb_position > 0.8:
                    bb_signal = 0.2
                else:
                    bb_signal = 0.5
                signals.append(bb_signal)

            else:  # right_side
                # 右侧交易信号

                # RSI趋势信号
                if 45 <= rsi <= 55:
                    rsi_signal = 0.8
                elif 40 <= rsi <= 60:
                    rsi_signal = 0.7
                elif rsi < 25:
                    rsi_signal = 0.2  # 极度超卖等待确认
                elif rsi > 75:
                    rsi_signal = 0.8  # 极度超买可能继续
                else:
                    rsi_signal = 0.5
                signals.append(rsi_signal)

                # 价格变化趋势信号
                if price_change_24h > 0.05:
                    change_signal = 0.8
                elif price_change_24h > 0.03:
                    change_signal = 0.7
                elif price_change_24h < -0.05:
                    change_signal = 0.2
                elif price_change_24h < -0.03:
                    change_signal = 0.3
                else:
                    change_signal = 0.5
                signals.append(change_signal)

                # 成交量确认信号
                if volume_ratio > 2:
                    if price_change_24h > 0:
                        volume_signal = 0.9
                    else:
                        volume_signal = 0.1
                elif volume_ratio > 1.5:
                    if price_change_24h > 0:
                        volume_signal = 0.8
                    else:
                        volume_signal = 0.2
                else:
                    volume_signal = 0.5
                signals.append(volume_signal)

                # 布林带突破信号
                if bb_position > 0.85:
                    bb_signal = 0.8  # 上轨突破
                elif bb_position > 0.8:
                    bb_signal = 0.7
                elif bb_position < 0.15:
                    bb_signal = 0.2  # 下轨突破
                elif bb_position < 0.2:
                    bb_signal = 0.3
                else:
                    bb_signal = 0.5
                signals.append(bb_signal)

            # 杠杆调整信号强度
            if leverage >= 8:
                # 极高杠杆：信号要求更严格
                signals = [min(1.0, max(0.0, (s - 0.5) * 0.6 + 0.5)) for s in signals]
            elif leverage >= 6:
                # 高杠杆：信号要求较严格
                signals = [min(1.0, max(0.0, (s - 0.5) * 0.8 + 0.5)) for s in signals]
            elif leverage <= 2:
                # 低杠杆：信号可以放宽
                signals = [min(1.0, max(0.0, (s - 0.5) * 1.2 + 0.5)) for s in signals]

            # 小资金账户增强
            if self.initial_balance < self.small_account_configs['threshold']:
                if volatility > 0.02:  # 高波动时更积极
                    signals = [min(1.0, max(0.0, (s - 0.5) * 1.1 + 0.5)) for s in signals]

            # 计算最终信号
            final_signal = np.mean(signals)
            confidence = max(0.3, min(0.95, 1 - np.std(signals)))

            # 根据杠杆调整置信度要求
            min_confidence = config['min_confidence']
            if leverage >= 8:
                min_confidence = max(min_confidence, 0.8)  # 极高杠杆要求更高置信度
            elif leverage >= 6:
                min_confidence = max(min_confidence, 0.7)  # 高杠杆要求较高置信度

            # 根据杠杆调整阈值
            if leverage >= 8:
                long_threshold = 0.65
                short_threshold = 0.35
            elif leverage >= 6:
                long_threshold = 0.6
                short_threshold = 0.4
            elif leverage >= 4:
                long_threshold = 0.55
                short_threshold = 0.45
            else:
                long_threshold = 0.52
                short_threshold = 0.48

            if final_signal > long_threshold:
                direction = '做多'
                strength = (final_signal - 0.5) * 2
            elif final_signal < short_threshold:
                direction = '做空'
                strength = (0.5 - final_signal) * 2
            else:
                direction = '等待'
                strength = 0

            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'signal_value': final_signal,
                'min_confidence': min_confidence,
                'trading_mode': trading_mode,
                'risk_mode': self.risk_preference,
                'leverage': leverage,
                'thresholds': {
                    'long': long_threshold,
                    'short': short_threshold
                },
                'signals_breakdown': signals
            }

        except Exception as e:
            return {
                'direction': '等待',
                'strength': 0,
                'confidence': 0.3,
                'trading_mode': trading_mode,
                'risk_mode': self.risk_preference,
                'leverage': leverage,
                'error': str(e)
            }

    def get_sentiment_signal(self) -> Dict:
        """获取情绪信号"""
        try:
            sentiment_data = self.sentiment_analyzer.get_comprehensive_sentiment()

            return {
                'direction': sentiment_data['trading_signal']['direction'],
                'score': sentiment_data['overall_sentiment_score'],
                'classification': sentiment_data['sentiment_classification'],
                'confidence': sentiment_data['trading_signal']['confidence']
            }

        except Exception as e:
            return {
                'direction': '等待',
                'score': 0.5,
                'classification': 'Neutral',
                'confidence': 0.3
            }

    def fuse_leverage_signals(self, technical_signal: Dict, sentiment_signal: Dict) -> Dict:
        """融合杠杆调整后的信号"""

        leverage = self.position['leverage']

        # 根据杠杆调整权重
        if leverage >= 8:
            technical_weight = 0.9  # 高杠杆更依赖技术分析
            sentiment_weight = 0.1
        elif leverage >= 6:
            technical_weight = 0.85
            sentiment_weight = 0.15
        elif leverage >= 4:
            technical_weight = 0.8
            sentiment_weight = 0.2
        else:
            technical_weight = 0.75
            sentiment_weight = 0.25

        # 转换信号为数值
        def signal_to_value(signal):
            if signal['direction'] in ['LONG', '做多']:
                return 0.5 + signal.get('strength', 0.5) * 0.5
            elif signal['direction'] in ['SHORT', '做空']:
                return 0.5 - signal.get('strength', 0.5) * 0.5
            else:
                return 0.5

        technical_value = technical_signal['signal_value']
        sentiment_value = sentiment_signal['score']

        # 加权融合
        final_value = technical_value * technical_weight + sentiment_value * sentiment_weight
        final_confidence = technical_signal['confidence'] * technical_weight + sentiment_signal['confidence'] * sentiment_weight

        # 使用技术信号的阈值
        thresholds = technical_signal.get('thresholds', {'long': 0.55, 'short': 0.45})

        # 确定方向
        if final_value > thresholds['long']:
            direction = '做多'
            strength = (final_value - 0.5) * 2
        elif final_value < thresholds['short']:
            direction = '做空'
            strength = (0.5 - final_value) * 2
        else:
            direction = '等待'
            strength = 0

        return {
            'direction': direction,
            'strength': strength,
            'confidence': final_confidence,
            'final_value': final_value,
            'trading_mode': technical_signal['trading_mode'],
            'risk_mode': self.risk_preference,
            'leverage': leverage,
            'thresholds': thresholds,
            'signal_breakdown': {
                'technical': {'value': technical_value, 'weight': technical_weight},
                'sentiment': {'value': sentiment_value, 'weight': sentiment_weight}
            }
        }

    def save_state(self):
        """保存状态"""
        state_data = {
            'account': self.account,
            'position': {k: v.isoformat() if isinstance(v, datetime) else v
                        for k, v in self.position.items() if k != 'batches'},
            'batches': [{k: v.isoformat() if isinstance(v, datetime) else v
                        for k, v in batch.items()} for batch in self.position['batches']],
            'trade_history': self.trade_history,
            'mode_switch_history': self.mode_switch_history,
            'risk_switch_history': self.risk_switch_history,
            'leverage_history': self.leverage_history,
            'current_trading_mode': self.current_trading_mode,
            'risk_preference': self.risk_preference,
            'daily_trade_count': self.daily_trade_count,
            'last_trade_date': self.last_trade_date.isoformat() if self.last_trade_date else None,
            'last_trade_time': self.last_trade_time.isoformat() if self.last_trade_time else None,
            'initial_balance': self.initial_balance
        }

        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, default=str)

    def load_state(self):
        """加载状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                self.account = state_data.get('account', self.account)

                position_data = state_data.get('position', {})
                if position_data.get('entry_time'):
                    position_data['entry_time'] = datetime.fromisoformat(position_data['entry_time'])
                self.position.update(position_data)

                # 加载批次信息
                batches_data = state_data.get('batches', [])
                self.position['batches'] = []
                for batch in batches_data:
                    if batch.get('time'):
                        batch['time'] = datetime.fromisoformat(batch['time'])
                    self.position['batches'].append(batch)

                self.trade_history = state_data.get('trade_history', [])
                self.mode_switch_history = state_data.get('mode_switch_history', [])
                self.risk_switch_history = state_data.get('risk_switch_history', [])
                self.leverage_history = state_data.get('leverage_history', [])
                self.current_trading_mode = state_data.get('current_trading_mode')
                self.risk_preference = state_data.get('risk_preference', self.risk_preference)
                self.daily_trade_count = state_data.get('daily_trade_count', 0)

                if state_data.get('last_trade_date'):
                    self.last_trade_date = datetime.fromisoformat(state_data['last_trade_date']).date()

                if state_data.get('last_trade_time'):
                    self.last_trade_time = datetime.fromisoformat(state_data['last_trade_time'])

                # 更新配置
                self.current_config = self.get_current_config()

                if self.account['balance'] != self.initial_balance or self.trade_history:
                    print(f"📂 加载历史状态: 余额${self.account['balance']:.2f}, {len(self.trade_history)}笔交易")
                    print(f"   杠杆: {self.position['leverage']}x, 风险偏好: {self.risk_preference}")

            except Exception as e:
                print(f"⚠️ 状态加载失败: {str(e)}")

    def run_leverage_cycle(self) -> bool:
        """运行一个杠杆调整后的交易周期"""
        try:
            # 1. 获取市场数据
            market_data = self.get_market_data()
            if not market_data:
                print("❌ 市场数据获取失败")
                return False

            current_price = market_data['current_price']

            # 2. 分析市场状态，决定交易模式
            market_state = self.analyze_market_state(market_data)
            recommended_mode = market_state['recommended_mode']

            # 3. 检查是否需要切换交易模式
            if market_state['mode_confidence'] > 0.8:  # 高置信度时才切换
                self.switch_trading_mode(recommended_mode, market_state['mode_reason'])
            elif not self.current_trading_mode:
                # 首次设定交易模式
                self.current_trading_mode = recommended_mode
                print(f"🎯 设定初始交易模式: {recommended_mode} ({self.position['leverage']}x杠杆)")

            # 4. 更新未实现盈亏
            self.update_unrealized_pnl(current_price)

            # 5. 检查退出条件
            if self.check_leverage_exit_conditions(current_price):
                print(f"🔔 触发退出条件，已平仓")
                self.save_state()
                return True

            # 6. 根据当前交易模式生成信号
            technical_signal = self.generate_leverage_adjusted_signal(market_data, self.current_trading_mode)

            # 7. 获取情绪信号
            sentiment_signal = self.get_sentiment_signal()

            # 8. 融合信号
            final_signal = self.fuse_leverage_signals(technical_signal, sentiment_signal)

            # 9. 执行交易决策
            if final_signal['direction'] in ['做多', '做空']:
                if self.open_leverage_position(final_signal, current_price):
                    mode_emoji = "🎯" if self.current_trading_mode == 'left_side' else "📈"
                    leverage_emoji = "⚡" if self.position['leverage'] >= 6 else "🔋"
                    batch_info = f"第{len(self.position['batches'])}批" if self.current_trading_mode == 'left_side' else ""
                    trading_mode_cn = "左侧" if self.current_trading_mode == 'left_side' else "右侧"
                    print(f"🔔 {mode_emoji}{leverage_emoji} {trading_mode_cn}交易: {final_signal['direction']} {batch_info} @ ${current_price:,.0f} ({self.position['leverage']}x)")
                else:
                    print("⚠️ 开仓条件不满足")

            # 10. 打印状态
            self.print_leverage_status(market_data, market_state, technical_signal, sentiment_signal, final_signal)

            # 11. 保存状态
            self.save_state()

            return True

        except Exception as e:
            print(f"❌ 杠杆调整交易周期失败: {str(e)}")
            return False

    def switch_trading_mode(self, new_mode: str, reason: str):
        """切换交易模式"""
        if self.current_trading_mode != new_mode:
            old_mode = self.current_trading_mode
            self.current_trading_mode = new_mode

            # 记录模式切换
            switch_record = {
                'timestamp': datetime.now().isoformat(),
                'from_mode': old_mode,
                'to_mode': new_mode,
                'reason': reason,
                'leverage': self.position['leverage']
            }

            self.mode_switch_history.append(switch_record)

            print(f"🔄 交易模式切换: {old_mode} → {new_mode} ({self.position['leverage']}x杠杆)")
            print(f"   切换原因: {reason}")

            return True
        return False

    def open_leverage_position(self, signal: Dict, current_price: float) -> bool:
        """开启杠杆调整后的仓位"""
        # 这里需要实现具体的开仓逻辑
        # 暂时返回False，避免实际交易
        print(f"💡 模拟开仓: {signal['direction']} @ ${current_price:,.0f}")
        return False

    def check_leverage_exit_conditions(self, current_price: float) -> bool:
        """检查杠杆调整后的退出条件"""
        # 这里需要实现具体的退出条件检查
        return False

    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.position['size'] == 0:
            self.account['unrealized_pnl'] = 0
            self.account['equity'] = self.account['balance']
            return

        leverage = self.position['leverage']

        if self.position['side'] in ['LONG', '做多']:
            price_diff = current_price - self.position['entry_price']
        else:
            price_diff = self.position['entry_price'] - current_price

        unrealized_pnl = self.position['size'] * price_diff * leverage
        self.account['unrealized_pnl'] = unrealized_pnl
        self.account['equity'] = self.account['balance'] + unrealized_pnl

    def print_leverage_status(self, market_data: Dict, market_state: Dict,
                             technical_signal: Dict, sentiment_signal: Dict, final_signal: Dict):
        """打印杠杆调整后的交易状态"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_price = market_data['current_price']
        config = self.current_config
        leverage = self.position['leverage']

        print(f"\n⚡ {current_time} | BTC: ${current_price:,.0f} | {config['name']} | {leverage}x杠杆")
        print("=" * 140)

        # 账户状态
        total_return = (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        print(f"💰 账户: ${self.account['balance']:.2f} + ${self.account['unrealized_pnl']:+.2f} = ${self.account['equity']:.2f} ({total_return:+.2f}%)")

        # 杠杆配置状态
        leverage_config = self.leverage_configs.get(int(leverage), self.leverage_configs[10])
        print(f"⚡ 杠杆配置: {leverage}x ({leverage_config['name']}) | 风险等级: {leverage_config['risk_level']} | 预期收益: {leverage_config['expected_return']}")

        # 风险配置状态
        print(f"⚙️ 风险配置: {config['position_size_ratio']:.0%}仓位 | {config['stop_loss_pct']:.1%}/{config['take_profit_pct']:.1%}止损止盈 | {config['min_confidence']:.0%}置信度")

        # 市场状态分析
        print(f"\n📊 市场分析:")
        print(f"   评分: {market_state['market_score']:.2f} (阈值{market_state.get('threshold', 0.7):.1f}) | 推荐: {market_state['recommended_mode']}")
        print(f"   置信度: {market_state['mode_confidence']:.1%} | 原因: {market_state['mode_reason']}")
        print(f"   当前模式: {self.current_trading_mode or '未设定'}")

        # 持仓状态
        if self.position['size'] != 0:
            hold_time = (datetime.now() - self.position['entry_time']).total_seconds() / 3600
            pnl_pct = self.account['unrealized_pnl'] / (self.position['size'] * self.position['entry_price'] * leverage) * 100

            print(f"\n📊 持仓状态:")
            side_cn = "看多" if self.position['side'] in ['LONG', '做多'] else "看空"
            trading_mode_cn = "左侧" if self.position['trading_mode'] == 'left_side' else "右侧"
            print(f"   🔥 {side_cn} {self.position['size']:.6f} BTC ({trading_mode_cn}, {leverage}x杠杆)")
            print(f"   成本: ${self.position['entry_price']:,.0f} | 当前: ${current_price:,.0f} | 时间: {hold_time:.1f}h | 盈亏: {pnl_pct:+.1f}%")
        else:
            print(f"\n📊 持仓状态: 💤 空仓")

        # 信号分析
        print(f"\n🎯 信号分析:")
        print(f"   技术信号: {technical_signal['direction']} (值{technical_signal['signal_value']:.2f}, 置信度{technical_signal['confidence']:.1%})")
        print(f"   情绪信号: {sentiment_signal['classification']} (分数{sentiment_signal['score']:.2f})")
        print(f"   最终决策: {final_signal['direction']} (置信度{final_signal['confidence']:.1%}, 杠杆{leverage}x)")

        # 杠杆效应分析
        metrics = self.calculate_leverage_metrics(leverage, self.account['balance'])
        print(f"💎 杠杆效应: 有效仓位${metrics['position_value']:.0f} | 5%涨幅收益${metrics['theoretical_profit_5pct']:.2f} | 最大止损${metrics['max_loss']:.2f}")

        # 高杠杆提示
        if leverage >= 6:
            print(f"⚠️ 高杠杆提示: {leverage}x杠杆风险较高，系统已调整止损至{config['stop_loss_pct']:.1%}")

    def get_statistics(self) -> Dict:
        """获取交易统计"""
        closed_trades = [t for t in self.trade_history if t['action'] == 'CLOSE']

        return {
            'total_trades': len(closed_trades),
            'current_leverage': self.position['leverage'],
            'leverage_switches': len(self.leverage_history),
            'total_return': (self.account['equity'] - self.initial_balance) / self.initial_balance * 100
        }

def show_leverage_selection_menu(balance: float, risk_preference: str) -> float:
    """显示杠杆选择菜单"""
    print("\n⚡ 自定义杠杆选择")
    print("=" * 80)
    print(f"💰 账户资金: ${balance:.2f}")
    print(f"🎯 风险偏好: {risk_preference}")

    # 小资金账户特别提示
    if balance < 100:
        print(f"\n💡 小资金账户(<$100)特别优化:")
        print(f"   🚀 推荐使用6x-10x高杠杆快速增长")
        print(f"   📈 理论收益潜力大幅提升")
        print(f"   ⚠️ 风险相应增加，需谨慎操作")

    print(f"\n📊 杠杆倍数选择 (1x-10x):")

    # 预设杠杆选项
    leverage_options = {
        1: {'name': '保守杠杆', 'risk': '极低', 'return': '5-15%/月', 'suitable': '新手、风险厌恶'},
        2: {'name': '平衡杠杆', 'risk': '低', 'return': '10-30%/月', 'suitable': '一般投资者'},
        4: {'name': '激进杠杆', 'risk': '中高', 'return': '20-60%/月', 'suitable': '有经验投资者'},
        6: {'name': '高风险杠杆', 'risk': '极高', 'return': '30-90%/月', 'suitable': '小资金快速增长'},
        10: {'name': '极限杠杆', 'risk': '极限', 'return': '50-150%/月', 'suitable': '专业交易者'}
    }

    for lev, info in leverage_options.items():
        # 计算理论收益
        metrics = calculate_leverage_preview(lev, balance)

        print(f"\n{lev:2d}x - {info['name']}")
        print(f"     风险等级: {info['risk']}")
        print(f"     预期收益: {info['return']}")
        print(f"     5%涨幅收益: ${metrics['profit_5pct']:.2f} ({metrics['return_5pct']:.1f}%)")
        print(f"     10%涨幅收益: ${metrics['profit_10pct']:.2f} ({metrics['return_10pct']:.1f}%)")
        print(f"     最大止损: ${metrics['max_loss']:.2f} ({metrics['loss_ratio']:.1f}%)")
        print(f"     适合人群: {info['suitable']}")

        # 小资金账户推荐标记
        if balance < 100 and lev >= 6:
            print(f"     💎 小资金推荐")

    print(f"\n🔧 自定义选项:")
    print(f"   输入任意1-10之间的数字 (如: 3.5, 7.2)")

    while True:
        try:
            choice = input(f"\n请选择杠杆倍数 (1-10): ").strip()

            if choice in ['1', '2', '4', '6', '10']:
                leverage = float(choice)
                break
            else:
                try:
                    leverage = float(choice)
                    if 1 <= leverage <= 10:
                        break
                    else:
                        print("❌ 杠杆倍数必须在1-10之间")
                except:
                    print("❌ 请输入有效的数字")
        except:
            print("❌ 输入错误，请重试")

    # 显示选择结果
    print(f"\n✅ 已选择 {leverage}x 杠杆")

    # 高杠杆风险确认
    if leverage >= 6:
        print(f"\n⚠️ 高杠杆风险确认:")
        print(f"   🔥 {leverage}x杠杆属于高风险级别")
        print(f"   🔥 价格波动将被{leverage}倍放大")
        print(f"   🔥 可能快速盈利，也可能快速亏损")
        print(f"   🔥 系统将自动调整止损保护资金")

        confirm = input(f"\n确认使用{leverage}x杠杆？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 用户取消高杠杆选择")
            return show_leverage_selection_menu(balance, risk_preference)

    return leverage

def calculate_leverage_preview(leverage: float, balance: float) -> Dict:
    """计算杠杆预览指标"""
    position_ratio = 0.6 if balance < 100 else 0.4
    position_value = balance * position_ratio * leverage

    # 5%和10%价格变动收益
    profit_5pct = position_value * 0.05
    return_5pct = profit_5pct / balance * 100

    profit_10pct = position_value * 0.1
    return_10pct = profit_10pct / balance * 100

    # 止损计算
    stop_loss_configs = {
        1: 0.05, 2: 0.05, 3: 0.045, 4: 0.04, 5: 0.035,
        6: 0.03, 7: 0.025, 8: 0.02, 9: 0.0175, 10: 0.015
    }
    stop_loss_pct = stop_loss_configs.get(int(leverage), 0.02)
    max_loss = position_value * stop_loss_pct
    loss_ratio = max_loss / balance * 100

    return {
        'position_value': position_value,
        'profit_5pct': profit_5pct,
        'return_5pct': return_5pct,
        'profit_10pct': profit_10pct,
        'return_10pct': return_10pct,
        'max_loss': max_loss,
        'loss_ratio': loss_ratio
    }

def analyze_small_account_potential():
    """分析小资金账户潜力"""
    print("\n💎 50美元小资金账户潜力分析:")
    print("=" * 60)

    scenarios = [
        {'leverage': 2, 'name': '保守策略'},
        {'leverage': 4, 'name': '平衡策略'},
        {'leverage': 6, 'name': '积极策略'},
        {'leverage': 8, 'name': '激进策略'},
        {'leverage': 10, 'name': '极限策略'}
    ]

    print(f"假设BTC上涨5%的收益对比:")
    for scenario in scenarios:
        lev = scenario['leverage']
        metrics = calculate_leverage_preview(lev, 50)
        print(f"  {scenario['name']} ({lev}x): ${metrics['profit_5pct']:.2f} ({metrics['return_5pct']:.1f}%)")

    print(f"\n假设BTC上涨10%的收益对比:")
    for scenario in scenarios:
        lev = scenario['leverage']
        metrics = calculate_leverage_preview(lev, 50)
        print(f"  {scenario['name']} ({lev}x): ${metrics['profit_10pct']:.2f} ({metrics['return_10pct']:.1f}%)")

    print(f"\n💡 结论:")
    print(f"  ✅ 高杠杆能显著提升小资金收益潜力")
    print(f"  ✅ 10x杠杆下，5%涨幅可获得30%账户收益")
    print(f"  ✅ AI系统将严格控制风险")
    print(f"  ⚠️ 高收益伴随高风险，需谨慎操作")

def run_leverage_adaptive_trading():
    """运行自定义杠杆多风险偏好自适应交易系统"""
    print("⚡ 自定义杠杆多风险偏好自适应交易系统")
    print("基于第三阶段完全真实化系统 - 自定义杠杆版")
    print("")

    # 分析小资金潜力
    analyze_small_account_potential()

    # 获取基本参数
    try:
        balance = float(input("\n💰 账户资金 (默认50): ") or "50")
        duration = float(input("⏰ 运行时长（小时，默认2）: ") or "2")
        interval = int(input("🔄 检测间隔（分钟，默认3）: ") or "3")

        # 选择风险偏好
        print(f"\n🎯 风险偏好快速选择:")
        print(f"1. 保守模式 - 严格风控，稳健增长")
        print(f"2. 平衡模式 - 适中风险，均衡收益")
        print(f"3. 激进模式 - 高风险高收益")
        print(f"4. 自动模式 - 系统智能选择")

        risk_choice = input("选择风险偏好 (1-4，默认3): ").strip() or "3"
        risk_map = {'1': 'conservative', '2': 'balanced', '3': 'aggressive', '4': 'auto'}
        risk_preference = risk_map.get(risk_choice, 'aggressive')

        # 选择杠杆倍数
        leverage = show_leverage_selection_menu(balance, risk_preference)

        print(f"\n⚡ 启动自定义杠杆多风险偏好自适应交易系统...")
        print(f"💰 账户资金: ${balance:.2f}")
        print(f"⏰ 运行时长: {duration}小时")
        print(f"🔄 检测间隔: {interval}分钟")
        print(f"🎯 风险偏好: {risk_preference}")
        print(f"⚡ 杠杆倍数: {leverage}x")

        confirm = input("\n🚀 是否开始高杠杆AI交易测试？(y/n，默认y): ").strip().lower()
        if confirm in ['n', 'no']:
            print("⏸️ 用户取消")
            return None

    except:
        balance = 50
        duration = 2
        interval = 3
        risk_preference = 'aggressive'
        leverage = 6

    # 创建杠杆自适应交易器
    trader = LeverageAdaptiveTrader(balance, risk_preference, leverage)

    start_time = datetime.now()
    end_time = start_time + timedelta(hours=duration)
    cycle_count = 0

    print(f"\n🎯 开始AI高杠杆交易能力测试...")
    print(f"📊 测试目标: 验证AI在{leverage}x杠杆下的风险控制能力")

    try:
        while datetime.now() < end_time:
            cycle_count += 1

            # 计算进度
            elapsed_time = (datetime.now() - start_time).total_seconds() / 3600
            progress = elapsed_time / duration * 100
            remaining_hours = duration - elapsed_time

            print(f"\n⚡ 第 {cycle_count} 个高杠杆交易周期 | 进度: {progress:.1f}% | 剩余: {remaining_hours:.1f}小时")

            # 运行杠杆调整交易周期
            trader.run_leverage_cycle()

            # 显示简要统计
            stats = trader.get_statistics()
            print(f"📈 当前权益: ${trader.account['equity']:.2f} | 杠杆: {trader.position['leverage']}x | 交易: {stats['total_trades']}笔")

            # 等待下一个周期
            remaining_time = (end_time - datetime.now()).total_seconds()
            interval_seconds = interval * 60

            if remaining_time > interval_seconds:
                print(f"⏳ 等待 {interval} 分钟...")
                time.sleep(interval_seconds)
            else:
                print(f"⏳ 剩余时间不足，等待 {remaining_time:.0f} 秒...")
                time.sleep(max(0, remaining_time))
                break

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断")

    print(f"\n🏁 自定义杠杆交易测试结束")
    print("=" * 80)
    print(f"✅ AI系统在{leverage}x杠杆下的表现测试完成")

    return trader

if __name__ == "__main__":
    print("⚡ 自定义杠杆多风险偏好自适应交易系统")
    print("测试AI在高杠杆环境下的真实交易能力")
    print("")

    try:
        trader = run_leverage_adaptive_trading()
        if trader:
            print(f"\n🎉 自定义杠杆交易测试完成！")

    except KeyboardInterrupt:
        print(f"\n👋 用户退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
