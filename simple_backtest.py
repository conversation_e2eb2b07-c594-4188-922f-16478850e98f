#!/usr/bin/env python3
"""
简单回测脚本 - 验证模型实际交易效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class SimpleBacktester:
    def __init__(self, initial_capital=10000, commission=0.001):
        """
        初始化回测器
        
        Args:
            initial_capital: 初始资金
            commission: 手续费率 (0.1%)
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.reset()
    
    def reset(self):
        """重置回测状态"""
        self.capital = self.initial_capital
        self.position = 0  # 当前持仓 (0=空仓, 1=多头, -1=空头)
        self.entry_price = 0
        self.trades = []
        self.equity_curve = []
        self.returns = []
        
    def execute_trade(self, signal, price, timestamp, confidence=1.0):
        """
        执行交易
        
        Args:
            signal: 交易信号 (0=持有, 1=买入, 2=卖出, 3=观望)
            price: 当前价格
            timestamp: 时间戳
            confidence: 预测置信度
        """
        # 根据置信度调整仓位大小
        position_size = self._calculate_position_size(confidence)
        
        trade_executed = False
        
        # 买入信号
        if signal == 1 and self.position <= 0:
            if self.position == -1:  # 先平空头
                self._close_position(price, timestamp, "平空")
            
            # 开多头
            if position_size > 0:
                self._open_position(1, price, timestamp, position_size, "开多")
                trade_executed = True
        
        # 卖出信号
        elif signal == 2 and self.position >= 0:
            if self.position == 1:  # 先平多头
                self._close_position(price, timestamp, "平多")
            
            # 开空头
            if position_size > 0:
                self._open_position(-1, price, timestamp, position_size, "开空")
                trade_executed = True
        
        # 记录权益曲线
        current_value = self._calculate_portfolio_value(price)
        self.equity_curve.append({
            'timestamp': timestamp,
            'capital': self.capital,
            'position': self.position,
            'price': price,
            'portfolio_value': current_value,
            'signal': signal,
            'confidence': confidence
        })
        
        return trade_executed
    
    def _calculate_position_size(self, confidence):
        """根据置信度计算仓位大小"""
        if confidence >= 0.9:
            return 0.8  # 高置信度：80%仓位
        elif confidence >= 0.8:
            return 0.5  # 中等置信度：50%仓位
        elif confidence >= 0.7:
            return 0.3  # 低置信度：30%仓位
        else:
            return 0    # 极低置信度：不交易
    
    def _open_position(self, direction, price, timestamp, size, action):
        """开仓"""
        trade_amount = self.capital * size
        commission_cost = trade_amount * self.commission
        
        self.position = direction
        self.entry_price = price
        self.capital -= commission_cost
        
        self.trades.append({
            'timestamp': timestamp,
            'action': action,
            'price': price,
            'amount': trade_amount,
            'commission': commission_cost,
            'capital_after': self.capital
        })
    
    def _close_position(self, price, timestamp, action):
        """平仓"""
        if self.position == 0:
            return
        
        # 计算盈亏
        if self.position == 1:  # 平多头
            pnl_ratio = (price - self.entry_price) / self.entry_price
        else:  # 平空头
            pnl_ratio = (self.entry_price - price) / self.entry_price
        
        trade_amount = self.capital
        pnl = trade_amount * pnl_ratio
        commission_cost = trade_amount * self.commission
        
        self.capital = self.capital + pnl - commission_cost
        
        self.trades.append({
            'timestamp': timestamp,
            'action': action,
            'price': price,
            'entry_price': self.entry_price,
            'pnl': pnl,
            'pnl_ratio': pnl_ratio,
            'commission': commission_cost,
            'capital_after': self.capital
        })
        
        self.position = 0
        self.entry_price = 0
    
    def _calculate_portfolio_value(self, current_price):
        """计算当前组合价值"""
        if self.position == 0:
            return self.capital
        
        # 计算未实现盈亏
        if self.position == 1:  # 多头
            unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:  # 空头
            unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        unrealized_pnl = self.capital * unrealized_pnl_ratio
        return self.capital + unrealized_pnl

def run_backtest(model_path, symbol='BTCUSDT', test_months=6, initial_capital=10000):
    """
    运行回测
    
    Args:
        model_path: 模型文件路径
        symbol: 交易对
        test_months: 回测月数
        initial_capital: 初始资金
    """
    print(f"🔍 开始回测 {symbol}...")
    print(f"📊 回测期间: {test_months}个月")
    print(f"💰 初始资金: ${initial_capital:,}")
    
    # 1. 加载模型
    print("📦 加载模型...")
    model_data = joblib.load(model_path)
    model = model_data['model']
    scaler = model_data['scaler']
    label_encoder = model_data['label_encoder']
    
    # 2. 获取测试数据
    print("📊 获取测试数据...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=test_months*30)
    
    fetcher = BinanceDataFetcher()
    df = fetcher.get_historical_data(symbol, '1h', start_date.strftime('%Y-%m-%d'))
    
    print(f"✅ 获取到 {len(df)} 条测试数据")
    
    # 3. 特征工程
    print("🔧 生成特征...")
    engineer = FeatureEngineer()
    df_features = engineer.create_features(df)
    
    # 准备特征数据
    X = df_features.drop(columns=['target'])
    y_true = df_features['target']
    
    # 移除原始列
    cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
    X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
    
    # 清理数据
    mask = ~(X.isna().any(axis=1) | y_true.isna())
    X = X[mask]
    y_true = y_true[mask]
    df_clean = df_features[mask].copy()
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.median(), inplace=True)
    
    print(f"✅ 清理后测试样本: {len(X)}")
    
    # 4. 模型预测
    print("🎯 生成预测...")
    X_scaled = scaler.transform(X)
    predictions = model.predict(X_scaled)
    prediction_proba = model.predict_proba(X_scaled)
    
    # 计算预测置信度
    confidences = np.max(prediction_proba, axis=1)
    
    # 5. 运行回测
    print("📈 执行回测...")
    backtester = SimpleBacktester(initial_capital)
    
    prices = df_clean['close'].values
    timestamps = df_clean.index
    
    for i in range(len(predictions)):
        signal = predictions[i]
        price = prices[i]
        timestamp = timestamps[i]
        confidence = confidences[i]
        
        backtester.execute_trade(signal, price, timestamp, confidence)
    
    # 最后平仓
    if backtester.position != 0:
        backtester._close_position(prices[-1], timestamps[-1], "强制平仓")
    
    # 6. 计算回测结果
    print("📊 计算回测指标...")
    
    equity_df = pd.DataFrame(backtester.equity_curve)
    trades_df = pd.DataFrame(backtester.trades)
    
    # 基本指标
    final_capital = backtester.capital
    total_return = (final_capital - initial_capital) / initial_capital
    
    # 基准收益（买入持有）
    buy_hold_return = (prices[-1] - prices[0]) / prices[0]
    
    # 交易统计
    profitable_trades = len([t for t in backtester.trades if t.get('pnl', 0) > 0])
    total_trades = len([t for t in backtester.trades if 'pnl' in t])
    win_rate = profitable_trades / total_trades if total_trades > 0 else 0
    
    # 最大回撤
    equity_values = equity_df['portfolio_value'].values
    peak = np.maximum.accumulate(equity_values)
    drawdown = (equity_values - peak) / peak
    max_drawdown = np.min(drawdown)
    
    # 夏普比率（简化计算）
    returns = np.diff(equity_values) / equity_values[:-1]
    sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(24*365) if np.std(returns) > 0 else 0
    
    # 7. 显示结果
    print("\n" + "="*60)
    print("🎉 回测完成!")
    print("="*60)
    print(f"📊 回测期间: {timestamps[0].strftime('%Y-%m-%d')} 到 {timestamps[-1].strftime('%Y-%m-%d')}")
    print(f"💰 初始资金: ${initial_capital:,.2f}")
    print(f"💰 最终资金: ${final_capital:,.2f}")
    print(f"📈 总收益率: {total_return:.2%}")
    print(f"📈 基准收益率: {buy_hold_return:.2%}")
    print(f"🎯 超额收益: {total_return - buy_hold_return:.2%}")
    print(f"")
    print(f"📊 交易统计:")
    print(f"   总交易次数: {total_trades}")
    print(f"   盈利交易: {profitable_trades}")
    print(f"   胜率: {win_rate:.2%}")
    print(f"   最大回撤: {max_drawdown:.2%}")
    print(f"   夏普比率: {sharpe_ratio:.2f}")
    
    # 8. 绘制图表
    print("\n📈 生成回测图表...")
    
    plt.figure(figsize=(15, 10))
    
    # 子图1: 价格和信号
    plt.subplot(3, 1, 1)
    plt.plot(timestamps, prices, label='价格', alpha=0.7)
    
    # 标记买卖信号
    buy_signals = equity_df[equity_df['signal'] == 1]
    sell_signals = equity_df[equity_df['signal'] == 2]
    
    if not buy_signals.empty:
        plt.scatter(buy_signals['timestamp'], buy_signals['price'], 
                   color='green', marker='^', s=50, label='买入信号')
    if not sell_signals.empty:
        plt.scatter(sell_signals['timestamp'], sell_signals['price'], 
                   color='red', marker='v', s=50, label='卖出信号')
    
    plt.title(f'{symbol} 价格和交易信号')
    plt.ylabel('价格')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2: 权益曲线
    plt.subplot(3, 1, 2)
    plt.plot(equity_df['timestamp'], equity_df['portfolio_value'], 
             label='策略权益', linewidth=2)
    
    # 基准线
    benchmark_values = initial_capital * (1 + (prices - prices[0]) / prices[0])
    plt.plot(timestamps, benchmark_values, 
             label='买入持有', alpha=0.7, linestyle='--')
    
    plt.title('权益曲线对比')
    plt.ylabel('资金')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图3: 回撤
    plt.subplot(3, 1, 3)
    plt.fill_between(equity_df['timestamp'], drawdown * 100, 0, 
                     alpha=0.3, color='red', label='回撤')
    plt.title('回撤曲线')
    plt.ylabel('回撤 (%)')
    plt.xlabel('时间')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = f"backtest_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存: {chart_path}")
    
    plt.show()
    
    # 9. 保存详细结果
    results = {
        'summary': {
            'symbol': symbol,
            'test_period': f"{timestamps[0].strftime('%Y-%m-%d')} 到 {timestamps[-1].strftime('%Y-%m-%d')}",
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_return': total_return,
            'benchmark_return': buy_hold_return,
            'excess_return': total_return - buy_hold_return,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio
        },
        'equity_curve': equity_df,
        'trades': trades_df
    }
    
    return results

if __name__ == "__main__":
    import sys
    
    # 默认使用最新的模型文件
    import glob
    model_files = glob.glob("models/simple_balanced_*.joblib")
    
    if not model_files:
        print("❌ 未找到模型文件，请先运行 simple_balanced_train.py")
        sys.exit(1)
    
    # 使用最新的模型
    latest_model = max(model_files, key=lambda x: x.split('_')[-1])
    
    symbol = sys.argv[1] if len(sys.argv) > 1 else 'BTCUSDT'
    test_months = int(sys.argv[2]) if len(sys.argv) > 2 else 3
    initial_capital = int(sys.argv[3]) if len(sys.argv) > 3 else 10000
    
    print(f"📦 使用模型: {latest_model}")
    
    results = run_backtest(latest_model, symbol, test_months, initial_capital)
