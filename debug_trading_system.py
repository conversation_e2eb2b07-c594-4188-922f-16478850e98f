#!/usr/bin/env python3
"""
调试版实时交易系统
显示详细的AI决策过程和特征分析
"""

import pandas as pd
import numpy as np
import logging
import time
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from collections import deque

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DebugTradingSystem:
    """调试版交易系统"""
    
    def __init__(self, symbol: str = "BTCUSDT", initial_balance: float = 50.0):
        self.symbol = symbol
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.leverage = 125.0
        
        # AI模型参数 (降低门槛以便调试)
        self.ai_accuracy = 0.836
        self.min_confidence = 0.65  # 降低到65%
        
        # 交易参数
        self.position_size_pct = 0.02
        self.stop_loss_pct = 0.008
        self.take_profit_pct = 0.015
        self.min_trade_interval = 120  # 降低到2分钟
        
        # 数据缓存
        self.kline_data = deque(maxlen=200)
        self.price_history = deque(maxlen=1000)
        
        # 交易状态
        self.current_position = None
        self.last_trade_time = None
        self.total_trades = 0
        self.winning_trades = 0
        self.trade_history = []
        
        # 调试信息
        self.debug_info = []
        
    def get_current_price(self) -> float:
        """获取当前永续合约价格"""
        try:
            url = "https://fapi.binance.com/fapi/v1/ticker/price"
            params = {'symbol': self.symbol}
            
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            
            data = response.json()
            price = float(data['price'])
            
            self.price_history.append({
                'timestamp': datetime.now(),
                'price': price
            })
            
            return price
            
        except Exception as e:
            logger.error(f"获取价格失败: {e}")
            return 0.0
    
    def get_latest_klines(self, limit: int = 50) -> pd.DataFrame:
        """获取最新永续合约K线数据"""
        try:
            url = "https://fapi.binance.com/fapi/v1/klines"
            params = {
                'symbol': self.symbol,
                'interval': '1m',
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                return pd.DataFrame()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades_count',
                'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
            ])
            
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df[['open', 'high', 'low', 'close', 'volume']]
            
        except Exception as e:
            logger.error(f"获取K线数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_features_debug(self, df: pd.DataFrame) -> dict:
        """计算特征并显示调试信息"""
        if len(df) < 20:
            logger.warning("数据不足，无法计算特征")
            return {}
        
        features = {}
        
        try:
            # 价格特征
            features['price_change_1'] = df['close'].pct_change().iloc[-1]
            features['price_change_3'] = df['close'].pct_change(3).iloc[-1]
            features['price_change_5'] = df['close'].pct_change(5).iloc[-1]
            features['price_change_10'] = df['close'].pct_change(10).iloc[-1]
            
            # 移动平均
            features['ma_5'] = df['close'].rolling(5).mean().iloc[-1]
            features['ma_10'] = df['close'].rolling(10).mean().iloc[-1]
            features['ma_20'] = df['close'].rolling(20).mean().iloc[-1]
            
            current_price = df['close'].iloc[-1]
            features['price_ma5_ratio'] = current_price / features['ma_5']
            features['price_ma10_ratio'] = current_price / features['ma_10']
            features['price_ma20_ratio'] = current_price / features['ma_20']
            
            # 移动平均趋势
            ma5_prev = df['close'].rolling(5).mean().iloc[-2]
            ma10_prev = df['close'].rolling(10).mean().iloc[-2]
            features['ma5_slope'] = (features['ma_5'] - ma5_prev) / ma5_prev if ma5_prev > 0 else 0
            features['ma10_slope'] = (features['ma_10'] - ma10_prev) / ma10_prev if ma10_prev > 0 else 0
            
            # 价格位置
            rolling_min_10 = df['low'].rolling(10).min().iloc[-1]
            rolling_max_10 = df['high'].rolling(10).max().iloc[-1]
            features['price_position_10'] = (current_price - rolling_min_10) / (rolling_max_10 - rolling_min_10) if rolling_max_10 > rolling_min_10 else 0.5
            
            # 波动率
            features['volatility_10'] = df['close'].pct_change().rolling(10).std().iloc[-1]
            features['volatility_20'] = df['close'].pct_change().rolling(20).std().iloc[-1]
            features['vol_ratio'] = features['volatility_10'] / features['volatility_20'] if features['volatility_20'] > 0 else 1.0
            
            # 成交量
            features['volume_ma_10'] = df['volume'].rolling(10).mean().iloc[-1]
            features['volume_ratio'] = df['volume'].iloc[-1] / features['volume_ma_10'] if features['volume_ma_10'] > 0 else 1.0
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            features['rsi'] = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=9).mean()
            features['macd'] = macd.iloc[-1]
            features['macd_signal'] = signal.iloc[-1]
            features['macd_histogram'] = (macd - signal).iloc[-1]
            
            # 显示特征调试信息
            logger.info("📊 特征分析:")
            logger.info(f"   价格变化: 1分钟={features['price_change_1']:.4f}, 5分钟={features['price_change_5']:.4f}")
            logger.info(f"   MA比率: MA5={features['price_ma5_ratio']:.4f}, MA10={features['price_ma10_ratio']:.4f}")
            logger.info(f"   MA趋势: MA5斜率={features['ma5_slope']:.6f}, MA10斜率={features['ma10_slope']:.6f}")
            logger.info(f"   RSI: {features['rsi']:.1f}")
            logger.info(f"   成交量比率: {features['volume_ratio']:.2f}")
            logger.info(f"   价格位置: {features['price_position_10']:.2f}")
            
        except Exception as e:
            logger.error(f"计算特征失败: {e}")
            return {}
        
        return features
    
    def predict_with_debug_ai(self, features: dict) -> tuple:
        """AI预测并显示详细决策过程"""
        if not features:
            return "HOLD", 0.0
        
        logger.info("🤖 AI决策分析:")
        
        # 多因子评分系统
        long_score = 0
        short_score = 0
        decision_log = []
        
        # 1. 趋势因子
        price_change_5 = features.get('price_change_5', 0)
        if price_change_5 > 0.001:  # 降低阈值到0.1%
            long_score += 2
            decision_log.append(f"✅ 5分钟上涨{price_change_5:.3%} -> 看多+2")
        elif price_change_5 < -0.001:
            short_score += 2
            decision_log.append(f"✅ 5分钟下跌{price_change_5:.3%} -> 看空+2")
        else:
            decision_log.append(f"⚪ 5分钟变化{price_change_5:.3%} -> 中性")
        
        # 2. 移动平均因子
        ma5_ratio = features.get('price_ma5_ratio', 1)
        ma10_ratio = features.get('price_ma10_ratio', 1)
        if ma5_ratio > 1.0005 and ma10_ratio > 1.0005:  # 降低阈值
            long_score += 2
            decision_log.append(f"✅ 价格高于MA5/MA10 -> 看多+2")
        elif ma5_ratio < 0.9995 and ma10_ratio < 0.9995:
            short_score += 2
            decision_log.append(f"✅ 价格低于MA5/MA10 -> 看空+2")
        else:
            decision_log.append(f"⚪ MA比率: MA5={ma5_ratio:.4f}, MA10={ma10_ratio:.4f} -> 中性")
        
        # 3. 移动平均趋势
        ma5_slope = features.get('ma5_slope', 0)
        ma10_slope = features.get('ma10_slope', 0)
        if ma5_slope > 0.0002 and ma10_slope > 0.0001:  # 降低阈值
            long_score += 1
            decision_log.append(f"✅ MA趋势向上 -> 看多+1")
        elif ma5_slope < -0.0002 and ma10_slope < -0.0001:
            short_score += 1
            decision_log.append(f"✅ MA趋势向下 -> 看空+1")
        else:
            decision_log.append(f"⚪ MA趋势: MA5={ma5_slope:.6f}, MA10={ma10_slope:.6f} -> 中性")
        
        # 4. RSI因子
        rsi = features.get('rsi', 50)
        if 25 < rsi < 45:  # 放宽RSI范围
            long_score += 1
            decision_log.append(f"✅ RSI={rsi:.1f} 偏低 -> 看多+1")
        elif 55 < rsi < 75:
            short_score += 1
            decision_log.append(f"✅ RSI={rsi:.1f} 偏高 -> 看空+1")
        else:
            decision_log.append(f"⚪ RSI={rsi:.1f} -> 中性")
        
        # 5. MACD因子
        macd_hist = features.get('macd_histogram', 0)
        if macd_hist > 0:
            long_score += 1
            decision_log.append(f"✅ MACD柱状图>0 -> 看多+1")
        elif macd_hist < 0:
            short_score += 1
            decision_log.append(f"✅ MACD柱状图<0 -> 看空+1")
        else:
            decision_log.append(f"⚪ MACD柱状图=0 -> 中性")
        
        # 6. 成交量确认
        volume_ratio = features.get('volume_ratio', 1)
        if volume_ratio > 1.2:  # 降低成交量阈值
            if long_score > short_score:
                long_score += 1
                decision_log.append(f"✅ 成交量放大{volume_ratio:.2f}倍，确认看多 -> 看多+1")
            elif short_score > long_score:
                short_score += 1
                decision_log.append(f"✅ 成交量放大{volume_ratio:.2f}倍，确认看空 -> 看空+1")
        else:
            decision_log.append(f"⚪ 成交量比率{volume_ratio:.2f} -> 无确认")
        
        # 显示决策过程
        for log in decision_log:
            logger.info(f"   {log}")
        
        logger.info(f"   📊 得分汇总: 看多={long_score}, 看空={short_score}")
        
        # 决策逻辑 (降低门槛)
        if long_score > short_score and long_score >= 2:  # 降低到2分
            direction = "LONG"
            base_confidence = 0.60 + (long_score - 2) * 0.05
            logger.info(f"   🚀 决策: {direction}, 基础置信度: {base_confidence:.1%}")
        elif short_score > long_score and short_score >= 2:
            direction = "SHORT"
            base_confidence = 0.60 + (short_score - 2) * 0.05
            logger.info(f"   🚀 决策: {direction}, 基础置信度: {base_confidence:.1%}")
        else:
            direction = "HOLD"
            base_confidence = 0.5
            logger.info(f"   ⏸️ 决策: {direction}, 得分不足")
        
        # 最终置信度
        final_confidence = min(0.90, base_confidence)
        
        # 模拟83.6%准确率
        is_correct = np.random.random() < self.ai_accuracy
        if not is_correct and direction != "HOLD":
            final_confidence *= 0.8
            logger.info(f"   ⚠️ 模拟错误预测，置信度调整为: {final_confidence:.1%}")
        
        logger.info(f"   🎯 最终决策: {direction}, 置信度: {final_confidence:.1%}")
        logger.info(f"   🚪 置信度门槛: {self.min_confidence:.1%}")
        
        if direction != "HOLD" and final_confidence >= self.min_confidence:
            logger.info(f"   ✅ 满足交易条件！")
        elif direction != "HOLD":
            logger.info(f"   ❌ 置信度不足，不交易")
        
        return direction, final_confidence
    
    def can_trade(self) -> bool:
        """检查是否可以交易"""
        if self.current_position:
            logger.info("   ❌ 已有持仓，不能开新仓")
            return False
        
        if self.last_trade_time:
            time_since_last = (datetime.now() - self.last_trade_time).total_seconds()
            if time_since_last < self.min_trade_interval:
                remaining = self.min_trade_interval - time_since_last
                logger.info(f"   ❌ 交易间隔不足，还需等待{remaining:.0f}秒")
                return False
        
        if self.current_balance < self.initial_balance * 0.3:
            logger.info("   ❌ 余额过低，停止交易")
            return False
        
        logger.info("   ✅ 满足交易条件")
        return True
    
    def execute_trade(self, direction: str, confidence: float, entry_price: float):
        """执行交易"""
        risk_amount = self.current_balance * self.position_size_pct
        position_value = risk_amount * self.leverage
        position_size = position_value / entry_price
        
        if direction == "LONG":
            stop_loss = entry_price * (1 - self.stop_loss_pct)
            take_profit = entry_price * (1 + self.take_profit_pct)
        else:
            stop_loss = entry_price * (1 + self.stop_loss_pct)
            take_profit = entry_price * (1 - self.take_profit_pct)
        
        self.current_position = {
            'direction': direction,
            'entry_price': entry_price,
            'entry_time': datetime.now(),
            'position_size': position_size,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'confidence': confidence
        }
        
        self.total_trades += 1
        self.last_trade_time = datetime.now()
        
        logger.info(f"🚀 执行交易: {direction} @ {entry_price:.2f}")
        logger.info(f"   置信度: {confidence:.1%}, 仓位: {position_size:.6f} BTC")
        logger.info(f"   止损: {stop_loss:.2f}, 止盈: {take_profit:.2f}")
    
    def run_debug_session(self, duration_minutes: int = 10):
        """运行调试会话"""
        logger.info(f"🔍 启动调试交易系统")
        logger.info(f"💰 初始资金: ${self.initial_balance}")
        logger.info(f"🤖 AI准确率: {self.ai_accuracy:.1%}")
        logger.info(f"🚪 置信度门槛: {self.min_confidence:.1%}")
        logger.info(f"⏰ 运行时间: {duration_minutes} 分钟")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        cycle_count = 0
        
        try:
            while datetime.now() < end_time:
                cycle_count += 1
                logger.info(f"\n🔄 第 {cycle_count} 轮分析 ({'='*50})")
                
                # 获取当前价格
                current_price = self.get_current_price()
                if current_price == 0:
                    time.sleep(5)
                    continue
                
                logger.info(f"💰 当前价格: {current_price:.2f} USDT")
                
                # 获取K线数据
                df = self.get_latest_klines(50)
                if len(df) >= 20:
                    # 计算特征
                    features = self.calculate_features_debug(df)
                    
                    if features:
                        logger.info("🔍 检查交易条件:")
                        if self.can_trade():
                            # AI预测
                            direction, confidence = self.predict_with_debug_ai(features)
                            
                            if direction in ["LONG", "SHORT"] and confidence >= self.min_confidence:
                                self.execute_trade(direction, confidence, current_price)
                            else:
                                logger.info("⏸️ 本轮不交易")
                        else:
                            logger.info("⏸️ 不满足交易条件")
                    else:
                        logger.warning("⚠️ 特征计算失败")
                else:
                    logger.warning("⚠️ K线数据不足")
                
                # 等待下一轮
                logger.info(f"⏳ 等待30秒进行下一轮分析...")
                time.sleep(30)
                
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断调试")
        
        # 显示结果
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance
        
        print(f"\n🎉 调试会话完成")
        print(f"📊 总交易数: {self.total_trades}")
        print(f"📈 胜率: {win_rate:.1%}")
        print(f"💰 收益率: {total_return:+.1%}")

if __name__ == "__main__":
    print("🔍 调试版实时交易系统")
    print("🤖 显示详细的AI决策过程")
    print("📊 分析为什么没有产生交易")
    
    debug_system = DebugTradingSystem()
    
    try:
        duration = int(input("请输入调试时间(分钟，默认10): ") or "10")
        debug_system.run_debug_session(duration_minutes=duration)
    except:
        print("使用默认10分钟调试...")
        debug_system.run_debug_session(duration_minutes=10)
