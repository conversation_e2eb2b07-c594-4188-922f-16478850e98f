#!/usr/bin/env python3
"""
Test script to verify the enhanced trading display fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader
from datetime import datetime

def test_enhanced_display():
    """Test the enhanced display functionality"""
    print("🧪 Testing Enhanced Trading Display")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    # Simulate some trading activity for testing
    print("\n📊 Testing Account Status Display:")
    
    # Test 1: Initial state (no trades)
    print("\n1️⃣ Initial State (No Trades):")
    trader._print_enhanced_account_status()
    trader._print_enhanced_trading_statistics()
    
    # Test 2: Simulate multiple completed trades
    print("\n2️⃣ After Multiple Simulated Trades:")
    # Add multiple fake trades to history
    trader.trade_history.extend([
        {
            'action': 'CLOSE',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'side': 'LONG',
            'price': 102000,
            'net_pnl': 5.50,
            'roi_percent': 11.0,
            'hold_time': 0.05,  # 3 minutes
            'reason': '止盈'
        },
        {
            'action': 'CLOSE',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'side': 'SHORT',
            'price': 101800,
            'net_pnl': -2.20,
            'roi_percent': -4.4,
            'hold_time': 0.02,  # 1.2 minutes
            'reason': '止损'
        },
        {
            'action': 'CLOSE',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'side': 'LONG',
            'price': 102200,
            'net_pnl': 3.80,
            'roi_percent': 7.6,
            'hold_time': 0.08,  # 4.8 minutes
            'reason': '止盈'
        }
    ])
    
    # Update account balance to reflect the trades
    total_realized_pnl = 5.50 - 2.20 + 3.80  # = 7.10
    trader.account['balance'] = 50.0 + total_realized_pnl
    trader.account['equity'] = trader.account['balance']
    
    trader._print_enhanced_account_status()
    trader._print_enhanced_trading_statistics()
    
    # Test 3: Simulate current position
    print("\n3️⃣ With Current Open Position:")
    # Simulate an open position
    trader.position = {
        'side': 'LONG',
        'size': 0.000191,
        'entry_price': 102224.30,
        'entry_time': datetime.now(),
        'stop_loss_price': 102142.52,
        'take_profit_price': 102428.75,
        'unrealized_pnl': 2.43,
        'roi_percent': 4.4
    }

    trader.account['unrealized_pnl'] = 2.43
    trader.account['equity'] = trader.account['balance'] + 2.43  # This should match our calculation
    trader.account['margin_used'] = 19.49
    trader.account['available_margin'] = trader.account['balance'] - 19.49
    
    trader._print_enhanced_account_status()
    trader._print_enhanced_trading_statistics()
    
    print("\n✅ Enhanced Display Test Complete!")
    print("=" * 60)
    
    # Verify calculations
    print("\n🔍 Calculation Verification:")
    print(f"Initial Balance: ${trader.initial_balance:.2f}")
    realized_pnl = 7.10
    unrealized_pnl = 2.43
    print(f"Realized P&L: ${realized_pnl:.2f}")
    print(f"Unrealized P&L: ${unrealized_pnl:.2f}")
    print(f"Expected Total Equity: ${trader.initial_balance + realized_pnl + unrealized_pnl:.2f}")
    print(f"Actual Total Equity: ${trader.account['equity']:.2f}")
    print(f"Match: {'✅' if abs(trader.account['equity'] - (trader.initial_balance + realized_pnl + unrealized_pnl)) < 0.01 else '❌'}")

if __name__ == "__main__":
    test_enhanced_display()
