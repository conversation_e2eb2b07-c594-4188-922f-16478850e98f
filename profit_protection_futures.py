#!/usr/bin/env python3
"""
智能利润保护永续合约交易系统 - 自动保护总收益
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class ProfitProtectionFuturesTrader:
    """
    智能利润保护交易器 - 自动执行利润保护策略
    """
    
    def __init__(self, initial_capital=50, leverage=2, model_path=None):
        """
        初始化利润保护交易器
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = min(max(leverage, 1), 3)
        
        # 持仓状态
        self.position = 0
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0
        
        # 🛡️ 利润保护参数
        self.profit_protection = {
            'enable': True,
            'trigger_return': 0.20,      # 20%收益率触发保护
            'protection_levels': {
                0.20: 0.30,  # 20%收益时保护30%头寸
                0.30: 0.50,  # 30%收益时保护50%头寸
                0.40: 0.70,  # 40%收益时保护70%头寸 ← 当前应该触发
                0.50: 0.80,  # 50%收益时保护80%头寸
            },
            'max_risk_after_protection': 0.10,  # 保护后最大风险10%
            'trailing_stop_trigger': 0.35,      # 35%收益后启用移动止损
        }
        
        # 动态交易参数
        self.adaptive_params = {
            'base_thresholds': {
                'strong_long': 0.70, 'weak_long': 0.55,
                'weak_short': 0.45, 'strong_short': 0.30
            },
            'position_sizes': {
                'strong': 0.7, 'weak': 0.5
            },
            'risk_params': {
                'strong_signal': {'stop_loss': 0.03, 'take_profit': 0.08, 'max_hold_hours': 24},
                'weak_signal': {'stop_loss': 0.025, 'take_profit': 0.06, 'max_hold_hours': 16}
            }
        }
        
        self.commission_rate = 0.0004
        self.funding_rate = 0.0001
        
        # 交易记录
        self.trades = []
        self.equity_history = []
        self.protection_actions = []
        
        # 加载模型
        if model_path is None:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if not model_files:
                raise ValueError("未找到BTCUSDT模型文件")
            model_path = max(model_files, key=lambda x: x.split('_')[-1])
        
        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']
        
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        
        print(f"🛡️ 智能利润保护交易器初始化完成")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   利润保护: 启用")
        print(f"   保护触发: {self.profit_protection['trigger_return']:.0%}收益率")
        print(f"   当前保护级别: 40%收益→保护70%头寸")
    
    def get_current_total_return(self):
        """
        计算当前总收益率
        """
        if self.equity_history:
            current_equity = self.equity_history[-1]['equity']
        else:
            current_equity = self.capital
            
        return (current_equity - self.initial_capital) / self.initial_capital
    
    def check_profit_protection_trigger(self, current_price):
        """
        检查是否需要触发利润保护
        """
        current_return = self.get_current_total_return()
        
        # 计算当前权益（包括未实现盈亏）
        if self.position != 0:
            if self.position > 0:
                unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
            else:
                unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
            
            unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
            total_equity = self.capital + self.margin_used + unrealized_pnl
            current_return = (total_equity - self.initial_capital) / self.initial_capital
        
        # 检查保护级别
        for return_threshold, protection_ratio in sorted(self.profit_protection['protection_levels'].items(), reverse=True):
            if current_return >= return_threshold:
                return True, protection_ratio, current_return
        
        return False, 0, current_return
    
    def execute_profit_protection(self, protection_ratio, current_price, current_return, reason):
        """
        执行利润保护操作
        """
        if self.position == 0:
            return False
        
        # 计算需要平仓的数量
        protection_amount = abs(self.position) * protection_ratio
        remaining_position = self.position - (protection_amount if self.position > 0 else -protection_amount)
        
        # 计算保护收益
        if self.position > 0:
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        leveraged_pnl = pnl_ratio * self.leverage * (self.margin_used * protection_ratio)
        closing_fee = protection_amount * current_price * self.commission_rate
        protected_profit = leveraged_pnl - closing_fee
        
        # 更新资金和持仓
        self.capital += (self.margin_used * protection_ratio) + protected_profit
        self.margin_used *= (1 - protection_ratio)
        self.position = remaining_position
        
        # 记录保护操作
        protection_record = {
            'timestamp': datetime.now(),
            'action': 'PROFIT_PROTECTION',
            'protection_ratio': protection_ratio,
            'current_return': current_return,
            'protected_profit': protected_profit,
            'remaining_position': remaining_position,
            'reason': reason,
            'price': current_price
        }
        
        self.protection_actions.append(protection_record)
        
        direction_text = "多头" if self.position > 0 else "空头"
        print(f"🛡️ 利润保护执行: 平仓{protection_ratio:.0%}{direction_text}头寸")
        print(f"   触发收益率: {current_return:.2%}")
        print(f"   保护利润: ${protected_profit:+.2f}")
        print(f"   剩余头寸: {abs(remaining_position):.6f} BTC")
        print(f"   当前资金: ${self.capital:.2f}")
        
        return True
    
    def get_current_prediction(self, symbol='BTCUSDT'):
        """
        获取当前预测
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                return None, None, None
            
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            current_time = datetime.now()
            
            return up_probability, current_price, current_time
            
        except Exception as e:
            print(f"❌ 获取预测错误: {str(e)}")
            return None, None, None
    
    def should_open_position(self, up_probability):
        """
        判断是否开仓 - 考虑当前收益率
        """
        if self.position != 0:
            return False, 0, 0, None, "已有持仓"
        
        # 根据当前收益率调整仓位大小
        current_return = self.get_current_total_return()
        
        # 如果收益率很高，降低新仓位风险
        if current_return > 0.30:
            position_multiplier = 0.5  # 降低50%仓位
        elif current_return > 0.20:
            position_multiplier = 0.7  # 降低30%仓位
        else:
            position_multiplier = 1.0  # 正常仓位
        
        thresholds = self.adaptive_params['base_thresholds']
        position_sizes = self.adaptive_params['position_sizes']
        
        # 确定信号类型
        if up_probability > thresholds['strong_long']:
            signal_type = 'strong_long'
            base_position = position_sizes['strong']
            risk_params = self.adaptive_params['risk_params']['strong_signal'].copy()
        elif up_probability > thresholds['weak_long']:
            signal_type = 'weak_long'
            base_position = position_sizes['weak']
            risk_params = self.adaptive_params['risk_params']['weak_signal'].copy()
        elif up_probability < thresholds['strong_short']:
            signal_type = 'strong_short'
            base_position = position_sizes['strong']
            risk_params = self.adaptive_params['risk_params']['strong_signal'].copy()
        elif up_probability < thresholds['weak_short']:
            signal_type = 'weak_short'
            base_position = position_sizes['weak']
            risk_params = self.adaptive_params['risk_params']['weak_signal'].copy()
        else:
            return False, 0, 0, None, f"中性信号 ({up_probability:.1%})"
        
        # 应用收益率调整
        final_position = base_position * position_multiplier
        
        # 确定方向
        direction = 1 if 'long' in signal_type else -1
        
        reason = f"{signal_type} (收益率调整: {position_multiplier:.1f}x, 当前收益: {current_return:.1%})"
        
        return True, direction, final_position, risk_params, reason
    
    def should_close_position(self, up_probability, current_price):
        """
        判断是否平仓 - 包含利润保护逻辑
        """
        if self.position == 0:
            return False, "无持仓"
        
        # 🛡️ 首先检查利润保护
        should_protect, protection_ratio, current_return = self.check_profit_protection_trigger(current_price)
        
        if should_protect:
            protection_executed = self.execute_profit_protection(
                protection_ratio, current_price, current_return, 
                f"收益率{current_return:.1%}触发{protection_ratio:.0%}保护"
            )
            
            if protection_executed and self.position == 0:
                return True, f"利润保护完全平仓 (收益率: {current_return:.1%})"
        
        # 标准平仓逻辑
        if self.position > 0:
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        # 使用当前风险参数
        if hasattr(self, 'current_risk_params'):
            risk_params = self.current_risk_params
        else:
            risk_params = self.adaptive_params['risk_params']['weak_signal']
        
        # 移动止损（高收益率时启用）
        if current_return > self.profit_protection['trailing_stop_trigger']:
            # 启用移动止损
            trailing_stop = max(risk_params['stop_loss'], current_return * 0.1)  # 至少保护10%收益
            if pnl_ratio < -trailing_stop:
                return True, f"移动止损 (保护{current_return:.1%}收益)"
        
        # 标准止损止盈
        if pnl_ratio < -risk_params['stop_loss']:
            return True, f"止损 ({pnl_ratio:.2%})"
        
        if pnl_ratio > risk_params['take_profit']:
            return True, f"止盈 ({pnl_ratio:.2%})"
        
        # 时间止损
        if self.entry_time:
            hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
            if hold_hours > risk_params['max_hold_hours']:
                return True, f"时间止损 ({hold_hours:.1f}h)"
        
        # 信号反转
        if self.position > 0 and up_probability < 0.35:
            return True, f"多头信号反转 ({up_probability:.1%})"
        elif self.position < 0 and up_probability > 0.65:
            return True, f"空头信号反转 ({up_probability:.1%})"
        
        return False, "持有"
    
    def execute_trade(self, action, direction, position_size_ratio, price, timestamp, confidence=None, reason="", risk_params=None):
        """
        执行交易
        """
        if action == 'OPEN':
            available_margin = self.capital * 0.8
            position_value = available_margin * position_size_ratio * self.leverage
            position_size = position_value / price
            
            if direction == -1:
                position_size = -position_size
            
            self.position = position_size
            self.entry_price = price
            self.entry_time = timestamp
            self.margin_used = available_margin * position_size_ratio
            self.current_risk_params = risk_params
            
            opening_fee = abs(position_size) * price * self.commission_rate
            self.capital -= opening_fee
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'OPEN',
                'direction': 'LONG' if direction == 1 else 'SHORT',
                'price': price,
                'position_size': position_size,
                'position_ratio': position_size_ratio,
                'margin_used': self.margin_used,
                'leverage': self.leverage,
                'confidence': confidence,
                'reason': reason,
                'risk_params': risk_params,
                'opening_fee': opening_fee
            }
            
            direction_text = "做多" if direction == 1 else "做空"
            print(f"✅ {direction_text} {abs(position_size):.6f} BTC @ ${price:,.2f}")
            print(f"   仓位: {position_size_ratio:.0%}, 置信度: {confidence:.1%}")
            print(f"   {reason}")
            
        elif action == 'CLOSE':
            if self.position > 0:
                pnl_ratio = (price - self.entry_price) / self.entry_price
            else:
                pnl_ratio = (self.entry_price - price) / self.entry_price
            
            leveraged_pnl = pnl_ratio * self.leverage * self.margin_used
            closing_fee = abs(self.position) * price * self.commission_rate
            funding_fee = self.calculate_funding_fee()
            final_pnl = leveraged_pnl - closing_fee + funding_fee
            
            self.capital = self.capital + self.margin_used + final_pnl
            
            hold_time = (timestamp - self.entry_time).total_seconds() / 3600 if self.entry_time else 0
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'CLOSE',
                'direction': 'LONG' if self.position > 0 else 'SHORT',
                'price': price,
                'entry_price': self.entry_price,
                'pnl_ratio': pnl_ratio,
                'leveraged_pnl': leveraged_pnl,
                'final_pnl': final_pnl,
                'hold_hours': hold_time,
                'confidence': confidence,
                'reason': reason,
                'capital_after': self.capital
            }
            
            direction_text = "平多" if self.position > 0 else "平空"
            print(f"✅ {direction_text} @ ${price:,.2f} (盈亏: {final_pnl:+.2f}, 资金: ${self.capital:.2f})")
            
            self.position = 0
            self.entry_price = 0
            self.entry_time = None
            self.margin_used = 0
            self.current_risk_params = None
        
        self.trades.append(trade_record)
    
    def calculate_funding_fee(self):
        """计算资金费率"""
        if self.position == 0 or not self.entry_time:
            return 0
        
        hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
        funding_periods = int(hold_hours / 8)
        
        if funding_periods > 0:
            position_value = abs(self.position) * self.entry_price
            funding_fee = position_value * self.funding_rate * funding_periods
            return -funding_fee if self.position > 0 else funding_fee
        return 0
    
    def update_equity(self, current_price, timestamp):
        """更新权益"""
        if self.position != 0:
            if self.position > 0:
                unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
            else:
                unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
            
            unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
            funding_fee = self.calculate_funding_fee()
            current_equity = self.capital + self.margin_used + unrealized_pnl + funding_fee
        else:
            current_equity = self.capital
        
        self.equity_history.append({
            'timestamp': timestamp,
            'price': current_price,
            'equity': current_equity,
            'total_return': (current_equity - self.initial_capital) / self.initial_capital
        })
    
    def print_status(self, current_price=None, up_probability=None):
        """打印状态"""
        if self.equity_history:
            latest_equity = self.equity_history[-1]['equity']
            total_return = self.equity_history[-1]['total_return']
        else:
            latest_equity = self.capital
            total_return = 0
        
        completed_trades = [t for t in self.trades if t['action'] == 'CLOSE']
        profitable_trades = [t for t in completed_trades if t.get('final_pnl', 0) > 0]
        win_rate = len(profitable_trades) / len(completed_trades) if completed_trades else 0
        
        print(f"\n🛡️ 智能利润保护交易状态")
        print("=" * 50)
        print(f"当前权益: ${latest_equity:.2f}")
        print(f"总收益率: {total_return:+.2%}")
        print(f"可用资金: ${self.capital:.2f}")
        print(f"占用保证金: ${self.margin_used:.2f}")
        
        # 显示利润保护状态
        should_protect, protection_ratio, current_return = self.check_profit_protection_trigger(current_price) if current_price else (False, 0, total_return)
        
        if should_protect:
            print(f"🛡️ 利润保护: 触发{protection_ratio:.0%}保护 (收益率: {current_return:.1%})")
        else:
            next_protection_level = None
            for level in sorted(self.profit_protection['protection_levels'].keys()):
                if current_return < level:
                    next_protection_level = level
                    break
            if next_protection_level:
                print(f"🛡️ 下一保护级别: {next_protection_level:.0%}收益率")
        
        if self.position != 0:
            position_type = "多头" if self.position > 0 else "空头"
            print(f"当前持仓: {position_type} {abs(self.position):.6f} BTC")
            print(f"入场价格: ${self.entry_price:,.2f}")
            
            if current_price:
                if self.position > 0:
                    unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
                else:
                    unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
                
                unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
                print(f"未实现盈亏: {unrealized_pnl:+.2f} ({unrealized_pnl_ratio * self.leverage:+.2%})")
        else:
            print(f"当前持仓: 空仓")
        
        print(f"完成交易: {len(completed_trades)}")
        print(f"胜率: {win_rate:.2%}")
        print(f"利润保护操作: {len(self.protection_actions)}次")
        
        if current_price and up_probability:
            print(f"\n📈 当前市场信息:")
            print(f"BTC永续价格: ${current_price:,.2f}")
            print(f"上涨概率: {up_probability:.1%}")
            
            thresholds = self.adaptive_params['base_thresholds']
            if up_probability > thresholds['strong_long']:
                signal = "🟢 强烈看涨"
            elif up_probability > thresholds['weak_long']:
                signal = "🟡 轻微看涨"
            elif up_probability < thresholds['strong_short']:
                signal = "🔴 强烈看跌"
            elif up_probability < thresholds['weak_short']:
                signal = "🟡 轻微看跌"
            else:
                signal = "⚪ 中性"
            
            print(f"市场信号: {signal}")

def run_profit_protection_simulation(check_interval=300, leverage=2):
    """运行利润保护模拟"""
    print("🛡️ 启动智能利润保护永续合约交易系统")
    print("=" * 60)
    print("特点: 自动利润保护、动态风险调整、收益率触发机制")
    print(f"杠杆倍数: {leverage}x")
    print(f"检查间隔: {check_interval}秒")
    print("")
    
    trader = ProfitProtectionFuturesTrader(initial_capital=50, leverage=leverage)
    
    try:
        while True:
            current_time = datetime.now()
            print(f"\n⏰ {current_time.strftime('%Y-%m-%d %H:%M:%S')} - 智能保护分析...")
            
            up_prob, current_price, _ = trader.get_current_prediction()
            
            if up_prob is None:
                print("❌ 无法获取预测，跳过")
                time.sleep(check_interval)
                continue
            
            trader.update_equity(current_price, current_time)
            
            # 检查平仓（包含利润保护）
            if trader.position != 0:
                should_close, close_reason = trader.should_close_position(up_prob, current_price)
                if should_close:
                    trader.execute_trade('CLOSE', 0, 0, current_price, current_time, up_prob, close_reason)
            
            # 检查开仓
            should_open, direction, position_size, risk_params, open_reason = trader.should_open_position(up_prob)
            if should_open:
                trader.execute_trade('OPEN', direction, position_size, current_price, current_time, up_prob, open_reason, risk_params)
            
            trader.print_status(current_price, up_prob)
            
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        print(f"\n🛑 停止利润保护交易")
        trader.print_status(current_price, up_prob)

if __name__ == "__main__":
    import sys
    
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300
    leverage = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    leverage = min(max(leverage, 1), 3)
    
    print("🛡️ 智能利润保护系统说明:")
    print("- 自动检测收益率并触发保护")
    print("- 40%收益率自动保护70%头寸")
    print("- 动态调整仓位大小和风险参数")
    print("- 移动止损保护高收益")
    print("- 完全自动化执行")
    print("")
    
    run_profit_protection_simulation(interval, leverage)
