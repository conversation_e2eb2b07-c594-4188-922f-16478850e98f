import os
import pandas as pd
from data_processor import DataProcessor
from model_trainer import ModelTrainer
from binance.client import Client # For KLINE_INTERVAL constants
import logging
from datetime import datetime, timedelta
from pathlib import Path # For cleaner path operations
import re # For finding latest model
from typing import Tuple, Optional, List, Dict, Any # Added Dict and Any
import time # Added for schedule loop
import schedule # Added for scheduling predictions
from binance.exceptions import BinanceAPIException # For specific API error handling
import json # Added for saving prediction report

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_latest_model_files(model_base_dir: Path, model_type_prefix: str = 'ensemble') -> Tuple[Optional[Path], Optional[Path]]:
    """
    在指定目录中查找最新保存的模型和scaler文件。
    文件名格式假定为: prefix_modeltype_timestamp.*
    例如: model_ensemble_20230101_120000.joblib, scaler_ensemble_20230101_120000.pkl
    """
    model_files = list(model_base_dir.glob(f'model_{model_type_prefix}_*.joblib'))
    scaler_files = list(model_base_dir.glob(f'scaler_{model_type_prefix}_*.pkl')) # 更正: Scaler 后缀为 .pkl

    if not model_files or not scaler_files:
        logger.warning(f"在 {model_base_dir} 中未能找到足够的模型或scaler文件 (prefix: {model_type_prefix})。")
        return None, None

    def get_timestamp_from_path(path: Path) -> str:
        match = re.search(r'_(\d{8}_\d{6})\.(joblib|pkl)$', path.name) # 适配 .pkl
        return match.group(1) if match else ''

    latest_model_file = max(model_files, key=get_timestamp_from_path, default=None)
    if not latest_model_file:
        logger.warning(f"未能从找到的模型文件中确定最新的模型文件 (prefix: {model_type_prefix})。")
        return None, None
        
    latest_timestamp = get_timestamp_from_path(latest_model_file)
    
    latest_scaler_file = next((sf for sf in scaler_files if get_timestamp_from_path(sf) == latest_timestamp), None)

    if not latest_scaler_file:
        logger.warning(f"找到了模型文件 {latest_model_file.name}，但未能找到匹配时间戳 {latest_timestamp} 的scaler文件。")
        return latest_model_file, None # 仍然返回模型，让调用者决定如何处理
        
    logger.info(f"找到最新的模型文件: {latest_model_file.name}")
    logger.info(f"找到最新的Scaler文件: {latest_scaler_file.name}")
    
    return latest_model_file, latest_scaler_file

def predict_next_move(
    symbol: str = 'BTCUSDT',
    model_base_dir_str: str = 'trained_models', # Changed from model_path
    model_type_prefix: str = 'ensemble', # e.g., 'ensemble', 'xgb' to match saved model filenames
    # Parameters for DataProcessor, ideally from model_info.json in the future
    # For now, providing defaults that are common in the project
    dp_intervals: List[str] = [Client.KLINE_INTERVAL_30MINUTE, Client.KLINE_INTERVAL_1HOUR, Client.KLINE_INTERVAL_4HOUR],
    dp_target_interval: str = Client.KLINE_INTERVAL_30MINUTE, # Should match the first interval in dp_intervals
    dp_future_periods: int = 2,
    dp_atr_multiplier: float = 0.75,
    data_history_needed: str = '100 days' # How much history to fetch for feature calculation
):
    """
    使用加载的ModelTrainer实例预测指定交易对的下一个价格走势。
    """
    logger.info(f"开始为 {symbol} 执行预测 (使用 {model_type_prefix} 类型模型)...")
    
    model_base_dir = Path(model_base_dir_str)

    # 1. 查找或指定模型文件
    model_path, scaler_path = find_latest_model_files(model_base_dir, model_type_prefix)
    
    if not model_path or not scaler_path:
        logger.error(f"在目录 {model_base_dir} 中未能找到模型 {model_type_prefix} 的完整文件集 (模型和匹配的scaler)。无法进行预测。")
        return None

    # 2. 加载 ModelTrainer 实例
    try:
        # 通过实例化ModelTrainer并传递路径来加载模型和scaler
        loaded_trainer = ModelTrainer(
            model_path=model_path,
            scaler_path=scaler_path,
            output_dir=model_base_dir, # 提供output_dir
            model_type=model_type_prefix # 提供model_type
        )
        # 检查模型和scaler是否真的加载成功
        if not loaded_trainer.model or not loaded_trainer.scaler:
            logger.error(f"即使提供了路径，ModelTrainer未能成功加载模型或Scaler。模型: {model_path.name}, Scaler: {scaler_path.name}")
            return None

        logger.info(f"成功初始化 ModelTrainer 实例并加载了模型和Scaler。模型: {model_path.name}, Scaler: {scaler_path.name}")
    except Exception as e:
        logger.error(f"初始化或加载ModelTrainer实例时发生错误: {str(e)}", exc_info=True)
        return None

    # TODO: 未来从 loaded_trainer.model_info (或直接从 loaded_trainer 属性) 中获取以下参数
    # current_dp_intervals = loaded_trainer.model_info.get('data_processing_config', {}).get('intervals', dp_intervals)
    # current_dp_target_interval = loaded_trainer.model_info.get('data_processing_config', {}).get('target_interval', dp_target_interval)
    # current_dp_future_periods = loaded_trainer.model_info.get('data_processing_config', {}).get('future_periods', dp_future_periods)
    # current_dp_atr_multiplier = loaded_trainer.model_info.get('data_processing_config', {}).get('atr_multiplier', dp_atr_multiplier)
    # For now, use function arguments:
    current_dp_intervals = dp_intervals
    current_dp_target_interval = dp_target_interval
    current_dp_future_periods = dp_future_periods
    current_dp_atr_multiplier = dp_atr_multiplier
    
    try:
        # 3. 初始化数据处理器
        processor = DataProcessor(
            intervals=current_dp_intervals,
            future_periods=current_dp_future_periods,
            atr_multiplier=current_dp_atr_multiplier
        )
        # Ensure target_interval is set correctly in DataProcessor if its init doesn't handle it based on intervals[0]
        processor.target_interval = current_dp_target_interval 
        
        logger.info(f"正在为 {symbol} 获取最新数据 (回溯 '{data_history_needed}' 用于指标计算)...")
        start_time = datetime.now() - pd.to_timedelta(data_history_needed)
        # Ensure start_str is compatible with _fetch_historical_data_in_chunks or prepare_data
        start_str_for_api = start_time.strftime("%Y-%m-%d %H:%M:%S") # More standard format

        # 4. 使用 DataProcessor.prepare_data 获取并处理特征
        # prepare_data returns X, y (or None for y in predict mode), and df_target_interval_with_indicators
        # We only need X_features for prediction input, but must unpack all three.
        X_features, _, df_target_original_for_report = processor.prepare_data(
            symbol=symbol,
            start_str=start_str_for_api, 
            end_str=None, # Fetch up to the latest available data
            mode='predict' # Explicitly set mode to 'predict'
        )

        if X_features.empty:
            logger.error("未能获取或处理最新的特征数据 (X_features为空)，无法进行预测。")
            return None

        # 获取最后一行为预测输入 (确保是DataFrame)
        latest_features_df = X_features.iloc[[-1]]

        # 5. 进行预测 (scaler.transform is handled inside loaded_trainer.predict)
        prediction_values = loaded_trainer.predict(latest_features_df)
        
        if prediction_values is None or len(prediction_values) == 0:
            logger.error("模型预测返回为空。")
            return None

        prediction = prediction_values[0]

        # 6. 解析预测结果 和 提取详细指标信息
        detailed_report: Dict[str, Any] = {}
        core_prediction_label = "未知"

        if prediction == 1:
            core_prediction_label = '上涨'
        elif prediction == -1:
            core_prediction_label = '下跌'
        elif prediction == 0:
            core_prediction_label = '横盘'
        else:
            logger.warning(f"模型预测返回了意外的值: {prediction}. 无法解析为常规涨跌平。")
            core_prediction_label = f"原始预测值: {prediction}"
        
        detailed_report['core_prediction'] = core_prediction_label
        detailed_report['symbol'] = symbol
        detailed_report['target_interval'] = processor.target_interval # Fetched from processor instance
        detailed_report['prediction_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        detailed_report['model_name'] = model_path.name if model_path else 'N/A'

        # 8. 获取并添加一个更实时的价格快照
        real_time_snapshot_price = "N/A"
        real_time_snapshot_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        try:
            fetcher = BinanceDataFetcher()
            # 根据symbol判断是否为永续合约
            is_futures = symbol.endswith('USDT')  # 这里可以根据实际需求修改判断逻辑
            real_time_snapshot_price = fetcher.get_current_price(symbol, is_futures)
            
            # 获取最新的历史数据（强制刷新）
            latest_data = fetcher.get_historical_data(
                symbol=symbol,
                interval=dp_target_interval,
                start_date=(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                is_futures=is_futures,
                force_refresh=True
            )
            
            if not latest_data.empty:
                latest_close = latest_data['close'].iloc[-1]
                logger.info(f"最新K线收盘价: {latest_close}")
            
            logger.info(f"成功获取 {symbol} 的实时{'永续合约' if is_futures else '现货'}价格: {real_time_snapshot_price} (获取时间: {real_time_snapshot_time})")
        except Exception as e:
            logger.error(f"获取 {symbol} 实时价格时发生错误: {e}", exc_info=True)

        detailed_report['real_time_snapshot_price'] = real_time_snapshot_price
        detailed_report['real_time_snapshot_time'] = real_time_snapshot_time
        detailed_report['is_futures'] = is_futures  # 添加合约类型信息到报告中

        # Extract latest indicator values from latest_features_df
        # latest_features_df is a single-row DataFrame with merged features
        # Column names have interval suffixes, e.g., 'close_15m', 'rsi_15m'
        # The target_interval is available from processor.target_interval
        ti = processor.target_interval
        indicators_to_extract = {
            'current_price': f'close_{ti}',
            'sma_20': f'sma_20_{ti}',
            'sma_50': f'sma_50_{ti}',
            'macd': f'macd_{ti}',
            'macd_signal': f'macd_signal_{ti}',
            'macd_diff': f'macd_diff_{ti}',
            'rsi_14': f'rsi_{ti}', # Assuming default RSI in DataProcessor is 14
            'bollinger_mavg': f'bollinger_mavg_{ti}',
            'bollinger_hband': f'bollinger_hband_{ti}',
            'bollinger_lband': f'bollinger_lband_{ti}',
            'stoch_k': f'stoch_k_{ti}',
            'stoch_d': f'stoch_d_{ti}',
            # Add more as needed and available from DataProcessor
        }
        
        extracted_indicators: Dict[str, Any] = {}
        # latest_features_df contains the scaled data if scaling was applied before prediction
        # For a human-readable report, we want the *original* (unscaled) values.
        # X_features (before iloc[-1]) contains the original, merged, unscaled features.
        # So, we should extract from X_features.iloc[-1] (which is a Series)
        
        latest_unscaled_features_series = X_features.iloc[-1] # This is a pandas Series

        for key, col_name in indicators_to_extract.items():
            if col_name in latest_unscaled_features_series.index:
                extracted_indicators[key] = latest_unscaled_features_series[col_name]
            else:
                extracted_indicators[key] = 'N/A' # Or some other placeholder
                logger.warning(f"指标列 '{col_name}' 在最新的特征数据中未找到.")
        
        detailed_report['indicators'] = extracted_indicators

        # 7. 添加简单的文字解读 (初步)
        # This section can be greatly expanded
        analysis_texts = []
        price = extracted_indicators.get('current_price')
        sma20 = extracted_indicators.get('sma_20')
        sma50 = extracted_indicators.get('sma_50')
        rsi14 = extracted_indicators.get('rsi_14')
        macd_val = extracted_indicators.get('macd')
        macd_sig = extracted_indicators.get('macd_signal')

        if price != 'N/A' and sma20 != 'N/A':
            analysis_texts.append(f"当前价格 {price:.2f} 位于 SMA20 ({sma20:.2f}) {'之上' if price > sma20 else '之下'}.")
        if sma20 != 'N/A' and sma50 != 'N/A':
            analysis_texts.append(f"SMA20 ({sma20:.2f}) 位于 SMA50 ({sma50:.2f}) {'之上 (短期多头)' if sma20 > sma50 else '之下 (短期空头)'}.")
        if rsi14 != 'N/A':
            rsi_text = f"RSI(14) 为 {rsi14:.2f}."
            if rsi14 > 70: rsi_text += " (超买区域)"
            elif rsi14 < 30: rsi_text += " (超卖区域)"
            else: rsi_text += " (中性区域)"
            analysis_texts.append(rsi_text)
        if macd_val != 'N/A' and macd_sig != 'N/A':
            macd_text = f"MACD ({macd_val:.2f}) / Signal ({macd_sig:.2f})."
            if macd_val > macd_sig: macd_text += " (MACD线上穿Signal线 - 金叉迹象)"
            else: macd_text += " (MACD线下穿Signal线 - 死叉迹象)"
            analysis_texts.append(macd_text)

        detailed_report['analysis_summary'] = analysis_texts

        # 8. 获取并添加一个更实时的价格快照 (可选)
        # real_time_snapshot_price = "N/A" # Moved up
        if hasattr(processor, 'client'): # Check if processor has the client attribute
            try:
                # Ensure SYMBOL_TO_PREDICT is correctly cased for the API if needed
                ticker_info = processor.client.get_symbol_ticker(symbol=symbol) 
                real_time_snapshot_price = float(ticker_info['price'])
                real_time_snapshot_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3] # Store with milliseconds
                logger.info(f"成功获取 {symbol} 的实时ticker价格: {real_time_snapshot_price} (获取时间: {real_time_snapshot_time})")
            except BinanceAPIException as bae:
                logger.warning(f"获取 {symbol} 实时ticker价格时发生币安API错误: {bae}")
            except Exception as e:
                logger.error(f"获取 {symbol} 实时ticker价格时发生未知错误: {e}", exc_info=True)
        
        detailed_report['real_time_snapshot_price'] = real_time_snapshot_price
        detailed_report['real_time_snapshot_time'] = real_time_snapshot_time # Add to report

        # ---- MODIFICATION START: Calculate and add expected amplitude based on ATR ----
        expected_change_abs = "N/A"
        expected_change_pct_str = "N/A"
        atr_threshold_value_for_report = "N/A"

        current_close_price_for_amplitude = extracted_indicators.get('current_price')
        # Assuming 'atr_20' is the one used for target definition and its root is 'atr_20'
        atr_col_name_for_amplitude = f'atr_20_{ti}' 
        
        if atr_col_name_for_amplitude in latest_unscaled_features_series.index and current_close_price_for_amplitude != 'N/A' and isinstance(current_close_price_for_amplitude, (int, float)):
            atr_value = latest_unscaled_features_series[atr_col_name_for_amplitude]
            # Use dp_atr_multiplier passed to the function, which should be consistent with DataProcessor
            atr_threshold_value = atr_value * current_dp_atr_multiplier 
            atr_threshold_value_for_report = f"{atr_threshold_value:.2f}" # For report

            if core_prediction_label == '上涨':
                expected_change_abs = f"+{atr_threshold_value:.2f}"
                expected_change_pct = (atr_threshold_value / current_close_price_for_amplitude) * 100
                expected_change_pct_str = f"约 +{expected_change_pct:.2f}%"
            elif core_prediction_label == '下跌':
                expected_change_abs = f"-{atr_threshold_value:.2f}"
                expected_change_pct = (-atr_threshold_value / current_close_price_for_amplitude) * 100
                expected_change_pct_str = f"约 {expected_change_pct:.2f}%"
            elif core_prediction_label == '横盘':
                expected_change_abs = f"±{atr_threshold_value:.2f}"
                expected_change_pct = (atr_threshold_value / current_close_price_for_amplitude) * 100
                expected_change_pct_str = f"约 ±{expected_change_pct:.2f}% 波动"
            else: # Unknown prediction
                pass # Keep as N/A
        
        detailed_report['expected_change_abs'] = expected_change_abs
        detailed_report['expected_change_pct_str'] = expected_change_pct_str
        detailed_report['atr_threshold_for_target'] = atr_threshold_value_for_report
        # ---- MODIFICATION END ----

        logger.info(f"对符号 {symbol} ({processor.target_interval}) 的详细预测报告已生成。核心预测: {core_prediction_label}")
        return detailed_report # Return the full report object

    except Exception as e:
        logger.error(f"预测过程中发生严重错误: {str(e)}", exc_info=True)
        return None

# Global/module-level configuration (populated in __main__)
# These are needed by the job function if it's defined globally
# Or, pass them as arguments to the job function using functools.partial if preferred
SYMBOL_TO_PREDICT = 'BTCUSDT'
TRAINING_DP_INTERVALS: List[str] = [] # Placeholder
TRAINING_DP_TARGET_INTERVAL = '' # Placeholder
TRAINING_DP_FUTURE_PERIODS = 1 # Placeholder
TRAINING_DP_ATR_MULTIPLIER = 0.75 # 修改这里的值
MODEL_DIR = 'trained_models'
MODEL_TYPE_FOR_LATEST = 'ensemble'
DATA_FETCH_HISTORY = '100 days'
# ---- NEW: Configuration for K-line alignment ----
KLINE_END_DELAY_SECONDS = 0 # Number of seconds to wait after K-line end before running prediction
# ---- NEW: Configuration for prediction log file ----
PREDICTION_LOG_FILE = Path('prediction_log.jsonl')

def _parse_interval_to_minutes(interval_str: str) -> Optional[int]:
    """Helper to parse Binance interval string to total minutes."""
    unit = interval_str[-1].lower()
    try:
        value = int(interval_str[:-1])
        if unit == 'm':
            return value
        elif unit == 'h':
            return value * 60
        elif unit == 'd':
            return value * 60 * 24
        elif unit == 'w':
            # Week is more complex due to start day, for now, less precise or handle if needed
            return value * 60 * 24 * 7 
        else:
            logger.error(f"无法解析的K线周期单位: {unit} from {interval_str}")
            return None
    except ValueError:
        logger.error(f"无法解析K线周期数值: {interval_str[:-1]} from {interval_str}")
        return None

def get_next_kline_run_time(target_interval_str: str, delay_seconds: int) -> datetime:
    """
    Calculates the next exact run time for the prediction job,
    aligned with K-line end plus a delay.
    """
    now = datetime.now()
    interval_minutes = _parse_interval_to_minutes(target_interval_str)

    if interval_minutes is None:
        logger.error(f"无法为周期 {target_interval_str} 计算下一次运行时间，将默认15分钟后重试。")
        return now + timedelta(minutes=15) # Fallback

    if interval_minutes >= 60 * 24: # Daily or weekly
        # For daily, next run is start of next day + delay
        next_run_base = (now + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    elif interval_minutes >= 60: # Hourly
        hours = interval_minutes // 60
        next_hour_start = (now.hour // hours + 1) * hours
        if next_hour_start >= 24:
            next_run_base = (now + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            next_run_base = now.replace(hour=next_hour_start, minute=0, second=0, microsecond=0)
        if next_run_base <= now : # If calculated time is in the past or current hour for multi-hour interval
             next_run_base += timedelta(hours=hours)

    else: # Minutely
        current_minute_block = (now.minute // interval_minutes) * interval_minutes
        # next_run_base is the start of the next interval block
        next_run_base = (now.replace(minute=current_minute_block, second=0, microsecond=0) + 
                         timedelta(minutes=interval_minutes))

    # Add the delay
    next_run_time = next_run_base + timedelta(seconds=delay_seconds)
    
    # If the calculated next_run_time is still in the past (e.g., due to script restart close to an interval end)
    # ensure it's for the *next* available slot.
    # This logic needs to be careful for different interval scales.
    # A simpler approach for "next": if next_run_time <= now, add one interval period.
    # This might cause a longer initial wait if script starts just after a kline close + delay
    # A more robust way:
    while next_run_time <= now: # Ensure it's strictly in the future
        logger.debug(f"计算得到的下次运行时间 {next_run_time.strftime('%Y-%m-%d %H:%M:%S')} 已过去或太近，重新计算下一个周期。")
        if interval_minutes >= 60*24 : #days
             next_run_time += timedelta(days=interval_minutes/(60*24))
        elif interval_minutes >= 60: #hours
             next_run_time += timedelta(hours=interval_minutes/60)
        else: #minutes
             next_run_time += timedelta(minutes=interval_minutes)
        # Re-apply delay just in case, though base should be correct
        # next_run_time = next_run_time.replace(second=0, microsecond=0) + timedelta(seconds=delay_seconds) # This might be too aggressive.
        # Simpler: the loop condition itself handles advancing the period. The initial delay addition is key.


    logger.info(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}, 下一个 {target_interval_str} K线预测运行时间 (含 {delay_seconds}s 延迟): {next_run_time.strftime('%Y-%m-%d %H:%M:%S')}")
    return next_run_time

def perform_prediction_job():
    """Job function to be scheduled.
       Uses global/module-level variables for configuration.
    """
    logger.info(f"定时任务触发: 开始为符号 {SYMBOL_TO_PREDICT} ({TRAINING_DP_TARGET_INTERVAL}) 执行预测...")
    
    prediction_report = predict_next_move(
        symbol=SYMBOL_TO_PREDICT,
        model_base_dir_str=MODEL_DIR,
        model_type_prefix=MODEL_TYPE_FOR_LATEST,
        dp_intervals=TRAINING_DP_INTERVALS,
        dp_target_interval=TRAINING_DP_TARGET_INTERVAL,
        dp_future_periods=TRAINING_DP_FUTURE_PERIODS,
        dp_atr_multiplier=TRAINING_DP_ATR_MULTIPLIER,
        data_history_needed=DATA_FETCH_HISTORY
    )

    if prediction_report:
        # ---- 新增：从报告中提取K线收盘价，作为明确的顶层字段 ----
        kline_close_price_basis = prediction_report.get('kline_close_price_basis', 'N/A') # 使用这里设置的kline_close_price_basis
        # prediction_report['kline_close_price_basis'] = kline_close_price_basis # 这行是多余的，因为上面已经获取了
        # ---- 提取结束 ----

        print("\n--- 定时预测分析报告 ---")
        print(f"交易对: {prediction_report.get('symbol')}")
        print(f"K线周期: {prediction_report.get('target_interval')}")
        print(f"预测执行时间: {prediction_report.get('prediction_time')}")
        
        snapshot_price = prediction_report.get('real_time_snapshot_price', 'N/A')
        snapshot_time = prediction_report.get('real_time_snapshot_time', 'N/A')
        if snapshot_price != 'N/A':
            price_str = f"{snapshot_price:.2f} USDT" if isinstance(snapshot_price, (float, int)) else snapshot_price
            time_str = f" (获取于 {snapshot_time})" if snapshot_time != 'N/A' else ""
            print(f"实时价格快照: {price_str}{time_str}")
        else:
            print(f"实时价格快照: N/A")
            
        print(f"使用模型: {prediction_report.get('model_name')}")
        
        # 使用上面从 prediction_report 直接获取的 kline_close_price_basis
        price_basis_str = f" (基于 {kline_close_price_basis:.2f} K线收盘价)" if isinstance(kline_close_price_basis, (float,int)) else ""
        
        print(f"核心预测方向{price_basis_str}:") # 作为下面详细预测的标题

        core_pred_label = prediction_report.get('core_prediction', 'N/A')
        abs_change_value_str = prediction_report.get('expected_change_abs', 'N/A') 
        pct_change_for_display = prediction_report.get('expected_change_pct_str', 'N/A')
        atr_for_target = prediction_report.get('atr_threshold_for_target', 'N/A')

        predicted_price_display = "N/A"
        change_direction_label = core_pred_label 
        
        cleaned_pct_str = "N/A"
        if isinstance(pct_change_for_display, str):
            cleaned_pct_str = pct_change_for_display.replace("约 ", "").replace(" 波动", "")
        
        if isinstance(kline_close_price_basis, (float, int)) and abs_change_value_str != 'N/A':
            try:
                numeric_change_match = re.search(r'[-+]?(\\d*\\.?\\d+)', abs_change_value_str)
                if numeric_change_match:
                    numeric_change = float(numeric_change_match.group(1))
                    
                    if core_pred_label == '上涨':
                        predicted_price_display = f"{kline_close_price_basis + numeric_change:.2f} USDT"
                        change_direction_label = "涨"
                    elif core_pred_label == '下跌':
                        predicted_price_display = f"{kline_close_price_basis - numeric_change:.2f} USDT"
                        change_direction_label = "跌"
                    elif core_pred_label == '横盘':
                        predicted_price_display = f"{kline_close_price_basis:.2f} ± {numeric_change:.2f} USDT"
                        change_direction_label = "横盘"
                    else: 
                        predicted_price_display = "无法计算"
                else:
                    predicted_price_display = "无法解析变动值"
            except ValueError:
                predicted_price_display = "计算错误"
        
        print(f"  预测结论: {core_pred_label}") 
        print(f"  预测价格为: {predicted_price_display}")
        print(f"  幅度比例: {cleaned_pct_str}") 
        print(f"  相对涨跌: {change_direction_label}")
        
        if atr_for_target != 'N/A':
            print(f"    (提示: 判断的ATR目标阈值约: {atr_for_target})")
        
        print("\n--- K线指标数据 (用于模型输入) ---")
        indicators = prediction_report.get('indicators', {})
        if indicators:
            for key, value in indicators.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.4f}")
                else:
                    print(f"  {key}: {value}")
        else:
            print("  未能提取指标数据。")
        print("\n--- 初步技术分析解读 ---")
        analysis = prediction_report.get('analysis_summary', [])
        if analysis:
            for point in analysis:
                print(f"- {point}")
        else:
            print("  无分析摘要。")
        print("---------------------------\n")

        # ---- 新增：记录预测结果到文件 ----
        try:
            with open(PREDICTION_LOG_FILE, 'a', encoding='utf-8') as f:
                json.dump(prediction_report, f, ensure_ascii=False)
                f.write('\n')
            logger.info(f"预测报告已成功追加到 {PREDICTION_LOG_FILE}")
        except Exception as e:
            logger.error(f"记录预测报告到 {PREDICTION_LOG_FILE} 时发生错误: {e}", exc_info=True)
        # ---- 记录结束 ----

    else:
        logger.error(f"定时预测任务未能完成对 {SYMBOL_TO_PREDICT} 的预测。请检查日志。")

if __name__ == "__main__":
    # Populate global config variables from here, so the job function can access them
    # This is a simple way; for more complex apps, consider classes or passing args via functools.partial
    
    # --- 配置预测参数 ---
    SYMBOL_TO_PREDICT = 'BTCUSDT'
    TRAINING_DP_INTERVALS = [Client.KLINE_INTERVAL_30MINUTE, Client.KLINE_INTERVAL_1HOUR, Client.KLINE_INTERVAL_4HOUR]
    TRAINING_DP_TARGET_INTERVAL = Client.KLINE_INTERVAL_30MINUTE # 确保与训练时目标定义一致
    TRAINING_DP_FUTURE_PERIODS = 2 
    TRAINING_DP_ATR_MULTIPLIER = 0.75 # 保留之前修改的0.75
    MODEL_DIR = 'trained_models'
    MODEL_TYPE_FOR_LATEST = 'ensemble'
    DATA_FETCH_HISTORY = '100 days'
    # KLINE_END_DELAY_SECONDS is defined globally at the top

    logger.info(f"初始化K线对齐的持续预测脚本 for symbol: {SYMBOL_TO_PREDICT}, Target Interval: {TRAINING_DP_TARGET_INTERVAL}")
    logger.info(f"预测将在每个K线周期结束后 {KLINE_END_DELAY_SECONDS} 秒执行。按 Ctrl+C 退出。")

    if _parse_interval_to_minutes(TRAINING_DP_TARGET_INTERVAL) is None:
        logger.error(f"配置的目标K线周期 '{TRAINING_DP_TARGET_INTERVAL}' 无法解析。脚本将退出。")
        exit()
        
    # --- 主调度循环 ---
    while True:
        try:
            next_run_datetime = get_next_kline_run_time(TRAINING_DP_TARGET_INTERVAL, KLINE_END_DELAY_SECONDS)
            now = datetime.now()
            
            wait_seconds = (next_run_datetime - now).total_seconds()
            
            if wait_seconds > 0:
                logger.info(f"等待 {wait_seconds:.2f} 秒至 {next_run_datetime.strftime('%Y-%m-%d %H:%M:%S')} 以执行下一次预测...")
                # Sleep in chunks to allow KeyboardInterrupt to be caught more readily
                sleep_chunk = 1 
                total_slept = 0
                while total_slept < wait_seconds:
                    time_to_sleep = min(sleep_chunk, wait_seconds - total_slept)
                    time.sleep(time_to_sleep)
                    total_slept += time_to_sleep
                    if total_slept >= wait_seconds : # check if loop should break
                        break
            
            logger.info(f"到达预定时间 {next_run_datetime.strftime('%Y-%m-%d %H:%M:%S')}, 执行预测任务。")
            perform_prediction_job()

        except KeyboardInterrupt:
            logger.info("持续预测被用户手动中断。")
            break
        except Exception as e:
            logger.error(f"主调度循环发生严重错误: {str(e)}", exc_info=True)
            logger.info("发生错误，等待60秒后尝试继续...")
            time.sleep(60) # Wait a bit before trying to continue, to avoid rapid-fire errors 