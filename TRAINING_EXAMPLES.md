# 🚀 训练模型代码使用指南

## 📋 目录
1. [基础训练方法](#基础训练方法)
2. [命令行训练](#命令行训练)
3. [快速训练](#快速训练)
4. [高级训练](#高级训练)
5. [批量训练](#批量训练)
6. [常见问题](#常见问题)

## 🎯 基础训练方法

### 1. 使用原始main.py训练

```bash
# 基础训练命令
python main.py train --symbol BTCUSDT --interval 1h --model_type xgb

# 带参数的训练
python main.py train \
    --symbol ETHUSDT \
    --interval 4h \
    --model_type lstm \
    --lstm_timesteps 15 \
    --lstm_epochs 100 \
    --use_optuna \
    --optuna_n_trials 100
```

### 2. 使用新的训练脚本

```bash
# 完整功能训练
python train_crypto_model.py \
    --symbol BTCUSDT \
    --interval 1h \
    --start-date 2022-01-01 \
    --model-type xgb \
    --optuna-trials 100

# LSTM模型训练
python train_crypto_model.py \
    --symbol ETHUSDT \
    --interval 1h \
    --model-type lstm \
    --lstm-timesteps 20 \
    --lstm-epochs 100
```

## ⚡ 快速训练

### 1. 最简单的训练方式

```bash
# 默认训练 (BTCUSDT, XGBoost, 90天数据)
python quick_train.py

# 指定交易对和模型
python quick_train.py quick ETHUSDT lgb

# 批量训练多个模型
python quick_train.py batch

# 模型性能比较
python quick_train.py compare BTCUSDT
```

### 2. Python代码中直接调用

```python
from quick_train import quick_train, compare_models

# 快速训练单个模型
trainer, results = quick_train('BTCUSDT', 'xgb')

# 比较多个模型
comparison = compare_models('ETHUSDT')
```

## 🔧 高级训练

### 1. 使用改进的训练脚本

```python
from improved_training import ImprovedModelTrainer

# 创建改进的训练器
trainer = ImprovedModelTrainer()

# 准备增强数据
X, y = trainer.prepare_enhanced_data('BTCUSDT', '1h', '2022-01-01')

# 训练优化模型
results = trainer.train_with_optimization(X, y, 'xgb')
```

### 2. 自定义配置训练

```python
from model_trainer import ModelTrainer
from feature_engineering import FeatureEngineer, FeatureConfig
from data_fetcher import BinanceDataFetcher

# 1. 获取数据
fetcher = BinanceDataFetcher()
df = fetcher.get_historical_data('BTCUSDT', '1h', '2022-01-01')

# 2. 自定义特征工程
config = FeatureConfig(
    ma_periods=[5, 10, 20, 50, 200],
    rsi_periods=[6, 14, 24],
    prediction_window=24
)
engineer = FeatureEngineer(config=config)
df_features = engineer.create_features(df)

# 3. 准备训练数据
X = df_features.drop(columns=['target'])
y = df_features['target']

# 4. 配置和训练模型
trainer = ModelTrainer(
    model_type='xgb',
    use_optuna=True,
    optuna_n_trials=100,
    scaler_type='robust',
    save_plots=True
)

results = trainer.train(X, y)
model_path, scaler_path = trainer.save_model()
```

## 📊 批量训练

### 1. 多交易对训练

```python
symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT']
models = ['xgb', 'lgb', 'rf']

for symbol in symbols:
    for model_type in models:
        print(f"训练 {symbol} - {model_type}")
        try:
            trainer, results = quick_train(symbol, model_type)
            print(f"✅ 完成: 准确率 {results['mean_scores']['accuracy']:.4f}")
        except Exception as e:
            print(f"❌ 失败: {str(e)}")
```

### 2. 多时间框架训练

```python
intervals = ['1h', '4h', '1d']
symbol = 'BTCUSDT'

for interval in intervals:
    print(f"训练 {symbol} {interval}")
    # 使用命令行调用
    import subprocess
    cmd = f"python train_crypto_model.py --symbol {symbol} --interval {interval} --model-type xgb"
    subprocess.run(cmd, shell=True)
```

## 🎛️ 参数配置示例

### 1. XGBoost优化配置

```python
trainer = ModelTrainer(
    model_type='xgb',
    use_optuna=True,
    optuna_n_trials=100,
    scaler_type='robust'
)
```

### 2. LSTM优化配置

```python
trainer = ModelTrainer(
    model_type='lstm',
    lstm_timesteps=20,      # 增加时间记忆
    lstm_units=128,         # 增加神经元数量
    lstm_dropout=0.3,       # 防止过拟合
    lstm_epochs=100,        # 增加训练轮数
    lstm_learning_rate=0.001,
    lstm_batch_size=64
)
```

### 3. 集成模型配置

```python
trainer = ModelTrainer(
    model_type='ensemble',
    use_optuna=True,
    optuna_n_trials=50
)
```

## 📈 训练结果示例

### 成功训练输出

```
🚀 开始快速训练 BTCUSDT xgb 模型...
📊 获取数据...
✅ 获取到 2160 条数据
🔧 特征工程...
✅ 特征: 89 个, 样本: 2136 个, 类别: 8 个
🎯 训练模型...

==================================================
🎉 训练完成!
==================================================
Accuracy    : 0.7234
Precision   : 0.7156
Recall      : 0.7234
F1          : 0.7189

💾 模型已保存:
   模型: models/model_20241219_143022.joblib
   缩放器: models/scaler_20241219_143022.joblib
```

### 模型比较输出

```
📊 BTCUSDT 模型性能比较
============================================================
Model   Accuracy    Precision   Recall      F1          
--------------------------------------------------------
RF      0.6892      0.6834      0.6892      0.6845      
GB      0.7123      0.7089      0.7123      0.7098      
XGB     0.7234      0.7156      0.7234      0.7189      
LGB     0.7198      0.7145      0.7198      0.7167      

🏆 最佳模型: XGB (准确率: 0.7234)
```

## ❗ 常见问题

### 1. 内存不足

```python
# 减少数据量
start_date = '2023-01-01'  # 使用更近的日期

# 减少特征数量
config = FeatureConfig(max_features_after_importance=50)

# 减少Optuna试验次数
trainer = ModelTrainer(optuna_n_trials=20)
```

### 2. 训练时间过长

```python
# 使用快速训练模式
trainer = ModelTrainer(
    use_optuna=False,  # 禁用超参数优化
    save_plots=False   # 不保存图表
)

# 或使用快速训练脚本
python quick_train.py quick BTCUSDT xgb
```

### 3. 模型性能不佳

```python
# 使用改进的训练脚本
python improved_training.py

# 或增加数据量和优化试验
python train_crypto_model.py \
    --start-date 2021-01-01 \
    --optuna-trials 200
```

### 4. CUDA/GPU问题 (LSTM)

```python
# 强制使用CPU
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

# 或减少批量大小
trainer = ModelTrainer(
    model_type='lstm',
    lstm_batch_size=16  # 减少批量大小
)
```

## 🔄 训练流程总结

1. **数据获取** → 从Binance获取历史K线数据
2. **特征工程** → 计算技术指标和衍生特征
3. **数据预处理** → 清理、缩放、分割数据
4. **模型训练** → 使用交叉验证训练模型
5. **超参数优化** → 使用Optuna自动调优
6. **模型评估** → 计算性能指标
7. **模型保存** → 保存训练好的模型和缩放器

选择适合您需求的训练方式，从简单的快速训练开始，逐步尝试更高级的配置！
