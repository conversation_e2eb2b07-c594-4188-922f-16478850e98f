@echo off
echo ========================================
echo 加密货币预测模型训练脚本
echo ========================================
echo.

echo 选择训练模式:
echo 1. 快速训练 (推荐新手)
echo 2. 标准训练
echo 3. 高级训练 (LSTM)
echo 4. 批量训练
echo 5. 模型比较
echo 6. 改进训练 (最高准确性)
echo.

set /p choice="请输入选择 (1-6): "

if "%choice%"=="1" goto quick_train
if "%choice%"=="2" goto standard_train
if "%choice%"=="3" goto lstm_train
if "%choice%"=="4" goto batch_train
if "%choice%"=="5" goto compare_models
if "%choice%"=="6" goto improved_train

echo 无效选择，退出...
goto end

:quick_train
echo.
echo 🚀 开始快速训练...
python quick_train.py
goto end

:standard_train
echo.
echo 🔧 开始标准训练...
python main.py train --symbol BTCUSDT --interval 1h --model_type xgb
goto end

:lstm_train
echo.
echo 🧠 开始LSTM训练...
python train_crypto_model.py --symbol BTCUSDT --interval 1h --model-type lstm --lstm-timesteps 15 --lstm-epochs 100
goto end

:batch_train
echo.
echo 📊 开始批量训练...
python quick_train.py batch
goto end

:compare_models
echo.
echo 🔍 开始模型比较...
python quick_train.py compare BTCUSDT
goto end

:improved_train
echo.
echo 🚀 开始改进训练...
python improved_training.py
goto end

:end
echo.
echo 训练完成！
pause
