#!/usr/bin/env python3
"""
Test script to verify the correct position size calculation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smart_ai_trader import SmartAITrader

def test_correct_position_size():
    """Test the correct position size calculation"""
    print("🧪 Testing Correct Position Size: 50u × 125倍 × 5%")
    print("=" * 60)
    
    # Create a test trader instance
    trader = SmartAITrader(
        initial_balance=50.0,
        leverage=125.0,
        trading_mode='aggressive',
        trading_frequency='high_frequency'
    )
    
    print("\n📊 User Requirements:")
    print("💰 Account: $50")
    print("⚡ Leverage: 125x")
    print("📊 Usage: 5% of account")
    print("🎯 Expected margin: $50 × 5% = $2.5")
    print("🚀 Expected buying power: $2.5 × 125 = $312.5")
    
    # Test position calculation
    mock_signal = {
        'direction': 'LONG',
        'confidence': 0.4,
        'trading_style': 'right_side',
        'signal_count': 2,
        'reasons': ['Test signal']
    }
    
    mock_market_data = {
        'current_price': 102400.0,
        'volatility': 0.005,
        'volume_ratio': 1.0
    }
    
    print(f"\n🔧 Testing Position Calculation:")
    print(f"   💰 Current Price: ${mock_market_data['current_price']:,.2f}")
    print(f"   💵 Available Balance: ${trader.account['balance']:.2f}")
    
    # Calculate position size using the corrected method
    position_size = trader.calculate_dynamic_position_size(mock_signal, mock_market_data)
    
    # Manual calculation for verification
    expected_margin = 50.0 * 0.05  # 5% of $50
    expected_nominal = expected_margin * 125  # 125x leverage
    expected_btc = expected_nominal / mock_market_data['current_price']
    
    print(f"\n✅ Manual Calculation:")
    print(f"   💰 Expected Margin: ${expected_margin:.2f}")
    print(f"   🚀 Expected Nominal Value: ${expected_nominal:.2f}")
    print(f"   📊 Expected BTC Size: {expected_btc:.6f} BTC")
    
    # System calculation
    if position_size > 0:
        actual_nominal = position_size * mock_market_data['current_price']
        actual_margin = actual_nominal / trader.leverage
        
        print(f"\n📊 System Calculation:")
        print(f"   📊 Actual BTC Size: {position_size:.6f} BTC")
        print(f"   💎 Actual Nominal Value: ${actual_nominal:.2f}")
        print(f"   💰 Actual Margin: ${actual_margin:.2f}")
        
        # Verification
        margin_diff = abs(actual_margin - expected_margin)
        btc_diff = abs(position_size - expected_btc)
        nominal_diff = abs(actual_nominal - expected_nominal)
        
        print(f"\n🔍 Verification:")
        print(f"   💰 Margin Difference: ${margin_diff:.2f}")
        print(f"   📊 BTC Difference: {btc_diff:.6f}")
        print(f"   💎 Nominal Difference: ${nominal_diff:.2f}")
        
        # Check if within acceptable range (considering confidence adjustment)
        if margin_diff < 1.0:  # Within $1
            print(f"   ✅ CORRECT: Margin calculation is accurate")
        else:
            print(f"   ❌ ERROR: Margin calculation is off")
        
        if btc_diff < 0.001:  # Within 0.001 BTC
            print(f"   ✅ CORRECT: BTC size calculation is accurate")
        else:
            print(f"   ❌ ERROR: BTC size calculation is off")
    else:
        print(f"\n❌ ERROR: No position calculated")
    
    # Risk analysis
    print(f"\n⚖️ Risk Analysis:")
    if position_size > 0:
        max_loss_2_percent = actual_margin * 0.02  # 2% ROI loss
        account_impact = (max_loss_2_percent / trader.account['balance']) * 100
        
        print(f"   🛑 2% ROI Loss: ${max_loss_2_percent:.2f}")
        print(f"   📊 Account Impact: {account_impact:.2f}%")
        print(f"   💰 Remaining After Loss: ${trader.account['balance'] - max_loss_2_percent:.2f}")
        
        if account_impact < 1:  # Less than 1% account impact
            print(f"   ✅ SAFE: Very low account risk")
        elif account_impact < 5:  # Less than 5% account impact
            print(f"   ✅ REASONABLE: Acceptable account risk")
        else:
            print(f"   ⚠️ HIGH: Consider reducing position")
    
    # Compare with previous disasters
    print(f"\n📊 Comparison with Previous Issues:")
    previous_losses = [15.47, 15.46, 15.46, 3.41]  # From the log
    avg_previous_loss = sum(previous_losses) / len(previous_losses)
    
    if position_size > 0:
        expected_max_loss = actual_margin  # Maximum loss in isolated margin
        improvement_factor = avg_previous_loss / expected_max_loss
        
        print(f"   ❌ Previous Average Loss: ${avg_previous_loss:.2f}")
        print(f"   ✅ Expected Max Loss: ${expected_max_loss:.2f}")
        print(f"   📈 Improvement Factor: {improvement_factor:.1f}x safer")
        
        if improvement_factor > 5:
            print(f"   🎉 EXCELLENT: Much safer than before")
        elif improvement_factor > 2:
            print(f"   ✅ GOOD: Significantly safer")
        else:
            print(f"   ⚠️ MARGINAL: Still needs improvement")
    
    print(f"\n" + "="*60)
    print("🎉 Position Size Test Complete!")
    
    if position_size > 0 and margin_diff < 1.0:
        print("✅ SUCCESS: Position calculation now follows user requirements")
        print("✅ Using exactly 5% of account ($2.5 margin)")
        print("✅ 125x leverage properly applied")
        print("✅ Much safer than previous disasters")
    else:
        print("❌ FAILED: Position calculation still has issues")
    
    print(f"\n💡 Key Points:")
    print(f"   📊 User requested: 50u × 125倍 × 5%")
    print(f"   💰 This means: $2.5 margin, $312.5 buying power")
    print(f"   📊 BTC amount: ~0.003 BTC (not 0.016!)")
    print(f"   🛡️ Maximum risk: $2.5 (5% of account)")
    
    return trader

if __name__ == "__main__":
    test_correct_position_size()
