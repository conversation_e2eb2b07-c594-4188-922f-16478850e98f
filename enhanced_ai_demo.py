#!/usr/bin/env python3
"""
增强版AI交易系统演示 - 展示如何将简单概率转换为完整策略
"""

import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple

class EnhancedAIDemo:
    """
    增强版AI演示 - 基于您的37.2%概率生成完整策略
    """
    
    def __init__(self):
        self.current_price = 104426.90
        self.ai_probability = 0.372  # 您的AI模型输出
        self.account_balance = 50.00
        self.leverage = 2
    
    def analyze_support_resistance(self) -> Dict:
        """
        模拟支撑阻力位分析
        """
        # 基于当前价格计算关键位
        current = self.current_price
        
        return {
            'support_levels': [103000, 102500, 102000],
            'resistance_levels': [105000, 105500, 106000],
            'ma_20': current * 0.995,  # 20日均线
            'ma_50': current * 0.988,  # 50日均线
        }
    
    def calculate_market_regime(self) -> str:
        """
        判断市场状态
        """
        # 基于价格位置和趋势判断
        if self.current_price > 104000:
            return "weak_downtrend"
        else:
            return "strong_downtrend"
    
    def calculate_volatility(self) -> float:
        """
        计算波动率
        """
        return 0.035  # 3.5%日波动率
    
    def classify_signal(self) -> Tuple[str, str]:
        """
        分类AI信号
        """
        prob = self.ai_probability
        
        if prob > 0.70:
            return 'long', 'strong'
        elif prob > 0.55:
            return 'long', 'weak'
        elif prob < 0.30:
            return 'short', 'strong'
        elif prob < 0.45:
            return 'short', 'weak'
        else:
            return 'neutral', 'none'
    
    def calculate_position_size(self, entry_price: float, stop_loss: float, confidence: float) -> Dict:
        """
        计算仓位大小
        """
        # 风险管理：最大风险2%
        max_risk = self.account_balance * 0.02
        
        # 价格风险
        price_risk = abs(entry_price - stop_loss) / entry_price
        
        # 根据置信度调整
        confidence_adj = min(confidence * 1.5, 1.0)
        adjusted_risk = max_risk * confidence_adj
        
        # 计算仓位
        position_value = adjusted_risk / price_risk
        margin_required = position_value / self.leverage
        btc_amount = position_value / entry_price
        
        # 限制最大保证金使用
        max_margin = self.account_balance * 0.6
        if margin_required > max_margin:
            margin_required = max_margin
            position_value = margin_required * self.leverage
            btc_amount = position_value / entry_price
        
        return {
            'btc_amount': btc_amount,
            'position_value': position_value,
            'margin_required': margin_required,
            'risk_amount': adjusted_risk,
            'risk_percentage': (adjusted_risk / self.account_balance) * 100
        }
    
    def calculate_stop_loss_take_profit(self, entry_price: float, direction: str) -> Dict:
        """
        计算止损止盈
        """
        volatility = self.calculate_volatility()
        
        # 基础止损2.5% + 波动率调整
        base_stop = 0.025
        vol_adjustment = min(volatility * 0.5, 0.01)
        final_stop = base_stop + vol_adjustment
        
        # 止盈 = 止损 * 2 (风险收益比2:1)
        take_profit_ratio = final_stop * 2
        
        if direction == 'long':
            stop_loss = entry_price * (1 - final_stop)
            take_profit = entry_price * (1 + take_profit_ratio)
        else:  # short
            stop_loss = entry_price * (1 + final_stop)
            take_profit = entry_price * (1 - take_profit_ratio)
        
        return {
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'stop_loss_pct': final_stop * 100,
            'take_profit_pct': take_profit_ratio * 100,
            'risk_reward_ratio': take_profit_ratio / final_stop
        }
    
    def generate_strategies(self) -> List[Dict]:
        """
        生成完整交易策略
        """
        direction, strength = self.classify_signal()
        sr_levels = self.analyze_support_resistance()
        market_regime = self.calculate_market_regime()
        
        strategies = []
        
        if direction == 'short':
            if strength == 'weak':
                # 策略1: 等待反弹做空
                entry_price = min(sr_levels['resistance_levels'])
                risk_metrics = self.calculate_stop_loss_take_profit(entry_price, 'short')
                position_calc = self.calculate_position_size(entry_price, risk_metrics['stop_loss'], 0.628)
                
                strategies.append({
                    'name': '等待反弹做空策略',
                    'action': '等待价格反弹至阻力位做空',
                    'entry_price': entry_price,
                    'trigger_condition': f'价格上涨至${entry_price:,.0f}',
                    'direction': 'SHORT',
                    'strength': 'WEAK',
                    'urgency': 'MEDIUM',
                    'position_sizing': position_calc,
                    'risk_management': risk_metrics,
                    'reasoning': f'AI显示{self.ai_probability:.1%}上涨概率(62.8%下跌)，等待更好价位',
                    'time_horizon': '1-3天',
                    'confidence': 0.75
                })
                
                # 策略2: 跌破支撑追空
                entry_price_2 = max(sr_levels['support_levels']) - 50
                risk_metrics_2 = self.calculate_stop_loss_take_profit(entry_price_2, 'short')
                position_calc_2 = self.calculate_position_size(entry_price_2, risk_metrics_2['stop_loss'], 0.8)
                
                strategies.append({
                    'name': '跌破支撑做空策略',
                    'action': '价格跌破关键支撑位追空',
                    'entry_price': entry_price_2,
                    'trigger_condition': f'价格跌破${max(sr_levels["support_levels"]):,.0f}',
                    'direction': 'SHORT',
                    'strength': 'MEDIUM',
                    'urgency': 'HIGH',
                    'position_sizing': position_calc_2,
                    'risk_management': risk_metrics_2,
                    'reasoning': '支撑位破位确认下跌趋势',
                    'time_horizon': '当日',
                    'confidence': 0.85
                })
        
        elif direction == 'neutral':
            strategies.append({
                'name': '观望等待策略',
                'action': '观望等待更明确信号',
                'reasoning': f'AI概率{self.ai_probability:.1%}接近中性，等待方向明确',
                'wait_conditions': [
                    f'跌破${max(sr_levels["support_levels"]):,.0f}→做空',
                    f'突破${min(sr_levels["resistance_levels"]):,.0f}→做多'
                ],
                'monitoring_points': [
                    '关注成交量变化',
                    '监控AI概率变化',
                    '观察关键价位反应'
                ]
            })
        
        return strategies
    
    def print_enhanced_analysis(self):
        """
        打印增强分析结果
        """
        print(f"\n🎯 【增强版AI交易策略分析】")
        print("=" * 70)
        
        # 基础AI分析
        direction, strength = self.classify_signal()
        print(f"🤖 AI模型分析:")
        print(f"   原始概率: {self.ai_probability:.1%} 上涨")
        print(f"   下跌概率: {1-self.ai_probability:.1%}")
        print(f"   信号方向: {direction.upper()}")
        print(f"   信号强度: {strength.upper()}")
        
        # 市场状况
        sr_levels = self.analyze_support_resistance()
        market_regime = self.calculate_market_regime()
        volatility = self.calculate_volatility()
        
        print(f"\n📊 市场状况:")
        print(f"   当前价格: ${self.current_price:,.2f}")
        print(f"   市场状态: {market_regime}")
        print(f"   波动率: {volatility:.2%}")
        
        print(f"\n📈 技术分析:")
        print(f"   支撑位: {', '.join([f'${level:,.0f}' for level in sr_levels['support_levels']])}")
        print(f"   阻力位: {', '.join([f'${level:,.0f}' for level in sr_levels['resistance_levels']])}")
        
        # 生成策略
        strategies = self.generate_strategies()
        
        print(f"\n🚀 交易策略建议:")
        for i, strategy in enumerate(strategies, 1):
            self.print_strategy_detail(strategy, i)
        
        print("=" * 70)
        
        # 对比说明
        print(f"\n💡 增强前后对比:")
        print(f"📊 原始AI输出: 37.2%概率")
        print(f"🎯 增强后输出: {len(strategies)}个完整策略")
        print(f"   • 具体入场价格和条件")
        print(f"   • 精确的仓位计算")
        print(f"   • 详细的风险管理")
        print(f"   • 多场景分析")
        print(f"   • 时间框架预估")
    
    def print_strategy_detail(self, strategy: Dict, index: int):
        """
        打印策略详情
        """
        urgency_emojis = {'HIGH': '🚨', 'MEDIUM': '⚠️', 'LOW': '📊'}
        
        print(f"\n   策略 {index}: {strategy['name']}")
        print(f"      动作: {strategy['action']}")
        
        if 'entry_price' in strategy:
            print(f"      入场价: ${strategy['entry_price']:,.2f}")
            print(f"      触发条件: {strategy['trigger_condition']}")
            print(f"      方向: {strategy['direction']}")
            print(f"      紧急度: {urgency_emojis.get(strategy['urgency'], '📊')} {strategy['urgency']}")
            
            pos = strategy['position_sizing']
            print(f"      仓位: {pos['btc_amount']:.6f} BTC")
            print(f"      保证金: ${pos['margin_required']:.2f}")
            print(f"      风险: ${pos['risk_amount']:.2f} ({pos['risk_percentage']:.1f}%)")
            
            risk = strategy['risk_management']
            print(f"      止损: ${risk['stop_loss']:,.2f} ({risk['stop_loss_pct']:.1f}%)")
            print(f"      止盈: ${risk['take_profit']:,.2f} ({risk['take_profit_pct']:.1f}%)")
            print(f"      风险收益比: 1:{risk['risk_reward_ratio']:.1f}")
            
            print(f"      持仓时间: {strategy['time_horizon']}")
            print(f"      置信度: {strategy['confidence']:.1%}")
            print(f"      理由: {strategy['reasoning']}")
        
        elif 'wait_conditions' in strategy:
            print(f"      理由: {strategy['reasoning']}")
            print(f"      等待条件:")
            for condition in strategy['wait_conditions']:
                print(f"        • {condition}")
            print(f"      监控要点:")
            for point in strategy['monitoring_points']:
                print(f"        • {point}")

def main():
    """
    主函数演示
    """
    print("🚀 增强版AI交易系统演示")
    print("=" * 50)
    print("演示如何将简单的AI概率转换为完整的交易策略")
    print("")
    
    # 创建演示实例
    demo = EnhancedAIDemo()
    
    # 执行增强分析
    demo.print_enhanced_analysis()
    
    print(f"\n🎯 总结:")
    print(f"通过增强框架，您的AI模型从输出单一概率值")
    print(f"升级为提供完整的交易决策支持系统！")

if __name__ == "__main__":
    main()
