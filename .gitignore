# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# 项目特定
*.log
logs/
data/
models/
results/
.env
config_local.py
performance_report.txt

# 测试
.coverage
htmlcov/
.pytest_cache/
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/ 