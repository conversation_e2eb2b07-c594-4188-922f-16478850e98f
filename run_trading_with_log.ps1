# 智能交易系统 - PowerShell日志保存脚本

Write-Host "🚀 智能交易系统 - 自动日志保存" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Yellow

# 创建logs目录
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs"
}

# 生成时间戳
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logfile = "logs\trading_log_$timestamp.log"

Write-Host "📝 日志将保存到: $logfile" -ForegroundColor Cyan
Write-Host "⏰ 开始时间: $(Get-Date)" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Yellow

try {
    # 运行交易系统并保存日志
    python smart_auto_trading.py 2>&1 | Tee-Object -FilePath $logfile
}
catch {
    Write-Host "❌ 运行出错: $_" -ForegroundColor Red
}
finally {
    Write-Host ""
    Write-Host "📝 完整日志已保存到: $logfile" -ForegroundColor Green
    Write-Host "按任意键退出..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
