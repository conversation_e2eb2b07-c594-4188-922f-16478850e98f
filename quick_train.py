#!/usr/bin/env python3
"""
快速训练脚本 - 简化版本
适合快速测试和原型开发
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入项目模块
from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer, FeatureConfig
# 暂时注释掉ModelTrainer以避免TensorFlow问题
# from model_trainer import ModelTrainer
from train_no_tensorflow import SimpleModelTrainer

# 简单日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_train(symbol='BTCUSDT', model_type='xgb', days_back=90):
    """
    快速训练模型 - 最简化版本
    
    参数:
        symbol: 交易对 (默认: BTCUSDT)
        model_type: 模型类型 (默认: xgb)
        days_back: 回看天数 (默认: 90天)
    """
    
    print(f"🚀 开始快速训练 {symbol} {model_type} 模型...")
    
    try:
        # 1. 获取数据 (最近90天)
        print("📊 获取数据...")
        start_date = (datetime.now() - pd.Timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        fetcher = BinanceDataFetcher()
        df = fetcher.get_historical_data(symbol, '1h', start_date)
        print(f"✅ 获取到 {len(df)} 条数据")
        
        # 2. 特征工程
        print("🔧 特征工程...")
        engineer = FeatureEngineer()
        df_features = engineer.create_features(df)
        
        # 分离特征和目标
        X = df_features.drop(columns=['target'])
        y = df_features['target']
        
        # 移除原始列
        original_cols = ['open', 'high', 'low', 'close', 'volume']
        X = X.drop(columns=[col for col in original_cols if col in X.columns], errors='ignore')
        
        print(f"✅ 特征: {X.shape[1]} 个, 样本: {len(y)} 个, 类别: {len(y.unique())} 个")
        
        # 3. 训练模型
        print("🎯 训练模型...")
        trainer = SimpleModelTrainer(
            model_type=model_type,
            use_optuna=True,
            n_trials=20  # 快速训练，减少试验次数
        )
        
        results = trainer.train(X, y)
        
        # 4. 显示结果
        print("\n" + "="*50)
        print("🎉 训练完成!")
        print("="*50)
        
        mean_scores = results.get('mean_scores', {})
        for metric in ['accuracy', 'precision', 'recall', 'f1']:
            if metric in mean_scores:
                print(f"{metric.capitalize():12}: {mean_scores[metric]:.4f}")
        
        # 5. 保存模型
        model_path, scaler_path = trainer.save_model()
        print(f"\n💾 模型已保存:")
        print(f"   模型: {model_path}")
        print(f"   缩放器: {scaler_path}")
        
        return trainer, results
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        raise

def batch_train():
    """批量训练多个模型"""
    
    symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
    models = ['xgb', 'lgb', 'rf']
    
    results = {}
    
    for symbol in symbols:
        for model_type in models:
            print(f"\n{'='*60}")
            print(f"训练 {symbol} - {model_type}")
            print(f"{'='*60}")
            
            try:
                trainer, result = quick_train(symbol, model_type)
                results[f"{symbol}_{model_type}"] = {
                    'trainer': trainer,
                    'results': result,
                    'accuracy': result['mean_scores']['accuracy']
                }
                print(f"✅ {symbol} - {model_type} 训练完成")
                
            except Exception as e:
                print(f"❌ {symbol} - {model_type} 训练失败: {str(e)}")
                results[f"{symbol}_{model_type}"] = {'error': str(e)}
    
    # 显示所有结果
    print(f"\n{'='*60}")
    print("📊 批量训练结果汇总")
    print(f"{'='*60}")
    
    for key, result in results.items():
        if 'accuracy' in result:
            print(f"{key:20}: 准确率 {result['accuracy']:.4f}")
        else:
            print(f"{key:20}: 训练失败")
    
    return results

def compare_models(symbol='BTCUSDT'):
    """比较不同模型性能"""
    
    models = ['rf', 'gb', 'xgb', 'lgb']
    results = {}
    
    print(f"🔍 比较 {symbol} 的不同模型性能...")
    
    for model_type in models:
        print(f"\n训练 {model_type.upper()} 模型...")
        try:
            trainer, result = quick_train(symbol, model_type)
            results[model_type] = result['mean_scores']
            print(f"✅ {model_type.upper()} 完成")
        except Exception as e:
            print(f"❌ {model_type.upper()} 失败: {str(e)}")
            results[model_type] = {'error': str(e)}
    
    # 显示比较结果
    print(f"\n{'='*60}")
    print(f"📊 {symbol} 模型性能比较")
    print(f"{'='*60}")
    
    metrics = ['accuracy', 'precision', 'recall', 'f1']
    
    # 表头
    print(f"{'Model':<8}", end='')
    for metric in metrics:
        print(f"{metric.capitalize():<12}", end='')
    print()
    print("-" * 56)
    
    # 数据行
    for model_type, scores in results.items():
        if 'error' not in scores:
            print(f"{model_type.upper():<8}", end='')
            for metric in metrics:
                if metric in scores:
                    print(f"{scores[metric]:<12.4f}", end='')
                else:
                    print(f"{'N/A':<12}", end='')
            print()
        else:
            print(f"{model_type.upper():<8}{'ERROR':<48}")
    
    # 找出最佳模型
    best_model = None
    best_accuracy = 0
    
    for model_type, scores in results.items():
        if 'accuracy' in scores and scores['accuracy'] > best_accuracy:
            best_accuracy = scores['accuracy']
            best_model = model_type
    
    if best_model:
        print(f"\n🏆 最佳模型: {best_model.upper()} (准确率: {best_accuracy:.4f})")
    
    return results

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'quick':
            # 快速训练单个模型
            symbol = sys.argv[2] if len(sys.argv) > 2 else 'BTCUSDT'
            model_type = sys.argv[3] if len(sys.argv) > 3 else 'xgb'
            quick_train(symbol, model_type)
            
        elif command == 'batch':
            # 批量训练
            batch_train()
            
        elif command == 'compare':
            # 模型比较
            symbol = sys.argv[2] if len(sys.argv) > 2 else 'BTCUSDT'
            compare_models(symbol)
            
        else:
            print("❌ 未知命令")
            print("使用方法:")
            print("  python quick_train.py quick [SYMBOL] [MODEL_TYPE]")
            print("  python quick_train.py batch")
            print("  python quick_train.py compare [SYMBOL]")
    else:
        # 默认快速训练
        print("🎯 默认快速训练模式")
        print("使用 'python quick_train.py --help' 查看更多选项")
        quick_train()
