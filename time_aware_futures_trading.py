#!/usr/bin/env python3
"""
时间感知永续合约交易系统 - 基于K线时间和市场周期的智能交易
"""

import pandas as pd
import numpy as np
import time
import json
from datetime import datetime, timedelta
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_fetcher import BinanceDataFetcher
from feature_engineering import FeatureEngineer

class TimeAwareFuturesTrader:
    """
    时间感知永续合约交易器
    """
    
    def __init__(self, initial_capital=50, leverage=2, model_path=None):
        """
        初始化时间感知交易器
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.leverage = min(max(leverage, 1), 3)
        
        # 持仓状态
        self.position = 0
        self.entry_price = 0
        self.entry_time = None
        self.margin_used = 0
        
        # 时间感知的交易参数
        self.time_based_thresholds = {
            # 亚洲时段 (UTC 0-8) - 相对平静
            'asia': {
                'strong_long': 0.72, 'weak_long': 0.58,
                'weak_short': 0.42, 'strong_short': 0.28,
                'position_multiplier': 0.8  # 降低仓位
            },
            # 欧洲时段 (UTC 8-16) - 中等活跃
            'europe': {
                'strong_long': 0.70, 'weak_long': 0.56,
                'weak_short': 0.44, 'strong_short': 0.30,
                'position_multiplier': 1.0  # 标准仓位
            },
            # 美洲时段 (UTC 16-24) - 最活跃
            'america': {
                'strong_long': 0.68, 'weak_long': 0.54,
                'weak_short': 0.46, 'strong_short': 0.32,
                'position_multiplier': 1.2  # 增加仓位
            }
        }
        
        # K线时间特征权重
        self.kline_time_weights = {
            'trend_continuation': {  # 趋势延续时段
                'hours': [1, 2, 3, 9, 10, 11, 17, 18, 19],
                'weight': 1.2
            },
            'reversal_prone': {     # 容易反转时段
                'hours': [0, 4, 8, 12, 16, 20],
                'weight': 0.8
            },
            'high_volatility': {    # 高波动时段
                'hours': [14, 15, 16, 21, 22, 23],
                'weight': 1.1
            }
        }
        
        # 周期性调整
        self.weekly_adjustments = {
            0: 0.9,  # 周一 - 谨慎
            1: 1.0,  # 周二 - 标准
            2: 1.1,  # 周三 - 积极
            3: 1.1,  # 周四 - 积极
            4: 0.8,  # 周五 - 保守
            5: 0.7,  # 周六 - 很保守
            6: 0.7   # 周日 - 很保守
        }
        
        # 基础风险参数
        self.base_risk_params = {
            'strong_signal': {
                'stop_loss': 0.03, 'take_profit': 0.08, 'max_hold_hours': 24
            },
            'weak_signal': {
                'stop_loss': 0.025, 'take_profit': 0.06, 'max_hold_hours': 16
            }
        }
        
        self.commission_rate = 0.0004
        self.funding_rate = 0.0001
        
        # 交易记录
        self.trades = []
        self.equity_history = []
        self.time_analysis = []
        
        # 加载模型
        if model_path is None:
            import glob
            model_files = glob.glob("models/binary_trend_BTCUSDT_*.joblib")
            if not model_files:
                raise ValueError("未找到BTCUSDT模型文件")
            model_path = max(model_files, key=lambda x: x.split('_')[-1])
        
        self.model_data = joblib.load(model_path)
        self.model = self.model_data['model']
        self.scaler = self.model_data['scaler']
        
        self.fetcher = BinanceDataFetcher()
        self.engineer = FeatureEngineer()
        
        print(f"🚀 时间感知永续合约交易器初始化完成")
        print(f"   初始资金: ${self.initial_capital}")
        print(f"   杠杆倍数: {self.leverage}x")
        print(f"   时间感知: 亚洲/欧洲/美洲时段差异化")
        print(f"   K线时间: 趋势延续/反转倾向/高波动识别")
        print(f"   周期调整: 工作日/周末差异化")
    
    def get_current_time_context(self, timestamp=None):
        """
        获取当前时间上下文
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        utc_hour = timestamp.hour
        weekday = timestamp.weekday()
        
        # 确定交易时段
        if 0 <= utc_hour < 8:
            session = 'asia'
        elif 8 <= utc_hour < 16:
            session = 'europe'
        else:
            session = 'america'
        
        # K线时间特征
        kline_weight = 1.0
        time_feature = 'normal'
        
        for feature, config in self.kline_time_weights.items():
            if utc_hour in config['hours']:
                kline_weight = config['weight']
                time_feature = feature
                break
        
        # 周期调整
        weekly_weight = self.weekly_adjustments[weekday]
        
        return {
            'session': session,
            'utc_hour': utc_hour,
            'weekday': weekday,
            'kline_weight': kline_weight,
            'time_feature': time_feature,
            'weekly_weight': weekly_weight,
            'thresholds': self.time_based_thresholds[session]
        }
    
    def analyze_time_adjusted_signal(self, up_probability, time_context):
        """
        基于时间上下文分析调整后的信号
        """
        thresholds = time_context['thresholds']
        kline_weight = time_context['kline_weight']
        weekly_weight = time_context['weekly_weight']
        position_multiplier = thresholds['position_multiplier']
        
        # 时间调整后的概率
        adjusted_probability = up_probability
        
        # K线时间权重调整
        if time_context['time_feature'] == 'trend_continuation':
            # 趋势延续时段，增强信号
            if up_probability > 0.5:
                adjusted_probability = min(0.95, up_probability * kline_weight)
            else:
                adjusted_probability = max(0.05, up_probability / kline_weight)
        elif time_context['time_feature'] == 'reversal_prone':
            # 反转倾向时段，减弱信号
            adjusted_probability = 0.5 + (up_probability - 0.5) * kline_weight
        
        # 确定信号类型
        if adjusted_probability > thresholds['strong_long']:
            signal_type = 'strong_long'
            base_position = 0.7
        elif adjusted_probability > thresholds['weak_long']:
            signal_type = 'weak_long'
            base_position = 0.5
        elif adjusted_probability < thresholds['strong_short']:
            signal_type = 'strong_short'
            base_position = 0.7
        elif adjusted_probability < thresholds['weak_short']:
            signal_type = 'weak_short'
            base_position = 0.5
        else:
            signal_type = 'neutral'
            base_position = 0
        
        # 应用时间和周期调整
        if signal_type != 'neutral':
            final_position = base_position * position_multiplier * weekly_weight
            final_position = min(0.8, max(0.3, final_position))  # 限制在30-80%
        else:
            final_position = 0
        
        return {
            'signal_type': signal_type,
            'original_probability': up_probability,
            'adjusted_probability': adjusted_probability,
            'position_size': final_position,
            'time_context': time_context
        }
    
    def get_current_prediction(self, symbol='BTCUSDT'):
        """
        获取当前预测
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = self.fetcher.get_historical_data(
                symbol, '1h', start_time.strftime('%Y-%m-%d'), is_futures=True
            )
            
            if len(df) < 200:
                return None, None, None
            
            df_features = self.engineer.create_features(df)
            X = df_features.drop(columns=['target'], errors='ignore')
            cols_to_remove = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
            X = X.drop(columns=[col for col in cols_to_remove if col in X.columns], errors='ignore')
            
            X_latest = X.iloc[-1:].copy()
            X_latest.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_latest.fillna(X.median(), inplace=True)
            
            X_scaled = self.scaler.transform(X_latest)
            prediction_proba = self.model.predict_proba(X_scaled)
            up_probability = prediction_proba[0, 1]
            
            current_price = self.fetcher.get_current_price(symbol, is_futures=True)
            current_time = datetime.now()
            
            return up_probability, current_price, current_time
            
        except Exception as e:
            print(f"❌ 获取预测错误: {str(e)}")
            return None, None, None
    
    def should_open_position(self, up_probability, current_time):
        """
        判断是否开仓 - 时间感知版本
        """
        if self.position != 0:
            return False, 0, 0, None, "已有持仓"
        
        time_context = self.get_current_time_context(current_time)
        signal_analysis = self.analyze_time_adjusted_signal(up_probability, time_context)
        
        if signal_analysis['signal_type'] == 'neutral':
            return False, 0, 0, None, f"中性信号 (原始:{up_probability:.1%}, 调整:{signal_analysis['adjusted_probability']:.1%})"
        
        # 确定方向
        if 'long' in signal_analysis['signal_type']:
            direction = 1
        else:
            direction = -1
        
        # 选择风险参数
        if 'strong' in signal_analysis['signal_type']:
            risk_params = self.base_risk_params['strong_signal'].copy()
        else:
            risk_params = self.base_risk_params['weak_signal'].copy()
        
        # 时间调整风险参数
        if time_context['time_feature'] == 'high_volatility':
            risk_params['stop_loss'] *= 1.2  # 高波动时段增加止损
            risk_params['take_profit'] *= 1.3  # 增加止盈
        elif time_context['time_feature'] == 'reversal_prone':
            risk_params['max_hold_hours'] *= 0.7  # 反转时段缩短持仓时间
        
        reason = f"{signal_analysis['signal_type']} ({time_context['session']}时段, {time_context['time_feature']})"
        
        return True, direction, signal_analysis['position_size'], risk_params, reason
    
    def should_close_position(self, up_probability, current_price, current_time):
        """
        判断是否平仓 - 时间感知版本
        """
        if self.position == 0:
            return False, "无持仓"
        
        # 计算盈亏
        if self.position > 0:
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        # 获取当前时间上下文
        time_context = self.get_current_time_context(current_time)
        
        # 使用当前风险参数
        if hasattr(self, 'current_risk_params'):
            risk_params = self.current_risk_params
        else:
            risk_params = self.base_risk_params['weak_signal']
        
        # 时间调整的平仓逻辑
        if time_context['time_feature'] == 'reversal_prone':
            # 反转时段更容易平仓
            if self.position > 0 and up_probability < 0.45:
                return True, f"反转时段多头平仓 ({up_probability:.1%})"
            elif self.position < 0 and up_probability > 0.55:
                return True, f"反转时段空头平仓 ({up_probability:.1%})"
        
        # 标准平仓逻辑
        if pnl_ratio < -risk_params['stop_loss']:
            return True, f"止损 ({pnl_ratio:.2%})"
        
        if pnl_ratio > risk_params['take_profit']:
            return True, f"止盈 ({pnl_ratio:.2%})"
        
        if self.entry_time:
            hold_hours = (current_time - self.entry_time).total_seconds() / 3600
            if hold_hours > risk_params['max_hold_hours']:
                return True, f"时间止损 ({hold_hours:.1f}h)"
        
        return False, "持有"
    
    def execute_trade(self, action, direction, position_size_ratio, price, timestamp, confidence=None, reason="", risk_params=None):
        """
        执行交易
        """
        time_context = self.get_current_time_context(timestamp)
        
        if action == 'OPEN':
            available_margin = self.capital * 0.8
            position_value = available_margin * position_size_ratio * self.leverage
            position_size = position_value / price
            
            if direction == -1:
                position_size = -position_size
            
            self.position = position_size
            self.entry_price = price
            self.entry_time = timestamp
            self.margin_used = available_margin * position_size_ratio
            self.current_risk_params = risk_params
            
            opening_fee = abs(position_size) * price * self.commission_rate
            self.capital -= opening_fee
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'OPEN',
                'direction': 'LONG' if direction == 1 else 'SHORT',
                'price': price,
                'position_size': position_size,
                'position_ratio': position_size_ratio,
                'margin_used': self.margin_used,
                'leverage': self.leverage,
                'confidence': confidence,
                'reason': reason,
                'time_context': time_context,
                'risk_params': risk_params,
                'opening_fee': opening_fee
            }
            
            direction_text = "做多" if direction == 1 else "做空"
            print(f"✅ {direction_text} {abs(position_size):.6f} BTC @ ${price:,.2f}")
            print(f"   时段: {time_context['session']}, 特征: {time_context['time_feature']}")
            print(f"   仓位: {position_size_ratio:.0%}, 置信度: {confidence:.1%}")
            
        elif action == 'CLOSE':
            if self.position > 0:
                pnl_ratio = (price - self.entry_price) / self.entry_price
            else:
                pnl_ratio = (self.entry_price - price) / self.entry_price
            
            leveraged_pnl = pnl_ratio * self.leverage * self.margin_used
            closing_fee = abs(self.position) * price * self.commission_rate
            funding_fee = self.calculate_funding_fee()
            final_pnl = leveraged_pnl - closing_fee + funding_fee
            
            self.capital = self.capital + self.margin_used + final_pnl
            
            hold_time = (timestamp - self.entry_time).total_seconds() / 3600 if self.entry_time else 0
            
            trade_record = {
                'timestamp': timestamp,
                'action': 'CLOSE',
                'direction': 'LONG' if self.position > 0 else 'SHORT',
                'price': price,
                'entry_price': self.entry_price,
                'pnl_ratio': pnl_ratio,
                'final_pnl': final_pnl,
                'hold_hours': hold_time,
                'confidence': confidence,
                'reason': reason,
                'time_context': time_context,
                'capital_after': self.capital
            }
            
            direction_text = "平多" if self.position > 0 else "平空"
            print(f"✅ {direction_text} @ ${price:,.2f} (盈亏: {final_pnl:+.2f}, 资金: ${self.capital:.2f})")
            print(f"   持仓时长: {hold_time:.1f}小时, 时段: {time_context['session']}")
            
            self.position = 0
            self.entry_price = 0
            self.entry_time = None
            self.margin_used = 0
            self.current_risk_params = None
        
        self.trades.append(trade_record)
    
    def calculate_funding_fee(self):
        """计算资金费率"""
        if self.position == 0 or not self.entry_time:
            return 0
        
        hold_hours = (datetime.now() - self.entry_time).total_seconds() / 3600
        funding_periods = int(hold_hours / 8)
        
        if funding_periods > 0:
            position_value = abs(self.position) * self.entry_price
            funding_fee = position_value * self.funding_rate * funding_periods
            return -funding_fee if self.position > 0 else funding_fee
        return 0
    
    def update_equity(self, current_price, timestamp):
        """更新权益"""
        if self.position != 0:
            if self.position > 0:
                unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
            else:
                unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
            
            unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
            funding_fee = self.calculate_funding_fee()
            current_equity = self.capital + self.margin_used + unrealized_pnl + funding_fee
        else:
            current_equity = self.capital
        
        self.equity_history.append({
            'timestamp': timestamp,
            'price': current_price,
            'equity': current_equity,
            'total_return': (current_equity - self.initial_capital) / self.initial_capital
        })
    
    def print_status(self, current_price=None, up_probability=None, current_time=None):
        """打印状态"""
        if self.equity_history:
            latest_equity = self.equity_history[-1]['equity']
            total_return = self.equity_history[-1]['total_return']
        else:
            latest_equity = self.capital
            total_return = 0
        
        completed_trades = [t for t in self.trades if t['action'] == 'CLOSE']
        profitable_trades = [t for t in completed_trades if t.get('final_pnl', 0) > 0]
        win_rate = len(profitable_trades) / len(completed_trades) if completed_trades else 0
        
        print(f"\n📊 时间感知永续合约交易状态")
        print("=" * 50)
        print(f"当前权益: ${latest_equity:.2f}")
        print(f"总收益率: {total_return:+.2%}")
        print(f"可用资金: ${self.capital:.2f}")
        print(f"占用保证金: ${self.margin_used:.2f}")
        
        if self.position != 0:
            position_type = "多头" if self.position > 0 else "空头"
            print(f"当前持仓: {position_type} {abs(self.position):.6f} BTC")
            print(f"入场价格: ${self.entry_price:,.2f}")
            
            if current_price:
                if self.position > 0:
                    unrealized_pnl_ratio = (current_price - self.entry_price) / self.entry_price
                else:
                    unrealized_pnl_ratio = (self.entry_price - current_price) / self.entry_price
                
                unrealized_pnl = unrealized_pnl_ratio * self.leverage * self.margin_used
                print(f"未实现盈亏: {unrealized_pnl:+.2f} ({unrealized_pnl_ratio * self.leverage:+.2%})")
        else:
            print(f"当前持仓: 空仓")
        
        print(f"完成交易: {len(completed_trades)}")
        print(f"胜率: {win_rate:.2%}")
        
        if current_price and up_probability and current_time:
            time_context = self.get_current_time_context(current_time)
            signal_analysis = self.analyze_time_adjusted_signal(up_probability, time_context)
            
            print(f"\n📈 时间感知市场分析:")
            print(f"BTC永续价格: ${current_price:,.2f}")
            print(f"原始概率: {up_probability:.1%}")
            print(f"调整概率: {signal_analysis['adjusted_probability']:.1%}")
            print(f"交易时段: {time_context['session']} ({time_context['utc_hour']}:00 UTC)")
            print(f"时间特征: {time_context['time_feature']}")
            print(f"周期权重: {time_context['weekly_weight']:.1f}")
            
            signal_map = {
                'strong_long': "🟢 强烈看涨",
                'weak_long': "🟡 轻微看涨", 
                'weak_short': "🟡 轻微看跌",
                'strong_short': "🔴 强烈看跌",
                'neutral': "⚪ 中性"
            }
            
            print(f"信号类型: {signal_map.get(signal_analysis['signal_type'], '未知')}")
            if signal_analysis['position_size'] > 0:
                print(f"建议仓位: {signal_analysis['position_size']:.0%}")

def run_time_aware_simulation(check_interval=300, leverage=2):
    """运行时间感知模拟"""
    print("🚀 启动时间感知永续合约模拟交易系统")
    print("=" * 60)
    print("特点: K线时间分析、交易时段优化、周期性调整")
    print(f"杠杆倍数: {leverage}x")
    print(f"检查间隔: {check_interval}秒")
    print("")
    
    trader = TimeAwareFuturesTrader(initial_capital=50, leverage=leverage)
    
    try:
        while True:
            current_time = datetime.now()
            print(f"\n⏰ {current_time.strftime('%Y-%m-%d %H:%M:%S')} - 时间感知市场分析...")
            
            up_prob, current_price, _ = trader.get_current_prediction()
            
            if up_prob is None:
                print("❌ 无法获取预测，跳过")
                time.sleep(check_interval)
                continue
            
            trader.update_equity(current_price, current_time)
            
            # 检查平仓
            if trader.position != 0:
                should_close, close_reason = trader.should_close_position(up_prob, current_price, current_time)
                if should_close:
                    trader.execute_trade('CLOSE', 0, 0, current_price, current_time, up_prob, close_reason)
            
            # 检查开仓
            should_open, direction, position_size, risk_params, open_reason = trader.should_open_position(up_prob, current_time)
            if should_open:
                trader.execute_trade('OPEN', direction, position_size, current_price, current_time, up_prob, open_reason, risk_params)
            
            trader.print_status(current_price, up_prob, current_time)
            
            print(f"\n⏳ 等待 {check_interval/60:.1f} 分钟...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        print(f"\n🛑 停止时间感知交易")
        trader.print_status(current_price, up_prob, current_time)

if __name__ == "__main__":
    import sys
    
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 300
    leverage = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    leverage = min(max(leverage, 1), 3)
    
    print("🕐 时间感知交易系统说明:")
    print("- 基于K线时间的智能阈值调整")
    print("- 亚洲/欧洲/美洲时段差异化策略")
    print("- 趋势延续/反转倾向/高波动时间识别")
    print("- 工作日/周末周期性调整")
    print("- 动态风险参数优化")
    print("")
    
    run_time_aware_simulation(interval, leverage)
